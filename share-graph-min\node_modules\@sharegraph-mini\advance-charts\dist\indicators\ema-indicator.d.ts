import { ISeriesApi, Nominal, SeriesType, SingleValueData, WhitespaceData } from 'lightweight-charts';
import { Context, IIndicatorBar } from '../helpers/execution-indicator';
import { SeriesPrimitiveBase } from '../custom-primitive/primitive-base';
import { LinePrimitivePaneView } from '../custom-primitive/pane-view/line';
import { ChartIndicator, ChartIndicatorOptions } from './abstract-indicator';

export interface EMAIndicatorOptions extends ChartIndicatorOptions {
    color: string;
    period: number;
}
export declare const defaultOptions: EMAIndicatorOptions;
export declare class EMAPrimitive extends SeriesPrimitiveBase<SingleValueData | WhitespaceData> {
    protected source: EMAIndicator;
    linePrimitive: LinePrimitivePaneView;
    constructor(source: EMAIndicator);
    update(indicatorBars: IIndicatorBar<EMAData>[]): void;
}
export type EMAData = readonly [Nominal<number, 'EMA'>];
export default class EMAIndicator extends ChartIndicator<EMAIndicatorOptions, EMAData> {
    emaPrimitive: EMAPrimitive;
    getDefaultOptions(): EMAIndicatorOptions;
    _mainSeriesChanged(series: ISeriesApi<SeriesType>): void;
    remove(): void;
    applyIndicatorData(): void;
    formula(c: Context): EMAData | undefined;
}
