import { useAppStore } from "../../stores/useAppStore";
import RangeChartItem from "./RangeChartItem";
import CustomRangeItem from "./CustomRangeItem";

export default function RangeChart() {
  const chartRange = useAppStore((state) => state.chartRange);
  const dateRangesAndInterval = useAppStore((state) => state.appSettings.dateRangesAndInterval);
  const { period } = chartRange;

  return (
    <>
      <div className="chart-range">
        <div className="chart-range__controls">
          <ul className="chart-range__timeline-options">
            {dateRangesAndInterval.map((option) => {
              const periodValue = option.period;
              const isActivePeriod = period === periodValue;
              return (
                <RangeChartItem
                  key={periodValue}
                  option={option}
                  isActivePeriod={isActivePeriod} 
                />
              );
            })}
            <CustomRangeItem period={period} />
          </ul>
        </div>
      </div>
    </>
  );
}
