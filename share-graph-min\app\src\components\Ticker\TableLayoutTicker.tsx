import dayjs from 'dayjs';
import { useAppStore } from '../../stores/useAppStore';
import clsx from 'clsx';
import { TICKER_DATE_FORMAT } from '../../constants/common';
import { i18n } from '@euroland/libs';
import InstrumentRadio from '../InstrumentRadio';


const TableLayoutTicker = () => {
  const marketInfo = useAppStore((state) => state.marketInfo);
  const tickers = useAppStore((state) => state.tickers);
  const selectedInstrumentId = useAppStore((state) => state.selectedInstrumentId);
  const formatNumberInstance = useAppStore((state) => state.formatNumberInstance);

  const instrumentIds = useAppStore((state) => state.appSettings.instrumentIds);
  const isSingleInstrument = instrumentIds.length <= 1;

  const ticker = tickers[selectedInstrumentId];

  const { currentPrice } = ticker;
  const tickerFields = [
    {
      id: 'column1',
      fields: [
        {
          label: i18n.translate('change'),
          value: (
            <span className={clsx("ticker-table__change", {
              up: currentPrice.change > 0,
              down: currentPrice.change < 0,
            })}>
              {`${formatNumberInstance.decimal(currentPrice.change)} (${formatNumberInstance.decimal(currentPrice.changePercentage)}%)`}
            </span>
          ),
          compareValue: currentPrice.change,
        },
        {
          label: i18n.translate('volume'),
          value: `${formatNumberInstance.decimal(currentPrice.volume)}`,
          compareValue: currentPrice.volume,
        },
        {
          label: i18n.translate('todayOpen'),
          value: `${formatNumberInstance.decimal(currentPrice.open)}`,
          compareValue: currentPrice.open,
        },
        {
          label: i18n.translate('preClose'),
          value: `${formatNumberInstance.decimal(currentPrice.prevClose)}`,
          compareValue: currentPrice.prevClose,
        },
      ],
    },
    {
      id: 'column2',
      fields: [
        {
          label: i18n.translate('todayHigh'),
          value: `${formatNumberInstance.decimal(currentPrice.high)}`,
          compareValue: currentPrice.high,
        },
        {
          label: i18n.translate('todayLow'),
          value: `${formatNumberInstance.decimal(currentPrice.low)}`,
          compareValue: currentPrice.low,
        },
        {
          label: i18n.translate('52WeekHigh'),
          value: `${formatNumberInstance.decimal(ticker.high52W)}`,
          compareValue: ticker.high52W,
        },
        {
          label: i18n.translate('52WeekLow'),
          value: `${formatNumberInstance.decimal(ticker.low52W)}`,
          compareValue: ticker.low52W,
        },
      ],
    },
  ];
  const date = marketInfo?.marketManger.toMarketDate(dayjs(currentPrice.date));
  return (
    <div className="ticker-table">
      <div className={clsx("ticker-table__price", {
        single: isSingleInstrument,
      })}>
        <InstrumentRadio />
        <p className="ticker-table__quote-price">
          {formatNumberInstance.decimal(currentPrice.last)} {ticker.currency.code}
        </p>
        <p className="ticker-table__quote-time">{dayjs(date).format(TICKER_DATE_FORMAT)}</p>
      </div>
      <div className="ticker-table__info">
        {tickerFields.map((column) => (
          <ul className="ticker-table__column" key={column.id}>
            {column.fields.map((item, index) => (
              <li className="ticker-table__item" key={index}>
                <div className="ticker-table__item__label">{item.label}</div>
                  <div className="ticker-table__item__content">{item.value}</div>
              </li>
            ))}
          </ul>
        ))}
      </div>
    </div>
  );
};

export default TableLayoutTicker;
