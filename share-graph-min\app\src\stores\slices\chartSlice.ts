

import { StateCreator } from "zustand"
import { TStoreState } from "../useAppStore"
import { IChartRangeStore, IMaxSelectedOption, TChartSetting, TChartSettingsStore, TChartType } from "../../types/store";
import { DEFAULT_STORE_CHART_SETTINGS } from "../../constants/chartConstant";
import { MAX_SELECTED_OPTIONS } from "../../constants/common";
import { getMaxSelectedOption, getCheckboxOptionValue, getRadioOptionValue } from "../../utils/store";
import DEFAULT_SETTING from "../../configs/defaultSetting";
import {IRealtimeTradeData} from "../../realtime/realtimeSetup";
export interface IChartStore {
    maxSelectedOptions: IMaxSelectedOption;
    chartType: TChartType;
    indicators: string[];
    overlays: string[];
    chartRange: IChartRangeStore;
    chartSettings: TChartSettingsStore;
    trades: Record<number, IRealtimeTradeData>,
    setChartType: (chartType: TChartType) => void;
    setChartIndicators: (selectedIndicator: string) => void;
    setChartOverlays: (selectedOverlay: string) => void;
    setChartRange: (updatedValue: IChartRangeStore) => void;
    setChartSettings: (updatedKey: TChartSetting, newValue: string) => void;
    setTrades: (trade: IRealtimeTradeData) => void
}

export const createChartSlice: StateCreator<
    TStoreState,
    [],
    [],
    IChartStore
> = (set, get) => ({
    trades: {},
    maxSelectedOptions: {
        indicators: MAX_SELECTED_OPTIONS.INDICATORS,
        overlays: MAX_SELECTED_OPTIONS.OVERLAYS,
    },
    chartType: DEFAULT_SETTING.chartConfiguration.chartType,
    indicators: [],
    overlays: [],
    chartRange: {} as IChartRangeStore,
    chartSettings: DEFAULT_STORE_CHART_SETTINGS,
    setChartType: (chartType) => set({ chartType }),
    setChartIndicators: (selectedIndicator) => {
        const { maxSelectedOptions, indicators } = get();
        const selectedMax = getMaxSelectedOption(maxSelectedOptions.indicators);

        set(() => ({
            indicators: getCheckboxOptionValue({
                oldData: indicators,
                newValue: selectedIndicator,
                maxSelectedOption: selectedMax
            }),
        }))
    },
    setChartOverlays: (selectedOverlay) => {
        const { maxSelectedOptions, overlays } = get();
        const selectedMax = getMaxSelectedOption(maxSelectedOptions.overlays);

        set(() => ({
            overlays: getCheckboxOptionValue({
                oldData: overlays,
                newValue: selectedOverlay,
                maxSelectedOption: selectedMax
            }),
        }))
    },
    setChartRange: (updatedValue) => set({ chartRange: updatedValue }),
    setChartSettings: (updatedKey, newValue) => {
        set((state) => ({
            chartSettings: {
                ...state.chartSettings,
                [updatedKey]: updatedKey === 'y-axis-preferences' ? getRadioOptionValue(
                    {
                        oldData: state.chartSettings[updatedKey],
                        newValue,
                    }
                ) : getCheckboxOptionValue({
                    oldData: state.chartSettings[updatedKey],
                    newValue,
                    maxSelectedOption: updatedKey === 'volume' ? MAX_SELECTED_OPTIONS.ALL : MAX_SELECTED_OPTIONS.DEFAULT,
                }),
            },
        }))
    },
    setTrades: (trades) => set({trades: { [trades.id]: trades }}) 
})