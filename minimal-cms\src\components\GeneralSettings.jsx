import { useEffect, useImperativeHandle, useMemo } from "react";
import { Col, Flex, Form, Input, Row, Select } from "antd";
import MainFont from "./MainFont";
import { LOCALE_OPTIONS } from "../configs/locale";
import { useInputStyle } from "../hooks/useInputStyle";
import { ColorPickerWrapper } from "./commons/DebounceColorPicker";
import DebounceInput from "./commons/DebounceInput";
import InstrumentSelection from "./InstrumentSelection";
import { getArrayFieldValue } from "../utils";
import { useSelector } from "react-redux";

export default function GeneralSettings({
  initState,
  handleRef,
  onDraftChange,
}) {
  const [form] = Form.useForm();
  const { inputStyle } = useInputStyle();
  const instrumentsByCompanyCode = useSelector(
    (state) => state.settings.instrumentsByCompanyCode.data
  );
  const instrumentIds = Form.useWatch(["instrumentIds"], form);

  const localeOptions = Object.values(LOCALE_OPTIONS).map((item) => ({
    value: item.value,
    label: `${item.name}`,
  }));

  const onFormLayoutChange = () => {
    const cloneValues = structuredClone(form.getFieldsValue(true));
    const { defaultSelectedInstrumentId } = cloneValues;
    if (
      cloneValues.instrumentIds &&
      !cloneValues.instrumentIds?.includes(defaultSelectedInstrumentId)
    ) {
      cloneValues.defaultSelectedInstrumentId =
        cloneValues.instrumentIds[0] || "";
    }
    onDraftChange(cloneValues);
  };

  const handleSubmit = async () => {
    try {
      await form.validateFields();
      onFormLayoutChange(); // ensure everything is up to date
      return true;
    } catch (errorInfo) {
      console.log("Failed:", errorInfo);
      return false;
    }
  };

  useImperativeHandle(handleRef, () => ({ onSubmit: handleSubmit }), []);

  useEffect(() => {
    const defaultSelectedInstrumentId = form.getFieldValue([
      "defaultSelectedInstrumentId",
    ]);
    if (instrumentIds?.includes(defaultSelectedInstrumentId)) {
      return;
    }
    form.setFieldValue(
      ["defaultSelectedInstrumentId"],
      instrumentIds?.[0] || ""
    );
  }, [form, instrumentIds]);
  const instrumentOptions = useMemo(
    () =>
      (instrumentIds || [])
        .map((id) => ({ id, ...instrumentsByCompanyCode[id] }))
        .map((item) => ({
          value: item.id,
          label: item.symbol,
        })),
    [instrumentIds]
  );

  return (
    <div>
      <Form
        layout={"vertical"}
        form={form}
        initialValues={initState}
        onValuesChange={onFormLayoutChange}
        style={{ width: "100%" }}
      >
        <Flex gap={10} wrap>
          <Form.Item
            label="Instruments"
            name={["instrumentIds"]}
            required
            rules={[{ required: true }]}
            style={{ marginBottom: 0 }}
          >
            <InstrumentSelection />
          </Form.Item>
          <Form.Item
            label="Default instrument"
            name={["defaultSelectedInstrumentId"]}
            style={{ marginBottom: 0 }}
          >
            <Select
              disabled={!instrumentOptions?.length}
              style={inputStyle}
              options={instrumentOptions}
            />
          </Form.Item>
          {/* <Form.Item
            label="Locale"
            name={["general", "locale"]}
            style={{ marginBottom: 0 }}
          >
            <Select style={{ width: 120 }} options={localeOptions} />
          </Form.Item> */}
        </Flex>

        <Flex wrap gap={10} className="mt-5">
          <Form.Item
            label="Primary color"
            name={["general", "primaryColor"]}
            style={{ marginBottom: 0 }}
          >
            <ColorPickerWrapper showText allowClear />
          </Form.Item>

          <Form.Item
            label="Font color"
            name={["general", "fontColor"]}
            style={{ marginBottom: 0 }}
          >
            <ColorPickerWrapper showText allowClear />
          </Form.Item>
          <MainFont />
        </Flex>

        <Flex gap={10} wrap className="mt-5">
          {/* <Form.Item label="Size" style={{ marginBottom: 0 }}>
            <Flex gap={6}>
              <Form.Item name={["size", "width"]} noStyle style={{ marginBottom: 0 }}>
                <DebounceInput placeholder="width" />
              </Form.Item>
              <Form.Item name={["size", "height"]} noStyle style={{ marginBottom: 0 }}>
                <DebounceInput placeholder="height" />
              </Form.Item>
            </Flex>
          </Form.Item> */}
          <Flex gap={10}>
            <Form.Item
              label="Up color"
              name={["general", "upColor"]}
              style={{ marginBottom: 0 }}
            >
              <ColorPickerWrapper showText allowClear />
            </Form.Item>
            <Form.Item
              label="Down color"
              name={["general", "downColor"]}
              style={{ marginBottom: 0 }}
            >
              <ColorPickerWrapper showText allowClear />
            </Form.Item>
          </Flex>
        </Flex>
      </Form>
    </div>
  );
}
