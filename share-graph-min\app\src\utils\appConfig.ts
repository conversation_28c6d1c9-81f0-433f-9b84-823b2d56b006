import { i18n } from "@euroland/libs";
import { useAppStore } from "../stores/useAppStore";
import { IDefaultSettingConfigs } from "../types/defaultConfig";
import { getCssSize, getFontFamily, loadTranslationJson, updateCSSVariables } from "./common";
import { NumberFormatterFactory } from "@sharegraph-mini/advance-charts";
import { getMarketInfo } from "../services/market-service";

export const updateAppSettingsToStore = (appSettings: IDefaultSettingConfigs) => {
    const { setAppSettings } = useAppStore.getState();
    setAppSettings(appSettings);
}

export const updateAppStyle = (appSettings: IDefaultSettingConfigs) => {
    const { general } = appSettings
    const { fontFamily, fontSize, fontColor, primaryColor, upColor, downColor } = general;


    const styleVariables = {
        'primary-color': primaryColor,
        'text-color': fontColor,
        'font-size': getCssSize(fontSize),
        'font-family': getFontFamily(fontFamily),
        'up-color': upColor,
        'down-color': downColor,

    }

    updateCSSVariables(styleVariables);
}

export const updateTranslation = async (locale: string) => {
    try {
        const data = await loadTranslationJson(locale);
        if (!data) return
        await i18n.load(locale, data as unknown as string, () => { });
    }
    catch {
        return
    }
}

export const updateFormatNumber = async (locale: string) => {
    try {
        const formatter = NumberFormatterFactory.formatter(locale);
        useAppStore.getState().setFormatNumberInstance(formatter);
    }
    catch {
        return
    }
}

export const validateAppSettings = (appSettings: IDefaultSettingConfigs): boolean => {
    const { defaultSelectedInstrumentId, companyCode } = appSettings;
    return !!defaultSelectedInstrumentId && !!companyCode;
}

export const validateAppAndAddError = (appSettings: IDefaultSettingConfigs) => {
    const isValid = validateAppSettings(appSettings);

    if (!isValid) {
        addErrorToRootElement("Missing instrumentIds or companyCode");
    }

    return isValid;
}

export const addErrorToRootElement = (message: string) => {
    const rootElement = document.getElementById('root');
    if (rootElement) {
        rootElement.innerText = message;
    }
}

export const getRequiredDataToShowApp = async (appConfig: IDefaultSettingConfigs) => {
    const locale = appConfig.general.locale;
    const { selectedInstrumentId, setMarketInfo } = useAppStore.getState();

    try {
        const results = await Promise.all([getMarketInfo(selectedInstrumentId), updateTranslation(locale)])
        const [marketInfo] = results;
        if (marketInfo) {
            setMarketInfo(marketInfo, selectedInstrumentId);
        }

        return true

    } catch (error) {
        console.error("bootstrapApp ~ error:", error)
        return false
    }
}