import { CHART_TYPE_KEYS, DEFAULT_STORE_CHART_SETTINGS, TIME_INTERVALS } from "../constants/chartConstant";
import { DropdownMenuItemId } from "./common";

export interface IChartSetting {
    [key: string]: DropdownMenuItemId[]
}

export type TChartType = typeof CHART_TYPE_KEYS[keyof typeof CHART_TYPE_KEYS]["key"];

export interface IMaxSelectedOption {
    indicators: number;
    overlays: number;
}

export type TChartRangePeriod = '1D' | '5D' | '1M' | '3M' | '6M' | '1Y' | '5Y' | '10Y' | 'MAX' | 'Custom';
export type TTimeIntervals = typeof TIME_INTERVALS[keyof typeof TIME_INTERVALS]['value'];
export interface IChartRangeStore {
    interval: TTimeIntervals;
    period: TChartRangePeriod;
    fromDate?: string;
    toDate?: string;
}

export type TChartSetting = keyof typeof DEFAULT_STORE_CHART_SETTINGS;

export type TChartSettingsStore = {
    [K in TChartSetting]: string[];
}



