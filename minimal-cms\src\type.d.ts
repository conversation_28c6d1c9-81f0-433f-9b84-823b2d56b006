import {
  CHART_TYPE_KEYS,
  DEFAULT_STORE_CHART_SETTINGS,
  TIME_INTERVALS,
} from "./constants/chartConstant";

export interface IChartSetting {
  [key: string]: string[];
}

export type TChartType =
  (typeof CHART_TYPE_KEYS)[keyof typeof CHART_TYPE_KEYS]["key"];

export interface IMaxSelectedOption {
  indicators: number;
  overlays: number;
}

export type TChartRangePeriod =
  | "1D"
  | "5D"
  | "1M"
  | "3M"
  | "6M"
  | "1Y"
  | "5Y"
  | "10Y"
  | "MAX"
  | "Custom";
export type TTimeIntervals =
  (typeof TIME_INTERVALS)[keyof typeof TIME_INTERVALS]["value"];
export interface IChartRangeStore {
  interval: TTimeIntervals;
  period: TChartRangePeriod;
  fromDate?: string;
  toDate?: string;
}

export type TChartSetting = keyof typeof DEFAULT_STORE_CHART_SETTINGS;

export type TChartSettingsStore = {
  [K in TChartSetting]: string[];
};

export interface IDateRangeAndInterval {
  period: TChartRangePeriod;
  intervals: TTimeIntervals[];
}

export type TDefaultRange = Pick<IChartRangeStore, "period" | "interval">;

export type TChartConfiguration = {
  gridColor: string;
  axesFontsize: string;
  axesColor: string;
  chartType: TChartType;
} & TChartSettingsStore;

export interface IDefaultSettingConfigs {
  size: {
    width: string;
    height: string;
  };
  symbol: string;
  dateRangesAndInterval: IDateRangeAndInterval[];
  defaultRange: TDefaultRange;
  general: {
    locale: string;
    fontFamily: string;
    fontSize: string;
    fontColor: string;
    upColor: string;
    downColor: string;
    primaryColor: string;
  };
  chartConfiguration: TChartConfiguration;
  tickerSettings: {
    dataFields: string;
    template: "ticker" | "table";
  };
  valueTracking: "legend" | "tooltip";
}
