import { describe, it, expect, beforeEach } from 'vitest';
import { Time } from 'lightweight-charts';
import EMAIndicator, { EMAData, EMAIndicatorOptions } from './ema-indicator';
import { getIndicatorInstance } from './utils.test';

describe('EMAIndicator', () => {
  let indicator: InstanceType<typeof EMAIndicator>;

  beforeEach(() => {
    indicator = getIndicatorInstance<EMAIndicatorOptions, EMAData, EMAIndicator>(EMAIndicator, { period: 5 });
  });

  describe('formula calculation', () => {
    it('should calculate EMA correctly with known data', () => {
      // Test data from tulipnode reference (atoz page 209)
      // Input: [25, 24.875, 24.781, 24.594, 24.5, 24.625, 25.219, 27.25]
      // Expected EMA(5): [25, 24.958, 24.899, 24.797, 24.698, 24.674, 24.856, 25.654]
      const dataSample = [
        { time: 1 as Time, open: 25, high: 25.5, low: 24.5, close: 25, volume: 1000, customValues: { time: 1 as Time, open: 25, high: 25.5, low: 24.5, close: 25, volume: 1000 } },
        { time: 2 as Time, open: 25, high: 25.5, low: 24.375, close: 24.875, volume: 1100, customValues: { time: 2 as Time, open: 25, high: 25.5, low: 24.375, close: 24.875, volume: 1100 } },
        { time: 3 as Time, open: 24.875, high: 25.281, low: 24.281, close: 24.781, volume: 1200, customValues: { time: 3 as Time, open: 24.875, high: 25.281, low: 24.281, close: 24.781, volume: 1200 } },
        { time: 4 as Time, open: 24.781, high: 25.094, low: 24.094, close: 24.594, volume: 1300, customValues: { time: 4 as Time, open: 24.781, high: 25.094, low: 24.094, close: 24.594, volume: 1300 } },
        { time: 5 as Time, open: 24.594, high: 25, low: 24, close: 24.5, volume: 1400, customValues: { time: 5 as Time, open: 24.594, high: 25, low: 24, close: 24.5, volume: 1400 } },
        { time: 6 as Time, open: 24.5, high: 25.125, low: 24.125, close: 24.625, volume: 1500, customValues: { time: 6 as Time, open: 24.5, high: 25.125, low: 24.125, close: 24.625, volume: 1500 } },
        { time: 7 as Time, open: 24.625, high: 25.719, low: 24.719, close: 25.219, volume: 1600, customValues: { time: 7 as Time, open: 24.625, high: 25.719, low: 24.719, close: 25.219, volume: 1600 } },
        { time: 8 as Time, open: 25.219, high: 27.75, low: 26.75, close: 27.25, volume: 1700, customValues: { time: 8 as Time, open: 25.219, high: 27.75, low: 26.75, close: 27.25, volume: 1700 } }
      ];

      const expected = [25, 24.958, 24.899, 24.797, 24.698, 24.674, 24.856, 25.654];
      indicator.setData(dataSample)
      const results = indicator.getData()
      console.log("results: ", results);
      
      // Allow small floating point differences
      expect(results.map(r => r.value?.[0]))?.toEqual(expected);
    });
  });
});
