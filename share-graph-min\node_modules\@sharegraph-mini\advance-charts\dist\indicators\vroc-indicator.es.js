var l = Object.defineProperty;
var p = (t, o, e) => o in t ? l(t, o, { enumerable: !0, configurable: !0, writable: !0, value: e }) : t[o] = e;
var n = (t, o, e) => p(t, typeof o != "symbol" ? o + "" : o, e);
import { LineSeries as u } from "lightweight-charts";
import { ChartIndicator as v } from "./abstract-indicator.es.js";
import { RegionPrimitive as m } from "../custom-primitive/primitive/region.es.js";
import { autoScaleInfoProviderCreator as d } from "../helpers/utils.es.js";
const f = {
  color: "rgba(255, 152, 0, 1)",
  // Orange for VROC
  priceLineColor: "rgba(150, 150, 150, 0.35)",
  backgroundColor: "#ff98001a",
  period: 14,
  // Default period for VROC
  overlay: !1
};
class P extends v {
  constructor(e, r, i) {
    super(e, r);
    n(this, "vrocSeries");
    this.vrocSeries = e.addSeries(u, {
      color: this.options.color,
      lineWidth: 2,
      priceLineVisible: !1,
      crosshairMarkerVisible: !1,
      priceScaleId: "vroc",
      autoscaleInfoProvider: d({ minValue: -100, maxValue: 100 })
    }, i), this.vrocSeries.attachPrimitive(
      new m({
        upPrice: 5,
        lowPrice: -5,
        lineColor: this.options.priceLineColor,
        backgroundColor: this.options.backgroundColor
      })
    );
  }
  getDefaultOptions() {
    return f;
  }
  formula(e) {
    const r = this.options.period, i = e.symbol.volume, s = e.new_var(i, r + 1);
    if (!s.calculable())
      return;
    const c = s.get(0), a = s.get(r);
    return a === 0 ? [0] : [(c - a) / a * 100];
  }
  applyIndicatorData() {
    const e = [];
    for (const r of this._executionContext.data) {
      const i = r.value;
      if (!i) continue;
      const s = r.time;
      e.push({ time: s, value: i[0] });
    }
    this.vrocSeries.setData(e);
  }
  remove() {
    super.remove(), this.chart.removeSeries(this.vrocSeries);
  }
  _applyOptions() {
    this.vrocSeries.applyOptions({ color: this.options.color }), this.applyIndicatorData();
  }
  setPaneIndex(e) {
    this.vrocSeries.moveToPane(e);
  }
  getPaneIndex() {
    return this.vrocSeries.getPane().paneIndex();
  }
}
export {
  P as default,
  f as defaultOptions
};
//# sourceMappingURL=vroc-indicator.es.js.map
