{"version": 3, "file": "vroc-indicator.cjs.js", "sources": ["../../src/indicators/vroc-indicator.ts"], "sourcesContent": ["import {IChartApi, ISeriesApi, LineSeries, Nominal, SeriesType, SingleValueData, Time} from \"lightweight-charts\";\nimport {ChartIndicator, ChartIndicatorOptions} from \"./abstract-indicator\";\nimport {RegionPrimitive} from \"../custom-primitive/primitive/region\";\nimport {autoScaleInfoProviderCreator} from \"../helpers/utils\";\nimport {Context} from \"../helpers/execution-indicator\";\n\nexport interface VROCIndicatorOptions extends ChartIndicatorOptions {\n  color: string,\n  period: number,\n  priceLineColor: string,\n  backgroundColor: string\n}\n\nexport const defaultOptions: VROCIndicatorOptions = {\n  color: \"rgba(255, 152, 0, 1)\",     // Orange for VROC\n  priceLineColor: \"rgba(150, 150, 150, 0.35)\",\n  backgroundColor: '#ff98001a',\n  period: 14,      // Default period for VROC\n  overlay: false\n}\n\nexport type VROCLine = Nominal<number, 'VROC'>\n\nexport type VROCData = [VROCLine]\n\nexport default class VROCIndicator extends ChartIndicator<VROCIndicatorOptions, VROCData> {\n  vrocSeries: ISeriesApi<SeriesType>\n\n  constructor(chart: IChartApi, options?: Partial<VROCIndicatorOptions>, paneIndex?: number) {\n    super(chart, options)\n    \n    this.vrocSeries = chart.addSeries(LineSeries, {\n      color: this.options.color,\n      lineWidth: 2,\n      priceLineVisible: false,\n      crosshairMarkerVisible: false,\n      priceScaleId: 'vroc',\n      autoscaleInfoProvider: autoScaleInfoProviderCreator({minValue: -100, maxValue: 100})\n    }, paneIndex);\n    \n    // Add zero line for reference\n    this.vrocSeries.attachPrimitive(\n      new RegionPrimitive({\n        upPrice: 5,\n        lowPrice: -5,\n        lineColor: this.options.priceLineColor,\n        backgroundColor: this.options.backgroundColor\n      })\n    );\n  }\n\n  getDefaultOptions(): VROCIndicatorOptions {\n    return defaultOptions\n  }\n\n  formula(c: Context): VROCData | undefined {\n    const period = this.options.period;\n    const volume = c.symbol.volume;\n\n    // We need enough data for the period calculation plus current volume\n    const volumeSeries = c.new_var(volume, period + 1);\n\n    if (!volumeSeries.calculable()) {\n        return;\n    }\n\n    // Get current volume (index 0) and volume from 'period' periods ago (index period)\n    const currentVolume = volumeSeries.get(0);\n    const pastVolume = volumeSeries.get(period);\n\n    // Calculate Volume Rate of Change\n    // VROC = ((Current Volume - Past Volume) / Past Volume) * 100\n    if (pastVolume === 0) {\n        return [0 as VROCLine]; // Avoid division by zero\n    }\n\n    const vroc = ((currentVolume - pastVolume) / pastVolume) * 100;\n\n    return [vroc as VROCLine];\n}\n\n  applyIndicatorData() {\n    const vrocData: SingleValueData[] = [];\n    \n    for(const bar of this._executionContext.data) {\n      const value = bar.value;\n      if(!value) continue;\n      \n      const time = bar.time as Time;\n      vrocData.push({time, value: value[0]});\n    }\n\n    this.vrocSeries.setData(vrocData);\n  }\n\n  remove() {\n    super.remove()\n    this.chart.removeSeries(this.vrocSeries);\n  }\n\n  _applyOptions() {\n    this.vrocSeries.applyOptions({color: this.options.color});\n    this.applyIndicatorData();\n  }\n\n  setPaneIndex(paneIndex: number) {\n    this.vrocSeries.moveToPane(paneIndex);\n  }\n\n  getPaneIndex(): number {\n    return this.vrocSeries.getPane().paneIndex();\n  }\n}\n"], "names": ["defaultOptions", "VROCIndicator", "ChartIndicator", "chart", "options", "paneIndex", "__publicField", "LineSeries", "autoScaleInfoProviderCreator", "RegionPrimitive", "c", "period", "volume", "volumeSeries", "currentVolume", "pastVolume", "vrocData", "bar", "value", "time"], "mappings": "6bAaaA,EAAuC,CAClD,MAAO,uBACP,eAAgB,4BAChB,gBAAiB,YACjB,OAAQ,GACR,QAAS,EACX,EAMA,MAAqBC,UAAsBC,EAAAA,cAA+C,CAGxF,YAAYC,EAAkBC,EAAyCC,EAAoB,CACzF,MAAMF,EAAOC,CAAO,EAHtBE,EAAA,mBAKO,KAAA,WAAaH,EAAM,UAAUI,EAAAA,WAAY,CAC5C,MAAO,KAAK,QAAQ,MACpB,UAAW,EACX,iBAAkB,GAClB,uBAAwB,GACxB,aAAc,OACd,sBAAuBC,EAA6B,6BAAA,CAAC,SAAU,KAAM,SAAU,GAAI,CAAA,GAClFH,CAAS,EAGZ,KAAK,WAAW,gBACd,IAAII,kBAAgB,CAClB,QAAS,EACT,SAAU,GACV,UAAW,KAAK,QAAQ,eACxB,gBAAiB,KAAK,QAAQ,eAC/B,CAAA,CACH,CAAA,CAGF,mBAA0C,CACjC,OAAAT,CAAA,CAGT,QAAQU,EAAkC,CAClC,MAAAC,EAAS,KAAK,QAAQ,OACtBC,EAASF,EAAE,OAAO,OAGlBG,EAAeH,EAAE,QAAQE,EAAQD,EAAS,CAAC,EAE7C,GAAA,CAACE,EAAa,aACd,OAIE,MAAAC,EAAgBD,EAAa,IAAI,CAAC,EAClCE,EAAaF,EAAa,IAAIF,CAAM,EAI1C,OAAII,IAAe,EACR,CAAC,CAAa,EAKlB,EAFQD,EAAgBC,GAAcA,EAAc,GAEnC,CAAA,CAG1B,oBAAqB,CACnB,MAAMC,EAA8B,CAAC,EAE3B,UAAAC,KAAO,KAAK,kBAAkB,KAAM,CAC5C,MAAMC,EAAQD,EAAI,MAClB,GAAG,CAACC,EAAO,SAEX,MAAMC,EAAOF,EAAI,KACjBD,EAAS,KAAK,CAAC,KAAAG,EAAM,MAAOD,EAAM,CAAC,EAAE,CAAA,CAGlC,KAAA,WAAW,QAAQF,CAAQ,CAAA,CAGlC,QAAS,CACP,MAAM,OAAO,EACR,KAAA,MAAM,aAAa,KAAK,UAAU,CAAA,CAGzC,eAAgB,CACd,KAAK,WAAW,aAAa,CAAC,MAAO,KAAK,QAAQ,MAAM,EACxD,KAAK,mBAAmB,CAAA,CAG1B,aAAaX,EAAmB,CACzB,KAAA,WAAW,WAAWA,CAAS,CAAA,CAGtC,cAAuB,CACrB,OAAO,KAAK,WAAW,QAAQ,EAAE,UAAU,CAAA,CAE/C"}