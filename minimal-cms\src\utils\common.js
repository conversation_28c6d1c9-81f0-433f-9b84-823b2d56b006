import dayjs from "dayjs";

export const compareObject = (a = {}, b = {}) => {
  if (a === b) return true;
  if (
    (Array.isArray(a) && !Array.isArray(b)) ||
    (!Array.isArray(a) && Array.isArray(b))
  )
    return false;
  const aggregate = { ...a, ...b };
  for (const key in aggregate) {
    if (typeof a[key] === "object" && typeof b[key] === "object") {
      return compareObject(a[key], b[key]);
    }
    if (a[key] !== b[key]) return false;
  }
  return true;
};

/**
 *
 * @param {Record<string,any>} data
 * @returns {Record<string,any>}
 */
export const trimObjectProperties = (data = {}) => {
  const newData = { ...data };
  for (const key in data) {
    if (Object.hasOwnProperty.call(data, key)) {
      const element = data[key];
      if (typeof element === "string") {
        newData[key] = element.trim();
      }
    }
  }
  return newData;
};

export const isObjectEmpty = (objectName) => {
  return (
    Object.keys(objectName).length === 0 && objectName.constructor === Object
  );
};

export const isArrayEmpty = (array) => {
  return array.length === 0;
};

export const generateFormatPreview = (formatString) => {
  return formatString
    ? `Preview: ${dayjs(new Date()).format(formatString)}`
    : "Type to generate preview";
};

export const isValidType = (name, allowType) => {
  for (let j = 0; j < allowType.length; j++) {
    let sCurExtension = allowType[j];
    if (
      name
        .substr(name.length - sCurExtension.length, sCurExtension.length)
        .toLowerCase() === sCurExtension.toLowerCase()
    ) {
      return true;
    }
  }
  return false;
};

export const validateFiles = (fileArray, allowFileType) => {
  return fileArray.filter((file) => isValidType(file.name, allowFileType));
};

export const hasScrollBar = (className) => {
  const element = document.querySelector(className);
  if (!element) {
    return false;
  }
  const hasScrollBar =
    element.clientHeight < element.scrollHeight ||
    element.clientWidth < element.scrollWidth;

  return hasScrollBar;
};

export const normalizeData = (data) => {
  const normalizedData = {
    entities: {},
    result: [],
  };

  data.forEach((item) => {
    const { id } = item;
    normalizedData.entities[id] = item;
    normalizedData.result.push(id);
  });

  return normalizedData;
};

export const removeShareGraphKey = () => {
  const localStorageKeys = Object.keys(localStorage);
  localStorageKeys.forEach((key) => {
    if (
      key.startsWith("myChartLayout") ||
      key.startsWith("myChartPreferences")
    ) {
      localStorage.removeItem(key);
    }
  });
};

export const getDecimalLength = (value) => {
  const matched = String(value).match(/\.(\d+)/);
  if (!matched) return 0;
  const [_, decimal] = matched;
  return decimal?.length || 0;
};

export const convertToDecimal = (value, { decimalLength = 0 }) => {
  value = String(value);
  let newValue = value;
  let valueDecimalLength = getDecimalLength(value);

  if (valueDecimalLength === 0 && decimalLength > 0) {
    newValue += Array.from({ length: decimalLength }).reduce(
      (s, c) => s + "0",
      "."
    );
  }
  if (valueDecimalLength > 0) {
    while (valueDecimalLength < decimalLength) {
      newValue += "0";
      valueDecimalLength++;
    }
  }
  if (valueDecimalLength > decimalLength) {
    let decimalLast = value.length - (valueDecimalLength - decimalLength);
    decimalLast = decimalLength === 0 ? decimalLast - 1 : decimalLast;

    newValue = value.substring(0, decimalLast);
  }
  return newValue;
};

/**
 *
 * @param {string} value
 * @return {boolean}
 */
export const isEmptyCSSValue = (value) => {
  if (
    (!value && typeof value !== "number") ||
    /^(px|em|rem|%| !important)\s*/.test(value)
  ) {
    return true;
  }
  return false;
};

const replaceVariable = (value, variables = {}) => {
  const variablesKeys = Object.keys(variables);
  return variablesKeys.reduce((s, c) => {
    if (s?.includes(c)) {
      s = s.replaceAll(c, `var(${variables[c]})`);
    }
    return s;
  }, value);
};

const getVariablesCss = (css = []) => {
  const variableCssRegex = /^--.+$/;
  const generalRoot =
    css
      .find((c) => c.section === "general")
      ?.selectors?.find((s) => s.selector === ":root")?.properties || [];

  return generalRoot.reduce((s, c) => {
    if (!variableCssRegex.test(c.name)) return s;
    s[c.value] = c.name;
    return s;
  }, {});
};

export const convertVariableCss = (css = []) => {
  const variables = getVariablesCss(css);

  const isVariableRoot = (name = "") => {
    return Object.values(variables).includes(name);
  };

  return css.map((c) => ({
    ...c,
    selectors: c.selectors.map((selector) => ({
      ...selector,
      properties: selector.properties.map((property) => ({
        ...property,
        value: !isVariableRoot(property.name)
          ? replaceVariable(property.value, variables)
          : property.value,
      })),
    })),
  }));
};

export const addCssExtension = (path) => {
  if (!path) return path;
  return path.indexOf(".css") === -1 ? path + ".css" : path;
};
export const randomId = () => {
  const length = 5;
  let result = "";
  const characters = "abcdefghijklmnopqrstuvwxyz";
  const charactersLength = characters.length;
  let counter = 0;
  while (counter < length) {
    result += characters.charAt(Math.floor(Math.random() * charactersLength));
    counter += 1;
  }
  return result;
};

export const resetObject = (object) => {
  return Object.keys(object).reduce((s, c) => {
    return {
      ...s,
      [c]: null,
    };
  }, {});
};

export const mapCustomPhrasesApply = (customPhrases) => {
  const cp = Object.keys(customPhrases).map((key) => {
    const values = customPhrases[key].map((entry) => {
      const language = Object.keys(entry)[0];
      const value = entry[language];
      return { language, value };
    });

    return { key, values };
  });
  return cp;
};

export function getNewSelectedPhrases(
  keywords,
  langs,
  allCustomPhrases,
  toolCode
) {
  const selectedPhrases = {};

  keywords.forEach((keyword) => {
    selectedPhrases[keyword] = langs.map((lang) => {
      const langPhrases = allCustomPhrases[toolCode][lang] || [];
      const matchingPhrase = langPhrases.find(
        (phrase) => phrase.key === keyword
      );
      return { [lang]: matchingPhrase ? matchingPhrase.value : "" };
    });
  });

  return selectedPhrases;
}

export function findCustomPhraseIndex(customPhrases, keyword, language) {
  const phrasesArray = customPhrases[keyword];

  if (phrasesArray) {
    for (let i = 0; i < phrasesArray.length; i++) {
      const obj = phrasesArray[i];
      if (Object.keys(obj)[0] === language) {
        return i; // Return the index if the object with the specified language is found
      }
    }
  }

  return -1; // Return -1 if the keyword or language is not found
}

export function parseSizeUnit(input) {
  if (typeof input !== "string") return null;
  const match = input.match(/^(\d+)([a-zA-Z%]+)$/);
  if (match) {
    return {
      number: parseFloat(match[1]),
      unit: match[2],
    };
  }
  return null;
}

export function createMultipartFormData(params) {
  const formData = new FormData();

  Object.entries(params).forEach(([key, value]) => {
    if (value instanceof File) {
      formData.append(key, value);
    } else if (Array.isArray(value)) {
      value.forEach((item) => formData.append(key, item));
    } else if (typeof value === "object" && value !== null) {
      formData.append(key, JSON.stringify(value));
    } else {
      formData.append(key, value);
    }
  });

  return formData;
}

async function loadFile(filePath) {
  const result = await (
    await fetch(import.meta.env.VITE_BASE_URL + filePath, {
      headers: {
        "Content-Type": "application/json",
      },
    })
  ).json();

  return result;
}

export async function loadTranslationJson(locale) {
  try {
    const result = await loadFile(`translations/${locale}.json`);
    return result;
  } catch (e) {
    return await loadFile("translations/en.json");
  }
}

const units = { D: 1, M: 30, Y: 365 };

export const sortedPeriods = (periods = []) => {
  const newPeriods = structuredClone(periods || []);
  return newPeriods.sort((a, b) => {
  const parsePeriod = (period) => {
    const value = parseInt(period.slice(0, -1), 10);
    const unit = period.slice(-1);
    return value * units[unit];
  };
  
  return parsePeriod(a.period) - parsePeriod(b.period);
})};

const intervalWeights = {
  m: 1, // 1 minute
  h: 60, // 1 hour = 60 minutes
  daily: 1440, // 1 day = 1440 minutes
  weekly: 10080, // 1 week = 10080 minutes
  monthly: 43200, // Approx. 1 month = 43200 minutes (30 days)
};

export const sortedIntervals = (intervals = []) => {
  const newIntervals = structuredClone(intervals || []);
  return newIntervals.sort((a, b) => {
    const parsePeriod = (period) => {
      const numericPart = parseInt(period, 10); // Extract numeric part, if any
      const unit = period.replace(numericPart, "").trim(); // Extract unit or named period

      // Handle numeric units like "1m", "5m", "1h"
      if (intervalWeights[unit] && !Number.isNaN(numericPart)) {
        return numericPart * intervalWeights[unit];
      }

      // Handle named periods like "daily", "weekly", "monthly"
      return intervalWeights[period];
    };

    return parsePeriod(a) - parsePeriod(b);
  });
};