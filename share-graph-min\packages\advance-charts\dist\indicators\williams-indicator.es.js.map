{"version": 3, "file": "williams-indicator.es.js", "sources": ["../../src/indicators/williams-indicator.ts"], "sourcesContent": ["import {IChartApi, ISeriesApi, LineSeries, Nominal, SeriesType, SingleValueData, Time} from \"lightweight-charts\";\r\nimport {ChartIndicator, ChartIndicatorOptions} from \"./abstract-indicator\";\r\nimport {RegionPrimitive} from \"../custom-primitive/primitive/region\";\r\nimport {autoScaleInfoProviderCreator} from \"../helpers/utils\";\r\nimport {Context} from \"../helpers/execution-indicator\";\r\n\r\nexport interface WilliamsIndicatorOptions extends ChartIndicatorOptions {\r\n  color: string,\r\n  period: number\r\n  priceLineColor: string,\r\n  backgroundColor: string\r\n}\r\n\r\nexport const defaultOptions: WilliamsIndicatorOptions = {\r\n  color: \"rgba(108, 80, 175, 1)\",\r\n  priceLineColor: \"rgba(150, 150, 150, 0.35)\",\r\n  backgroundColor: '#7e57c21a',\r\n  period: 14,\r\n  overlay: false\r\n}\r\n\r\nexport type WilliamsLine = Nominal<number, 'Williams'>\r\n\r\nexport type WilliamsData = [WilliamsLine]\r\n\r\nexport default class WilliamsIndicator extends ChartIndicator<WilliamsIndicatorOptions, WilliamsData> {\r\n  williamsSeries: ISeriesApi<SeriesType>\r\n\r\n  constructor(chart: IChartApi, options?: Partial<WilliamsIndicatorOptions>, paneIndex?: number) {\r\n    super(chart, options)\r\n    this.williamsSeries = chart.addSeries(LineSeries, {\r\n      color: this.options.color,\r\n      lineWidth: 1,\r\n      priceLineVisible: false,\r\n      crosshairMarkerVisible: false,\r\n      priceScaleId: 'williams',\r\n      autoscaleInfoProvider: autoScaleInfoProviderCreator({maxValue: 0, minValue: -100})\r\n    }, paneIndex);\r\n\r\n    this.williamsSeries.attachPrimitive(\r\n      new RegionPrimitive({\r\n        upPrice: -20,\r\n        lowPrice: -80,\r\n        lineColor: this.options.priceLineColor,\r\n        backgroundColor: this.options.backgroundColor\r\n      })\r\n    );\r\n  }\r\n\r\n  getDefaultOptions(): WilliamsIndicatorOptions {\r\n    return defaultOptions\r\n  }\r\n\r\n  formula(c: Context): WilliamsData | undefined {\r\n    const period = this.options.period;\r\n    \r\n    const highSeries = c.new_var(c.symbol.high, period + 1);\r\n    const lowSeries = c.new_var(c.symbol.low, period + 1);\r\n  \r\n    if(!highSeries.calculable() || !lowSeries.calculable()) return;\r\n  \r\n    const historicalHighs = highSeries.getAll().slice(0, -1);\r\n    const historicalLows = lowSeries.getAll().slice(0, -1);\r\n    const currentClose = c.symbol.close;\r\n  \r\n    if(historicalHighs.length < period || historicalLows.length < period) return;\r\n  \r\n    const highestHigh = Math.max(...historicalHighs.slice(-period));\r\n    const lowestLow = Math.min(...historicalLows.slice(-period));\r\n  \r\n    if(highestHigh === lowestLow) return [-50 as WilliamsLine];\r\n  \r\n    const williamsR = ((highestHigh - currentClose) / (highestHigh - lowestLow)) * -100;\r\n    \r\n    return [williamsR as WilliamsLine];\r\n  }\r\n\r\n\r\n  applyIndicatorData() {\r\n    const williamsData: SingleValueData[] = [];\r\n    for(const bar of this._executionContext.data) {\r\n      const value = bar.value;\r\n      if(!value) continue;\r\n      williamsData.push({time: bar.time as Time, value: value[0]})\r\n    }\r\n\r\n    this.williamsSeries.setData(williamsData)\r\n  }\r\n\r\n  remove() {\r\n    super.remove()\r\n    this.chart.removeSeries(this.williamsSeries);\r\n  }\r\n\r\n  _applyOptions() {\r\n    this.williamsSeries.applyOptions({color: this.options.color})\r\n    this.applyIndicatorData();\r\n  }\r\n\r\n\r\n  setPaneIndex(paneIndex: number) {\r\n    this.williamsSeries.moveToPane(paneIndex)\r\n  }\r\n\r\n  getPaneIndex(): number {\r\n    return this.williamsSeries.getPane().paneIndex()\r\n  }\r\n}"], "names": ["defaultOptions", "WilliamsIndicator", "ChartIndicator", "chart", "options", "paneIndex", "__publicField", "LineSeries", "autoScaleInfoProviderCreator", "RegionPrimitive", "c", "period", "highSeries", "lowSeries", "historicalHighs", "historicalLows", "currentClose", "highestHigh", "lowestLow", "williamsData", "bar", "value"], "mappings": ";;;;;;;AAaO,MAAMA,IAA2C;AAAA,EACtD,OAAO;AAAA,EACP,gBAAgB;AAAA,EAChB,iBAAiB;AAAA,EACjB,QAAQ;AAAA,EACR,SAAS;AACX;AAMA,MAAqBC,UAA0BC,EAAuD;AAAA,EAGpG,YAAYC,GAAkBC,GAA6CC,GAAoB;AAC7F,UAAMF,GAAOC,CAAO;AAHtB,IAAAE,EAAA;AAIO,SAAA,iBAAiBH,EAAM,UAAUI,GAAY;AAAA,MAChD,OAAO,KAAK,QAAQ;AAAA,MACpB,WAAW;AAAA,MACX,kBAAkB;AAAA,MAClB,wBAAwB;AAAA,MACxB,cAAc;AAAA,MACd,uBAAuBC,EAA6B,EAAC,UAAU,GAAG,UAAU,KAAK,CAAA;AAAA,OAChFH,CAAS,GAEZ,KAAK,eAAe;AAAA,MAClB,IAAII,EAAgB;AAAA,QAClB,SAAS;AAAA,QACT,UAAU;AAAA,QACV,WAAW,KAAK,QAAQ;AAAA,QACxB,iBAAiB,KAAK,QAAQ;AAAA,MAC/B,CAAA;AAAA,IACH;AAAA,EAAA;AAAA,EAGF,oBAA8C;AACrC,WAAAT;AAAA,EAAA;AAAA,EAGT,QAAQU,GAAsC;AACtC,UAAAC,IAAS,KAAK,QAAQ,QAEtBC,IAAaF,EAAE,QAAQA,EAAE,OAAO,MAAMC,IAAS,CAAC,GAChDE,IAAYH,EAAE,QAAQA,EAAE,OAAO,KAAKC,IAAS,CAAC;AAEpD,QAAG,CAACC,EAAW,WAAA,KAAgB,CAACC,EAAU,aAAc;AAExD,UAAMC,IAAkBF,EAAW,OAAS,EAAA,MAAM,GAAG,EAAE,GACjDG,IAAiBF,EAAU,OAAS,EAAA,MAAM,GAAG,EAAE,GAC/CG,IAAeN,EAAE,OAAO;AAE9B,QAAGI,EAAgB,SAASH,KAAUI,EAAe,SAASJ,EAAQ;AAEhE,UAAAM,IAAc,KAAK,IAAI,GAAGH,EAAgB,MAAM,CAACH,CAAM,CAAC,GACxDO,IAAY,KAAK,IAAI,GAAGH,EAAe,MAAM,CAACJ,CAAM,CAAC;AAE3D,WAAGM,MAAgBC,IAAkB,CAAC,GAAmB,IAIlD,EAFaD,IAAcD,MAAiBC,IAAcC,KAAc,IAE9C;AAAA,EAAA;AAAA,EAInC,qBAAqB;AACnB,UAAMC,IAAkC,CAAC;AAC/B,eAAAC,KAAO,KAAK,kBAAkB,MAAM;AAC5C,YAAMC,IAAQD,EAAI;AAClB,MAAIC,KACSF,EAAA,KAAK,EAAC,MAAMC,EAAI,MAAc,OAAOC,EAAM,CAAC,GAAE;AAAA,IAAA;AAGxD,SAAA,eAAe,QAAQF,CAAY;AAAA,EAAA;AAAA,EAG1C,SAAS;AACP,UAAM,OAAO,GACR,KAAA,MAAM,aAAa,KAAK,cAAc;AAAA,EAAA;AAAA,EAG7C,gBAAgB;AACd,SAAK,eAAe,aAAa,EAAC,OAAO,KAAK,QAAQ,OAAM,GAC5D,KAAK,mBAAmB;AAAA,EAAA;AAAA,EAI1B,aAAad,GAAmB;AACzB,SAAA,eAAe,WAAWA,CAAS;AAAA,EAAA;AAAA,EAG1C,eAAuB;AACrB,WAAO,KAAK,eAAe,QAAQ,EAAE,UAAU;AAAA,EAAA;AAEnD;"}