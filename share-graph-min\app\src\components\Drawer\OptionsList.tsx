import CheckIcon from '../../icons/CheckIcon';

interface Option {
  id: string;
  label: string;
}

interface OptionsListProps {
  options: Option[];
  selectedOptions: string[];
  onToggleOption: (optionId: string, item: Option) => void;
}

const OptionsList: React.FC<OptionsListProps> = ({ 
  options, 
  selectedOptions, 
  onToggleOption
}) => {
  return (
    <ul className="mobile-drawer__options">
      {options.map(option => (
        <li 
          key={option.id} 
          className={`mobile-drawer__option ${selectedOptions.includes(option.id) ? "mobile-drawer__option--selected" : ""}`}
          onClick={() => onToggleOption(option.id, option)}
        >
          <span className="mobile-drawer__option-label">{option.label}</span>
          <span className="mobile-drawer__option-checkbox">
            {selectedOptions.includes(option.id) && <CheckIcon />}
          </span>
        </li>
      ))}
    </ul>
  );
};

export default OptionsList; 