import {client} from "../graphql/client";
import {MARKET_QUERY} from "../graphql/queries/marketQuery";
import {ensureDefined, ensureNotNull} from "@sharegraph-mini/advance-charts";
import { getCacheMarketInfoByInsId, setCacheMarketInfo } from "../utils/common";

export interface MarketInfo {
  open: string;
  close: string;
  timezone: string;
  status: {
    isOpened: boolean;
    remainingTime: number;
  };
}
export async function getMarketInfo(instrumentId: number): Promise<MarketInfo | undefined> {
  // Check cache first
  const cachedMarketInfo = getCacheMarketInfoByInsId(instrumentId);
  if (cachedMarketInfo) return cachedMarketInfo;

  try {
    // Fetch data only if not in cache
    const { data } = await client.query(MARKET_QUERY, { id: instrumentId });
    const market = data?.instrumentById?.market;
    
    if (!market) return undefined;
    
    // Process and cache the result
    const marketInfo = getMarketInfoData(market);
    setCacheMarketInfo(instrumentId, marketInfo);
    
    return marketInfo;
  } catch {
    return undefined;
  }
}

export const getMarketInfoData = (market): MarketInfo => {
  const result = {
    open: ensureDefined(ensureNotNull(market?.openTimeLocal)),
    close: ensureDefined(ensureNotNull(market?.closeTimeLocal)),
    timezone: ensureDefined(ensureNotNull(market?.timezone?.nameIANA)),
    status: {
      isOpened: ensureDefined(ensureNotNull(market?.status?.isOpened)),
      remainingTime: ensureDefined(ensureNotNull(market?.status?.remainingTime))
    }
  }

  return result
}