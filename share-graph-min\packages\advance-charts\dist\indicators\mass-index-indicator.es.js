var b = Object.defineProperty;
var M = (o, r, e) => r in o ? b(o, r, { enumerable: !0, configurable: !0, writable: !0, value: e }) : o[r] = e;
var S = (o, r, e) => M(o, typeof r != "symbol" ? r + "" : r, e);
import { LineSeries as V } from "lightweight-charts";
import { ChartIndicator as w } from "./abstract-indicator.es.js";
import { RegionPrimitive as C } from "../custom-primitive/primitive/region.es.js";
import { autoScaleInfoProviderCreator as E } from "../helpers/utils.es.js";
const N = {
  color: "#3179f5",
  // for Mass Index
  priceLineColor: "#a1c3ff",
  backgroundColor: "#d2e0fa",
  emaPeriod: 9,
  // 9-period EMA as per TradingView standard
  sumPeriod: 25,
  // 25-period sum as per TradingView standard
  overlay: !1
};
class R extends w {
  constructor(e, s, t) {
    super(e, s);
    S(this, "massIndexSeries");
    this.massIndexSeries = e.addSeries(V, {
      color: this.options.color,
      lineWidth: 2,
      priceLineVisible: !1,
      crosshairMarkerVisible: !1,
      priceScaleId: "massindex",
      autoscaleInfoProvider: E({ maxValue: 30, minValue: 20 })
    }, t), this.massIndexSeries.attachPrimitive(
      new C({
        upPrice: 27.5,
        lowPrice: 26.5,
        lineColor: this.options.priceLineColor,
        backgroundColor: this.options.backgroundColor
      })
    );
  }
  getDefaultOptions() {
    return N;
  }
  formula(e) {
    const s = this.options.emaPeriod, t = this.options.sumPeriod, c = e.symbol.high, P = e.symbol.low, m = c - P, l = 2 / (s + 1), p = e.new_var(m, s), f = e.new_var(NaN, 2);
    if (!p.calculable()) return;
    const h = f.get(1);
    let i;
    isNaN(h) ? i = p.getAll().reduce((n, u) => n + u, 0) / s : i = l * m + (1 - l) * h, f.set(i);
    const I = e.new_var(i, s), x = e.new_var(NaN, 2);
    if (!I.calculable()) return;
    const v = x.get(1);
    let a;
    isNaN(v) ? a = I.getAll().reduce((n, u) => n + u, 0) / s : a = l * i + (1 - l) * v, x.set(a);
    const A = a !== 0 ? i / a : 1, g = e.new_var(A, t);
    return g.calculable() ? [g.getAll().reduce((d, n) => d + n, 0)] : void 0;
  }
  applyIndicatorData() {
    const e = [];
    for (const s of this._executionContext.data) {
      const t = s.value;
      if (!t) continue;
      const c = s.time;
      e.push({ time: c, value: t[0] });
    }
    this.massIndexSeries.setData(e);
  }
  remove() {
    super.remove(), this.chart.removeSeries(this.massIndexSeries);
  }
  _applyOptions() {
    this.massIndexSeries.applyOptions({ color: this.options.color }), this.applyIndicatorData();
  }
  setPaneIndex(e) {
    this.massIndexSeries.moveToPane(e);
  }
  getPaneIndex() {
    return this.massIndexSeries.getPane().paneIndex();
  }
}
export {
  R as default,
  N as defaultOptions
};
//# sourceMappingURL=mass-index-indicator.es.js.map
