{"version": 3, "file": "index.cjs.js", "sources": ["../../src/indicators/index.ts"], "sourcesContent": ["import BBIndicator from \"./bb-indicator\";\r\nimport MACDIndicator from \"./macd-indicator\";\r\nimport RSIIndicator from \"./rsi-indicator\";\r\nimport VolumeIndicator from \"./volume-indicator\";\r\nimport {IndicatorFactory} from \"./indicator-factory\";\r\nimport SMAIndicator from \"./sma-indicator\";\r\nimport StochasticIndicator from \"./stochastic-indicator\";\r\nimport EMAIndicator from \"./ema-indicator\";\r\nimport WMAIndicator from \"./wma-indicator\";\r\nimport MomentumIndicator from \"./momentum-indicator\";\r\nimport WilliamsIndicator from \"./williams-indicator\";\r\nimport DMIIndicator from \"./dmi-indicator\";\r\nimport MassIndexIndicator from \"./mass-index-indicator\";\r\nimport UltimateOscillatorIndicator from \"./ultimate-oscillator-indicator\";\r\nimport VROCIndicator from \"./vroc-indicator\";\r\nimport ChaikinsVolatilityIndicator from \"./chaikins-volatility-indicator\";\r\n\r\nIndicatorFactory.registerIndicator('bb', BBIndicator)\r\nIndicatorFactory.registerIndicator('rsi', RSIIndicator)\r\nIndicatorFactory.registerIndicator('macd', MACDIndicator)\r\nIndicatorFactory.registerIndicator('volume_overlay', VolumeIndicator, { overlay: true })\r\nIndicatorFactory.registerIndicator('volume', VolumeIndicator)\r\nIndicatorFactory.registerIndicator('sma', SMAIndicator)\r\nIndicatorFactory.registerIndicator('stochastic', StochasticIndicator)\r\nIndicatorFactory.registerIndicator('ema', EMAIndicator)\r\nIndicatorFactory.registerIndicator('wma', WMAIndicator)\r\nIndicatorFactory.registerIndicator('momentum', MomentumIndicator)\r\nIndicatorFactory.registerIndicator('williams', WilliamsIndicator)\r\nIndicatorFactory.registerIndicator('dmi', DMIIndicator)\r\nIndicatorFactory.registerIndicator('massindex', MassIndexIndicator)\r\nIndicatorFactory.registerIndicator('ultimateoscillator', UltimateOscillatorIndicator)\r\nIndicatorFactory.registerIndicator('vroc', VROCIndicator)\r\nIndicatorFactory.registerIndicator('chaikinsvolatility', ChaikinsVolatilityIndicator)\r\n\r\nexport { IndicatorFactory } from './indicator-factory'\r\nexport {default as BBIndicator} from \"./bb-indicator\";\r\nexport {default as MACDIndicator} from \"./macd-indicator\";\r\nexport {default as RSIIndicator} from \"./rsi-indicator\";\r\nexport {default as VolumeIndicator} from \"./volume-indicator\";\r\nexport {default as SMAIndicator} from \"./sma-indicator\";\r\nexport { default as StochasticIndicator } from \"./stochastic-indicator\";\r\nexport { default as EMAIndicator } from \"./ema-indicator\";\r\nexport { default as WMAIndicator } from \"./wma-indicator\";\r\nexport { default as MomentumIndicator } from \"./momentum-indicator\";\r\nexport { default as WilliamsIndicator } from \"./williams-indicator\";\r\nexport { default as DMIIndicator } from \"./dmi-indicator\";\r\nexport { default as MassIndexIndicator } from \"./mass-index-indicator\";\r\nexport { default as UltimateOscillatorIndicator } from \"./ultimate-oscillator-indicator\";\r\nexport { default as VROCIndicator } from \"./vroc-indicator\";\r\nexport { default as ChaikinsVolatilityIndicator } from \"./chaikins-volatility-indicator\";\r\n"], "names": ["IndicatorFactory", "BBIndicator", "RSIIndicator", "MACDIndicator", "VolumeIndicator", "SMAIndicator", "StochasticIndicator", "EMAIndicator", "WMAIndicator", "MomentumIndicator", "WilliamsIndicator", "DMIIndicator", "MassIndexIndicator", "UltimateOscillatorIndicator", "VROCIndicator", "ChaikinsVolatilityIndicator"], "mappings": "stBAiBAA,EAAAA,iBAAiB,kBAAkB,KAAMC,SAAW,EACpDD,EAAAA,iBAAiB,kBAAkB,MAAOE,SAAY,EACtDF,EAAAA,iBAAiB,kBAAkB,OAAQG,SAAa,EACxDH,EAAAA,iBAAiB,kBAAkB,iBAAkBI,EAAAA,QAAiB,CAAE,QAAS,GAAM,EACvFJ,EAAAA,iBAAiB,kBAAkB,SAAUI,SAAe,EAC5DJ,EAAAA,iBAAiB,kBAAkB,MAAOK,SAAY,EACtDL,EAAAA,iBAAiB,kBAAkB,aAAcM,SAAmB,EACpEN,EAAAA,iBAAiB,kBAAkB,MAAOO,SAAY,EACtDP,EAAAA,iBAAiB,kBAAkB,MAAOQ,SAAY,EACtDR,EAAAA,iBAAiB,kBAAkB,WAAYS,SAAiB,EAChET,EAAAA,iBAAiB,kBAAkB,WAAYU,SAAiB,EAChEV,EAAAA,iBAAiB,kBAAkB,MAAOW,SAAY,EACtDX,EAAAA,iBAAiB,kBAAkB,YAAaY,SAAkB,EAClEZ,EAAAA,iBAAiB,kBAAkB,qBAAsBa,SAA2B,EACpFb,EAAAA,iBAAiB,kBAAkB,OAAQc,SAAa,EACxDd,EAAAA,iBAAiB,kBAAkB,qBAAsBe,SAA2B"}