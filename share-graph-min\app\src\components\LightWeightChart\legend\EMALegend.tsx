import { FC } from 'react';
import { Legend } from './Legend';
import { AdvanceChart } from '@sharegraph-mini/advance-charts';
import { EMAIndicator } from '@sharegraph-mini/advance-charts';

const EMALegend: FC<{
  indicator: EMAIndicator;
  advanceChart: AdvanceChart;
}> = ({ indicator, advanceChart }) => {
  return (
    <Legend
      name="EMA"
      indicator={indicator}
      renderer={(d) =>
        d && d.value ? (
          <>
            {' '}
            <span style={{ color: indicator.options.color }}>
              {advanceChart.numberFormatter.decimal(d.value[0])}
            </span>{' '}
          </>
        ) : null
      }
    />
  );
};

export default EMALegend;
