import { IChartApi } from 'lightweight-charts';
import { ChartIndicator, ChartIndicatorOptions } from './abstract-indicator';

export interface IChartIndicatorConstructor<IOptions extends ChartIndicatorOptions = ChartIndicatorOptions, IIndicatorData extends readonly number[] = number[]> {
    new (chart: I<PERSON>hart<PERSON><PERSON>, options?: Partial<IOptions>, paneIndex?: number): ChartIndicator<IOptions, IIndicatorData>;
}
export declare class IndicatorFactory {
    private static registry;
    static registerIndicator<IOptions extends ChartIndicatorOptions, IIndicatorData extends readonly number[] = number[]>(name: string, indicatorClass: IChartIndicatorConstructor<IOptions, IIndicatorData>, defaultOptions?: Partial<IOptions>): void;
    static indicatorRegistered(name: string): boolean;
    static createIndicator(name: string, chartApi: IChartApi, options?: Partial<ChartIndicatorOptions>, paneIndex?: number): ChartIndicator<ChartIndicatorOptions, number[], import('../interface').OHLCVData>;
}
