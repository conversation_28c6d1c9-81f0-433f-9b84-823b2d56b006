import { Form, Select } from "antd";
import { useEffect, useState } from "react";
import { FONTS } from "../../configs/defaultSetting";
import { useInputStyle } from "../../hooks/useInputStyle";

const FormItemSelectFontFamily = ({ name, label = "", className }) => {
  const options = Object.values(FONTS).map((font) => ({
    value: font.key,
    label: font.fontFamily,
  }));
  const { inputStyle } = useInputStyle();
  return (
    <Form.Item name={name} label={label} className={className} style={{ marginBottom: 0 }}>
      <Select
        maxTagCount="responsive"
        placeholder="Font family"
        notFoundContent="No font options available"
        options={options}
        style={{ ...inputStyle, marginBottom: 0 }}
      />
    </Form.Item>
  );
};
const SelectFontFamily = ({ options, onChange, value = "" }) => {
  const [fontValue, setFontValue] = useState([]);
  useEffect(() => {
    if (value) {
      const fontValue = [];
      value.split(", ").forEach((v) => {
        const font = options.find((o) => o.label === v);
        if (font) {
          fontValue.push(font.value);
        }
      });
      setFontValue(fontValue);
    }
  }, [value, options]);
  return (
    <Select
      mode="multiple"
      value={fontValue}
      maxTagCount="responsive"
      placeholder="Font family"
      notFoundContent="No font options available"
      options={options}
      onChange={(value) => {
        const fontFamily = value
          .map((v) => {
            const font = options.find((o) => o.value === v);
            return font?.label || "";
          })
          .join(", ");
        setFontValue(value);
        onChange(fontFamily);
      }}
      style={{ width: "100%", marginBottom: 0 }}
    />
  );
};
export { FormItemSelectFontFamily };
