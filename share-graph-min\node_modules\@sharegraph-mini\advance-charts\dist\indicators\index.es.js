import t from "./bb-indicator.es.js";
import o from "./macd-indicator.es.js";
import a from "./rsi-indicator.es.js";
import i from "./volume-indicator.es.js";
import { IndicatorFactory as r } from "./indicator-factory.es.js";
import m from "./sma-indicator.es.js";
import e from "./stochastic-indicator.es.js";
import c from "./ema-indicator.es.js";
import n from "./wma-indicator.es.js";
import d from "./momentum-indicator.es.js";
import I from "./williams-indicator.es.js";
import s from "./dmi-indicator.es.js";
import l from "./mass-index-indicator.es.js";
import p from "./ultimate-oscillator-indicator.es.js";
import f from "./vroc-indicator.es.js";
import g from "./chaikins-volatility-indicator.es.js";
r.registerIndicator("bb", t);
r.registerIndicator("rsi", a);
r.registerIndicator("macd", o);
r.registerIndicator("volume_overlay", i, { overlay: !0 });
r.registerIndicator("volume", i);
r.registerIndicator("sma", m);
r.registerIndicator("stochastic", e);
r.registerIndicator("ema", c);
r.registerIndicator("wma", n);
r.registerIndicator("momentum", d);
r.registerIndicator("williams", I);
r.registerIndicator("dmi", s);
r.registerIndicator("massindex", l);
r.registerIndicator("ultimateoscillator", p);
r.registerIndicator("vroc", f);
r.registerIndicator("chaikinsvolatility", g);
export {
  t as BBIndicator,
  g as ChaikinsVolatilityIndicator,
  s as DMIIndicator,
  c as EMAIndicator,
  r as IndicatorFactory,
  o as MACDIndicator,
  l as MassIndexIndicator,
  d as MomentumIndicator,
  a as RSIIndicator,
  m as SMAIndicator,
  e as StochasticIndicator,
  p as UltimateOscillatorIndicator,
  f as VROCIndicator,
  i as VolumeIndicator,
  n as WMAIndicator,
  I as WilliamsIndicator
};
//# sourceMappingURL=index.es.js.map
