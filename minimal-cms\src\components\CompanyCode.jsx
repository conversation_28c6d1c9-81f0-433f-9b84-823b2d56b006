import {
  useCallback,
  useContext,
  useLayoutEffect,
  useMemo,
  useRef,
  useState,
} from "react";
import { AutoComplete, Form, Spin } from "antd";
import { LoadingOutlined } from "@ant-design/icons";
import { useDispatch, useSelector } from "react-redux";
import debounce from "lodash.debounce";

import { companyCodesLoadMoreSelector } from "../store/selector/settingSelector";
import KeyFormContext from "../context/KeyFormContext";
import { getCompanyCodeByIdAction } from "../store/actions";
import { resetSession } from "../store/slice/forms";

const ContainerHeight = 256;

export default function CompanyCode({ value, onChange, width = 310 }) {
  const { loadMoreCompanyCodes, loading, isLoadMore } = useSelector(
    companyCodesLoadMoreSelector
  );

  const [companyNameValue, setCompanyNameValue] = useState();
  const searchKeywordRef = useRef("");
  const pageNumberRef = useRef(1);

  const dispatch = useDispatch();
  const { onChangeFormKey } = useContext(KeyFormContext);

  const getCompanyCodes = useCallback(
    (params) => dispatch(getCompanyCodeByIdAction(params)),
    [dispatch]
  );

  useLayoutEffect(() => {
    if (!value) return;
    if (value.label !== companyNameValue) {
      setCompanyNameValue(value.label);
    }
  }, [value]);

  const options = useMemo(
    () =>
      loadMoreCompanyCodes.map((companyCode) => ({
        label: companyCode.name,
        value: companyCode.code,
      })),
    [loadMoreCompanyCodes]
  );

  const resetForm = () => {
    dispatch(resetSession());
    onChangeFormKey();
  };

  const handleSearchChange = useMemo(
    () =>
      debounce(({ searchValue, pageSize = 20, pageNumber = 1 }) => {
        if (!searchValue.trim()) return;
        if (searchValue.length > 2) {
          getCompanyCodes({ companyCode: searchValue, pageSize, pageNumber });
        }
      }, 300),
    []
  );

  const handleChange = (value) => {
    setCompanyNameValue(value);
  };
  const handleSelect = (value, option) => {
    resetForm();
    onChange(option);
  };
  const handleBlur = () => {
    const selectedOption = options.find(
      (opt) =>
        opt.label?.toLocaleLowerCase() === companyNameValue?.toLocaleLowerCase()
    );
    if (!selectedOption) {
      setCompanyNameValue(value.label || undefined);
      searchKeywordRef.current = value.label || undefined;
      return;
    }
    searchKeywordRef.current = selectedOption.value;
    setCompanyNameValue(selectedOption.label);
    if (selectedOption.value === value.value) return;

    resetForm();
    onChange(selectedOption);
  };

  const handleClear = () => {
    resetForm();
    onChange({});
  };

  const handlePopupScroll = (e) => {
    if (!searchKeywordRef.current || !isLoadMore) return;
    if (
      Math.abs(
        e.currentTarget.scrollHeight -
          e.currentTarget.scrollTop -
          ContainerHeight
      ) <= 1
    ) {
      pageNumberRef.current += 1;
      handleSearchChange({
        searchValue: searchKeywordRef.current,
        pageSize: 20,
        pageNumber: pageNumberRef.current,
      });
    }
  };

  const handleSearch = (searchValue) => {
    searchKeywordRef.current = searchValue;
    pageNumberRef.current = 1;
    handleSearchChange({ searchValue, pageSize: 20, pageNumber: 1 });
  };

  return (
    <div className="relative">
      {loading && (
        <Spin
          size="small"
          className="absolute z-10 top-[50%] translate-y-[-50%] right-2.5"
          indicator={<LoadingOutlined spin />}
        />
      )}
      <AutoComplete
        allowClear={!loading}
        options={options}
        style={{ width }}
        value={companyNameValue}
        onChange={handleChange}
        onSearch={handleSearch}
        placeholder="Company name"
        onSelect={handleSelect}
        onBlur={handleBlur}
        onClear={handleClear}
        onPopupScroll={handlePopupScroll}
      />
    </div>
  );
}
