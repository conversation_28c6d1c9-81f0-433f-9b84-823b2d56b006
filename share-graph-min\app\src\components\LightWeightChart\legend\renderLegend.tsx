import { AdvanceChart } from "@sharegraph-mini/advance-charts";
import {
  BBIndicator,
  DMIIndicator,
  EMAIndicator,
  MACDIndicator,
  MomentumIndicator,
  RSIIndicator,
  SMAIndicator,
  VolumeIndicator,
  WMAIndicator,
  WilliamsIndicator,
  MassIndexIndicator,
  UltimateOscillatorIndicator,
  VROCIndicator,
  ChaikinsVolatilityIndicator
} from "@sharegraph-mini/advance-charts";
import { ChartIndicator } from "@sharegraph-mini/advance-charts";
import {StochasticIndicator} from "@sharegraph-mini/advance-charts";

import EMALegend from './EMALegend';
import BBLegend from './BBLegend';
import DMILegend from './DMILegend';
import MassIndexLegend from './MassIndexLegend';
import MACDLegend from './MACDLegend';
import RSILegend from './RSILegend';
import SMALegend from './SMALegend';
import StochasticLegend from './StochasticLegend';
import VolumeLegend from './VolumeLegend';
import MomentumLegend from './MMLegend';
import WMALegend from './WMALegend';
import WilliamsLegend from "./WilliamsLegend";
import UltimateOscillatorLegend from "./UltimateOscillatorLegend";
import VROCLegend from "./VROCLegend";
import CVSLegend from "./CVSLegend";

export default function renderLegend(
  indicator: ChartIndicator,
  index: number | string,
  advanceChart: AdvanceChart
) {
  if (indicator instanceof VolumeIndicator)
    return (
      <VolumeLegend
        indicator={indicator}
        advanceChart={advanceChart}
        key={index}
      />
    );

  if (indicator instanceof RSIIndicator)
    return (
      <RSILegend
        indicator={indicator}
        advanceChart={advanceChart}
        key={index}
      />
    );

  if (indicator instanceof MACDIndicator)
    return (
      <MACDLegend
        indicator={indicator}
        advanceChart={advanceChart}
        key={index}
      />
    );

  if (indicator instanceof BBIndicator)
    return (
      <BBLegend indicator={indicator} advanceChart={advanceChart} key={index} />
    );

  if (indicator instanceof StochasticIndicator)
    return (
      <StochasticLegend
        indicator={indicator}
        advanceChart={advanceChart}
        key={index}
      />
    );

  if (indicator instanceof SMAIndicator)
    return (
      <SMALegend
        indicator={indicator}
        advanceChart={advanceChart}
        key={index}
      />
    );

  if (indicator instanceof EMAIndicator)
    return (
      <EMALegend
        indicator={indicator}
        advanceChart={advanceChart}
        key={index}
      />
    );

  if (indicator instanceof WMAIndicator)
    return (
      <WMALegend
        indicator={indicator}
        advanceChart={advanceChart}
        key={index}
      />
    );

  if (indicator instanceof MomentumIndicator)
    return (
      <MomentumLegend
        indicator={indicator}
        advanceChart={advanceChart}
        key={index}
      />
    );
  if (indicator instanceof WilliamsIndicator)
    return (
      <WilliamsLegend
        indicator={indicator}
        advanceChart={advanceChart}
        key={index}
      />
    );

  if (indicator instanceof DMIIndicator)
    return (
      <DMILegend
        indicator={indicator}
        advanceChart={advanceChart}
        key={index}
      />
    );
  if (indicator instanceof MassIndexIndicator)
    return (
      <MassIndexLegend
        indicator={indicator}
        advanceChart={advanceChart}
        key={index}
      />
    );
  if(indicator instanceof UltimateOscillatorIndicator)
    return (
      <UltimateOscillatorLegend
        indicator={indicator}
        advanceChart={advanceChart}
        key={index}
      />
    );
  if(indicator instanceof VROCIndicator)
    return (
      <VROCLegend
        indicator={indicator}
        advanceChart={advanceChart}
        key={index}
      />
    );
    if(indicator instanceof ChaikinsVolatilityIndicator)
    return (
      <CVSLegend
        indicator={indicator}
        advanceChart={advanceChart}
        key={index}
      />
    );  
}
