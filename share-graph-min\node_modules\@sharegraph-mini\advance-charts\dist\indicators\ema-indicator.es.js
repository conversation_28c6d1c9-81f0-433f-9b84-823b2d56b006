var v = Object.defineProperty;
var h = (t, i, e) => i in t ? v(t, i, { enumerable: !0, configurable: !0, writable: !0, value: e }) : t[i] = e;
var l = (t, i, e) => h(t, typeof i != "symbol" ? i + "" : i, e);
import { SeriesPrimitiveBase as d } from "../custom-primitive/primitive-base.es.js";
import { LinePrimitivePaneView as P } from "../custom-primitive/pane-view/line.es.js";
import { ChartIndicator as f } from "./abstract-indicator.es.js";
const w = {
  color: "#d26400",
  period: 9,
  overlay: !0
};
class x extends d {
  constructor(e) {
    super();
    l(this, "linePrimitive");
    this.source = e, this.linePrimitive = new P({
      lineColor: this.source.options.color
    }), this._paneViews = [this.linePrimitive];
  }
  update(e) {
    const r = [];
    for (const o of e) {
      const a = o.value;
      a && r.push({ time: o.time, price: a[0] });
    }
    this.linePrimitive.update(r);
  }
}
class N extends f {
  constructor() {
    super(...arguments);
    l(this, "emaPrimitive", new x(this));
  }
  getDefaultOptions() {
    return w;
  }
  _mainSeriesChanged(e) {
    e.attachPrimitive(this.emaPrimitive);
  }
  remove() {
    var e;
    super.remove(), (e = this.mainSeries) == null || e.detachPrimitive(this.emaPrimitive);
  }
  applyIndicatorData() {
    this.emaPrimitive.update(
      this._executionContext.data
    );
  }
  formula(e) {
    const r = this.options.period, o = e.symbol.close, a = 2 / (r + 1), m = e.new_var(o, r), n = e.new_var(NaN, 2);
    if (!m.calculable()) return;
    const c = n.get(1);
    let s;
    return isNaN(c) ? (s = m.getAll().reduce((u, p) => u + p, 0) / r, n.set(s), [s]) : (s = a * o + (1 - a) * c, n.set(s), [s]);
  }
}
export {
  x as EMAPrimitive,
  N as default,
  w as defaultOptions
};
//# sourceMappingURL=ema-indicator.es.js.map
