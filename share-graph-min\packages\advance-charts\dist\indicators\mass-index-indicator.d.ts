import { IChartApi, ISeriesApi, Nominal, SeriesType } from 'lightweight-charts';
import { ChartIndicator, ChartIndicatorOptions } from './abstract-indicator';
import { Context } from '../helpers/execution-indicator';

export interface MassIndexIndicatorOptions extends ChartIndicatorOptions {
    color: string;
    emaPeriod: number;
    sumPeriod: number;
    priceLineColor: string;
    backgroundColor: string;
}
export declare const defaultOptions: MassIndexIndicatorOptions;
export type MassIndexLine = Nominal<number, 'MassIndex'>;
export type MassIndexData = [MassIndexLine];
export default class MassIndexIndicator extends ChartIndicator<MassIndexIndicatorOptions, MassIndexData> {
    massIndexSeries: ISeriesApi<SeriesType>;
    constructor(chart: IChartApi, options?: Partial<MassIndexIndicatorOptions>, paneIndex?: number);
    getDefaultOptions(): MassIndexIndicatorOptions;
    formula(c: Context): MassIndexData | undefined;
    applyIndicatorData(): void;
    remove(): void;
    _applyOptions(): void;
    setPaneIndex(paneIndex: number): void;
    getPaneIndex(): number;
}
