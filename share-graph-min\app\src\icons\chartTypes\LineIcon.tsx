import React from 'react';
import { ISvgIconProps } from '../../types/common';

const LineIcon: React.FC<ISvgIconProps> = ({ width = 28, height = 28, color = 'currentColor', className }) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 28 28"
      width={width}
      height={height}
      className={className}
    >
      <path
        fill={color}
        d="m25.39 7.31-8.83 10.92-6.02-5.47-7.16 8.56-.76-.64 7.82-9.36 6 5.45L24.61 6.7l.78.62Z"
      />
    </svg>
  );
};

export default LineIcon;
