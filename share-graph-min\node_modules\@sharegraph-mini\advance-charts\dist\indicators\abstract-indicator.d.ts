import { IChartApi, ISeriesApi, MouseEventParams, OhlcData, SeriesDataItemTypeMap, SeriesType, SingleValueData, Time, WhitespaceData } from 'lightweight-charts';
import { OHLCVData } from '../interface';
import { Delegate, IPublicDelegate } from '../helpers/delegate';
import { NumberFormatter } from '../helpers/number-formatter';
import { Context, ExecutionContext, IIndicatorBar } from '../helpers/execution-indicator';

export declare const upColor = "#26a69a";
export declare const downColor = "#ef5350";
export type IndicatorData = OhlcData | SingleValueData;
export type InputData = OhlcData | SingleValueData;
export type SimpleData = WhitespaceData | SingleValueData;
export interface ChartIndicatorOptions {
    overlay: boolean;
    upColor?: string;
    downColor?: string;
    numberFormatter?: () => NumberFormatter;
}
export declare abstract class ChartIndicator<IOptions extends ChartIndicatorOptions = ChartIndicatorOptions, IIndicatorData extends readonly number[] = number[], IData extends OHLCVData = OHLCVData> {
    protected chart: IChartApi;
    protected data: Array<IData> | null;
    options: IOptions;
    mainSeries: ISeriesApi<SeriesType> | null;
    _dataHovered: Delegate<IIndicatorBar<IIndicatorData> | undefined, void, void>;
    indicatorData: Array<IIndicatorData>;
    _executionContext: ExecutionContext<IIndicatorData>;
    formula(c: Context): IIndicatorData | undefined;
    mainSeriesChanged(series: ISeriesApi<SeriesType>): void;
    onCrosshairMove(param: MouseEventParams<Time>): void;
    dataHovered(): IPublicDelegate<typeof this._dataHovered>;
    constructor(chart: IChartApi, options?: Partial<IOptions>);
    abstract getDefaultOptions(): IOptions;
    setData(data: Array<IData>): void;
    update(): void;
    applyOptions(options: Partial<IOptions>): void;
    remove(): void;
    getDataByCrosshair<TSeriesType extends SeriesType, TData extends SeriesDataItemTypeMap<Time>[TSeriesType]>({ logical }: {
        logical?: number;
    }, series: ISeriesApi<TSeriesType, Time, TData>): TData | undefined;
    dataByTime(time: Time): IIndicatorBar<IIndicatorData> | undefined;
    lastPoint(): IIndicatorBar<IIndicatorData> | undefined;
    getData(): IIndicatorBar<IIndicatorData>[];
    calcIndicatorData(): void;
    recalc?(): void;
    applyIndicatorData(): void;
    setPaneIndex(paneIndex: number): void;
    getPaneIndex(): number;
    _applyOptions?(options: Partial<IOptions>): void;
    _mainSeriesChanged?(series: ISeriesApi<SeriesType>): void;
}
