import React, { useState } from 'react';
import IndicatorIcon from '../../icons/Indicator';
import Button from '../Button';
import { i18n } from '@euroland/libs';
import { IndicatorDrawer } from '../Drawer';

const MobileIndicator = () => {
  const [isIndicatorDrawerOpen, setIsIndicatorDrawerOpen] = useState(false);

  const toggleIndicatorDrawer = () => {
    setIsIndicatorDrawerOpen(!isIndicatorDrawerOpen);
  };

  return (
    <>
      <Button onClick={toggleIndicatorDrawer} className="mobile-button">
        <IndicatorIcon /> <span>{i18n.translate('indicator')}</span>
      </Button>
      <IndicatorDrawer
        isOpen={isIndicatorDrawerOpen}
        onClose={toggleIndicatorDrawer}
      />
    </>
  );
};

export default React.memo(MobileIndicator);