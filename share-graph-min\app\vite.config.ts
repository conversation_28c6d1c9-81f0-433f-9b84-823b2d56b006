import { defineConfig as viteDefineConfig, loadEnv } from 'vite'
import { defineConfig, mergeConfig } from 'vitest/config'
import react from '@vitejs/plugin-react'
import path, {join} from 'path'

// https://vite.dev/config/
export default ({ mode }: { mode: string }) => {
  // Load app-level env vars to node-level env vars.
  process.env = {...process.env, ...loadEnv(mode, join(__dirname, '../'))};
  const BASE_URL = process.env.VITE_BASE_URL || '/';

  return mergeConfig(
    viteDefineConfig({
      base: BASE_URL,
      plugins: [react()],
      build: {
        outDir: path.resolve(__dirname, '../dist'),
        emptyOutDir: true,
      }
    })
    ,
    defineConfig({
      test: {
        environment: 'jsdom',
        globals: true,
        setupFiles: './src/setupTests.ts'
      },
      server: {
        proxy: {
          '/tools/apigateway/graphql': {
            target: 'https://dev.vn.euroland.com',
            changeOrigin: true,
            secure: true,
          },
        },
      },
    })
  );
}
