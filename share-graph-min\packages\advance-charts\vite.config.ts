import { defineConfig as viteDefineConfig } from "vite";
import packageJson from "./package.json";
import dts from "vite-plugin-dts";
import { defineConfig, mergeConfig } from 'vitest/config'

export default mergeConfig(viteDefineConfig({
  plugins: [
    dts({
      entryRoot: "src",
      afterDiagnostic: function (diagnostics) {
        if (diagnostics.length > 0)
          throw new Error("Please fix all TypeScript errors before building");
      },
    }),
  ],
  build: {
    emptyOutDir: true,
    reportCompressedSize: true,
    commonjsOptions: {
      transformMixedEsModules: true,
    },
    lib: {
      // Could also be a dictionary or array of multiple entry points.
      entry: "src/advance-charts.ts",
      name: packageJson.name,
      fileName: "advance-charts",
      // Change this to the formats you want to support.
      // Don't forget to update your package.json as well.
      formats: ["es", "cjs"],
    },
    rollupOptions: {
      // External packages that should not be bundled into your library.
      external: Array.from(new Set([
        'es-toolkit/compat',
        "dayjs/plugin/utc",
        "dayjs/plugin/timezone",
        "dayjs/plugin/weekYear",
        "dayjs/plugin/weekOfYear",
        ...Object.keys(packageJson.dependencies ?? {})
      ])),
      output: {
        preserveModules: true,
        preserveModulesRoot: "src",
        exports: "named",
        entryFileNames: "[name].[format].js",
      },
    },
    sourcemap: true,
  },
}), defineConfig({
  test: {
    environment: 'jsdom',
    globals: true,
    setupFiles: './src/setupTests.ts'
  },
}));
