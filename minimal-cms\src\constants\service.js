export const API = {
  LOGOUT: '/api/auth/logout',
  GET_LOGIN: '/api/auth/login',
  GET_INSTRUMENT: '/api/instrument/search',
  GET_INSTRUMENT_BY_COMPANY_CODE:
    'https://gamma.euroland.com/supporttools/companyinfo/api/company/get-company-info',
  GET_COMPANY_CODES:
    'https://gamma.euroland.com/supporttools/companyinfo/api/company',
  GET_AVAILABLE_LANGUAGES: '/api/common/get-supported-languages',
  GET_AVAILABLE_TOOLS: '/api/common/get-tools',
  GET_TIME_ZONE: '/api/common/get-time-zones',
  GET_CURRENCIES: '/api/common/get-currencies',
  SAVE_SESSION_STATE: '/api/setting/save-session-state',
  GET_SESSION_STATE: '/api/setting/get-session-state',
  APPLY_SETTINGS: '/api/setting/apply',
  UPLOAD_FONT: '/api/setting/upload-font',
  GET_FONT: '/api/setting/get-font',
  DELETE_FONT_BY_ID: '/api/setting/delete-font',
  DELETE_FONT_BY_FONT_FAMILY: '/api/setting/delete-fontfamily',
  GENERATE_LINK: '/api/setting/generate',
  GET_XML: '/api/setting/get-xml',
  GET_VERSIONS_BY_COMPANY_CODE: '/api/setting/get-xml-versions',
  GET_CSS_VERSIONS_BY_COMPANY_CODE: '/api/setting/get-css-versions',
  GET_INSTRUMENT_BY_IDS: '/api/common/get-instruments',
  GET_OLD_SOLUTIONS_AND_VERSIONS_TOOL_DATA: '/api/common/get-solution',
  MIGRATE_SOLUTION: '/api/migrate',
  GET_LIST_FILES: '/api/file/get-list-files',
  GET_TOOL_CENTER_LINK: '/api/migration/tool-center-link',
  GET_CONTENT_FILE: '/api/file/get-content-file',
  SAVE_FILE: '/api/file/save-file',
  GET_CAPTCHA: '/api/migration/get-captcha',
  MIGRATION: '/api/migration/migrate-update-tool-center',
  GET_USERNAMES: '/api/common/get-usernames',
  GET_EVENT_TYPES: '/api/common/get-event-types',
  GET_EVENT_DESCRIPTIONS: '/api/common/get-event-descriptions',
  GET_EVENT_STATUS: '/api/common/get-event-statuses',
  GET_ACTIVITY_LOG: '/api/log',
  CONFIRM_MIGRATION: '/api/migration/confirm-migrate-update-tool-center',
  GET_CUSTOME_PHRASES: '/api/file/get-list-customphrases',
  SAVE_SETTING_FILE: '/api/setting/save-setting-files',
  UPLOAD_IMAGES: '/api/setting/upload-image',
  GET_IMAGES: '/api/setting/get-images',
  DELETE_IMAGE: '/api/setting/delete-image',
  SAVE_PACKAGE: '/api/setting/save-package',
  GET_SAVED_PACKAGES: '/api/setting/get-packages',

  GET_USER_PERMISSIONS: '/api/user/permissions',
  ROLE: '/api/role',
  GET_PERMISSIONS: '/api/role/get-permissions',
  ASSIGN_ROLE: '/api/user/update-user-roles',
  SEARCH_USER_BY_EMAIL: '/api/user/search',
  GET_ALL_USER_ROLES: '/api/user/get-user-roles',
};

export const STATUS_CODE = {
  BAD_REQUEST: 400,
  SUCCESS: 200,
  SERVER_ERROR: 500,
};
