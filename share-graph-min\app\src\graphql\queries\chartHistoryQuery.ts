import { gql } from '../utils';

export const CHART_INTRADAY_DATA = gql(/* GraphQL */`query ChartHistory($id: Int!, $fromDate: DateTime, $toDate: DateTime, $timeIntervalGrouping: Int) {
  instrumentById(id: $id) {
    market {
      timezone {
        nameIANA
      }
      openTimeLocal
      closeTimeLocal
    }
    intraday(
      where: {dateTime: {gte: $fromDate, lte: $toDate}}
      first: 9999999
      order: {dateTime: ASC}
      timeIntervalGrouping: $timeIntervalGrouping
    ) {
      nodes {
        close
        dateTime
        high
        low
        open
        volume
        instrumentId
      }
    }
  }
}`);

export const CHART_HISTORICAL_DATA = gql(/* GraphQL */`query ChartHistoricalData(
  $id: Int!
  $fromDate: DateTime
  $toDate: DateTime
) {
  instrumentById(id: $id) {
   
    historicals(
      where: { dateTime: { gte: $fromDate, lte: $toDate } }
      first: 9999999
      order: { dateTime: ASC }
    ) {
      nodes {
        close
        dateTime
        high
        low
        open
        volume
        instrumentId
      }
    }
  }
}
`);