import { FC } from 'react';
import { Legend } from './Legend';
import { AdvanceChart } from "@sharegraph-mini/advance-charts";
import { SMAIndicator } from "@sharegraph-mini/advance-charts";

const SMALegend: FC<{
  indicator: SMAIndicator;
  advanceChart: AdvanceChart;
}> = ({ indicator, advanceChart }) => {
  return (
    <Legend
      name="SMA"
      indicator={indicator}
      renderer={(d) =>
        d ? (
          <>
            {' '}
            <span style={{ color: indicator.options.color }}>
              {advanceChart.numberFormatter.decimal(d.value.at(0))}
            </span>{' '}
          </>
        ) : null
      }
    />
  );
};

export default SMALegend;
