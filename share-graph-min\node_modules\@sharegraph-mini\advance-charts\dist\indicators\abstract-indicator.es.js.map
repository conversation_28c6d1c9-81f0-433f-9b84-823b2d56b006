{"version": 3, "file": "abstract-indicator.es.js", "sources": ["../../src/indicators/abstract-indicator.ts"], "sourcesContent": ["import type {<PERSON><PERSON>hart<PERSON><PERSON>, <PERSON>eries<PERSON>pi, MouseEventParams, OhlcData, SeriesDataItemTypeMap, SeriesType, SingleValueData, Time, WhitespaceData} from \"lightweight-charts\";\r\nimport {cloneDeep, merge} from \"es-toolkit\";\r\nimport {OHLCVData} from \"../interface\";\r\nimport {Delegate, IPublicDelegate} from \"../helpers/delegate\";\r\nimport {binarySearch, timeToDate} from \"../helpers/utils\";\r\nimport {NumberFormatter} from \"../helpers/number-formatter\";\r\nimport {Context, ExecutionContext, IIndicatorBar} from \"../helpers/execution-indicator\";\r\n\r\nexport const upColor = '#26a69a'; // Green for bullish candles\r\nexport const downColor = '#ef5350'; // Red for bearish candles\r\n\r\nexport type IndicatorData = OhlcData | SingleValueData\r\n\r\nexport type InputData = OhlcData | SingleValueData\r\nexport type SimpleData = WhitespaceData | SingleValueData\r\n\r\nexport interface ChartIndicatorOptions {\r\n  overlay: boolean,\r\n  upColor?: string;\r\n  downColor?: string;\r\n  numberFormatter?: () => NumberFormatter\r\n}\r\n\r\nexport abstract class ChartIndicator <\r\n  IOptions extends ChartIndicatorOptions = ChartIndicatorOptions, \r\n  IIndicatorData extends readonly number[] = number[],\r\n  IData extends OHLCVData = OHLCVData,\r\n> {\r\n  protected data: Array<IData> | null = null\r\n  options: IOptions;\r\n\r\n  mainSeries: ISeriesApi<SeriesType> | null = null\r\n\r\n  _dataHovered = new Delegate<IIndicatorBar<IIndicatorData> | undefined>()\r\n\r\n  indicatorData: Array<IIndicatorData> = []\r\n\r\n  _executionContext: ExecutionContext<IIndicatorData>\r\n\r\n  formula(c: Context): IIndicatorData | undefined\r\n  formula(): IIndicatorData | undefined {\r\n    return undefined\r\n  }\r\n\r\n  mainSeriesChanged(series: ISeriesApi<SeriesType>) {\r\n    this.mainSeries = series\r\n\r\n    this._mainSeriesChanged?.(series)\r\n  } \r\n\r\n\r\n\r\n  onCrosshairMove(param: MouseEventParams<Time>) {\r\n    if(param.time === undefined) return this._dataHovered.fire(undefined)\r\n    this._dataHovered.fire(this.dataByTime(param.time))\r\n  }\r\n\r\n  dataHovered() {\r\n    return this._dataHovered as IPublicDelegate<typeof this._dataHovered>\r\n  }\r\n\r\n  constructor(protected chart: IChartApi, options?: Partial<IOptions>) {\r\n    this.options = merge(cloneDeep(this.getDefaultOptions()), options ?? {})\r\n    this.onCrosshairMove = this.onCrosshairMove.bind(this)\r\n    this.chart.subscribeCrosshairMove(this.onCrosshairMove)\r\n\r\n    this._executionContext = new ExecutionContext<IIndicatorData>((c) => this.formula?.(c))\r\n  }\r\n\r\n  abstract getDefaultOptions(): IOptions\r\n\r\n  setData(data: Array<IData>) {\r\n    this.data = data;\r\n    this._executionContext.recalc(data.map(item => ({\r\n      open: item.open, \r\n      high: item.high, \r\n      low: item.low, \r\n      time: Math.floor(timeToDate(item.time).getTime() / 1000), \r\n      isNew: false, \r\n      volume: item.volume, \r\n      close: item.close\r\n    })))\r\n    this.calcIndicatorData();\r\n    this.applyIndicatorData();\r\n  }\r\n\r\n  update() {\r\n    if(!this.data) return;\r\n    const lastData = this.data[this.data.length - 1];\r\n    this._executionContext.update({\r\n      open: lastData.open, \r\n      high: lastData.high, \r\n      low: lastData.low, \r\n      time: Math.floor(timeToDate(lastData.time).getTime() / 1000), \r\n      volume: lastData.volume, \r\n      close: lastData.close\r\n    })\r\n    this.recalc?.()\r\n    this.applyIndicatorData();\r\n  }\r\n\r\n  applyOptions(options: Partial<IOptions>) {\r\n    this.options = merge(this.options, options)\r\n    this._applyOptions?.(options);\r\n  }\r\n\r\n  remove() {\r\n    if(this.onCrosshairMove) {\r\n      this.chart.unsubscribeCrosshairMove(this.onCrosshairMove)\r\n    }\r\n  }\r\n\r\n  getDataByCrosshair<TSeriesType extends SeriesType, TData extends SeriesDataItemTypeMap<Time>[TSeriesType]>({ logical }: {logical?: number}, series: ISeriesApi<TSeriesType, Time, TData>) {\r\n    if(logical !== undefined) {\r\n      return series.dataByIndex(logical) ?? undefined\r\n    }\r\n  }\r\n\r\n  dataByTime(time: Time) {\r\n    return binarySearch(this._executionContext.data, Math.floor(timeToDate(time).getTime() / 1000), item => item.time);\r\n  }\r\n\r\n  lastPoint() {\r\n    const data = this._executionContext.data;\r\n    if(data.length === 0) return;\r\n    return data[data.length - 1]\r\n  }\r\n\r\n  getData() {\r\n    return this._executionContext.data\r\n  }\r\n\r\n  calcIndicatorData() {}\r\n  recalc?(): void\r\n  applyIndicatorData() {}\r\n  setPaneIndex(paneIndex: number): void\r\n  setPaneIndex() {}\r\n\r\n  getPaneIndex() {\r\n    return this.mainSeries?.getPane().paneIndex() ?? 0\r\n  }\r\n  _applyOptions?(options: Partial<IOptions>): void\r\n  _mainSeriesChanged?(series: ISeriesApi<SeriesType>): void\r\n}"], "names": ["upColor", "downColor", "ChartIndicator", "chart", "options", "__publicField", "Delegate", "merge", "cloneDeep", "ExecutionContext", "c", "_a", "series", "param", "data", "item", "timeToDate", "lastData", "logical", "time", "binarySearch"], "mappings": ";;;;;;;AAQO,MAAMA,IAAU,WACVC,IAAY;AAclB,MAAeC,EAIpB;AAAA,EAkCA,YAAsBC,GAAkBC,GAA6B;AAjC3D,IAAAC,EAAA,cAA4B;AACtC,IAAAA,EAAA;AAEA,IAAAA,EAAA,oBAA4C;AAE5C,IAAAA,EAAA,sBAAe,IAAIC,EAAoD;AAEvE,IAAAD,EAAA,uBAAuC,CAAC;AAExC,IAAAA,EAAA;AAwBsB,SAAA,QAAAF,GACf,KAAA,UAAUI,EAAMC,EAAU,KAAK,kBAAmB,CAAA,GAAGJ,KAAW,EAAE,GACvE,KAAK,kBAAkB,KAAK,gBAAgB,KAAK,IAAI,GAChD,KAAA,MAAM,uBAAuB,KAAK,eAAe,GAEjD,KAAA,oBAAoB,IAAIK,EAAiC,CAACC;;AAAM,cAAAC,IAAA,KAAK,YAAL,gBAAAA,EAAA,WAAeD;AAAA,KAAE;AAAA,EAAA;AAAA,EA1BxF,UAAsC;AAAA,EAC7B;AAAA,EAGT,kBAAkBE,GAAgC;;AAChD,SAAK,aAAaA,IAElBD,IAAA,KAAK,uBAAL,QAAAA,EAAA,WAA0BC;AAAA,EAAM;AAAA,EAKlC,gBAAgBC,GAA+B;AAC7C,QAAGA,EAAM,SAAS,eAAkB,KAAK,aAAa,KAAK,MAAS;AACpE,SAAK,aAAa,KAAK,KAAK,WAAWA,EAAM,IAAI,CAAC;AAAA,EAAA;AAAA,EAGpD,cAAc;AACZ,WAAO,KAAK;AAAA,EAAA;AAAA,EAad,QAAQC,GAAoB;AAC1B,SAAK,OAAOA,GACZ,KAAK,kBAAkB,OAAOA,EAAK,IAAI,CAASC,OAAA;AAAA,MAC9C,MAAMA,EAAK;AAAA,MACX,MAAMA,EAAK;AAAA,MACX,KAAKA,EAAK;AAAA,MACV,MAAM,KAAK,MAAMC,EAAWD,EAAK,IAAI,EAAE,QAAQ,IAAI,GAAI;AAAA,MACvD,OAAO;AAAA,MACP,QAAQA,EAAK;AAAA,MACb,OAAOA,EAAK;AAAA,MACZ,CAAC,GACH,KAAK,kBAAkB,GACvB,KAAK,mBAAmB;AAAA,EAAA;AAAA,EAG1B,SAAS;;AACJ,QAAA,CAAC,KAAK,KAAM;AACf,UAAME,IAAW,KAAK,KAAK,KAAK,KAAK,SAAS,CAAC;AAC/C,SAAK,kBAAkB,OAAO;AAAA,MAC5B,MAAMA,EAAS;AAAA,MACf,MAAMA,EAAS;AAAA,MACf,KAAKA,EAAS;AAAA,MACd,MAAM,KAAK,MAAMD,EAAWC,EAAS,IAAI,EAAE,QAAQ,IAAI,GAAI;AAAA,MAC3D,QAAQA,EAAS;AAAA,MACjB,OAAOA,EAAS;AAAA,IAAA,CACjB,IACDN,IAAA,KAAK,WAAL,QAAAA,EAAA,YACA,KAAK,mBAAmB;AAAA,EAAA;AAAA,EAG1B,aAAaP,GAA4B;;AACvC,SAAK,UAAUG,EAAM,KAAK,SAASH,CAAO,IAC1CO,IAAA,KAAK,kBAAL,QAAAA,EAAA,WAAqBP;AAAA,EAAO;AAAA,EAG9B,SAAS;AACP,IAAG,KAAK,mBACD,KAAA,MAAM,yBAAyB,KAAK,eAAe;AAAA,EAC1D;AAAA,EAGF,mBAA2G,EAAE,SAAAc,EAAQ,GAAuBN,GAA8C;AACxL,QAAGM,MAAY;AACN,aAAAN,EAAO,YAAYM,CAAO,KAAK;AAAA,EACxC;AAAA,EAGF,WAAWC,GAAY;AACrB,WAAOC,EAAa,KAAK,kBAAkB,MAAM,KAAK,MAAMJ,EAAWG,CAAI,EAAE,QAAY,IAAA,GAAI,GAAG,CAAAJ,MAAQA,EAAK,IAAI;AAAA,EAAA;AAAA,EAGnH,YAAY;AACJ,UAAAD,IAAO,KAAK,kBAAkB;AACjC,QAAAA,EAAK,WAAW;AACZ,aAAAA,EAAKA,EAAK,SAAS,CAAC;AAAA,EAAA;AAAA,EAG7B,UAAU;AACR,WAAO,KAAK,kBAAkB;AAAA,EAAA;AAAA,EAGhC,oBAAoB;AAAA,EAAA;AAAA,EAEpB,qBAAqB;AAAA,EAAA;AAAA,EAErB,eAAe;AAAA,EAAA;AAAA,EAEf,eAAe;;AACb,aAAOH,IAAA,KAAK,eAAL,gBAAAA,EAAiB,UAAU,gBAAe;AAAA,EAAA;AAIrD;"}