import { useLayoutEffect, useState } from "react";

const useResize = () => {
  const [width, setWidth] = useState();
  useLayoutEffect(() => {
    const handleResize = e => {
      setWidth(e.target.innerWidth);
    };
    setWidth(window.innerWidth);
    window.addEventListener("resize", handleResize);
    return () => {
      window.removeEventListener("resize", handleResize);
    };
  }, []);
  return { width };
};

/**
 * @returns {{
 * breakpoint: 'ssm'|'sm'|'md'|'mobile'|'tablet'|'xl',
 * isMobileScreen: boolean
 * }}
 */
export const useBreakpoint = () => {
  const [breakpoint, setBreakpoint] = useState("");
  const [isMobileScreen, setIsMobileScreen] = useState();
  const [isTabletScreen, setIsTabletScreen] = useState();
  const { width } = useResize();

  useLayoutEffect(() => {
    const computedStyles = getComputedStyle(document.documentElement);
    const ssm = parseInt(computedStyles.getPropertyValue("--break-point-mobile__ssm"));
    const sm = parseInt(computedStyles.getPropertyValue("--break-point-mobile__sm"));
    const md = parseInt(computedStyles.getPropertyValue(" --break-point-mobile__md"));
    const mobile = parseInt(computedStyles.getPropertyValue("--break-point-mobile"));
    const tablet = parseInt(computedStyles.getPropertyValue("--break-point-tablet"));
    setBreakpoint(() => {
      if (width <= ssm) {
        return "ssm";
      }
      if (width <= sm) {
        return "sm";
      }
      if (width <= md) {
        return "md";
      }
      if (width <= mobile) {
        return "mobile";
      }
      if (width <= tablet) {
        return "tablet";
      }
      return "xl";
    });
    setIsMobileScreen(() => width <= mobile);
    setIsTabletScreen(() => width <= tablet);
  }, [width]);

  return { breakpoint, isMobileScreen, isTabletScreen };
};

export class UserAgent {
  static get nav() {
    return typeof navigator !== "undefined" ? navigator : { userAgent: "" };
  }
  static get userAgent() {
    return this.nav.userAgent;
  }
  static get ipad() {
    return (
      this.userAgent.indexOf("iPad") != -1 ||
      (this.nav.platform === "MacIntel" && this.nav.maxTouchPoints > 1)
    );
  }

  static get iphone() {
    return this.userAgent.indexOf("iPhone") != -1;
  }
  static get isAndroid() {
    return this.userAgent.toLowerCase().indexOf("android") > -1;
  }
  static get isSafari() {
    return this.userAgent.indexOf("Safari/") > -1;
  }
  static get isMobile() {
    return this.ipad || this.iphone || this.isAndroid;
  }
}

export default useResize;
