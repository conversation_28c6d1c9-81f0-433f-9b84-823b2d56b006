import { Layout, theme } from "antd";

const { Header, Content } = Layout;

const MainLayout = ({ children }) => {
  const {
    token: { colorBgContainer, borderRadiusLG },
  } = theme.useToken();
  return (
    <Layout className="min-h-[100vh]">
      <Header
        style={{
          position: "sticky",
          top: 0,
          zIndex: 1,
          width: "100%",
          display: "flex",
          alignItems: "center",
        }}
      >
        <div className="font-semibold text-white text-[25px]">CMS</div>
      </Header>
      <Content className="flex">
        <div
          className={`p-[5px] lg:p-[24px] flex-1`}
          style={{
            background: colorBgContainer,
            borderRadius: borderRadiusLG,
          }}
        >
          {children}
        </div>
      </Content>
    </Layout>
  );
};
export default MainLayout;
