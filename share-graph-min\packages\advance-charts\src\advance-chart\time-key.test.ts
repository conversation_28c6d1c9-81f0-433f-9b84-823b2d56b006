import { describe, expect, it } from 'vitest';
import { timeKey } from './time-key';
import { Period } from './i-advance-chart';
import { UTCTimestamp } from 'lightweight-charts';

describe('timeKey', () => {
  // Helper to create UTC timestamps (reuse pattern)
  const createTime = (year: number, month: number, day: number, hour: number, minute: number, second: number = 0) =>
    Math.floor(Date.UTC(year, month, day, hour, minute, second) / 1000) as UTCTimestamp;

  // Helper to create period objects
  const createPeriod = (period: Period, times: number = 1) => ({ period, times });

  // Test data for consistent input/output pairs
  const testTime = createTime(2024, 0, 15, 14, 32, 45); // Random mid-time

  describe('time rounding behavior', () => {
    it('should round down to minute intervals', () => {
      const result = timeKey(testTime, createPeriod(Period.minute, 5));
      const expected = createTime(2024, 0, 15, 14, 30, 0); // Rounds down to nearest 5min
      
      expect(result).toBe(expected);
    });

    it('should round down to hour intervals', () => {
      const result = timeKey(testTime, createPeriod(Period.hour, 4));
      const expected = createTime(2024, 0, 15, 12, 0, 0); // Rounds down to nearest 4hr
      
      expect(result).toBe(expected);
    });

    it('should round down to day intervals', () => {
      const result = timeKey(testTime, createPeriod(Period.day));
      const expected = createTime(2024, 0, 15, 0, 0, 0); // Start of day
      
      expect(result).toBe(expected);
    });

    it('should round down to week intervals', () => {
      // Test with known Monday (Jan 1, 2024)
      const mondayTime = createTime(2024, 0, 4, 14, 32, 45); // Thursday
      const result = timeKey(mondayTime, createPeriod(Period.week));
      const expected = createTime(2024, 0, 1, 0, 0, 0); // Previous Monday
      
      expect(result).toBe(expected);
    });

    it('should round down to month intervals', () => {
      const result = timeKey(testTime, createPeriod(Period.month));
      const expected = createTime(2024, 0, 1, 0, 0, 0); // Start of month
      
      expect(result).toBe(expected);
    });
  });

  describe('interval multipliers', () => {
    it('should handle different interval sizes', () => {
      // Test that times parameter works correctly
      const time = createTime(2024, 0, 1, 10, 32, 0);
      
      const result1min = timeKey(time, createPeriod(Period.minute, 1));
      const result5min = timeKey(time, createPeriod(Period.minute, 5));
      
      expect(result1min).toBe(createTime(2024, 0, 1, 10, 32, 0));
      expect(result5min).toBe(createTime(2024, 0, 1, 10, 30, 0));
    });
  });

  describe('error handling', () => {
    it('should reject invalid periods', () => {
      expect(() => timeKey(testTime, createPeriod('invalid' as Period)))
        .toThrow('Invalid period');
    });
  });
});
