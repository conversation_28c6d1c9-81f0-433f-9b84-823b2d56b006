import queryString from "query-string";

export const getArrayFieldValue = (value) => {
  return value && typeof value === "string"
    ? value
        .split(",")
        ?.map((i) => i.trim())
        ?.filter((i) => i !== "")
    : [];
};

export const stringifyParamsToURL = (data) => {
  // http://localhost:5173/?tooltip=tooltip&volume=show-hide,underlay&show-last-close-line=show-last-close-line&chartType=line&width=100&height=100&instrumentId=32864&defaultRange=1M,daily&dateRangesAndInterval=1D;5D|1m,5m,10m&locale=en&fontFamily=Arial&fontSize=20&fontColor=#131722&upColor=green&downColor=red&primaryColor=#2962ff&gridColor=#2962ff&axesFontsize=20&axesColor=#131722&dataFields=open,high,low,close,volume&template=ticker&valueTracking=legend
  const result = queryString.stringify(data);
  return "https://minimalsharegraph.z7.web.core.windows.net/?" + result;
};
