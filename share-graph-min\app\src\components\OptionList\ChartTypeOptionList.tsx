import { i18n } from '@euroland/libs';
import { CHART_TYPE_KEYS } from '../../constants/chartConstant';
import OptionList from './OptionList';
import { IOptionList } from '../../types/OptionList';
import { useAppStore } from '../../stores/useAppStore';
import { TChartType } from '../../types/store';

const ChartTypeOptionList = () => {
  const menuData: IOptionList[] = [
    {
      id: CHART_TYPE_KEYS.MOUNTAIN.key,
      label: i18n.translate(CHART_TYPE_KEYS.MOUNTAIN.label),
    },
    {
      id: CHART_TYPE_KEYS.LINE.key,
      label: i18n.translate(CHART_TYPE_KEYS.LINE.label),
    },
    {
      id: CHART_TYPE_KEYS.CANDLESTICK.key,
      label: i18n.translate(CHART_TYPE_KEYS.CANDLESTICK.label),
    },
    {
      id: CHART_TYPE_KEYS.BAR_OHLC.key,
      label: i18n.translate(CHART_TYPE_KEYS.BAR_OHLC.label),
    },
    {
      id: CHART_TYPE_KEYS.BASELINE.key,
      label: i18n.translate(CHART_TYPE_KEYS.BASELINE.label),
    },
    {
      id: CHART_TYPE_KEYS.BASE_MOUNTAIN.key,
      label: i18n.translate(CHART_TYPE_KEYS.BASE_MOUNTAIN.label),
    },
  ];

  const chartType = useAppStore((state) => state.chartType);
  const setChartType = useAppStore((state) => state.setChartType);

  const handleChangeValue = (optionId: string) => {
    setChartType(optionId as TChartType);

  };

  return (
    <OptionList
      title={i18n.translate('chartType')}
      options={menuData}
      onChange={handleChangeValue}
      value={[chartType]}
    />
  );
};

export default ChartTypeOptionList;
