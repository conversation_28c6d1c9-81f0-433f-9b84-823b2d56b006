import { ReactNode, useEffect, useState } from 'react';
import { createPortal } from 'react-dom';

interface DrawerPortalProps {
  children: ReactNode;
}

const DrawerPortal: React.FC<DrawerPortalProps> = ({ children }) => {
  const [portalContainer, setPortalContainer] = useState<HTMLElement | null>(null);

  useEffect(() => {
    // Check if portal container already exists
    let container = document.getElementById('drawer-portal-container');
    
    // If not, create it and add to body
    if (!container) {
      container = document.createElement('div');
      container.id = 'drawer-portal-container';
      container.style.position = 'fixed';
      container.style.top = '0';
      container.style.left = '0';
      container.style.width = '100%';
      container.style.height = '100%';
      container.style.pointerEvents = 'none';
      container.style.zIndex = '9999';
      document.body.appendChild(container);
    }
    
    setPortalContainer(container);
    
    // Cleanup when component unmounts
    return () => {
      if (container && !container.childElementCount) {
        document.body.removeChild(container);
      }
    };
  }, []);

  // Only render in the portal when the container is available
  if (!portalContainer) return null;
  
  return createPortal(children, portalContainer);
};

export default DrawerPortal; 