import { CHART_INDICATOR_KEYS } from '../../constants/chartConstant';
import OptionList from './OptionList';
import { IOptionList } from '../../types/OptionList';
import { useAppStore } from '../../stores/useAppStore';
import { TChartType } from '../../types/store';
import { i18n } from '@euroland/libs';

const OverlaysOptionList = () => {
  const menuData: IOptionList[] = [
    {
      id: CHART_INDICATOR_KEYS.SMA.key,
      label: CHART_INDICATOR_KEYS.SMA.label,
      // icon: <CheckboxIcon />,
      // activeIcon: <CheckboxIcon active />,
    },
    {
      id: CHART_INDICATOR_KEYS.EMA.key,
      label: CHART_INDICATOR_KEYS.EMA.label,
      // icon: <CheckboxIcon />,
      // activeIcon: <CheckboxIcon active />,
    },
    {
      id: CHART_INDICATOR_KEYS.WMA.key,
      label: CHART_INDICATOR_KEYS.WMA.label,
      // icon: <CheckboxIcon />,
      // activeIcon: <CheckboxIcon active />,
    },
    {
      id: CHART_INDICATOR_KEYS.BOLLINGER_BANDS.key,
      label: CHART_INDICATOR_KEYS.BOLLINGER_BANDS.label,
      // icon: <CheckboxIcon />,
      // activeIcon: <CheckboxIcon active />,
    },
  ];

  const overlays = useAppStore((state) => state.overlays);
  const setChartOverlays = useAppStore((state) => state.setChartOverlays);

  const handleChangeValue = (optionId: string) => {
    setChartOverlays(optionId as TChartType);

  };

  return (
    <OptionList
      title={i18n.translate('overlays')}
      options={menuData}
      onChange={handleChangeValue}
      value={overlays}
    />
  );
};

export default OverlaysOptionList;
