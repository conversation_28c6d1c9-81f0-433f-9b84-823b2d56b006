import {
  ISeriesApi,
  SeriesType,
  HistogramSeries,
  IChartApi,
  Nominal,
  Time,
} from 'lightweight-charts';
import { ChartIndicator, ChartIndicatorOptions } from './abstract-indicator';
import { Color } from '../helpers/color';
import {Context} from '../helpers/execution-indicator';
import {ensureDefined} from '../helpers/assertions';

export interface VolumeIndicatorOptions extends ChartIndicatorOptions {
  upColor: string;
  downColor: string;
}

export const defaultOptions: VolumeIndicatorOptions = {
  upColor: '#26a69a',
  downColor: '#ef5350',
  overlay: false,
};

export type VolumePoint = Nominal<number, 'VolumePoint'>
export type VolumePositive = Nominal<0 | 1, 'Positive'>

export type VolumeIndicatorData = [VolumePoint, VolumePositive]

export default class VolumeIndicator extends ChartIndicator<
  VolumeIndicatorOptions,
  VolumeIndicatorData
> {
  volumeSeries: ISeriesApi<SeriesType>;

  constructor(
    protected chart: IChartApi,
    options?: Partial<VolumeIndicatorOptions>,
    paneIndex?: number
  ) {
    super(chart, options);
    const numberFormatter = this.options.numberFormatter;

    this.volumeSeries = this.chart.addSeries(
      HistogramSeries,
      {
        priceLineVisible: false,
        priceFormat: numberFormatter
          ? {
              type: 'custom',
              formatter: (volume: number) => numberFormatter().volume(volume),
            }
          : { type: 'volume' },
        priceScaleId: 'volume',
      },
      this.options.overlay ? 0 : paneIndex
    );

    this.applyPriceScaleMargins()
  }

  applyPriceScaleMargins() {
    if (this.options.overlay) {
      this.volumeSeries.priceScale().applyOptions({
        scaleMargins: {
          top: 0.7,
          bottom: 0,
        },
      });
    } else {
      this.volumeSeries.priceScale().applyOptions({
        scaleMargins: {
          top: 0.1,
          bottom: 0,
        },
      });
    }
  }
  _applyOptions(options: Partial<VolumeIndicatorOptions>): void {
    if (options.downColor || options.upColor) {
      this.applyIndicatorData();
    }
  }

  applyIndicatorData(): void {
    const volumeData = this._executionContext.data.filter(item => item.value)
    this.volumeSeries.setData(
      volumeData.map((item) => ({
        time: item.time as Time,
        value: ensureDefined(item.value)[0],
        color: Color.applyAlpha(
          ensureDefined(item.value)[1] === 1
            ? this.options.upColor
            : this.options.downColor,
          this.options.overlay ? 0.6 : 1
        ),
      }))
    );
  }

  formula(c: Context): VolumeIndicatorData | undefined {
    const closeSeries = c.new_var(c.symbol.close, 2);
    if(!closeSeries.calculable()) return;
    const positive = closeSeries.get(0) > closeSeries.get(1) ? 1 : 0
    return [c.symbol.volume as VolumePoint, positive as VolumePositive]
  }

  getDefaultOptions(): VolumeIndicatorOptions {
    return defaultOptions;
  }

  remove(): void {
    super.remove();
    this.chart.removeSeries(this.volumeSeries);
  }

  setPaneIndex(paneIndex: number): void {
    this.volumeSeries.moveToPane(paneIndex);
    this.applyPriceScaleMargins()
  }

  getPaneIndex(): number {
    return this.volumeSeries.getPane().paneIndex();
  }
}
