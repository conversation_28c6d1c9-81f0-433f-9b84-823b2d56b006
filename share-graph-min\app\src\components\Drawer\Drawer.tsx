import { ReactNode, useEffect } from "react";
import DrawerPortal from "./DrawerPortal";

interface DrawerProps {
  isOpen: boolean;
  onClose: () => void;
  title: string;
  children: ReactNode;
}

const Drawer: React.FC<DrawerProps> = ({ isOpen, onClose, title, children }) => {
  // When drawer is open, prevent body scrolling
  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = '';
    }
    
    return () => {
      document.body.style.overflow = '';
    };
  }, [isOpen]);

  // Only render the drawer when it's open to save resources
  if (!isOpen) return null;

  return (
    <DrawerPortal>
      <div 
        className={`mobile-drawer mobile-drawer--open`}
        style={{ pointerEvents: 'auto' }} // Enable pointer events in the portal
      >
        <div className="mobile-drawer__overlay" onClick={onClose}></div>
        <div className="mobile-drawer__content">
          <div className="mobile-drawer__header">
            <h3>{title}</h3>
            <button className="mobile-drawer__close" onClick={onClose}>×</button>
          </div>
          <div className="mobile-drawer__body">
            {children}
          </div>
        </div>
      </div>
    </DrawerPortal>
  );
};

export default Drawer;