@use "../common/mixins" as *;

.light-weight-chart {
    position: relative;
    overflow: hidden;
}

.chart-legends {
    display: flex;
    flex-wrap: wrap;
    flex-direction: column;
    text-align: left;
    gap: 4px;
    position: absolute;
    top: 5px;
    left: 5px;
    z-index: 3;
    width: 100%;
    pointer-events: none;

    > * {
        pointer-events: auto;
    }

    &__indicators {
        width: max-content;
        display: inline-block;
    }
}

.chart-legend-item {
    padding: 3px 5px;
    gap: 5px;
    cursor: pointer;
}

.main-legend {
    padding: 5px 5px 3px;
}

.top-setting-chart {
    display: flex;
    align-items: center;
    justify-content: space-between;

    @include respond-to(sm) {
        flex-direction: column-reverse;
        align-items: flex-start;
        width: 100%;
        gap: 10px;
    }
}

.go-home-btn {
    position: absolute;
    bottom: 10px;
    right: 10px;
    z-index: 2;
    cursor: pointer;
    background-color: #e0e3eb80;
    padding: 4px 4px;
    outline: unset;
    border: unset;
    border-radius: 6px;
    display: flex;

    &:hover {
        color: var(--primary-color)
    }
}

.loading-rotating {
    animation: spin 0.7s linear infinite;
    transform-origin: center;
}

@keyframes spin {
    from {
        transform: rotate(360deg);
    }
    to {
        transform: rotate(0deg);
    }
}