/**
 * @type{import("antd").ThemeConfig}
 */
export const theme = {
  token: {
    // Seed Token
    colorPrimary: "#131722",
    colorPrimaryBg: "#ffffff",
    fontSize: 15,
  },
  cssVar: true,
  components: {
    //   Layout: {
    //     headerBg: '#082B45'
    //   },
    Input: {
      borderRadius: 6,
      // paddingBlock: 8,
      activeBorderColor: "#131722",
      hoverBorderColor: "#131722",
    },
    InputNumber: {
      borderRadius: 6,
      activeBorderColor: "#131722",
      hoverBorderColor: "#131722",
    },
    Select: {
      borderRadius: 6,
      activeBorderColor: "#131722",
      hoverBorderColor: "#131722",
    },
    Button: {
      borderRadius: 6,
    },
    //   Table: {
    //     headerBg: "#ffffff",
    //     borderRadius: 12,
    //     borderColor: "#F2F2F2",
    //     lineWidth: 1,
    //     lineType: "solid",
    //     cellFontSizeSM: 12,
    //     rowSelectedHoverBg: '#082B4533'
    //   },
    //   Modal: {
    //     titleFontSize: "1.25rem",
    //     titleLineHeight: "1.5rem",
    //   },
    //   Pagination: {
    //     borderRadius: 16,
    //   },
    //   ColorPicker: {
    //     borderRadius: 8,
    //     colorPrimaryHover: "#131722",
    //     colorPrimaryBorder: "#131722",
    //     colorPrimary: "#131722",
    //   },
    //   DatePicker: {
    //     activeBorderColor: "#131722",
    //     hoverBorderColor: "#131722",
    //     controlHeight: 32,
    //   },
  },
};
