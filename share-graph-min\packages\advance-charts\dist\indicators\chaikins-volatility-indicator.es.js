var V = Object.defineProperty;
var k = (a, e, i) => e in a ? V(a, e, { enumerable: !0, configurable: !0, writable: !0, value: i }) : a[e] = i;
var d = (a, e, i) => k(a, typeof e != "symbol" ? e + "" : e, i);
import { LineSeries as v } from "lightweight-charts";
import { ChartIndicator as S } from "./abstract-indicator.es.js";
import { RegionPrimitive as g } from "../custom-primitive/primitive/region.es.js";
import { autoScaleInfoProviderCreator as P } from "../helpers/utils.es.js";
const b = {
  color: "rgba(255, 152, 0, 1)",
  // Orange for Chaikin's Volatility
  priceLineColor: "rgba(150, 150, 150, 0.35)",
  backgroundColor: "#ff98001a",
  period: 10,
  // Default EMA period for High-Low spread
  rocPeriod: 12,
  // Default Rate of Change lookback period
  overlay: !1
};
class A extends S {
  constructor(i, o, t) {
    super(i, o);
    d(this, "chaikinsVolatilitySeries");
    this.chaikinsVolatilitySeries = i.addSeries(v, {
      color: this.options.color,
      lineWidth: 2,
      priceLineVisible: !1,
      crosshairMarkerVisible: !1,
      priceScaleId: "chaikinsvolatility",
      autoscaleInfoProvider: P({ maxValue: 80, minValue: -80 })
    }, t), this.chaikinsVolatilitySeries.attachPrimitive(
      new g({
        upPrice: 5,
        lowPrice: -5,
        lineColor: this.options.priceLineColor,
        backgroundColor: this.options.backgroundColor
      })
    );
  }
  getDefaultOptions() {
    return b;
  }
  formula(i) {
    const o = this.options.period, t = this.options.rocPeriod, l = i.symbol.high, m = i.symbol.low, c = l - m, h = 2 / (o + 1), p = i.new_var(c, o), n = i.new_var(NaN, t + 1);
    if (!p.calculable()) return;
    const u = n.get(1);
    let r;
    isNaN(u) ? r = p.getAll().reduce((f, y) => f + y, 0) / o : r = h * c + (1 - h) * u, n.set(r);
    const s = n.get(t);
    return isNaN(s) || s === 0 ? [0] : [(r - s) / s * 100];
  }
  applyIndicatorData() {
    const i = [];
    for (const o of this._executionContext.data) {
      const t = o.value;
      if (!t) continue;
      const l = o.time;
      i.push({ time: l, value: t[0] });
    }
    this.chaikinsVolatilitySeries.setData(i);
  }
  remove() {
    super.remove(), this.chart.removeSeries(this.chaikinsVolatilitySeries);
  }
  _applyOptions() {
    this.chaikinsVolatilitySeries.applyOptions({ color: this.options.color }), this.applyIndicatorData();
  }
  setPaneIndex(i) {
    this.chaikinsVolatilitySeries.moveToPane(i);
  }
  getPaneIndex() {
    return this.chaikinsVolatilitySeries.getPane().paneIndex();
  }
}
export {
  A as default,
  b as defaultOptions
};
//# sourceMappingURL=chaikins-volatility-indicator.es.js.map
