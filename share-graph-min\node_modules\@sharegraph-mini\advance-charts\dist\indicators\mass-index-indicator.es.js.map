{"version": 3, "file": "mass-index-indicator.es.js", "sources": ["../../src/indicators/mass-index-indicator.ts"], "sourcesContent": ["import {IChartApi, ISeriesApi, LineSeries, Nominal, SeriesType, SingleValueData, Time} from \"lightweight-charts\";\nimport {ChartIndicator, ChartIndicatorOptions} from \"./abstract-indicator\";\nimport {RegionPrimitive} from \"../custom-primitive/primitive/region\";\nimport {autoScaleInfoProviderCreator} from \"../helpers/utils\";\nimport {Context} from \"../helpers/execution-indicator\";\n\nexport interface MassIndexIndicatorOptions extends ChartIndicatorOptions {\n  color: string,\n  emaPeriod: number,\n  sumPeriod: number,\n  priceLineColor: string,\n  backgroundColor: string\n}\n\nexport const defaultOptions: MassIndexIndicatorOptions = {\n  color: \"#3179f5\",     // for Mass Index\n  priceLineColor: \"#a1c3ff\",\n  backgroundColor: '#d2e0fa',\n  emaPeriod: 9,      // 9-period EMA as per TradingView standard\n  sumPeriod: 25,     // 25-period sum as per TradingView standard\n  overlay: false\n}\n\nexport type MassIndexLine = Nominal<number, 'MassIndex'>\n\nexport type MassIndexData = [MassIndexLine]\n\nexport default class MassIndexIndicator extends ChartIndicator<MassIndexIndicatorOptions, MassIndexData> {\n  massIndexSeries: ISeriesApi<SeriesType>\n\n  constructor(chart: IChartApi, options?: Partial<MassIndexIndicatorOptions>, paneIndex?: number) {\n    super(chart, options)\n    \n    this.massIndexSeries = chart.addSeries(LineSeries, {\n      color: this.options.color,\n      lineWidth: 2,\n      priceLineVisible: false,\n      crosshairMarkerVisible: false,\n      priceScaleId: 'massindex',\n      autoscaleInfoProvider: autoScaleInfoProviderCreator({maxValue: 30, minValue: 20})\n    }, paneIndex);\n    \n    // Add region primitive for visual reference at 27 threshold\n    this.massIndexSeries.attachPrimitive(\n      new RegionPrimitive({\n        upPrice: 27.5,\n        lowPrice: 26.5,\n        lineColor: this.options.priceLineColor,\n        backgroundColor: this.options.backgroundColor\n      })\n    );\n  }\n\n  getDefaultOptions(): MassIndexIndicatorOptions {\n    return defaultOptions\n  }\n\n  formula(c: Context): MassIndexData | undefined {\n    const emaPeriod = this.options.emaPeriod;\n    const sumPeriod = this.options.sumPeriod;\n    const high = c.symbol.high;\n    const low = c.symbol.low;\n\n    // Calculate High-Low range\n    const range = high - low;\n\n    // Use custom EMA formula with alpha = 2/(n+1) as established in codebase\n    const emaAlpha = 2 / (emaPeriod + 1);\n\n    // Step 1: Calculate first EMA of (High-Low) range\n    // Follow the same pattern as EMA indicator\n    const rangeSeries = c.new_var(range, emaPeriod);\n    const firstEMAVar = c.new_var(NaN, 2);\n\n    if (!rangeSeries.calculable()) return;\n\n    const prevFirstEMA = firstEMAVar.get(1);\n    let currentFirstEMA: number;\n\n    if (isNaN(prevFirstEMA)) {\n      // Initialize first EMA with simple average of range values\n      const ranges = rangeSeries.getAll();\n      currentFirstEMA = ranges.reduce((sum, val) => sum + val, 0) / emaPeriod;\n    } else {\n      // EMA formula: EMA = alpha * x + (1 - alpha) * EMA[1]\n      currentFirstEMA = emaAlpha * range + (1 - emaAlpha) * prevFirstEMA;\n    }\n\n    firstEMAVar.set(currentFirstEMA);\n\n    // Step 2: Calculate second EMA of the first EMA\n    // Follow the same pattern as EMA indicator\n    const firstEMASeries = c.new_var(currentFirstEMA, emaPeriod);\n    const secondEMAVar = c.new_var(NaN, 2);\n\n    if (!firstEMASeries.calculable()) return;\n\n    const prevSecondEMA = secondEMAVar.get(1);\n    let currentSecondEMA: number;\n\n    if (isNaN(prevSecondEMA)) {\n      // Initialize second EMA with simple average of first EMA values\n      const firstEMAValues = firstEMASeries.getAll();\n      currentSecondEMA = firstEMAValues.reduce((sum, val) => sum + val, 0) / emaPeriod;\n    } else {\n      // EMA formula: EMA = alpha * x + (1 - alpha) * EMA[1]\n      currentSecondEMA = emaAlpha * currentFirstEMA + (1 - emaAlpha) * prevSecondEMA;\n    }\n\n    secondEMAVar.set(currentSecondEMA);\n\n    // Step 3: Calculate the ratio (First EMA / Second EMA)\n    // Avoid division by zero and ensure reasonable ratio values\n    const ratio = currentSecondEMA !== 0 ? currentFirstEMA / currentSecondEMA : 1;\n\n    // Step 4: Calculate Mass Index as sum of ratios over sumPeriod\n    const ratioSeries = c.new_var(ratio, sumPeriod);\n\n    if (!ratioSeries.calculable()) return;\n\n    const ratioValues = ratioSeries.getAll();\n\n    // Sum all ratios to get Mass Index\n    const massIndex = ratioValues.reduce((sum, val) => sum + val, 0);\n\n    return [massIndex as MassIndexLine];\n  }\n\n  applyIndicatorData() {\n    const massIndexData: SingleValueData[] = [];\n    \n    for(const bar of this._executionContext.data) {\n      const value = bar.value;\n      if(!value) continue;\n      \n      const time = bar.time as Time;\n      massIndexData.push({time, value: value[0]});\n    }\n\n    this.massIndexSeries.setData(massIndexData);\n  }\n\n  remove() {\n    super.remove()\n    this.chart.removeSeries(this.massIndexSeries);\n  }\n\n  _applyOptions() {\n    this.massIndexSeries.applyOptions({color: this.options.color});\n    this.applyIndicatorData();\n  }\n\n  setPaneIndex(paneIndex: number) {\n    this.massIndexSeries.moveToPane(paneIndex);\n  }\n\n  getPaneIndex(): number {\n    return this.massIndexSeries.getPane().paneIndex();\n  }\n}\n"], "names": ["defaultOptions", "MassIndexIndicator", "ChartIndicator", "chart", "options", "paneIndex", "__publicField", "LineSeries", "autoScaleInfoProviderCreator", "RegionPrimitive", "c", "emaPeriod", "sumPeriod", "high", "low", "range", "emaAlpha", "rangeSeries", "firstEMAVar", "prevFirstEMA", "currentFirstEMA", "sum", "val", "firstEMASeries", "secondEMAVar", "prevSecondEMA", "currentSecondEMA", "ratio", "ratioSeries", "massIndexData", "bar", "value", "time"], "mappings": ";;;;;;;AAcO,MAAMA,IAA4C;AAAA,EACvD,OAAO;AAAA;AAAA,EACP,gBAAgB;AAAA,EAChB,iBAAiB;AAAA,EACjB,WAAW;AAAA;AAAA,EACX,WAAW;AAAA;AAAA,EACX,SAAS;AACX;AAMA,MAAqBC,UAA2BC,EAAyD;AAAA,EAGvG,YAAYC,GAAkBC,GAA8CC,GAAoB;AAC9F,UAAMF,GAAOC,CAAO;AAHtB,IAAAE,EAAA;AAKO,SAAA,kBAAkBH,EAAM,UAAUI,GAAY;AAAA,MACjD,OAAO,KAAK,QAAQ;AAAA,MACpB,WAAW;AAAA,MACX,kBAAkB;AAAA,MAClB,wBAAwB;AAAA,MACxB,cAAc;AAAA,MACd,uBAAuBC,EAA6B,EAAC,UAAU,IAAI,UAAU,GAAG,CAAA;AAAA,OAC/EH,CAAS,GAGZ,KAAK,gBAAgB;AAAA,MACnB,IAAII,EAAgB;AAAA,QAClB,SAAS;AAAA,QACT,UAAU;AAAA,QACV,WAAW,KAAK,QAAQ;AAAA,QACxB,iBAAiB,KAAK,QAAQ;AAAA,MAC/B,CAAA;AAAA,IACH;AAAA,EAAA;AAAA,EAGF,oBAA+C;AACtC,WAAAT;AAAA,EAAA;AAAA,EAGT,QAAQU,GAAuC;AACvC,UAAAC,IAAY,KAAK,QAAQ,WACzBC,IAAY,KAAK,QAAQ,WACzBC,IAAOH,EAAE,OAAO,MAChBI,IAAMJ,EAAE,OAAO,KAGfK,IAAQF,IAAOC,GAGfE,IAAW,KAAKL,IAAY,IAI5BM,IAAcP,EAAE,QAAQK,GAAOJ,CAAS,GACxCO,IAAcR,EAAE,QAAQ,KAAK,CAAC;AAEhC,QAAA,CAACO,EAAY,aAAc;AAEzB,UAAAE,IAAeD,EAAY,IAAI,CAAC;AAClC,QAAAE;AAEA,IAAA,MAAMD,CAAY,IAGFC,IADHH,EAAY,OAAO,EACT,OAAO,CAACI,GAAKC,MAAQD,IAAMC,GAAK,CAAC,IAAIX,IAG5CS,IAAAJ,IAAWD,KAAS,IAAIC,KAAYG,GAGxDD,EAAY,IAAIE,CAAe;AAI/B,UAAMG,IAAiBb,EAAE,QAAQU,GAAiBT,CAAS,GACrDa,IAAed,EAAE,QAAQ,KAAK,CAAC;AAEjC,QAAA,CAACa,EAAe,aAAc;AAE5B,UAAAE,IAAgBD,EAAa,IAAI,CAAC;AACpC,QAAAE;AAEA,IAAA,MAAMD,CAAa,IAGFC,IADIH,EAAe,OAAO,EACX,OAAO,CAACF,GAAKC,MAAQD,IAAMC,GAAK,CAAC,IAAIX,IAGpDe,IAAAV,IAAWI,KAAmB,IAAIJ,KAAYS,GAGnED,EAAa,IAAIE,CAAgB;AAIjC,UAAMC,IAAQD,MAAqB,IAAIN,IAAkBM,IAAmB,GAGtEE,IAAclB,EAAE,QAAQiB,GAAOf,CAAS;AAE1C,WAACgB,EAAY,eAOV,CALaA,EAAY,OAAO,EAGT,OAAO,CAACP,GAAKC,MAAQD,IAAMC,GAAK,CAAC,CAE7B,IAPH;AAAA,EAOG;AAAA,EAGpC,qBAAqB;AACnB,UAAMO,IAAmC,CAAC;AAEhC,eAAAC,KAAO,KAAK,kBAAkB,MAAM;AAC5C,YAAMC,IAAQD,EAAI;AAClB,UAAG,CAACC,EAAO;AAEX,YAAMC,IAAOF,EAAI;AACjB,MAAAD,EAAc,KAAK,EAAC,MAAAG,GAAM,OAAOD,EAAM,CAAC,GAAE;AAAA,IAAA;AAGvC,SAAA,gBAAgB,QAAQF,CAAa;AAAA,EAAA;AAAA,EAG5C,SAAS;AACP,UAAM,OAAO,GACR,KAAA,MAAM,aAAa,KAAK,eAAe;AAAA,EAAA;AAAA,EAG9C,gBAAgB;AACd,SAAK,gBAAgB,aAAa,EAAC,OAAO,KAAK,QAAQ,OAAM,GAC7D,KAAK,mBAAmB;AAAA,EAAA;AAAA,EAG1B,aAAaxB,GAAmB;AACzB,SAAA,gBAAgB,WAAWA,CAAS;AAAA,EAAA;AAAA,EAG3C,eAAuB;AACrB,WAAO,KAAK,gBAAgB,QAAQ,EAAE,UAAU;AAAA,EAAA;AAEpD;"}