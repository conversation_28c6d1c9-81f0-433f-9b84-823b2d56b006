export type Callback<T1 = void, T2 = void, T3 = void> = (param1: T1, param2: T2, param3: T3) => void;

export interface ISubscription<T1 = void, T2 = void, T3 = void> {
	subscribe(callback: Callback<T1, T2, T3>, linkedObject?: unknown, singleshot?: boolean): void;
	unsubscribe(callback: Callback<T1, T2, T3>): void;
	unsubscribeAll(linkedObject: unknown): void;
  lastParams(): [T1, T2, T3];
}

interface Listener<T1, T2, T3> {
	callback: Callback<T1, T2, T3>;
	linkedObject?: unknown;
	singleshot: boolean;
}

export class Delegate<T1 = void, T2 = void, T3 = void> implements ISubscription<T1, T2, T3> {
  private _listeners: Listener<T1, T2, T3>[] = [];

  private _params: [T1, T2, T3] = [undefined as unknown as T1, undefined as unknown as T2, undefined as unknown as T3];

  public fire(param1: T1, param2: T2, param3: T3) {
    this._params = [param1, param2, param3];
    const listenersSnapshot = [...this._listeners]
    this._listeners = this._listeners.filter(listener => !listener.singleshot);

    listenersSnapshot.forEach(listener => listener.callback(param1, param2, param3))
  }

  public lastParams(): [T1, T2, T3] {
    return this._params;
  }

  public subscribe(callback: Callback<T1, T2, T3>, linkedObject?: unknown, singleshot?: boolean): void {
    this._listeners.push({callback, linkedObject, singleshot: Boolean(singleshot)})
  }

  public unsubscribe(callback: Callback<T1, T2, T3>): void {
    const index = this._listeners.findIndex(listener => listener.callback === callback);

    if(index > -1) {
      this._listeners.splice(index, 1)
    }
  }

  public unsubscribeAll(linkedObject: unknown): void {
    this._listeners = this._listeners.filter(listener => listener.linkedObject !== linkedObject)
  }


  hasListener() {
    return Boolean(this._listeners.length)
  }
  destroy() {
    this._listeners = []
  }
}

// eslint-disable-next-line @typescript-eslint/no-explicit-any
export type IPublicDelegate<T extends Delegate<any, any, any>> = T extends Delegate<infer T1, infer T2, infer T3>
  ? ISubscription<T1, T2, T3>
  : never;

