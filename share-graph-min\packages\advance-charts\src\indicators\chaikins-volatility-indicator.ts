import { IChartApi, ISeriesApi, LineSeries, Nominal, SeriesType, SingleValueData, Time } from "lightweight-charts";
import { ChartIndicator, ChartIndicatorOptions } from "./abstract-indicator";
import { RegionPrimitive } from "../custom-primitive/primitive/region";
import { autoScaleInfoProviderCreator } from "../helpers/utils";
import { Context } from "../helpers/execution-indicator";

export interface ChaikinsVolatilityIndicatorOptions extends ChartIndicatorOptions {
  color: string;
  period: number;        // EMA period for High-Low spread (typically 10)
  rocPeriod: number;     // Rate of Change lookback period (typically 10)
  priceLineColor: string;
  backgroundColor: string;
}

export const defaultOptions: ChaikinsVolatilityIndicatorOptions = {
  color: "rgba(255, 152, 0, 1)",     // Orange for Chaikin's Volatility
  priceLineColor: "rgba(150, 150, 150, 0.35)",
  backgroundColor: '#ff98001a',
  period: 10,        // Default EMA period for High-Low spread
  rocPeriod: 12,     // Default Rate of Change lookback period
  overlay: false
}

export type ChaikinsVolatilityLine = Nominal<number, 'ChaikinsVolatility'>

export type ChaikinsVolatilityData = readonly [ChaikinsVolatilityLine]

export default class ChaikinsVolatilityIndicator extends ChartIndicator<ChaikinsVolatilityIndicatorOptions, ChaikinsVolatilityData> {
  chaikinsVolatilitySeries: ISeriesApi<SeriesType>

  constructor(chart: IChartApi, options?: Partial<ChaikinsVolatilityIndicatorOptions>, paneIndex?: number) {
    super(chart, options)

    this.chaikinsVolatilitySeries = chart.addSeries(LineSeries, {
      color: this.options.color,
      lineWidth: 2,
      priceLineVisible: false,
      crosshairMarkerVisible: false,
      priceScaleId: 'chaikinsvolatility',
      autoscaleInfoProvider: autoScaleInfoProviderCreator({ maxValue: 80, minValue: -80 })
    }, paneIndex);

    this.chaikinsVolatilitySeries.attachPrimitive(
      new RegionPrimitive({
        upPrice: 5,
        lowPrice: -5,
        lineColor: this.options.priceLineColor,
        backgroundColor: this.options.backgroundColor
      })
    );
  }

  getDefaultOptions(): ChaikinsVolatilityIndicatorOptions {
    return defaultOptions
  }

  formula(c: Context): ChaikinsVolatilityData | undefined {
    const emaPeriod = this.options.period;      // EMA period for High-Low spread (typically 10)
    const rocPeriod = this.options.rocPeriod;   // Rate of Change lookback period (typically 10)

    const high = c.symbol.high;
    const low = c.symbol.low;

    // Step 1: Calculate High-Low spread (not close price!)
    const highLowSpread = high - low;

    const alpha = 2 / (emaPeriod + 1);

    const hlSeries = c.new_var(highLowSpread, emaPeriod);
    const emaSeries = c.new_var(NaN, rocPeriod + 1);

    if (!hlSeries.calculable()) return;

    const previousEMA = emaSeries.get(1);

    let currentEMA;
    if (isNaN(previousEMA)) {
      const hlValues = hlSeries.getAll();
      currentEMA = hlValues.reduce((sum, val) => sum + val, 0) / emaPeriod;
    } else {
      currentEMA = alpha * highLowSpread + (1 - alpha) * previousEMA;
    }

    emaSeries.set(currentEMA);

    const pastEMA = emaSeries.get(rocPeriod);

    if (isNaN(pastEMA) || pastEMA === 0) {
      return [0 as ChaikinsVolatilityLine];
    }

    // Chaikin Volatility Value = (Current EMA value - EMA value rocPeriod periods ago) / EMA value rocPeriod periods ago * 100
    const chaikinVolatility = ((currentEMA - pastEMA) / pastEMA) * 100;

    return [chaikinVolatility as ChaikinsVolatilityLine];
  }


  applyIndicatorData() {
    const chaikinsVolatilityData: SingleValueData[] = [];

    for (const bar of this._executionContext.data) {
      const value = bar.value;
      if (!value) continue;

      const time = bar.time as Time;
      chaikinsVolatilityData.push({ time, value: value[0] });
    }

    this.chaikinsVolatilitySeries.setData(chaikinsVolatilityData);
  }

  remove() {
    super.remove()
    this.chart.removeSeries(this.chaikinsVolatilitySeries);
  }

  _applyOptions() {
    this.chaikinsVolatilitySeries.applyOptions({ color: this.options.color });
    this.applyIndicatorData();
  }

  setPaneIndex(paneIndex: number) {
    this.chaikinsVolatilitySeries.moveToPane(paneIndex);
  }

  getPaneIndex(): number {
    return this.chaikinsVolatilitySeries.getPane().paneIndex();
  }
}