import React, { useCallback, useState } from "react";
import { Tag, Button, Input, Form, Tooltip } from "antd";
import { i18n } from "@euroland/libs";
import AddDateRangeIntervalModal from "./AddDateRangeIntervalModal";
import {
  RANGE_CHART_INFO,
  TIME_INTERVALS,
} from "../../../constants/chartConstant";
import UpdateDateRangeIntervalModal from "./UpdateDateRangeIntervalModal";
import DEFAULT_SETTING from "../../../configs/defaultSetting";
import { getIntervalLabel } from "../../../constants/common";
import { sortedPeriods } from "../../../utils/common";

const DateRangeIntervalTags = ({ value, onChange }) => {
  const [isOpenAddModal, setIsOpenAddModal] = useState(false);
  const [updateDateRangeItem, setUpdateDateRangeItem] = useState();
  const form = Form.useFormInstance();

  const [key, setKey] = useState("mmm");


  const onChangeFormKey = useCallback(
    () => setKey(Math.random().toString(32).slice(2, 7)),
    []
  );

  const handleClose = (range) => {
    const currentArray =
      structuredClone(form.getFieldValue("dateRangesAndInterval")) || [];

    const existingIndex = currentArray.findIndex(
      (item) => item.period === range.period
    );

    if (existingIndex === -1) return;
    currentArray.splice(existingIndex, 1);
    if (currentArray.length === 0) {
      onChange(DEFAULT_SETTING.dateRangesAndInterval);
      onChangeFormKey();
      return;
    }

    const updatedArray = [...currentArray];

    onChange(sortedPeriods(updatedArray));
  };

  const addRange = () => {
    setIsOpenAddModal(true);
  };

  const handleAddRange = (range) => {
    const currentArray =
      structuredClone(form.getFieldValue("dateRangesAndInterval")) || [];

    const existingIndex = currentArray.findIndex(
      (item) => item.period === range.period
    );

    if (existingIndex !== -1) {
      currentArray.splice(existingIndex, 1);
    }

    const updatedArray = [...currentArray, range];

    onChange(sortedPeriods(updatedArray));
    setIsOpenAddModal(false);
  };

  const handleUpdateRange = (range) => {
    const currentArray =
      structuredClone(form.getFieldValue("dateRangesAndInterval")) || [];

    const indexToUpdate = currentArray.findIndex(
      (item) => item.period === range.period
    );

    if (indexToUpdate === -1) return;
    currentArray[indexToUpdate] = range;

    const updatedArray = [...currentArray];

    onChange(sortedPeriods(updatedArray));
    setUpdateDateRangeItem();
  };

  return (
    <div className="flex flex-wrap gap-y-2" key={key}>
      {value.map((option, index) => {
        const periodValue = option.period;
        const intervalsValue = option.intervals;
        const intervalsString = intervalsValue
          .map((interval) => {
            const intervalOption = TIME_INTERVALS[interval];
            return getIntervalLabel(
              intervalOption.label,
              intervalOption.interval.times
            );
          })
          .join(",");
        const { label } = RANGE_CHART_INFO[periodValue];

        return (
          <Tooltip key={periodValue} title={intervalsString}>
            <Tag
              key={periodValue}
              closable
              onClose={() => handleClose(option)}
              className="px-2 py-2 bg-slate-100 border-none cursor-pointer hover:bg-slate-200 text-[15px]"
              onClick={() => setUpdateDateRangeItem(option)}
            >
              {i18n.translate(label)}
            </Tag>
          </Tooltip>
        );
      })}
      <Button
        type="dashed"
        color="primary"
        onClick={addRange}
        className="px-2 py-4 text-[15px]"
      >
        + Add range
      </Button>
      <AddDateRangeIntervalModal
        isModalOpen={isOpenAddModal}
        onOk={handleAddRange}
        onCancel={() => setIsOpenAddModal(false)}
      />
      <UpdateDateRangeIntervalModal
        isModalOpen={!!updateDateRangeItem}
        updateDateRangeItem={updateDateRangeItem}
        onOk={handleUpdateRange}
        onCancel={() => setUpdateDateRangeItem()}
      />
    </div>
  );
};

export default DateRangeIntervalTags;
