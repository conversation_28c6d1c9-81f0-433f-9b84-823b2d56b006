import {IChartApi, ISeriesApi, LineSeries, Nominal, SeriesType, SingleValueData, Time} from "lightweight-charts";
import {ChartIndicator, ChartIndicatorOptions} from "./abstract-indicator";
import {Context} from "../helpers/execution-indicator";

export interface MomentumIndicatorOptions extends ChartIndicatorOptions {
  period: number,
  usePercentage: boolean,
  color: string,
  zeroLineColor: string
};

export const defaultOptions: MomentumIndicatorOptions = {
  period: 14,
  usePercentage: false,
  color: '#2b97f1',
  zeroLineColor: '#808080',
  overlay: false
}

export type MomentumData = readonly [Nominal<number, 'Momentum'>]

export default class MomentumIndicator extends ChartIndicator<MomentumIndicatorOptions, MomentumData> {
  momentumSeries: ISeriesApi<SeriesType>

  constructor(chart: IChartApi, options?: Partial<MomentumIndicatorOptions>, paneIndex?: number) {
    super(chart, options);

    this.momentumSeries = chart.addSeries(LineSeries, {
      color: this.options.color,
      lineWidth: 2,
      priceLineVisible: false,
      crosshairMarkerVisible: false,
      priceScaleId: 'momentum'
    }, paneIndex)
  }

  applyIndicatorData(): void {
    const momentumData: SingleValueData[] = []

    for(const bar of this._executionContext.data) {
      const value = bar.value;
      const time = bar.time as Time;

      if(!value) continue;
      const [momentum] = value;
      if(!isNaN(momentum)) momentumData.push({time, value: momentum})
    }

    this.momentumSeries.setData(momentumData)
  }

  formula(c: Context) {
      const period = this.options.period;
      const currentClose = c.symbol.close;

      const closeSeries = c.new_var(currentClose, period + 1);
      if (!closeSeries.calculable()) return;

      const closeNPeriodsAgo = closeSeries.get(period);

      let momentum: number;

      if (this.options.usePercentage) {
          momentum = ((currentClose - closeNPeriodsAgo) / closeNPeriodsAgo) * 100;
      } else {
          momentum = currentClose - closeNPeriodsAgo;
      }

      return [momentum as Nominal<number, 'Momentum'>] as MomentumData;
  }

  _applyOptions(options: Partial<MomentumIndicatorOptions>): void {
    if(options.period || options.usePercentage) {
      this.calcIndicatorData()
    }

    if(options.color) this.momentumSeries.applyOptions({color: options.color})

    this.applyIndicatorData()
  }

  getDefaultOptions() {
    return defaultOptions
  }

  remove(): void {
    super.remove()
    this.chart.removeSeries(this.momentumSeries)
  }

  setPaneIndex(paneIndex: number): void {
    this.momentumSeries.moveToPane(paneIndex);
  }

  getPaneIndex(): number {
    return this.momentumSeries.getPane().paneIndex()
  }
}
