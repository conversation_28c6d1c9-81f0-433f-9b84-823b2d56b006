import { client } from "../graphql/client";
import { TICKER_INIT_QUERY, TICKER_UPDATE_QUERY } from "../graphql/queries/tickerQuery";

export interface ITickerData {
    shareName: string;
    symbol: string;
    id: number;
    high52W: number;
    low52W: number;
    currency: {
        code: string;
        name: string;
    };
    currentPrice: {
        last: number;
        high: number;
        low: number;
        open: number;
        change: number;
        changePercentage: number;
        prevClose: number;
        volume: number;
        date: string;
    };
    market: {
        closeTimeLocal: string;
        openTimeLocal: string;
        status: {
            isOpened: boolean;
            remainingTime: number;
        };
        timezone: {
            nameIANA: string;
        };
    };

}

export async function getInitTicker(instrumentIds: number[]): Promise<ITickerData[] | undefined> {
    try {
        const result = await client.query(TICKER_INIT_QUERY, { ids: instrumentIds })

        return result.data?.instrumentByIds as ITickerData[] || []
    } catch (error) {
        console.error("getInitTicker ~ error:", error)
        return undefined
    }
}

export async function getUpdateTicker(instrumentIds: number[]): Promise<ITickerData[] | undefined> {
    try {
        const result = await client.query(TICKER_UPDATE_QUERY, { ids: instrumentIds, additionalRealtimeIds: [], adjClose: false })
        return result.data?.instrumentByIds as ITickerData[] || []
    } catch (error) {
        console.error("getUpdateTicker ~ error:", error)
        return undefined
    }
}