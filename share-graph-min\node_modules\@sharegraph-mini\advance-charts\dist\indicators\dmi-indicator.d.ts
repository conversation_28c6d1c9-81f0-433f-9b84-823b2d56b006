import { IChartApi, ISeriesApi, Nominal, SeriesType } from 'lightweight-charts';
import { ChartIndicator, ChartIndicatorOptions } from './abstract-indicator';
import { Context } from '../helpers/execution-indicator';

export interface DMIIndicatorOptions extends ChartIndicatorOptions {
    adxColor: string;
    plusDIColor: string;
    minusDIColor: string;
    period: number;
    priceLineColor: string;
    backgroundColor: string;
}
export declare const defaultOptions: DMIIndicatorOptions;
export type ADXLine = Nominal<number, 'ADX'>;
export type PlusDILine = Nominal<number, 'PlusDI'>;
export type MinusDILine = Nominal<number, 'MinusDI'>;
export type DMIData = [ADXLine, PlusDILine, MinusDILine];
export default class DMIIndicator extends ChartIndicator<DMIIndicatorOptions, DMIData> {
    adxSeries: ISeriesApi<SeriesType>;
    plusDISeries: ISeriesApi<SeriesType>;
    minusDISeries: ISeriesApi<SeriesType>;
    constructor(chart: IChart<PERSON>pi, options?: Partial<DMIIndicatorOptions>, paneIndex?: number);
    getDefaultOptions(): DMIIndicatorOptions;
    formula(c: Context): DMIData | undefined;
    applyIndicatorData(): void;
    remove(): void;
    _applyOptions(): void;
    setPaneIndex(paneIndex: number): void;
    getPaneIndex(): number;
}
