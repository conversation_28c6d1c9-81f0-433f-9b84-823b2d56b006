{"version": 3, "file": "williams-indicator.cjs.js", "sources": ["../../src/indicators/williams-indicator.ts"], "sourcesContent": ["import {IChartApi, ISeriesApi, LineSeries, Nominal, SeriesType, SingleValueData, Time} from \"lightweight-charts\";\r\nimport {ChartIndicator, ChartIndicatorOptions} from \"./abstract-indicator\";\r\nimport {RegionPrimitive} from \"../custom-primitive/primitive/region\";\r\nimport {autoScaleInfoProviderCreator} from \"../helpers/utils\";\r\nimport {Context} from \"../helpers/execution-indicator\";\r\n\r\nexport interface WilliamsIndicatorOptions extends ChartIndicatorOptions {\r\n  color: string,\r\n  period: number\r\n  priceLineColor: string,\r\n  backgroundColor: string\r\n}\r\n\r\nexport const defaultOptions: WilliamsIndicatorOptions = {\r\n  color: \"rgba(108, 80, 175, 1)\",\r\n  priceLineColor: \"rgba(150, 150, 150, 0.35)\",\r\n  backgroundColor: '#7e57c21a',\r\n  period: 14,\r\n  overlay: false\r\n}\r\n\r\nexport type WilliamsLine = Nominal<number, 'Williams'>\r\n\r\nexport type WilliamsData = [WilliamsLine]\r\n\r\nexport default class WilliamsIndicator extends ChartIndicator<WilliamsIndicatorOptions, WilliamsData> {\r\n  williamsSeries: ISeriesApi<SeriesType>\r\n\r\n  constructor(chart: IChartApi, options?: Partial<WilliamsIndicatorOptions>, paneIndex?: number) {\r\n    super(chart, options)\r\n    this.williamsSeries = chart.addSeries(LineSeries, {\r\n      color: this.options.color,\r\n      lineWidth: 1,\r\n      priceLineVisible: false,\r\n      crosshairMarkerVisible: false,\r\n      priceScaleId: 'williams',\r\n      autoscaleInfoProvider: autoScaleInfoProviderCreator({maxValue: 0, minValue: -100})\r\n    }, paneIndex);\r\n\r\n    this.williamsSeries.attachPrimitive(\r\n      new RegionPrimitive({\r\n        upPrice: -20,\r\n        lowPrice: -80,\r\n        lineColor: this.options.priceLineColor,\r\n        backgroundColor: this.options.backgroundColor\r\n      })\r\n    );\r\n  }\r\n\r\n  getDefaultOptions(): WilliamsIndicatorOptions {\r\n    return defaultOptions\r\n  }\r\n\r\n  formula(c: Context): WilliamsData | undefined {\r\n    const period = this.options.period;\r\n    \r\n    const highSeries = c.new_var(c.symbol.high, period + 1);\r\n    const lowSeries = c.new_var(c.symbol.low, period + 1);\r\n  \r\n    if(!highSeries.calculable() || !lowSeries.calculable()) return;\r\n  \r\n    const historicalHighs = highSeries.getAll().slice(0, -1);\r\n    const historicalLows = lowSeries.getAll().slice(0, -1);\r\n    const currentClose = c.symbol.close;\r\n  \r\n    if(historicalHighs.length < period || historicalLows.length < period) return;\r\n  \r\n    const highestHigh = Math.max(...historicalHighs.slice(-period));\r\n    const lowestLow = Math.min(...historicalLows.slice(-period));\r\n  \r\n    if(highestHigh === lowestLow) return [-50 as WilliamsLine];\r\n  \r\n    const williamsR = ((highestHigh - currentClose) / (highestHigh - lowestLow)) * -100;\r\n    \r\n    return [williamsR as WilliamsLine];\r\n  }\r\n\r\n\r\n  applyIndicatorData() {\r\n    const williamsData: SingleValueData[] = [];\r\n    for(const bar of this._executionContext.data) {\r\n      const value = bar.value;\r\n      if(!value) continue;\r\n      williamsData.push({time: bar.time as Time, value: value[0]})\r\n    }\r\n\r\n    this.williamsSeries.setData(williamsData)\r\n  }\r\n\r\n  remove() {\r\n    super.remove()\r\n    this.chart.removeSeries(this.williamsSeries);\r\n  }\r\n\r\n  _applyOptions() {\r\n    this.williamsSeries.applyOptions({color: this.options.color})\r\n    this.applyIndicatorData();\r\n  }\r\n\r\n\r\n  setPaneIndex(paneIndex: number) {\r\n    this.williamsSeries.moveToPane(paneIndex)\r\n  }\r\n\r\n  getPaneIndex(): number {\r\n    return this.williamsSeries.getPane().paneIndex()\r\n  }\r\n}"], "names": ["defaultOptions", "WilliamsIndicator", "ChartIndicator", "chart", "options", "paneIndex", "__publicField", "LineSeries", "autoScaleInfoProviderCreator", "RegionPrimitive", "c", "period", "highSeries", "lowSeries", "historicalHighs", "historicalLows", "currentClose", "highestHigh", "lowestLow", "williamsData", "bar", "value"], "mappings": "6bAaaA,EAA2C,CACtD,MAAO,wBACP,eAAgB,4BAChB,gBAAiB,YACjB,OAAQ,GACR,QAAS,EACX,EAMA,MAAqBC,UAA0BC,EAAAA,cAAuD,CAGpG,YAAYC,EAAkBC,EAA6CC,EAAoB,CAC7F,MAAMF,EAAOC,CAAO,EAHtBE,EAAA,uBAIO,KAAA,eAAiBH,EAAM,UAAUI,EAAAA,WAAY,CAChD,MAAO,KAAK,QAAQ,MACpB,UAAW,EACX,iBAAkB,GAClB,uBAAwB,GACxB,aAAc,WACd,sBAAuBC,EAA6B,6BAAA,CAAC,SAAU,EAAG,SAAU,IAAK,CAAA,GAChFH,CAAS,EAEZ,KAAK,eAAe,gBAClB,IAAII,kBAAgB,CAClB,QAAS,IACT,SAAU,IACV,UAAW,KAAK,QAAQ,eACxB,gBAAiB,KAAK,QAAQ,eAC/B,CAAA,CACH,CAAA,CAGF,mBAA8C,CACrC,OAAAT,CAAA,CAGT,QAAQU,EAAsC,CACtC,MAAAC,EAAS,KAAK,QAAQ,OAEtBC,EAAaF,EAAE,QAAQA,EAAE,OAAO,KAAMC,EAAS,CAAC,EAChDE,EAAYH,EAAE,QAAQA,EAAE,OAAO,IAAKC,EAAS,CAAC,EAEpD,GAAG,CAACC,EAAW,WAAA,GAAgB,CAACC,EAAU,aAAc,OAExD,MAAMC,EAAkBF,EAAW,OAAS,EAAA,MAAM,EAAG,EAAE,EACjDG,EAAiBF,EAAU,OAAS,EAAA,MAAM,EAAG,EAAE,EAC/CG,EAAeN,EAAE,OAAO,MAE9B,GAAGI,EAAgB,OAASH,GAAUI,EAAe,OAASJ,EAAQ,OAEhE,MAAAM,EAAc,KAAK,IAAI,GAAGH,EAAgB,MAAM,CAACH,CAAM,CAAC,EACxDO,EAAY,KAAK,IAAI,GAAGH,EAAe,MAAM,CAACJ,CAAM,CAAC,EAE3D,OAAGM,IAAgBC,EAAkB,CAAC,GAAmB,EAIlD,EAFaD,EAAcD,IAAiBC,EAAcC,GAAc,IAE9C,CAAA,CAInC,oBAAqB,CACnB,MAAMC,EAAkC,CAAC,EAC/B,UAAAC,KAAO,KAAK,kBAAkB,KAAM,CAC5C,MAAMC,EAAQD,EAAI,MACdC,GACSF,EAAA,KAAK,CAAC,KAAMC,EAAI,KAAc,MAAOC,EAAM,CAAC,EAAE,CAAA,CAGxD,KAAA,eAAe,QAAQF,CAAY,CAAA,CAG1C,QAAS,CACP,MAAM,OAAO,EACR,KAAA,MAAM,aAAa,KAAK,cAAc,CAAA,CAG7C,eAAgB,CACd,KAAK,eAAe,aAAa,CAAC,MAAO,KAAK,QAAQ,MAAM,EAC5D,KAAK,mBAAmB,CAAA,CAI1B,aAAad,EAAmB,CACzB,KAAA,eAAe,WAAWA,CAAS,CAAA,CAG1C,cAAuB,CACrB,OAAO,KAAK,eAAe,QAAQ,EAAE,UAAU,CAAA,CAEnD"}