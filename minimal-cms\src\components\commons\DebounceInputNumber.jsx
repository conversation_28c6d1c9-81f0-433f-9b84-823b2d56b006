import { useEffect, useState } from "react";
import { Input, InputNumber } from "antd";

import useDebounce from "../../hooks/useDebounce";

const DebounceInputNumber = ({ value, onChange, ...props }) => {
  const [val, setVal] = useState(value);

  const debouncedOnChange = useDebounce(() => {
    onChange(val);
  });

  const handleChange = (v) => {
    setVal(v);
    debouncedOnChange();
  };

  useEffect(() => {
    setVal(value);
  }, [value]);

  return <InputNumber {...props} value={val} onChange={handleChange} />;
};

export default DebounceInputNumber;
