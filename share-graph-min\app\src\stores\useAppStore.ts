import { create, StateCreator } from 'zustand';
import { createChartSlice, IChartStore } from './slices/chartSlice';
// import { immer } from 'zustand/middleware/immer';
import { createAppSlice, IAppStore } from './slices/appSlice';
import { createEventsSlice, IEventStore } from './slices/eventSlice';
import { createTickerSlice, ITickerStore } from './slices/tickerSlice';

export type TStoreState = IChartStore & IAppStore & IEventStore & ITickerStore;

const createRootSlice: StateCreator<TStoreState> = (...a) => ({
  ...createChartSlice(...a),
  ...createEventsSlice(...a),
  ...createTickerSlice(...a),
  ...createAppSlice(...a),
});

export const useAppStore = create<TStoreState>()((createRootSlice));
