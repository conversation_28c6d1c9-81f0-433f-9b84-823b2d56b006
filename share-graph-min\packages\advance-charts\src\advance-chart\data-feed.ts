import { Logical, LogicalRange, Time } from 'lightweight-charts';
import { Destroyable, OHLCVSimple } from '../interface';
import { dayjsToTime, timeToDate, timeToDayjs } from '../helpers/utils';
import { AdvanceChart } from './advance-chart';
import {mergeOhlcData} from '../helpers/mergeData';
import {Delegate, IPublicDelegate} from '../helpers/delegate';
import {Interval, Period} from './i-advance-chart';
import {groupBy} from 'es-toolkit';
import dayjs from '../helpers/dayjs-setup';
import {timeKey} from './time-key';
import {log} from '../helpers/log';
import {Dayjs} from 'dayjs';

export interface IDataFetchQuery {
  from: Date;
  to: Date;
  interval: Interval;
}

export type IDataFetchUtils = {
  forward: (time: Time, step: number) => Dayjs
}

export interface IDataFetch {
  refeshTime?: number;

  /**
   * Fetches the initial data when:
   * - The chart is first loaded
   * - The user changes the visible time range
   * - The interval/period is changed
   * 
   * This provides the base dataset that the chart will display initially.
   * Subsequent updates/pagination will build upon this data.
   */
  fetchInitialData(param: IDataFetchQuery, utils: IDataFetchUtils): Promise<OHLCVSimple[]>;

  /**
   * Fetches historical data that's outside the current visible chart range.
   * This data is used to:
   * - Calculate indicators that require lookback periods
   * - Provide seamless scrolling experience when user reaches chart boundaries
   * Automatically triggered when scrolling to the beginning/end of loaded data.
   */
  fetchPaginationData(param: IDataFetchQuery, utils: IDataFetchUtils): Promise<OHLCVSimple[]>;

  /**
   * Fetches updated data periodically based on refreshTime interval.
   * Queries data from the timestamp of last data point to current time.
   * New data points with same timestamps will overwrite existing ones.
   * Used for real-time updates to the chart.
   */
  fetchUpdateData(param: IDataFetchQuery, utils: IDataFetchUtils): Promise<OHLCVSimple[]>;
}

export function roundTime(time: Time, period: Period) {
  switch (period) {
    case Period.minute:
    case Period.hour:
    case Period.day:
      return time;
    case Period.week:
      return dayjs.tz(timeToDayjs(time), 'UTC').startOf('week').unix() as Time;
    case Period.month:
      return dayjs.tz(timeToDayjs(time), 'UTC').startOf('month').unix() as Time;
    default:
      throw new Error(`Period : ${period} not support`)
  }
}

export const aggregate = (items: OHLCVSimple[]) => {
  const high = Math.max(...items.map(item => item.high))
  const low = Math.min(...items.map(item => item.low))
  const volume = items.reduce((acc, item) => acc + item.volume, 0)
  const open = items[0].open
  const close = items[items.length - 1].close
  return {
    time: items[0].time, 
    open,
    high,
    low,
    close,
    volume
  } satisfies OHLCVSimple
}


export class DataFeed implements Destroyable {
  _loading: boolean = false;
  _data: OHLCVSimple[] = [];
  _dataChanged = new Delegate();
  interval: Interval = { period: Period.day, times: 1 };
  initialData = false;
  endOfData = false;
  _refeshTimer: NodeJS.Timeout | null = null;

  _destroyed = false

  
  constructor(
    public advanceChart: AdvanceChart,
    protected dataFetch: IDataFetch
  ) {
    this.onVisibleLogicalRangeChange =
      this.onVisibleLogicalRangeChange.bind(this);
    this.advanceChart.chartApi
      .timeScale()
      .subscribeVisibleLogicalRangeChange(this.onVisibleLogicalRangeChange);

    if(this.dataFetch.refeshTime) {
      this._refeshTimer = setInterval(async() => this.updateData(), this.dataFetch.refeshTime)
    }
  }

  get data () {
    return this._data
  }

  set data (d: OHLCVSimple[]) {
    this._data = d;

    this._dataChanged.fire()
  }

  isNeedPaging(logicalRange: LogicalRange) {
    if (!this.initialData) return false;
    if (this.data.length === 0) return false;
    if (this.endOfData) return false;
    const { from } = logicalRange;
    if (from > 30) return false;
    return true
  }



  groupData() {
    const interval = this.interval

    if(interval.period === Period.day) return this._data
    if(interval.period === Period.hour) return this._data
    if(interval.period === Period.minute) return this._data

    const data = groupBy(this._data, item => timeKey(item.time, interval));

    return Object.entries(data)
      .map(([keyBy, items]) => [parseInt(keyBy), aggregate(items)] as const)
      .sort((a, b) => a[0] - b[0]).map(item => item[1]);
  }

  processNewData(data: OHLCVSimple[]) {
    const newData = mergeOhlcData(data, this.data);
    if (newData.length === this.data.length) return;

    this.data = newData
    this.advanceChart.setData(this.groupData(), this.interval);

    return newData;
  }

  async updateData() {
    const lastData = this.data[this.data.length - 1];
    const lastTime = lastData.time;
    const data = await this.dataFetch.fetchUpdateData({
      interval: this.interval,
      from: timeToDate(lastTime),
      to: new Date
    }, { forward: this.forward.bind(this) })

    if(this._destroyed) return

    this.processNewData(data)
  }

  async pagingData(logicalRange: LogicalRange) {
    const { from } = logicalRange;
    const toTime = this.data[0].time;
    const toDate = timeToDayjs(toTime);
    const fromDate = this.forward(toTime, from - 200);
    
    const data = await this.dataFetch.fetchPaginationData({
      interval: this.interval,
      from: fromDate.toDate(),
      to: toDate.toDate(),
    }, { forward: this.forward.bind(this) });

    if(this._destroyed) return
    
    if(!this.processNewData(data)) {
      this.endOfData = true;
    }
  }

  async onVisibleLogicalRangeChange(logicalRange: LogicalRange | null) {
    if(!logicalRange) return;
    const isSameRange = (range1: LogicalRange, range2: LogicalRange) => range1.from === range2.from && range1.to === range2.to
    if(this.advanceChart.loading) return
    this.advanceChart.loading = true
    try {
      while(this.isNeedPaging(logicalRange)) {
        await this.pagingData(logicalRange)
        const newRange = this.advanceChart.chartApi.timeScale().getVisibleLogicalRange();
        if(!newRange) break;
        if(isSameRange(newRange, logicalRange)) break;
        logicalRange = newRange
      }
    } finally {
      this.advanceChart.loading = false
    }
  }

  forward(time: Time, step: number) {
    step = Math.round(step);
    const period = this.interval.period
    switch (period) {
      case Period.minute:
        return timeToDayjs(time).add(step, 'minute');
      case Period.hour:
        return timeToDayjs(time).add(step, 'hour');
      case Period.day:
        return timeToDayjs(time).add(step, 'day');
      case Period.week:
        return timeToDayjs(time).add(step, 'week');
      case Period.month:
        return timeToDayjs(time).add(step, 'month');
      default:
        throw new Error(`Period : ${period} not support`)
    }
  }

  async setRange({ from, to, interval }: IDataFetchQuery) {
    this.resetState();
    
    this.advanceChart.loading = true;
    this.interval = interval;
    const data = await this.dataFetch.fetchInitialData({ from, to, interval }, { forward: this.forward.bind(this) });
    this.data = data;
    this.advanceChart.loading = false;

    if(this._destroyed) return

    this.advanceChart.setData(this.groupData(), interval);
    this.initialData = true;

    const timeScale = this.advanceChart.chartApi.timeScale();

    const fromIndex = timeScale.timeToIndex(dayjsToTime(dayjs(from)), true)
    const toIndex = timeScale.timeToIndex(dayjsToTime(dayjs(to)), true)

    if(fromIndex !== undefined && toIndex !== undefined) {
      this.advanceChart.fitRange({ from: fromIndex as unknown as Logical, to: toIndex as unknown as Logical })
    }
    
    await this.onVisibleLogicalRangeChange(
      timeScale.getVisibleLogicalRange()
    );
  }

  private resetState() {
    this.advanceChart.loading = false;
    this.initialData = false;
    this.endOfData = false;
  }

  dataChanged (){
    return this._dataChanged as IPublicDelegate<typeof this._dataChanged>
  }

  trade(trade: { time: Time, price: number, volume: number }) {
    const [lastPoint] = this.advanceChart.lastPoint();

    const currentKey = timeKey(lastPoint.time, this.interval)
    const newKey = timeKey(trade.time, this.interval)
    if(newKey < currentKey) {
      log.warn(`Trade timestamp ${newKey} is older than current ${currentKey}`)
      return;
    }
    if(currentKey === newKey) {
      this.advanceChart.update({
        open: lastPoint.open,
        high: Math.max(lastPoint.high, trade.price),
        low: Math.min(lastPoint.low, trade.price),
        close: trade.price,
        volume: lastPoint.volume + trade.volume,
        time: trade.time
      }, true)
    } else {
      this.advanceChart.update({
        open: trade.price,
        high: trade.price,
        low: trade.price,
        close: trade.price,
        volume: trade.volume,
        time: trade.time
      })
    }
  }

  destroy() {
    this._dataChanged.destroy();
    this.advanceChart.chartApi
      .timeScale()
      .unsubscribeVisibleLogicalRangeChange(this.onVisibleLogicalRangeChange);

    if(this._refeshTimer) {
      clearInterval(this._refeshTimer);
      this._refeshTimer = null;
    }
  }
}
