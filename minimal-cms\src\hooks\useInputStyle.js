import { useBreakpoint } from "./useResize";

const useInputStyle = () => {
  const { breakpoint } = useBreakpoint();
  const isMobile = ["ssm", "sm", "md", "mobile"].includes(breakpoint);
  const isSmallMobile = ["ssm", "sm"].includes(breakpoint);
  const getStyleInput = () => {
    if (isSmallMobile) {
      return {
        width: "100%",
      };
    }
    return {
      minWidth: 200,
      width: "fit-content",
    };
  };

  return { isSmallMobile, isMobile, inputStyle: getStyleInput() };
};

export { useInputStyle };
