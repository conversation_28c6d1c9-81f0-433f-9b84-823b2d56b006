import React from 'react';
import clsx from 'clsx';

interface SwitcherProps {
  checked?: boolean;
  onChange: (checked: boolean) => void;
  className?: string;
  disabled?: boolean;
}

const Switcher: React.FC<SwitcherProps> = ({
  checked = true,
  onChange,
  className,
  disabled = false,
}) => {
  const switcherClass = clsx('switcher', className, {
    'switcher--checked': checked,
    'switcher--disabled': disabled,
  });

  const handleChange = () => {
    if (!disabled) {
      onChange(!checked);
    }
  };

  return (
    <div 
      className={switcherClass} 
      onClick={handleChange}
      role="switch"
      aria-checked={checked}
      tabIndex={disabled ? -1 : 0}
      aria-disabled={disabled}
    >
      <div className="switcher__toggle"></div>
    </div>
  );
};

export default Switcher; 