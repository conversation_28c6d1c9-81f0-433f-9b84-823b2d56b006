import React from 'react';
import { ISvgIconProps } from '../types/common';

const IndicatorIcon: React.FC<ISvgIconProps> = ({ width = 16, height = 16,  }) => {
  return (
    <svg
    xmlns="http://www.w3.org/2000/svg"
    width={24}
    height={24}
    viewBox="0 0 24 24"
    fill="none"
    stroke="currentColor"
    strokeWidth={2}
    strokeLinecap="round"
    strokeLinejoin="round"
    style={{color: 'currentColor', width: width, height: height}}
  >
    <path d="M3 3v18h18" />
    <path d="M13 17V9" />
    <path d="M18 17V5" />
    <path d="M8 17v-3" />
  </svg>
  );
};

export default IndicatorIcon;
