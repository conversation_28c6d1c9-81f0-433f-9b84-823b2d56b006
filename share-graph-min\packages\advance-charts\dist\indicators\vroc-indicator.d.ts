import { IChartApi, ISeriesApi, Nominal, SeriesType } from 'lightweight-charts';
import { ChartIndicator, ChartIndicatorOptions } from './abstract-indicator';
import { Context } from '../helpers/execution-indicator';

export interface VROCIndicatorOptions extends ChartIndicatorOptions {
    color: string;
    period: number;
    priceLineColor: string;
    backgroundColor: string;
}
export declare const defaultOptions: VROCIndicatorOptions;
export type VROCLine = Nominal<number, 'VROC'>;
export type VROCData = [VROCLine];
export default class VROCIndicator extends ChartIndicator<VROCIndicatorOptions, VROCData> {
    vrocSeries: ISeriesApi<SeriesType>;
    constructor(chart: IChartApi, options?: Partial<VROCIndicatorOptions>, paneIndex?: number);
    getDefaultOptions(): VROCIndicatorOptions;
    formula(c: Context): VROCData | undefined;
    applyIndicatorData(): void;
    remove(): void;
    _applyOptions(): void;
    setPaneIndex(paneIndex: number): void;
    getPaneIndex(): number;
}
