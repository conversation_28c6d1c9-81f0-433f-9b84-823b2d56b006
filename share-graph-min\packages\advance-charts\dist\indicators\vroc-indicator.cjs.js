"use strict";var u=Object.defineProperty;var p=(t,o,e)=>o in t?u(t,o,{enumerable:!0,configurable:!0,writable:!0,value:e}):t[o]=e;var n=(t,o,e)=>p(t,typeof o!="symbol"?o+"":o,e);Object.defineProperties(exports,{__esModule:{value:!0},[Symbol.toStringTag]:{value:"Module"}});const v=require("lightweight-charts"),d=require("./abstract-indicator.cjs.js"),h=require("../custom-primitive/primitive/region.cjs.js"),f=require("../helpers/utils.cjs.js"),c={color:"rgba(255, 152, 0, 1)",priceLineColor:"rgba(150, 150, 150, 0.35)",backgroundColor:"#ff98001a",period:14,overlay:!1};class m extends d.ChartIndicator{constructor(e,r,i){super(e,r);n(this,"vrocSeries");this.vrocSeries=e.addSeries(v.LineSeries,{color:this.options.color,lineWidth:2,priceLineVisible:!1,crosshairMarkerVisible:!1,priceScaleId:"vroc",autoscaleInfoProvider:f.autoScaleInfoProviderCreator({minValue:-100,maxValue:100})},i),this.vrocSeries.attachPrimitive(new h.RegionPrimitive({upPrice:5,lowPrice:-5,lineColor:this.options.priceLineColor,backgroundColor:this.options.backgroundColor}))}getDefaultOptions(){return c}formula(e){const r=this.options.period,i=e.symbol.volume,s=e.new_var(i,r+1);if(!s.calculable())return;const l=s.get(0),a=s.get(r);return a===0?[0]:[(l-a)/a*100]}applyIndicatorData(){const e=[];for(const r of this._executionContext.data){const i=r.value;if(!i)continue;const s=r.time;e.push({time:s,value:i[0]})}this.vrocSeries.setData(e)}remove(){super.remove(),this.chart.removeSeries(this.vrocSeries)}_applyOptions(){this.vrocSeries.applyOptions({color:this.options.color}),this.applyIndicatorData()}setPaneIndex(e){this.vrocSeries.moveToPane(e)}getPaneIndex(){return this.vrocSeries.getPane().paneIndex()}}exports.default=m;exports.defaultOptions=c;
//# sourceMappingURL=vroc-indicator.cjs.js.map
