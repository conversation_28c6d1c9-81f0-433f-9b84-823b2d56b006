stages:
  - build
  - test
  - quality
  #- release
  - deploy

variables:
  DOCKER_DRIVER: overlay2
  GIT_STRATEGY: fetch

######### Shared variables #########
.deploy_template: &deploy_template
  script:
    - |
      $deployParams = @{
          source      = Join-Path $PWD "dist"
          siteName   = "$env:SITE_NAME"
          appPath    = "$env:APP_PATH"
          user       = "$DEPLOY_USER"
          passwd     = "$DEPLOY_PWD"
          server     = "$env:DEPLOY_SERVER1"
          port       = $env:DEPLOY_SERVER_PORT1
      }

      & ".\ms_deploy.ps1" @deployParams

      if($env:DEPLOY_SERVER2 -and $env:DEPLOY_SERVER_PORT2) {
        $deployParams.server = $env:DEPLOY_SERVER2
        $deployParams.port = $env:DEPLOY_SERVER_PORT2

        & ".\ms_deploy.ps1" @deployParams
      }

      if ($LASTEXITCODE -ne 0) {
        Write-Host "Deployment failed with exit code $LASTEXITCODE" -ForegroundColor Red
        exit $LASTEXITCODE
      }

build-qa:
  tags:
    - vn-v-docker
  image: node:20.19.0
  stage: build
  script:
    - npm install
    - npm run build "--" --mode qa
  rules:
    - if: $CI_MERGE_REQUEST_TITLE =~ /^wip[:]/i
      when: never
    - if: $CI_PIPELINE_SOURCE == "merge_request_event" && $CI_MERGE_REQUEST_TARGET_BRANCH_NAME == "develop"
      when: always
    - if: $CI_COMMIT_BRANCH == "develop"
      when: always  # Run always on develop branch commits
  artifacts:
    paths:
      - dist/
    untracked: false
    name: "client-$CI_JOB_STAGE-$CI_COMMIT_REF_NAME"
    expire_in: 2 hours

test-qa:
  stage: test
  tags:
    - vn-v-docker
  image: node:20.19.0
  dependencies: []
  script:
    - npm install
    - npm run test
  rules:
    - if: $CI_MERGE_REQUEST_TITLE =~ /^wip[:]/i
      when: never
    - if: $CI_PIPELINE_SOURCE == "merge_request_event" && $CI_MERGE_REQUEST_TARGET_BRANCH_NAME == "develop"
      when: always

quality-qa:
  stage: quality
  tags:
    - vn-v-docker
  image: node:20.19.0
  dependencies: []
  script:
    - npm install
    - npm run lint
  rules:
    - if: $CI_MERGE_REQUEST_TITLE =~ /^wip[:]/i
      when: never
    - if: $CI_PIPELINE_SOURCE == "merge_request_event" && $CI_MERGE_REQUEST_TARGET_BRANCH_NAME == "develop"
      when: always

deploy-qa:
  stage: deploy
  tags:
    - vietnam-buildtest-powershell
  dependencies:
    - build-qa
  variables:
    ENVIRONMENT: 'QA'
    DEPLOY_SERVER1: 'BINGO'
    DEPLOY_SERVER_PORT1: 8172
    SITE_NAME: 'tools site'
    APP_PATH: '/tools/minimalsharegraph'
  before_script:
    - Set-Variable -Name "DEPLOY_USER" -Value "$env:DEV_VN_DEPLOY_USER"
    - Set-Variable -Name "DEPLOY_PWD" -Value "$env:DEV_VN_DEPLOY_PSW"
  <<: *deploy_template
  rules:
    - if: $CI_COMMIT_BRANCH == "develop"
      when: on_success

# Production jobs
build-production:
  tags:
    - ee-v-docker1-dind
  image: node:20
  stage: build
  script:
    - npm install
    - npm run build "--" --mode production
  rules:
    - if: $CI_COMMIT_BRANCH == "next"
      when: always
    - if: $CI_COMMIT_BRANCH == "master"
      when: always
  artifacts:
    paths:
      - dist/
    untracked: false
    name: "client-$CI_JOB_STAGE-$CI_COMMIT_REF_NAME"

deploy-gamma:
  stage: deploy
  tags:
    - ee-buildtest-powershell
  dependencies:
    - build-production
  variables:
    ENVIRONMENT: 'Gamma'
    DEPLOY_SERVER1: 'ee-v-gamma1.euroland.com'
    DEPLOY_SERVER_PORT1: 8172
    SITE_NAME: 'Default Web Site'
    APP_PATH: '/tools/minimalsharegraph'
  environment:
    name: gamma
    url: 'https://gamma.euroland.com/tools/minimalsharegraph'
  before_script:
    - Set-Variable -Name "DEPLOY_USER" -Value "$env:GAMMA_DEPLOY_USER"
    - Set-Variable -Name "DEPLOY_PWD" -Value "$env:GAMMA_DEPLOY_PSW"
  <<: *deploy_template
  rules:
    - if: $CI_COMMIT_BRANCH == "next"
      when: on_success

deploy-staging:
  stage: deploy
  tags:
    - ee-buildtest-powershell
  dependencies:
    - build-production
  variables:
    ENVIRONMENT: 'Staging'
    DEPLOY_SERVER1: 'ee-v-webcat151.euroland.com'
    DEPLOY_SERVER_PORT1: 8172
    DEPLOY_SERVER2: 'ee-v-webcat161.euroland.com'
    DEPLOY_SERVER_PORT2: 8172
    SITE_NAME: 'staging-site'
    APP_PATH: '/tools/minimalsharegraph'
  environment:
    name: staging
    url: 'https://staging-gr.eurolandir.com/tools/minimalsharegraph'
  before_script:
    - Set-Variable -Name "DEPLOY_USER" -Value "$env:STAGING_DEPLOY_USER"
    - Set-Variable -Name "DEPLOY_PWD" -Value "$env:STAGING_DEPLOY_PWD"
  <<: *deploy_template
  rules:
    - if: $CI_COMMIT_BRANCH == "master"
      when: manual

"ee-v-webcat15,16":
  stage: deploy
  tags:
    - ee-buildtest-powershell
  dependencies:
    - build-production
  variables:
    ENVIRONMENT: 'Production'
    DEPLOY_SERVER1: 'ee-v-webcat161.euroland.com'
    DEPLOY_SERVER_PORT1: 8172
    DEPLOY_SERVER2: 'ee-v-webcat151.euroland.com'
    DEPLOY_SERVER_PORT2: 8172
    SITE_NAME: 'production-site'
    APP_PATH: '/tools/minimalsharegraph'
  environment:
    name: ground
    url: 'https://gr-web-ws1.eurolandir.com/tools/minimalsharegraph'
  before_script:
    - Set-Variable -Name "DEPLOY_USER" -Value "$env:STAGING_DEPLOY_USER"
    - Set-Variable -Name "DEPLOY_PWD" -Value "$env:STAGING_DEPLOY_PWD"
  <<: *deploy_template
  rules:
    - if: $CI_COMMIT_BRANCH == "master"
      when: manual

"ne-web-ws1,2":
  stage: deploy
  tags:
    - ee-buildtest-powershell
  dependencies:
    - build-production
  variables:
    ENVIRONMENT: 'CloudNE'
    DEPLOY_SERVER1: 'ne-web-haproxy.northeurope.cloudapp.azure.com'
    DEPLOY_SERVER_PORT1: 8173
    DEPLOY_SERVER2: 'ne-web-haproxy.northeurope.cloudapp.azure.com'
    DEPLOY_SERVER_PORT2: 8172
    SITE_NAME: 'Default Web Site'
    APP_PATH: '/tools/minimalsharegraph'
  environment:
    name: cloudne
    url: 'https://ne-web-ws1.eurolandir.com.com/tools/minimalsharegraph'
  before_script:
    - Set-Variable -Name "DEPLOY_USER" -Value "$env:STAGING_DEPLOY_USER"
    - Set-Variable -Name "DEPLOY_PWD" -Value "$env:STAGING_DEPLOY_PWD"
  <<: *deploy_template
  rules:
    - if: $CI_COMMIT_BRANCH == "master"
      when: manual

"ea-web-ws1,2":
  stage: deploy
  tags:
    - ee-buildtest-powershell
  dependencies:
    - build-production
  variables:
    ENVIRONMENT: 'CloudEA'
    DEPLOY_SERVER1: 'ea-web-haproxy.eastasia.cloudapp.azure.com'
    DEPLOY_SERVER_PORT1: 8173
    DEPLOY_SERVER2: 'ea-web-haproxy.eastasia.cloudapp.azure.com'
    DEPLOY_SERVER_PORT2: 8172
    SITE_NAME: 'Default Web Site'
    APP_PATH: '/tools/minimalsharegraph'
  environment:
    name: cloudea
    url: 'https://ea-web-ws1.eurolandir.com.com/tools/minimalsharegraph'
  before_script:
    - Set-Variable -Name "DEPLOY_USER" -Value "$env:STAGING_DEPLOY_USER"
    - Set-Variable -Name "DEPLOY_PWD" -Value "$env:STAGING_DEPLOY_PWD"
  <<: *deploy_template
  rules:
    - if: $CI_COMMIT_BRANCH == "master"
      when: manual

"uae-web-ws1,2":
  stage: deploy
  tags:
    - ee-buildtest-powershell
  dependencies:
    - build-production
  variables:
    ENVIRONMENT: 'CloudUAE'
    DEPLOY_SERVER1: 'uaewebhaproxy1.uaenorth.cloudapp.azure.com'
    DEPLOY_SERVER_PORT1: 8173
    DEPLOY_SERVER2: 'uaewebhaproxy1.uaenorth.cloudapp.azure.com'
    DEPLOY_SERVER_PORT2: 8172
    SITE_NAME: 'Default Web Site'
    APP_PATH: '/tools/minimalsharegraph'
  environment:
    name: clouduae
    url: 'https://uae-web-ws1.eurolandir.com.com/tools/minimalsharegraph'
  before_script:
    - Set-Variable -Name "DEPLOY_USER" -Value "$env:STAGING_DEPLOY_USER"
    - Set-Variable -Name "DEPLOY_PWD" -Value "$env:STAGING_DEPLOY_PWD"
  <<: *deploy_template
  rules:
    - if: $CI_COMMIT_BRANCH == "master"
      when: manual

"ksa-web-ws1,2":
  stage: deploy
  tags:
    - ee-buildtest-powershell
  dependencies:
    - build-production
  variables:
    ENVIRONMENT: 'CloudKSA'
    DEPLOY_SERVER1: '**************'
    DEPLOY_SERVER_PORT1: 8173
    DEPLOY_SERVER2: '**************'
    DEPLOY_SERVER_PORT2: 8172
    SITE_NAME: 'Default Web Site'
    APP_PATH: '/tools/minimalsharegraph'
  environment:
    name: cloudksa
    url: 'https://ksa-web-ws1.eurolandir.com.com/tools/minimalsharegraph'
  before_script:
    - Set-Variable -Name "DEPLOY_USER" -Value "$env:STAGING_DEPLOY_USER"
    - Set-Variable -Name "DEPLOY_PWD" -Value "$env:STAGING_DEPLOY_PWD"
  <<: *deploy_template
  rules:
    - if: $CI_COMMIT_BRANCH == "master"
      when: manual

"cn-web-ws1,2":
  stage: deploy
  tags:
    - ee-buildtest-powershell
  dependencies:
    - build-production
  variables:
    ENVIRONMENT: 'CloudCN'
    DEPLOY_SERVER1: 'cn-web-haproxy.chinanorth.cloudapp.chinacloudapi.cn'
    DEPLOY_SERVER_PORT1: 8173
    DEPLOY_SERVER2: 'cn-web-haproxy.chinanorth.cloudapp.chinacloudapi.cn'
    DEPLOY_SERVER_PORT2: 8172
    SITE_NAME: 'Default Web Site'
    APP_PATH: '/tools/minimalsharegraph'
  environment:
    name: cloudcn
    url: 'https://ksa-web-ws1.eurolandir.com.com/tools/minimalsharegraph'
  before_script:
    - Set-Variable -Name "DEPLOY_USER" -Value "$env:STAGING_DEPLOY_USER"
    - Set-Variable -Name "DEPLOY_PWD" -Value "$env:STAGING_DEPLOY_PWD"
  <<: *deploy_template
  rules:
    - if: $CI_COMMIT_BRANCH == "master"
      when: manual
