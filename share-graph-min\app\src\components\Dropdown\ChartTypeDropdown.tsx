import Dropdown from '../Dropdown';
import { DropdownMenuItem, OnChangeDropdown } from '../../types/common';
import { CHART_TYPE_KEYS } from '../../constants/chartConstant';
import { useAppStore } from '../../stores/useAppStore';
import { CHART_KEYS } from '../../constants/common';
import { TChartType } from '../../types/store';
import { i18n } from '@euroland/libs';
import ChartTypeIcon from '../../icons/chartTypes/ChartTypeIcon';
import { selectRadioIcon } from './common';

const ChartTypeDropdown = () => {
  const chartType = useAppStore((state) => state.chartType);
  const setChartType = useAppStore((state) => state.setChartType);
  const value = {
    [CHART_KEYS.CHART_TYPE]: [chartType],
  };

  const menuData: DropdownMenuItem[] = [
    
    {
      id: CHART_TYPE_KEYS.MOUNTAIN.key,
      label: i18n.translate(CHART_TYPE_KEYS.MOUNTAIN.label),
      ...selectRadioIcon,
      // icon: <MountainIcon />
    },
    {
      id: CHART_TYPE_KEYS.LINE.key,
      label: i18n.translate(CHART_TYPE_KEYS.LINE.label),
      ...selectRadioIcon,
      // icon: <LineIcon />
    },
    {
      id: CHART_TYPE_KEYS.CANDLESTICK.key,
      label: i18n.translate(CHART_TYPE_KEYS.CANDLESTICK.label),
      ...selectRadioIcon,
      // selectRadioIcon: <CandleIcon />
    },
    {
      id: CHART_TYPE_KEYS.BAR_OHLC.key,
      label: i18n.translate(CHART_TYPE_KEYS.BAR_OHLC.label),
      ...selectRadioIcon,
      // selectRadioIcon: <BarIcon />
    },
    {
      id: CHART_TYPE_KEYS.BASELINE.key,
      label: i18n.translate(CHART_TYPE_KEYS.BASELINE.label),
      ...selectRadioIcon,
      // selectRadioIcon: <BaselineIcon />
    },
    {
      id: CHART_TYPE_KEYS.BASE_MOUNTAIN.key,
      label: i18n.translate(CHART_TYPE_KEYS.BASE_MOUNTAIN.label),
      ...selectRadioIcon,
      // icon: <MountainIcon />
    },
  ];

  const handleChange: OnChangeDropdown = (val) => {
    setChartType(val as TChartType);
  };

  const placeholder = () => {
    // const chartTypeInfo = menuData.find((item) => item.id === chartType);
    // const {icon, label} = chartTypeInfo || {};
    return (
      <>
        <ChartTypeIcon /> <span>{i18n.translate('chart')}</span>
      </>
    );
  };

  return (
    <Dropdown
      menuData={menuData}
      onChange={handleChange}
      value={value}
      placeholder={placeholder()}
      parentIdFromProp={CHART_KEYS.CHART_TYPE}
      description={i18n.translate('chartType')}
    />
  );
};

export default ChartTypeDropdown;
