var h = Object.defineProperty;
var d = (i, t, e) => t in i ? h(i, t, { enumerable: !0, configurable: !0, writable: !0, value: e }) : i[t] = e;
var o = (i, t, e) => d(i, typeof t != "symbol" ? t + "" : t, e);
import { merge as s, cloneDeep as c } from "es-toolkit";
import { Delegate as l } from "../helpers/delegate.es.js";
import { timeToDate as a, binarySearch as u } from "../helpers/utils.es.js";
import { ExecutionContext as p } from "../helpers/execution-indicator.es.js";
const g = "#26a69a", D = "#ef5350";
class _ {
  constructor(t, e) {
    o(this, "data", null);
    o(this, "options");
    o(this, "mainSeries", null);
    o(this, "_dataHovered", new l());
    o(this, "indicatorData", []);
    o(this, "_executionContext");
    this.chart = t, this.options = s(c(this.getDefaultOptions()), e ?? {}), this.onCrosshairMove = this.onCrosshairMove.bind(this), this.chart.subscribeCrosshairMove(this.onCrosshairMove), this._executionContext = new p((n) => {
      var r;
      return (r = this.formula) == null ? void 0 : r.call(this, n);
    });
  }
  formula() {
  }
  mainSeriesChanged(t) {
    var e;
    this.mainSeries = t, (e = this._mainSeriesChanged) == null || e.call(this, t);
  }
  onCrosshairMove(t) {
    if (t.time === void 0) return this._dataHovered.fire(void 0);
    this._dataHovered.fire(this.dataByTime(t.time));
  }
  dataHovered() {
    return this._dataHovered;
  }
  setData(t) {
    this.data = t, this._executionContext.recalc(t.map((e) => ({
      open: e.open,
      high: e.high,
      low: e.low,
      time: Math.floor(a(e.time).getTime() / 1e3),
      isNew: !1,
      volume: e.volume,
      close: e.close
    }))), this.calcIndicatorData(), this.applyIndicatorData();
  }
  update() {
    var e;
    if (!this.data) return;
    const t = this.data[this.data.length - 1];
    this._executionContext.update({
      open: t.open,
      high: t.high,
      low: t.low,
      time: Math.floor(a(t.time).getTime() / 1e3),
      volume: t.volume,
      close: t.close
    }), (e = this.recalc) == null || e.call(this), this.applyIndicatorData();
  }
  applyOptions(t) {
    var e;
    this.options = s(this.options, t), (e = this._applyOptions) == null || e.call(this, t);
  }
  remove() {
    this.onCrosshairMove && this.chart.unsubscribeCrosshairMove(this.onCrosshairMove);
  }
  getDataByCrosshair({ logical: t }, e) {
    if (t !== void 0)
      return e.dataByIndex(t) ?? void 0;
  }
  dataByTime(t) {
    return u(this._executionContext.data, Math.floor(a(t).getTime() / 1e3), (e) => e.time);
  }
  lastPoint() {
    const t = this._executionContext.data;
    if (t.length !== 0)
      return t[t.length - 1];
  }
  getData() {
    return this._executionContext.data;
  }
  calcIndicatorData() {
  }
  applyIndicatorData() {
  }
  setPaneIndex() {
  }
  getPaneIndex() {
    var t;
    return ((t = this.mainSeries) == null ? void 0 : t.getPane().paneIndex()) ?? 0;
  }
}
export {
  _ as ChartIndicator,
  D as downColor,
  g as upColor
};
//# sourceMappingURL=abstract-indicator.es.js.map
