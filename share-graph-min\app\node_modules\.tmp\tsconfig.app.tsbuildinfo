{"root": ["../../src/app.tsx", "../../src/integratetool.ts", "../../src/main.tsx", "../../src/setuptests.ts", "../../src/vite-env.d.ts", "../../src/components/animatedvalue/animatedvalue.tsx", "../../src/components/animatedvalue/index.ts", "../../src/components/button/button.tsx", "../../src/components/button/index.ts", "../../src/components/chartinfo/chartinfo.tsx", "../../src/components/chartinfo/index.ts", "../../src/components/drawer/charttypedrawer.tsx", "../../src/components/drawer/drawer.tsx", "../../src/components/drawer/drawerportal.tsx", "../../src/components/drawer/indicatordrawer.tsx", "../../src/components/drawer/optionslist.tsx", "../../src/components/drawer/overlaydrawer.tsx", "../../src/components/drawer/settingdrawer.tsx", "../../src/components/drawer/index.ts", "../../src/components/dropdown/charttypedropdown.tsx", "../../src/components/dropdown/dropdown.tsx", "../../src/components/dropdown/eventdropdown.tsx", "../../src/components/dropdown/indicatordropdown.tsx", "../../src/components/dropdown/overlaydropdown.tsx", "../../src/components/dropdown/settingsdropdown.tsx", "../../src/components/dropdown/common.tsx", "../../src/components/dropdown/index.ts", "../../src/components/header/header.tsx", "../../src/components/header/index.ts", "../../src/components/instrumentradio/instrumentradio.tsx", "../../src/components/instrumentradio/index.ts", "../../src/components/lightweightchart/gohome.tsx", "../../src/components/lightweightchart/lightweightchart.tsx", "../../src/components/lightweightchart/loading.tsx", "../../src/components/lightweightchart/mainlegend.tsx", "../../src/components/lightweightchart/nodata.tsx", "../../src/components/lightweightchart/panesrenderer.tsx", "../../src/components/lightweightchart/realtimeupdate.tsx", "../../src/components/lightweightchart/settings.tsx", "../../src/components/lightweightchart/topsettingchart.tsx", "../../src/components/lightweightchart/context.tsx", "../../src/components/lightweightchart/index.ts", "../../src/components/lightweightchart/legend/bblegend.tsx", "../../src/components/lightweightchart/legend/emalegend.tsx", "../../src/components/lightweightchart/legend/legend.tsx", "../../src/components/lightweightchart/legend/macdlegend.tsx", "../../src/components/lightweightchart/legend/mmlegend.tsx", "../../src/components/lightweightchart/legend/rsilegend.tsx", "../../src/components/lightweightchart/legend/smalegend.tsx", "../../src/components/lightweightchart/legend/stochasticlegend.tsx", "../../src/components/lightweightchart/legend/volumelegend.tsx", "../../src/components/lightweightchart/legend/wmalegend.tsx", "../../src/components/lightweightchart/legend/renderlegend.tsx", "../../src/components/lightweightchart/markerevents/dividendmarker.tsx", "../../src/components/lightweightchart/markerevents/earningmarker.tsx", "../../src/components/lightweightchart/markerevents/eventpoint.tsx", "../../src/components/lightweightchart/markerevents/markerevents.tsx", "../../src/components/lightweightchart/markerevents/utils/data-loader.test.ts", "../../src/components/lightweightchart/markerevents/utils/data-loader.ts", "../../src/components/lightweightchart/markerevents/utils/dividend-marker.ts", "../../src/components/lightweightchart/markerevents/utils/earning-marker.ts", "../../src/components/lightweightchart/markerevents/utils/findnearestposition.ts", "../../src/components/maincontent/maincontent.tsx", "../../src/components/maincontent/index.ts", "../../src/components/mobileselectsettings/mobilecharttype.tsx", "../../src/components/mobileselectsettings/mobileindicator.tsx", "../../src/components/mobileselectsettings/mobileoverlay.tsx", "../../src/components/mobileselectsettings/mobileselectsettings.tsx", "../../src/components/mobileselectsettings/mobilesettings.tsx", "../../src/components/mobileselectsettings/index.ts", "../../src/components/modal/customrangechartmodal.tsx", "../../src/components/modal/modal.tsx", "../../src/components/modal/index.ts", "../../src/components/optionlist/chartpreferenceoptionlist.tsx", "../../src/components/optionlist/charttypeoptionlist.tsx", "../../src/components/optionlist/eventsoptionlist.tsx", "../../src/components/optionlist/indicatoroptionlist.tsx", "../../src/components/optionlist/lastcloseoptionlist.tsx", "../../src/components/optionlist/optionlist.tsx", "../../src/components/optionlist/overlaysoptionlist.tsx", "../../src/components/optionlist/volumeoptionlist.tsx", "../../src/components/optionlist/yaxispreferenceoptionlist.tsx", "../../src/components/optionlist/index.ts", "../../src/components/rangechart/customrangeitem.tsx", "../../src/components/rangechart/rangechart.tsx", "../../src/components/rangechart/rangechartitem.tsx", "../../src/components/rangechart/index.ts", "../../src/components/rangedatepicker/datepicker.tsx", "../../src/components/rangedatepicker/rangedatepicker.tsx", "../../src/components/rangedatepicker/index.ts", "../../src/components/selectsettings/selectsettings.tsx", "../../src/components/selectsettings/index.ts", "../../src/components/sidebar/sidebar.tsx", "../../src/components/sidebar/index.ts", "../../src/components/switcher/switcher.tsx", "../../src/components/switcher/switcherexample.tsx", "../../src/components/switcher/index.ts", "../../src/components/ticker/tablelayoutticker.tsx", "../../src/components/ticker/tablelayouttickerskeleton.tsx", "../../src/components/ticker/ticker.tsx", "../../src/components/ticker/index.ts", "../../src/configs/defaultsetting.ts", "../../src/constants/chartconstant.ts", "../../src/constants/common.ts", "../../src/graphql/client.ts", "../../src/graphql/utils.ts", "../../src/graphql/queries/charthistoryquery.ts", "../../src/graphql/queries/dividendquery.ts", "../../src/graphql/queries/earningeventquery.ts", "../../src/graphql/queries/marketquery.ts", "../../src/graphql/queries/ticker-query.ts", "../../src/graphql/queries/tickerquery.ts", "../../src/hooks/useclickoutside.ts", "../../src/hooks/usemediaquery.ts", "../../src/hooks/usemobiledetect.ts", "../../src/hooks/useprevious.ts", "../../src/icons/checkicon.tsx", "../../src/icons/checkboxicon.tsx", "../../src/icons/closeicon.tsx", "../../src/icons/doticon.tsx", "../../src/icons/indicator.tsx", "../../src/icons/overlayicon.tsx", "../../src/icons/radioicon.tsx", "../../src/icons/rightcollapseicon.tsx", "../../src/icons/settingicon.tsx", "../../src/icons/charttypes/baricon.tsx", "../../src/icons/charttypes/baseline.tsx", "../../src/icons/charttypes/candleicon.tsx", "../../src/icons/charttypes/charttypeicon.tsx", "../../src/icons/charttypes/lineicon.tsx", "../../src/icons/charttypes/mountainicon.tsx", "../../src/realtime/realtimesetup.ts", "../../src/services/chart-data-service.ts", "../../src/services/common.ts", "../../src/services/getjsonappsetting.ts", "../../src/services/market-service.ts", "../../src/services/ticker-services.ts", "../../src/stores/useappstore.ts", "../../src/stores/slices/appslice.ts", "../../src/stores/slices/chartslice.ts", "../../src/stores/slices/eventslice.ts", "../../src/stores/slices/tickerslice.ts", "../../src/test/utils/common.test.ts", "../../src/test/utils/config.test.ts", "../../src/types/optionlist.ts", "../../src/types/common.ts", "../../src/types/datepicker.ts", "../../src/types/defaultconfig.ts", "../../src/types/events.ts", "../../src/types/store.ts", "../../src/utils/appconfig.ts", "../../src/utils/common.ts", "../../src/utils/config.ts", "../../src/utils/decodebase64.ts", "../../src/utils/marker.ts", "../../src/utils/store.ts"], "version": "5.6.3"}