import { useState } from "react";
import { <PERSON>ton, Checkbox, Col, Flex, Row } from "antd";

// import { useAvailableTools } from "../../../../hooks/useSettings";
// import useAppSelector from "../../../../hooks/useAppSelector";

export default function AvailableTool(props) {
  // const [{ availableTools }] = useAvailableTools();
  const availableTools = [
    {
      name: "Minimal Share Graph",
      value: "SG",
      shortName: "Share graph",
    },
  ];

  return (
    <Checkbox.Group {...props} className="w-full">
      <Row gutter={8} className="gap-y-4">
        {availableTools.map((tool) => {
          const { value, name } = tool;
          return (
            <Checkbox key={value} value={value} size="" disabled>
              {name}
            </Checkbox>
          );
        })}
      </Row>
    </Checkbox.Group>
  );
}
