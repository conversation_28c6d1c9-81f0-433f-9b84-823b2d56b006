{"$schema": "https://turbo.build/schema.json", "globalDependencies": [".env*", "yarn.lock"], "tasks": {"build": {"dependsOn": ["^build"], "outputs": ["../dist/**", "dist/**"], "env": ["MODE"], "passThroughEnv": ["MODE"]}, "lint": {"outputs": []}, "dev": {"cache": false, "persistent": true, "dependsOn": ["^build"]}, "test": {"dependsOn": ["^build"], "outputs": []}, "codegen": {"outputs": ["src/_gql/**"]}, "preview": {"dependsOn": ["build"]}, "benchmark": {"dependsOn": ["build"]}, "tsc": {"outputs": []}}}