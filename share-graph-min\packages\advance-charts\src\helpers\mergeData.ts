import type { Time } from 'lightweight-charts';

export function mergeOhlcData<D extends {time: Time } >(
  oldData: D[],
  newData: D[]
): D[] {
  // Handle trivial edge cases
  if (oldData.length === 0) return newData;
  if (newData.length === 0) return oldData;

  /**
   * 1. Find where oldData might overlap newData:
   *    Walk backward until we find the last item in oldData whose time <= newData[0].time
   */
  let overlapIndex = oldData.length - 1;
  while (overlapIndex >= 0 && oldData[overlapIndex].time > newData[0].time) {
    overlapIndex--;
  }

  /**
   * 2. Start our merged result up to that overlap point.
   *    If overlapIndex is -1, that means oldData is all strictly > newData[0].time,
   *    so we effectively start with an empty merged array.
   */
  const merged: D[] = oldData.slice(0, overlapIndex + 1);

  /**
   * 3. If there's an exact time match at overlapIndex, pop it so
   *    it can be fully replaced by newData below.
   */
  if (overlapIndex >= 0 && oldData[overlapIndex].time === newData[0].time) {
    merged.pop();
  }

  /**
   * 4. Insert all the newData (this overwrites any collided times).
   */
  merged.push(...newData);

  /**
   * 5. Skip over any oldData that is overlapped or replaced by newData's range.
   *    i.e. any oldData whose time <= newData[newData.length - 1].time
   */
  const lastNewTime = newData[newData.length - 1].time;
  let i = overlapIndex + 1;
  while (i < oldData.length && oldData[i].time <= lastNewTime) {
    i++;
  }

  /**
   * 6. Append oldData beyond that time (if any).
   */
  while (i < oldData.length) {
    merged.push(oldData[i]);
    i++;
  }

  return merged;
}
