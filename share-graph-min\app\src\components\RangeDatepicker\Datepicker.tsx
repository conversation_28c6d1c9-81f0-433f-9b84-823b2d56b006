import type React from 'react'
import DatePicker, { DatePickerProps } from 'react-date-picker'
import { useAppStore } from '../../stores/useAppStore'
import 'react-date-picker/dist/DatePicker.css';
import 'react-calendar/dist/Calendar.css';


const Datepicker: React.FC<DatePickerProps> = (props) => {
  const { appSettings } =  useAppStore();

  return (
    <DatePicker {...props} locale={appSettings.general.locale} />
  )
}

export default Datepicker