import { Splitter } from "antd";
import { useDispatch } from "react-redux";
import { useCallback, useRef, useState } from "react";

import AvailableToolSetting from "./AvailableToolSetting";
import ShareGraphSetting from "./tools/ShareGraph/ShareGraphSetting";
import GeneralSettings from "./GeneralSettings";
import { useAppStore } from "../hooks/useAppStore";
import PreviewTools from "./PreviewTools/PreviewTools";
import KeyFormContext from "../context/KeyFormContext";
import {
  setAvailableToolsDraft,
  setBasicSettingsDraft,
  setToolDraft,
} from "../store/slice/forms";
import { SHARE_GRAPH_TOOL } from "../configs/defaultStateSetting";
import { useChangeForm } from "../hooks/useChangeForm";
import useResize from "../hooks/useResize";

export default function SettingForm() {
  const store = useAppStore();
  const availableTools = store.getState().forms.availableTools;
  const generalSettings = store.getState().forms.generalSettings;
  const shareGraphSettings = store.getState().forms.tools.shareGraph;

  const [step, setStep] = useState(2);
  const handleAvailableToolRef = useRef();
  const handleGeneralSettingsRef = useRef();
  const [key, setKey] = useState("abc");
  const handleToolIFrameRef = useRef();

  const dispatch = useDispatch();
  const { width } = useResize();

  useChangeForm((form) => {
    handleToolIFrameRef.current.onReload(form);
  });

  const onChangeFormKey = useCallback(
    () => setKey(Math.random().toString(32).slice(2, 7)),
    []
  );

  return (
    <div className="flex flex-col h-full" key={key}>
      <KeyFormContext.Provider value={{ key, onChangeFormKey }}>
        {width < 768 ? (
          <Splitter
            style={{
              boxShadow: "0 0 10px rgba(0, 0, 0, 0.1)",
            }}
            layout={width < 768 ? "vertical" : "horizontal"}
          >
            <Splitter.Panel defaultSize="50%" min="30%" max="70%">
              <PreviewTools handleRef={handleToolIFrameRef} />
            </Splitter.Panel>
            <Splitter.Panel defaultSize="50%" min="30%" max="70%">
              <div className="flex-1 flex flex-col gap-5 p-5 max-h-[calc(100vh-123px)]">
                <SectionRender label="Available Tool">
                  <AvailableToolSetting
                    handleRef={handleAvailableToolRef}
                    initState={availableTools.draft ?? availableTools.initState}
                    onDraftChange={(state) =>
                      dispatch(setAvailableToolsDraft(state))
                    }
                  />
                </SectionRender>
                <SectionRender label="General Settings">
                  <GeneralSettings
                    handleRef={handleGeneralSettingsRef}
                    initState={
                      generalSettings.draft ?? generalSettings.initState
                    }
                    onDraftChange={(state) => {
                      dispatch(setBasicSettingsDraft(state));
                    }}
                  />
                </SectionRender>
                <SectionRender label="Minimal Share Graph">
                  <ShareGraphSetting
                    handleRef={handleGeneralSettingsRef}
                    initState={
                      shareGraphSettings.draft ?? shareGraphSettings.initState
                    }
                    onDraftChange={(state) =>
                      dispatch(
                        setToolDraft({ toolName: SHARE_GRAPH_TOOL, ...state })
                      )
                    }
                  />
                </SectionRender>
              </div>
            </Splitter.Panel>
          </Splitter>
        ) : (
          <Splitter
            style={{
              boxShadow: "0 0 10px rgba(0, 0, 0, 0.1)",
            }}
            layout={width < 768 ? "vertical" : "horizontal"}
          >
            <Splitter.Panel defaultSize="40%" min="30%" max="70%">
              <div className="flex-1 flex flex-col gap-5 p-5 max-h-[calc(100vh-123px)]">
                <SectionRender label="Available Tool">
                  <AvailableToolSetting
                    handleRef={handleAvailableToolRef}
                    initState={availableTools.draft ?? availableTools.initState}
                    onDraftChange={(state) =>
                      dispatch(setAvailableToolsDraft(state))
                    }
                  />
                </SectionRender>
                <SectionRender label="General Settings" className="mt-3">
                  <GeneralSettings
                    handleRef={handleGeneralSettingsRef}
                    initState={
                      generalSettings.draft ?? generalSettings.initState
                    }
                    onDraftChange={(state) => {
                      dispatch(setBasicSettingsDraft(state));
                    }}
                  />
                </SectionRender>
                <SectionRender label="Minimal Share Graph" className="mt-3">
                  <ShareGraphSetting
                    handleRef={handleGeneralSettingsRef}
                    initState={
                      shareGraphSettings.draft ?? shareGraphSettings.initState
                    }
                    onDraftChange={(state) =>
                      dispatch(
                        setToolDraft({ toolName: SHARE_GRAPH_TOOL, ...state })
                      )
                    }
                  />
                </SectionRender>
              </div>
            </Splitter.Panel>
            <Splitter.Panel>
              <PreviewTools handleRef={handleToolIFrameRef} />
            </Splitter.Panel>
          </Splitter>
        )}
      </KeyFormContext.Provider>
    </div>
  );
}

const SectionRender = ({ label, children, className = "" }) => {
  return (
    <div className={className}>
      <p className="bg-slate-200 px-3 py-3 rounded-md text-[16px] font-semibold">
        {label}
      </p>
      <div className="mt-5">{children}</div>
    </div>
  );
};
