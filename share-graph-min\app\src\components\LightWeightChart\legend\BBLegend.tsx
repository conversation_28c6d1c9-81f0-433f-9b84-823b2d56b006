import { FC } from 'react';
import { Legend } from './Legend';
import { BBIndicator } from "@sharegraph-mini/advance-charts";
import { AdvanceChart } from "@sharegraph-mini/advance-charts";

const BBLegend: FC<{
  indicator: BBIndicator;
  advanceChart: AdvanceChart;
}> = ({ indicator, advanceChart }) => {
  return (
    <Legend
      name="BB"
      indicator={indicator}
      renderer={(d) =>
        d && d.value ? (
          <>
            {' '}
            <span style={{ color: indicator.options.middleLineColor }}>
              {advanceChart.numberFormatter.decimal(d.value[1])}
            </span>{' '}
            <span style={{ color: indicator.options.upperLineColor }}>
              {advanceChart.numberFormatter.decimal(d.value[0])}
            </span>{' '}
            <span style={{ color: indicator.options.lowerLineColor }}>
              {advanceChart.numberFormatter.decimal(d.value[2])}
            </span>{' '}
          </>
        ) : null
      }
    />
  );
};

export default BBLegend;
