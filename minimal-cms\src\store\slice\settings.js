import { createSlice } from "@reduxjs/toolkit";

import {
  applySettingsAction,
  getAvailableLanguagesAction,
  getAvailableToolsAction,
  getCompanyCodeByIdAction,
  getCurrenciesAction,
  getInstrumentByCompanyCodeAction,
  getFontAction,
  getSessionStateAction,
  getTimeZonesAction,
  saveSessionStateAction,
  uploadFontAction,
  deleteFontByIdAction,
  deleteFontByFontFamilyAction,
  generateLinkAction,
  getVersionsAction,
  getOldSolutionsAndVersionsToolDataAction,
  checkExistingVersionAction,
  getCssVersionsAction,
  getCustomePhraseAction,
} from "../actions";
import { normalizeData, trimObjectProperties } from "../../utils/common";
import {
  savePackageAction,
  getSavedPackagesAction,
} from "../actions/packageAction";
import {
  DEFAULT_AVAILABLE_TOOLS,
  defaultInstruments,
} from "../../configs/defaultStateSetting";

const initialState = {
  availableLanguages: {
    data: {},
    ids: [],
    loading: false,
    error: null,
    selectedIds: [],
  },
  companyCodes: {
    data: {},
    ids: [],
    keywordLoadMore: "",
    loadMoreIds: [],
    isLoadMore: true,
    loading: false,
    error: null,
  },
  availableTools: {
    data: /** @type {Record<string, {name: string, value: string, shortName: string, entryModuleUrl: string, toolNameIntegration: string, defaultSettings: any, validationRule: Array<{Context: string, Assertions: Array<{Expression: string, InvalidationMessage: string}>}>}>} */ ({}),
    ids: [],
    loading: false,
    error: null,
    selectedIds: [],
  },
  tools: {
    data: /** @type {Array<{name: string, entry: string}>} */ ([]),
    loading: false,
    error: null,
  },
  timeZones: {
    data: {},
    ids: [],
    loading: false,
    error: null,
  },
  currencies: {
    data: {},
    ids: [],
    loading: false,
    error: null,
  },
  instrumentsByCompanyCode: {
    data: defaultInstruments.reduce((s, item) => {
      s[item.id] = item;
      return s;
    }, {}),
    companyCode: null,
    ids: defaultInstruments.map((item) => item.id),
    loading: false,
    error: null,
  },
  sessionState: {},
  saveSessionState: {
    loading: false,
    error: null,
  },
  getSessionState: {
    loading: false,
    error: null,
  },
  packagesState: {},
  savePackage: {
    loading: false,
    error: null,
  },
  getSavedPackages: {
    ids: [],
    loading: false,
    error: null,
  },
  apply: {
    data: {
      companyCode: "",
      versions: "",
      availableLanguages: [],
      selectedLanguage: "",
      availableTools: [],
      selectedTool: "",
    },
    loading: false,
    error: null,
  },
  generateLink: {
    data: {},
    loading: false,
    error: null,
  },
  fonts: {
    data: {},
    ids: [],
    defaultFonts: [],
    loading: false,
    error: null,
    selectedFontFamily: "",
    deletingId: null,
    deletingFontFamily: null,
  },
  getXMLState: {
    data: {},
    loading: false,
    error: null,
  },
  versionsByCompanyCode: {
    versions: {},
    companyCode: "",
    tool: "",
    loading: false,
    error: null,
  },
  cssVersionsByCompanyCode: {
    versions: {},
    companyCode: "",
    tool: "",
    loading: false,
    error: null,
  },
  oldSolutionsAndVersions: {
    versionIds: [],
    versions: {},
    companyCode: "",
    loading: false,
    error: null,
  },
  migrate: {
    data: {},
    solutionId: "",
    loading: false,
    error: null,
  },
  existingVersion: {
    data: {},
    loading: false,
    error: null,
  },
  customPhrases: {
    data: {},
    loading: false,
    error: null,
    changeBy: null,
  },
  isLastSavePopupTriggered: false,
};
const settingsSlice = createSlice({
  name: "settings",
  initialState,
  reducers: {
    setSelectedLanguages: (state, action) => {
      state.availableLanguages.selectedIds = action.payload;
    },
    setSelectedTools: (state, action) => {
      state.availableTools.selectedIds = action.payload;
    },
    setSelectedFontFamily: (state, action) => {
      state.fonts.selectedFontFamily = action.payload;
    },
    setAppliedTool: (state, action) => {
      state.apply.data.selectedTool = action.payload;
    },
    setAppliedLanguage: (state, action) => {
      state.apply.data.selectedLanguage = action.payload;
    },
    resetApplyState: (state, action) => {
      state.apply = initialState.apply;
    },
    resetVersionByCompanyCode: (state) => {
      state.versionsByCompanyCode = initialState.versionsByCompanyCode;
    },
    resetGenerate: (state, action) => {
      state.generateLink = initialState.generateLink;
    },
    setLastSavePopupTriggered(state, { payload }) {
      state.isLastSavePopupTriggered = payload;
    },
    resetCompanyCode: (state) => {
      state.companyCodes = initialState.companyCodes;
    },
  },
  extraReducers: (builder) => {
    // companyCode
    builder.addCase(getCompanyCodeByIdAction.pending, (state) => {
      state.companyCodes.loading = true;
    });
    builder.addCase(getCompanyCodeByIdAction.fulfilled, (state, action) => {
      const { data } = action.payload;
      state.companyCodes.data = data.reduce(
        (s, companyCode) => ({
          ...s,
          [companyCode.id]: trimObjectProperties(companyCode),
        }),
        state.companyCodes.data
      );
      state.companyCodes.ids = data.map((companyCode) => companyCode.id);

      state.companyCodes.isLoadMore = true;
      if (data.length < 1) {
        state.companyCodes.isLoadMore = false;
      }

      if (state.companyCodes.keywordLoadMore === action.meta.arg.companyCode) {
        state.companyCodes.ids.forEach((id) => {
          if (!state.companyCodes.loadMoreIds.includes(id)) {
            state.companyCodes.loadMoreIds =
              state.companyCodes.loadMoreIds.concat(id);
          }
        });
      } else {
        state.companyCodes.loadMoreIds = state.companyCodes.ids;
      }
      state.companyCodes.keywordLoadMore = action.meta.arg.companyCode;
      state.companyCodes.loading = false;
    });
    builder.addCase(getCompanyCodeByIdAction.rejected, (state, action) => {
      state.companyCodes.error = action.error;
      state.companyCodes.loading = false;
    });
    // availableLanguages
    builder.addCase(getAvailableLanguagesAction.pending, (state) => {
      state.availableLanguages.loading = true;
    });
    builder.addCase(getAvailableLanguagesAction.fulfilled, (state, action) => {
      const { data } = action.payload;
      state.availableLanguages.data = data.reduce((s, availableLanguage) => {
        availableLanguage.label = availableLanguage.value;
        availableLanguage.id = availableLanguage.value;
        return {
          ...s,
          [availableLanguage.value]: trimObjectProperties(availableLanguage),
        };
      }, {});
      state.availableLanguages.ids = data.map(
        (availableLanguage) => availableLanguage.value
      );
      state.availableLanguages.loading = false;
    });
    builder.addCase(getAvailableLanguagesAction.rejected, (state, action) => {
      state.availableLanguages.error = action.error;
      state.availableLanguages.loading = false;
    });

    // availableTools
    builder.addCase(getAvailableToolsAction.pending, (state) => {
      state.availableTools.loading = true;
    });
    builder.addCase(getAvailableToolsAction.fulfilled, (state, action) => {
      const { data } = action.payload;
      state.availableTools.data = data.reduce((s, availableTool) => {
        return {
          ...s,
          [availableTool.value]: trimObjectProperties(availableTool),
        };
      }, {});
      state.availableTools.ids = data.map(
        (availableTool) => availableTool.value
      );
      state.availableTools.loading = false;
    });
    builder.addCase(getAvailableToolsAction.rejected, (state, action) => {
      state.availableTools.error = action.error;
      state.availableTools.loading = false;
    });

    // timeZones
    builder.addCase(getTimeZonesAction.pending, (state) => {
      state.timeZones.loading = true;
    });
    builder.addCase(getTimeZonesAction.fulfilled, (state, action) => {
      const data = action.payload;
      state.timeZones.data = data.reduce((s, timezone) => {
        return {
          ...s,
          [timezone.value]: trimObjectProperties(timezone),
        };
      }, {});
      state.timeZones.ids = data.map((timezone) => timezone.value);
      state.timeZones.loading = false;
    });
    builder.addCase(getTimeZonesAction.rejected, (state, action) => {
      state.timeZones.error = action.error;
      state.timeZones.loading = false;
    });

    // currencies
    builder.addCase(getCurrenciesAction.pending, (state) => {
      state.currencies.loading = true;
    });
    builder.addCase(getCurrenciesAction.fulfilled, (state, action) => {
      const { data } = action.payload;
      const transformData = data.map(({ value, name }) => ({
        id: value,
        label: name,
      }));
      state.currencies.data = transformData.reduce((s, currency) => {
        return {
          ...s,
          [currency.id]: trimObjectProperties(currency),
        };
      }, {});
      state.currencies.ids = transformData.map((currency) => currency.id);
      state.currencies.loading = false;
    });
    builder.addCase(getCurrenciesAction.rejected, (state, action) => {
      state.currencies.error = action.error;
      state.currencies.loading = false;
    });

    // instrument
    builder.addCase(getInstrumentByCompanyCodeAction.pending, (state) => {
      state.instrumentsByCompanyCode.loading = true;
    });
    builder.addCase(
      getInstrumentByCompanyCodeAction.fulfilled,
      (state, action) => {
        const { instruments, companyCode } = action.payload;
        state.instrumentsByCompanyCode.companyCode = companyCode;
        state.instrumentsByCompanyCode.data = instruments.reduce(
          (s, currency) => {
            return {
              ...s,
              [currency.id]: trimObjectProperties(currency),
            };
          },
          {}
        );
        state.instrumentsByCompanyCode.ids = instruments.map(
          (currency) => currency.id
        );
        state.instrumentsByCompanyCode.loading = false;
      }
    );
    builder.addCase(
      getInstrumentByCompanyCodeAction.rejected,
      (state, action) => {
        state.instrumentsByCompanyCode.error = action.error;
        state.instrumentsByCompanyCode.loading = false;
        state.instrumentsByCompanyCode.companyCode = null;
      }
    );

    // save session state
    builder.addCase(saveSessionStateAction.pending, (state) => {
      state.saveSessionState.loading = true;
    });
    builder.addCase(saveSessionStateAction.fulfilled, (state, action) => {
      state.saveSessionState.loading = false;
      state.saveSessionState.error = null;
    });
    builder.addCase(saveSessionStateAction.rejected, (state, action) => {
      state.saveSessionState.error = action.error;
      state.saveSessionState.loading = false;
    });

    // get session state
    builder.addCase(getSessionStateAction.pending, (state) => {
      state.getSessionState.loading = true;
    });
    builder.addCase(getSessionStateAction.fulfilled, (state, action) => {
      const data = action.payload.data;
      state.sessionState = data;
      state.getSessionState.loading = false;
      state.getSessionState.error = null;
    });
    builder.addCase(getSessionStateAction.rejected, (state, action) => {
      state.getSessionState.error = action.error;
      state.getSessionState.loading = false;
    });

    builder.addCase(savePackageAction.pending, (state) => {
      state.savePackage.loading = true;
    });
    builder.addCase(savePackageAction.fulfilled, (state, action) => {
      state.savePackage.loading = false;
      state.savePackage.error = null;
    });
    builder.addCase(savePackageAction.rejected, (state, action) => {
      state.savePackage.error = action.error;
      state.savePackage.loading = false;
    });

    builder.addCase(getSavedPackagesAction.pending, (state) => {
      state.getSavedPackages.loading = true;
    });
    builder.addCase(getSavedPackagesAction.fulfilled, (state, action) => {
      if (action.payload) {
        state.packagesState = (action.payload || []).reduce((s, pk) => {
          s[pk.id] = pk;
          return s;
        }, {});
      }
      state.getSavedPackages.ids = (action.payload || []).map((pk) => pk.id);
      state.getSavedPackages.loading = false;
      state.getSavedPackages.error = null;
    });
    builder.addCase(getSavedPackagesAction.rejected, (state, action) => {
      state.getSavedPackages.error = action.error;
      state.getSavedPackages.loading = false;
    });

    // apply
    builder.addCase(applySettingsAction.pending, (state) => {
      state.apply.loading = true;
    });
    builder.addCase(applySettingsAction.fulfilled, (state, action) => {
      const data = action.payload;
      state.apply.data = data;
      state.apply.loading = false;
      state.apply.error = null;
    });
    builder.addCase(applySettingsAction.rejected, (state, action) => {
      state.apply.error = action.error;
      state.apply.loading = false;
    });

    // generate link
    builder.addCase(generateLinkAction.pending, (state) => {
      state.generateLink.loading = true;
    });
    builder.addCase(generateLinkAction.fulfilled, (state, action) => {
      const data = action.payload.data;
      state.generateLink.data = data;
      state.generateLink.loading = false;
      state.generateLink.error = null;
    });
    builder.addCase(generateLinkAction.rejected, (state, action) => {
      state.generateLink.error = action.payload || action.error;
      state.generateLink.loading = false;
    });

    // upload font
    builder.addCase(uploadFontAction.pending, (state) => {
      state.fonts.loading = true;
    });
    builder.addCase(uploadFontAction.fulfilled, (state, action) => {
      const { data } = action.payload;
      const { entities, result } = normalizeData(data);

      state.fonts.data = { ...state.fonts.data, ...entities };
      state.fonts.ids = [...state.fonts.ids, ...result];
      state.fonts.loading = false;
    });
    builder.addCase(uploadFontAction.rejected, (state, action) => {
      state.fonts.error = action.error;
      state.fonts.loading = false;
    });

    // get font
    builder.addCase(getFontAction.pending, (state) => {
      state.fonts.loading = true;
    });
    builder.addCase(getFontAction.fulfilled, (state, action) => {
      const { customFonts, defaultFonts } = action.payload.data;
      state.fonts.data = customFonts.reduce((s, font) => {
        return {
          ...s,
          [font.id]: trimObjectProperties(font),
        };
      }, {});
      state.fonts.loading = false;
      state.fonts.ids = customFonts.map((font) => font.id);
      state.fonts.defaultFonts = defaultFonts;
    });
    builder.addCase(getFontAction.rejected, (state, action) => {
      state.fonts.error = action.error;
      state.fonts.loading = false;
    });

    // delete font by id
    builder.addCase(deleteFontByIdAction.pending, (state, action) => {
      state.fonts.loading = true;
      state.fonts.deletingId = action.meta.arg;
    });
    builder.addCase(deleteFontByIdAction.fulfilled, (state, action) => {
      const fontId = action.payload.data;
      const fontsById = { ...state.fonts.data };
      const fontIds = [...state.fonts.ids];

      delete fontsById[fontId];
      const filteredFontIds = fontIds.filter((id) => id !== fontId);

      state.fonts.ids = filteredFontIds;
      state.fonts.data = fontsById;
      state.fonts.loading = false;
      state.fonts.deletingId = null;
    });
    builder.addCase(deleteFontByIdAction.rejected, (state, action) => {
      state.fonts.error = action.error;
      state.fonts.loading = false;
      state.fonts.deletingId = null;
    });

    // delete font by font family
    builder.addCase(deleteFontByFontFamilyAction.pending, (state, action) => {
      state.fonts.loading = true;
      state.fonts.deletingFontFamily = action.meta.arg;
    });
    builder.addCase(deleteFontByFontFamilyAction.fulfilled, (state, action) => {
      const ids = action.payload.data;
      const fontsById = { ...state.fonts.data };
      const fontIds = [...state.fonts.ids];
      ids.forEach((id) => {
        delete fontsById[id];
      });
      const filteredFontIds = fontIds.filter((id) => !ids.includes(id));
      state.fonts.ids = filteredFontIds;
      state.fonts.data = fontsById;
      state.fonts.loading = false;
      state.fonts.deletingFontFamily = null;
    });
    builder.addCase(deleteFontByFontFamilyAction.rejected, (state, action) => {
      state.fonts.error = action.error;
      state.fonts.loading = false;
      state.fonts.deletingFontFamily = null;
    });

    // get versions by companyCode
    builder.addCase(getVersionsAction.pending, (state, action) => {
      state.versionsByCompanyCode.loading = true;
    });
    builder.addCase(getVersionsAction.fulfilled, (state, action) => {
      const { versions, companyCode, tool } = action.payload;
      state.versionsByCompanyCode.versions = versions;
      state.versionsByCompanyCode.companyCode = companyCode;
      state.versionsByCompanyCode.tool = tool;
      state.versionsByCompanyCode.loading = false;
      state.versionsByCompanyCode.error = null;
    });
    builder.addCase(getVersionsAction.rejected, (state, action) => {
      state.versionsByCompanyCode.error = action.error;
      state.versionsByCompanyCode.loading = false;
    });

    // get css versions by companyCode
    builder.addCase(getCssVersionsAction.pending, (state, action) => {
      state.cssVersionsByCompanyCode.loading = true;
    });
    builder.addCase(getCssVersionsAction.fulfilled, (state, action) => {
      const { versions, companyCode, tool } = action.payload;
      state.cssVersionsByCompanyCode.versions = versions;
      state.cssVersionsByCompanyCode.companyCode = companyCode;
      state.cssVersionsByCompanyCode.tool = tool;
      state.cssVersionsByCompanyCode.loading = false;
      state.cssVersionsByCompanyCode.error = null;
    });
    builder.addCase(getCssVersionsAction.rejected, (state, action) => {
      state.cssVersionsByCompanyCode.error = action.error;
      state.cssVersionsByCompanyCode.loading = false;
    });

    builder.addCase(
      getOldSolutionsAndVersionsToolDataAction.pending,
      (state, action) => {
        state.oldSolutionsAndVersions.loading = true;
      }
    );
    builder.addCase(
      getOldSolutionsAndVersionsToolDataAction.fulfilled,
      (state, action) => {
        const { versions, companyCode } = action.payload;
        state.oldSolutionsAndVersions.versionIds = versions.map(
          (verItem) => verItem.version || verItem.solutionId
        );
        state.oldSolutionsAndVersions.versions = versions.reduce(
          (acc, verItem) => {
            acc[verItem.version || verItem.solutionId] = verItem;
            return acc;
          },
          {}
        );

        state.oldSolutionsAndVersions.companyCode = companyCode;
        state.oldSolutionsAndVersions.loading = false;
        state.oldSolutionsAndVersions.error = null;
      }
    );
    builder.addCase(
      getOldSolutionsAndVersionsToolDataAction.rejected,
      (state, action) => {
        state.oldSolutionsAndVersions.error = action.error;
        state.oldSolutionsAndVersions.loading = false;
      }
    );

    // check existing version
    builder.addCase(checkExistingVersionAction.pending, (state) => {
      state.existingVersion.loading = true;
    });
    builder.addCase(checkExistingVersionAction.fulfilled, (state, action) => {
      state.existingVersion.data = action.payload;
      state.existingVersion.loading = false;
      state.existingVersion.error = null;
    });
    builder.addCase(checkExistingVersionAction.rejected, (state, action) => {
      state.existingVersion.error = action.payload;
      state.existingVersion.loading = false;
    });

    // CustomPhrases
    builder.addCase(getCustomePhraseAction.pending, (state) => {
      state.customPhrases.loading = true;
    });
    builder.addCase(getCustomePhraseAction.fulfilled, (state, action) => {
      const { data, changeBy } = action.payload;
      state.customPhrases.data = data.reduce((s, { tool, translations }) => {
        const listTranslation = translations.reduce(
          (listCustomPhrase, { languageCode, customPhrases }) => {
            listCustomPhrase[languageCode] = customPhrases;
            return listCustomPhrase;
          },
          {}
        );
        s[tool] = listTranslation;
        return s;
      }, {});
      state.customPhrases.loading = false;
      state.customPhrases.error = null;
      state.customPhrases.changeBy = changeBy;
    });
    builder.addCase(getCustomePhraseAction.rejected, (state, action) => {
      state.customPhrases.error = action.payload;
      state.customPhrases.loading = false;
    });
  },
});

export const {
  setSelectedLanguages,
  setSelectedTools,
  setSelectedFontFamily,
  setAppliedTool,
  setAppliedLanguage,
  resetApplyState,
  resetVersionByCompanyCode,
  resetGenerate,
  setLastSavePopupTriggered,
  resetCompanyCode,
} = settingsSlice.actions;
export default settingsSlice;
