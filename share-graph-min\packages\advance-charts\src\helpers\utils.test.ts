import { describe, it, expect } from "vitest";
import { timeToDayjs, binarySearchIndex, binarySearch, dateToTime } from "./utils";
import dayjs from "dayjs";
import { UTCTimestamp } from "lightweight-charts";

describe('utils', () => {
  describe('timeToDayjs', () => {
    it('should convert various time formats to Dayjs objects', () => {
      // Test core behavior: handles different input types
      const timestamp = Math.round(Date.now() / 1000) as UTCTimestamp;
      const timeString = "2024-12-23T12:00:00Z";
      const timeObject = { year: 2024, month: 12, day: 23 };

      const resultNumber = timeToDayjs(timestamp);
      const resultString = timeToDayjs(timeString);
      const resultObject = timeToDayjs(timeObject);

      expect(resultNumber.isValid()).toBe(true);
      expect(resultString.isSame(dayjs(timeString))).toBe(true);
      expect(resultObject.isSame(dayjs(new Date(2024, 11, 23)))).toBe(true);
    });
  });

  describe('binarySearchIndex', () => {
    // Helper for test data
    const sortedArray = [1, 3, 5, 7, 9];

    it('should find index of existing element', () => {
      const index = binarySearchIndex(sortedArray, 5, (x) => x);
      
      expect(index).toBe(2);
    });

    it('should return -1 for non-existent element', () => {
      const index = binarySearchIndex(sortedArray, 4, (x) => x);
      
      expect(index).toBe(-1);
    });

    it('should work with custom comparison', () => {
      const reverseArray = [9, 7, 5, 3, 1];
      const customCompare = (a: number, b: number) => b - a;
      
      const index = binarySearchIndex(reverseArray, 5, (x) => x, customCompare);
      
      expect(index).toBe(2);
    });
  });

  describe('binarySearch', () => {
    // Helper for test data
    const testObjects = [{ id: 1 }, { id: 3 }, { id: 5 }, { id: 7 }, { id: 9 }];

    it('should find element by key', () => {
      const result = binarySearch(testObjects, 5, (item) => item.id);
      
      expect(result).toEqual({ id: 5 });
    });

    it('should return undefined when element not found', () => {
      const result = binarySearch(testObjects, 4, (item) => item.id);
      
      expect(result).toBeUndefined();
    });

    it('should handle empty array gracefully', () => {
      const result = binarySearch([], 5, (x) => x);
      
      expect(result).toBeUndefined();
    });

    it('should work with single element', () => {
      const singleItem = [{ id: 5 }];
      
      const found = binarySearch(singleItem, 5, (item) => item.id);
      const notFound = binarySearch(singleItem, 3, (item) => item.id);
      
      expect(found).toEqual({ id: 5 });
      expect(notFound).toBeUndefined();
    });
  });

  describe('dateToTime', () => {
    it('should convert Date to Unix timestamp in seconds', () => {
      // Use a known date for predictable result
      const testDate = new Date('2024-12-23T12:00:00.000Z');
      const expectedTimestamp = Math.floor(testDate.getTime() / 1000);
      
      const result = dateToTime(testDate);
      
      expect(result).toBe(expectedTimestamp);
    });
  });
});

