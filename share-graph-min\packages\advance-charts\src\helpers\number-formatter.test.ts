import {describe, expect, it, vi} from "vitest";
import {NumberFormatter} from "./number-formatter";

describe('NumberFormatter', () => {
  // Helper for test formatter
  const formatter = new NumberFormatter();

  describe('volume formatting', () => {
    it('should format large numbers with compact notation', () => {
      const result = formatter.volume(1234567);
      
      expect(result).toBe('1.23M');
    });

    it('should handle null values gracefully', () => {
      const result = formatter.volume(null);
      
      expect(result).toBe('');
    });

    it('should handle negative numbers', () => {
      const result = formatter.volume(-1000);
      
      expect(result).toBe('-1K');
    });
  });

  describe('decimal formatting', () => {
    it('should format numbers with appropriate precision', () => {
      const result = formatter.decimal(0.123456);
      
      expect(result).toBe('0.123');
    });

    it('should handle large numbers with thousand separators', () => {
      const formatter = new NumberFormatter('en-gb');
      const result = formatter.decimal(-1234.567);
      
      expect(result).toBe('-1,234.567');
    });
  });

  describe('percent formatting', () => {
    it('should format percentages with two decimal places', () => {
      const result = formatter.percent(1.2345); // 123.45%
      
      expect(result).toBe('123.45%');
    });

    it('should handle negative percentages', () => {
      const result = formatter.percent(-0.1234);
      
      expect(result).toBe('-12.34%');
    });
  });

  describe('lazy initialization', () => {
    it('should initialize formatters only once', () => {
      const testFormatter = new NumberFormatter();
      const spy = vi.spyOn(Intl, 'NumberFormat');

      // First call initializes
      testFormatter.volume(1000);
      expect(spy).toHaveBeenCalledTimes(1);

      // Second call reuses formatter
      testFormatter.volume(2000);
      expect(spy).toHaveBeenCalledTimes(1);

      spy.mockRestore();
    });
  });

  describe('edge cases', () => {
    it('should handle special values', () => {
      // Test core behavior: handles special JavaScript number values
      expect(formatter.volume(0)).toBe('0');
      expect(formatter.volume(NaN)).toBe('NaN');
      expect(formatter.percent(0)).toBe('0.00%');
    });

    it('should handle null/undefined consistently', () => {
      // Test core behavior: all methods handle nullish values the same way
      expect(formatter.volume(null)).toBe('');
      expect(formatter.decimal(undefined)).toBe('');
      expect(formatter.percent(null)).toBe('');
    });
  });
});