import { Flex, Form, Select, Switch } from "antd";
import React, { useEffect } from "react";
import { useInputStyle } from "../../../hooks/useInputStyle";
import { i18n } from "@euroland/libs";
import { getIntervalLabel } from "../../../constants/common";
import { TIME_INTERVALS } from "../../../constants/chartConstant";

export default function DefaultRangeInterval({ onChange }) {
  const { inputStyle } = useInputStyle();
  const form = Form.useFormInstance();
  const dateRangesAndInterval =
    Form.useWatch("dateRangesAndInterval", form) || [];
  const defaultPeriod = Form.useWatch(["defaultRange", "period"], form);
  const defaultIntervals =
    dateRangesAndInterval.find((range) => range.period === defaultPeriod)
      ?.intervals || [];

  useEffect(() => {
    const defaultRange = structuredClone(form.getFieldValue("defaultRange"));
    if (!dateRangesAndInterval?.length) return;
    const defaultDateRangesAndInterval = dateRangesAndInterval.find(
      (range) => range.period === defaultRange.period
    );
    if (!defaultDateRangesAndInterval) {
      const newDefaultRange = {
        period: dateRangesAndInterval[0]?.period,
        interval: dateRangesAndInterval[0]?.intervals?.[0],
      };
      onChange(newDefaultRange);
      return;
    }
    const defaultIntervals = defaultDateRangesAndInterval.intervals;
    if (
      !defaultIntervals.some((interval) => interval === defaultRange.interval)
    ) {
      const newDefaultRange = {
        period: defaultDateRangesAndInterval.period,
        interval: defaultIntervals[0],
      };
      onChange(newDefaultRange);
      return;
    }
  }, [dateRangesAndInterval]);

  useEffect(() => {
    const defaultRange = structuredClone(form.getFieldValue("defaultRange"));
    const dateRangesAndIntervalState = structuredClone(
      form.getFieldValue("dateRangesAndInterval")
    );
    const defaultIntervals = dateRangesAndIntervalState.find(
      (range) => range.period === defaultPeriod
    )?.intervals;
    if (!defaultPeriod || !defaultIntervals?.length) return;
    if (
      !defaultIntervals.some((interval) => interval === defaultRange.interval)
    ) {
      const newDefaultRange = {
        period: defaultRange.period,
        interval: defaultIntervals[0],
      };
      onChange(newDefaultRange);
    }
  }, [defaultPeriod]);

  return (
    <Flex gap={20}>
      <Form.Item
        style={{ ...inputStyle, marginBottom: 0 }}
        name={["defaultRange", "period"]}
        label={"Default range"}
      >
        <Select
          options={dateRangesAndInterval.map((range) => ({
            value: range.period,
            label: i18n.translate(range.period),
          }))}
        />
      </Form.Item>
      <Form.Item
        style={{ ...inputStyle, marginBottom: 0 }}
        name={["defaultRange", "interval"]}
        label={"Default interval"}
      >
        <Select
          options={defaultIntervals.map((interval) => {
            const intervalOption = TIME_INTERVALS[interval];
            const intervalValue = intervalOption.value;
            return {
              value: intervalValue,
              label: getIntervalLabel(
                intervalOption.label,
                intervalOption.interval.times
              ),
            };
          })}
        />
      </Form.Item>
    </Flex>
  );
}
