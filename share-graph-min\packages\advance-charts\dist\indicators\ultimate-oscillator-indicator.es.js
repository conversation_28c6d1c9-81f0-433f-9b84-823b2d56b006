var V = Object.defineProperty;
var H = (r, e, t) => e in r ? V(r, e, { enumerable: !0, configurable: !0, writable: !0, value: t }) : r[e] = t;
var P = (r, e, t) => H(r, typeof e != "symbol" ? e + "" : e, t);
import { LineSeries as R } from "lightweight-charts";
import { ChartIndicator as W } from "./abstract-indicator.es.js";
import { RegionPrimitive as C } from "../custom-primitive/primitive/region.es.js";
import { autoScaleInfoProviderCreator as A } from "../helpers/utils.es.js";
const T = {
  color: "#3179f5",
  // for Ultimate Oscillator
  priceLineColor: "#a1c3ff",
  backgroundColor: "#d2e0fa",
  period1: 7,
  // Short period as per TradingView standard
  period2: 14,
  // Medium period as per TradingView standard
  period3: 28,
  // Long period as per TradingView standard
  weight1: 4,
  // Weight for short period
  weight2: 2,
  // Weight for medium period
  weight3: 1,
  // Weight for long period
  overlay: !1
};
class G extends W {
  constructor(t, o, i) {
    super(t, o);
    P(this, "ultimateOscillatorSeries");
    this.ultimateOscillatorSeries = t.addSeries(R, {
      color: this.options.color,
      lineWidth: 2,
      priceLineVisible: !1,
      crosshairMarkerVisible: !1,
      priceScaleId: "ultimateoscillator",
      autoscaleInfoProvider: A({ maxValue: 100, minValue: 0 })
    }, i), this.ultimateOscillatorSeries.attachPrimitive(
      new C({
        upPrice: 75,
        lowPrice: 70,
        lineColor: this.options.priceLineColor,
        backgroundColor: this.options.backgroundColor
      })
    ), this.ultimateOscillatorSeries.attachPrimitive(
      new C({
        upPrice: 30,
        lowPrice: 25,
        lineColor: this.options.priceLineColor,
        backgroundColor: this.options.backgroundColor
      })
    );
  }
  getDefaultOptions() {
    return T;
  }
  formula(t) {
    const o = this.options.period1, i = this.options.period2, s = this.options.period3, h = this.options.weight1, m = this.options.weight2, d = this.options.weight3, O = t.symbol.high, x = t.symbol.low, g = t.symbol.close, l = Math.max(o, i, s), f = t.new_var(g, l + 1);
    if (!f.calculable()) return;
    const v = f.get(1), w = Math.min(x, v), I = g - w, y = Math.max(O, v) - w, n = t.new_var(I, l), c = t.new_var(y, l);
    if (!n.calculable() || !c.calculable() || !n.calculable() || !c.calculable()) return;
    const u = (M) => {
      let b = 0, p = 0;
      for (let a = 0; a < M; a++)
        b += n.get(a), p += c.get(a);
      return p > 0 ? b / p : 0;
    }, k = u(o), L = u(i), D = u(s), _ = h * k + m * L + d * D, S = h + m + d;
    return [S > 0 ? 100 * (_ / S) : 0];
  }
  applyIndicatorData() {
    const t = [];
    for (const o of this._executionContext.data) {
      const i = o.value;
      if (!i) continue;
      const s = o.time;
      t.push({ time: s, value: i[0] });
    }
    this.ultimateOscillatorSeries.setData(t);
  }
  remove() {
    super.remove(), this.chart.removeSeries(this.ultimateOscillatorSeries);
  }
  _applyOptions() {
    this.ultimateOscillatorSeries.applyOptions({ color: this.options.color }), this.applyIndicatorData();
  }
  setPaneIndex(t) {
    this.ultimateOscillatorSeries.moveToPane(t);
  }
  getPaneIndex() {
    return this.ultimateOscillatorSeries.getPane().paneIndex();
  }
}
export {
  G as default,
  T as defaultOptions
};
//# sourceMappingURL=ultimate-oscillator-indicator.es.js.map
