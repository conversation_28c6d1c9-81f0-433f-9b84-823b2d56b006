{"version": 3, "file": "dmi-indicator.es.js", "sources": ["../../src/indicators/dmi-indicator.ts"], "sourcesContent": ["import {IChartApi, ISeriesApi, LineSeries, Nominal, SeriesType, SingleValueData, Time} from \"lightweight-charts\";\nimport {ChartIndicator, ChartIndicatorOptions} from \"./abstract-indicator\";\nimport {RegionPrimitive} from \"../custom-primitive/primitive/region\";\nimport {autoScaleInfoProviderCreator} from \"../helpers/utils\";\nimport {Context} from \"../helpers/execution-indicator\";\n\nexport interface DMIIndicatorOptions extends ChartIndicatorOptions {\n  adxColor: string,\n  plusDIColor: string,\n  minusDIColor: string,\n  period: number,\n  priceLineColor: string,\n  backgroundColor: string\n}\n\nexport const defaultOptions: DMIIndicatorOptions = {\n  adxColor: \"#f23645\",     // Orange for ADX\n  plusDIColor: \"#2962ff\",   // Green for +DI\n  minusDIColor: \"#ff6d00\",  // Red for -DI\n  priceLineColor: \"rgba(150, 150, 150, 0.35)\",\n  backgroundColor: '#ff98001a',\n  period: 14,\n  overlay: false\n}\n\nexport type ADXLine = Nominal<number, 'ADX'>\nexport type PlusDILine = Nominal<number, 'PlusDI'>\nexport type MinusDILine = Nominal<number, 'MinusDI'>\n\nexport type DMIData = [ADXLine, PlusDILine, MinusDILine]\n\nexport default class DMIIndicator extends ChartIndicator<DMIIndicatorOptions, DMIData> {\n  adxSeries: ISeriesApi<SeriesType>\n  plusDISeries: ISeriesApi<SeriesType>\n  minusDISeries: ISeriesApi<SeriesType>\n\n  constructor(chart: IChartApi, options?: Partial<DMIIndicatorOptions>, paneIndex?: number) {\n    super(chart, options)\n    \n    this.adxSeries = chart.addSeries(LineSeries, {\n      color: this.options.adxColor,\n      lineWidth: 2,\n      priceLineVisible: false,\n      crosshairMarkerVisible: false,\n      priceScaleId: 'dmi',\n      autoscaleInfoProvider: autoScaleInfoProviderCreator({maxValue: 100, minValue: 0})\n    }, paneIndex);\n    \n    this.plusDISeries = chart.addSeries(LineSeries, {\n      color: this.options.plusDIColor,\n      lineWidth: 1,\n      priceLineVisible: false,\n      crosshairMarkerVisible: false,\n      priceScaleId: 'dmi',\n      autoscaleInfoProvider: autoScaleInfoProviderCreator({maxValue: 100, minValue: 0})\n    }, paneIndex);\n    \n    this.minusDISeries = chart.addSeries(LineSeries, {\n      color: this.options.minusDIColor,\n      lineWidth: 1,\n      priceLineVisible: false,\n      crosshairMarkerVisible: false,\n      priceScaleId: 'dmi',\n      autoscaleInfoProvider: autoScaleInfoProviderCreator({maxValue: 100, minValue: 0})\n    }, paneIndex);\n    \n    this.adxSeries.attachPrimitive(\n      new RegionPrimitive({\n        upPrice: 25,\n        lowPrice: 20,\n        lineColor: this.options.priceLineColor,\n        backgroundColor: this.options.backgroundColor\n      })\n    );\n  }\n\n  getDefaultOptions(): DMIIndicatorOptions {\n    return defaultOptions\n  }\n\n  formula(c: Context): DMIData | undefined {\n    const period = this.options.period;\n    const high = c.symbol.high;\n    const low = c.symbol.low;\n    const close = c.symbol.close;\n\n    const highSeries = c.new_var(high, period + 1);\n    const lowSeries = c.new_var(low, period + 1);\n    const closeSeries = c.new_var(close, period + 1);\n\n    if (!highSeries.calculable() || !lowSeries.calculable() || !closeSeries.calculable()) {\n      return;\n    }\n\n    const currentHigh = high;\n    const currentLow = low;\n    const prevHigh = highSeries.get(1);\n    const prevLow = lowSeries.get(1);\n    const prevClose = closeSeries.get(1);\n\n    // Step 1: Calculate +DM and -DM (Directional Movement) using TradingView logic\n    const upMove = currentHigh - prevHigh;\n    const downMove = prevLow - currentLow;\n\n    let plusDM = 0;\n    let minusDM = 0;\n\n    if (upMove > downMove && upMove > 0) {\n      plusDM = upMove;\n    } else if (downMove > upMove && downMove > 0) {\n      minusDM = downMove;\n    }\n\n    // Step 2: Calculate True Range (TR)\n    const tr1 = currentHigh - currentLow;\n    const tr2 = Math.abs(currentHigh - prevClose);\n    const tr3 = Math.abs(currentLow - prevClose);\n    const trueRange = Math.max(tr1, tr2, tr3);\n\n    const wilderAlpha = 1 / period;\n\n    const plusDMSmoothed = c.new_var(0, period + 1);\n    const minusDMSmoothed = c.new_var(0, period + 1);\n    const trSmoothed = c.new_var(0, period + 1);\n\n    // Get historical data for initialization\n    const plusDMHistory = c.new_var(plusDM, period + 1);\n    const minusDMHistory = c.new_var(minusDM, period + 1);\n    const trHistory = c.new_var(trueRange, period + 1);\n\n    let smoothedPlusDM: number;\n    let smoothedMinusDM: number;\n    let smoothedTR: number;\n\n    // Check if we have enough data for proper Wilder's smoothing\n    if (plusDMHistory.calculable() && minusDMHistory.calculable() && trHistory.calculable()) {\n      const prevSmoothedPlusDM = plusDMSmoothed.get(1);\n      const prevSmoothedMinusDM = minusDMSmoothed.get(1);\n      const prevSmoothedTR = trSmoothed.get(1);\n\n      if (isNaN(prevSmoothedPlusDM) || isNaN(prevSmoothedMinusDM) || isNaN(prevSmoothedTR)) {\n        const plusDMValues = plusDMHistory.getAll();\n        const minusDMValues = minusDMHistory.getAll();\n        const trValues = trHistory.getAll();\n\n        smoothedPlusDM = plusDMValues.reduce((sum, val) => sum + val, 0) / period;\n        smoothedMinusDM = minusDMValues.reduce((sum, val) => sum + val, 0) / period;\n        smoothedTR = trValues.reduce((sum, val) => sum + val, 0) / period;\n      } else {\n        // Wilder's smoothing formula: Previous + (Current - Previous) / Period\n        // This is equivalent to: (Previous * (Period-1) + Current) / Period\n        smoothedPlusDM = prevSmoothedPlusDM + (plusDM - prevSmoothedPlusDM) * wilderAlpha;\n        smoothedMinusDM = prevSmoothedMinusDM + (minusDM - prevSmoothedMinusDM) * wilderAlpha;\n        smoothedTR = prevSmoothedTR + (trueRange - prevSmoothedTR) * wilderAlpha;\n      }\n    } else {\n      return;\n    }\n\n    plusDMSmoothed.set(smoothedPlusDM);\n    minusDMSmoothed.set(smoothedMinusDM);\n    trSmoothed.set(smoothedTR);\n\n    // Step 3: Calculate +DI and -DI (Directional Indicators)\n    const plusDI = smoothedTR !== 0 ? (100 * smoothedPlusDM) / smoothedTR : 0;\n    const minusDI = smoothedTR !== 0 ? (100 * smoothedMinusDM) / smoothedTR : 0;\n\n    // Step 4: Calculate DX\n    const diSum = plusDI + minusDI;\n    const dx = diSum !== 0 ? (100 * Math.abs(plusDI - minusDI)) / diSum : 0;\n\n    // Step 5: Calculate ADX using Wilder's smoothing on DX values\n    const dxHistory = c.new_var(dx, period + 1);\n    const adxSmoothed = c.new_var(0, period + 1);\n\n    let adx: number;\n\n    if (dxHistory.calculable()) {\n      const prevADX = adxSmoothed.get(1);\n\n      if (isNaN(prevADX)) {\n        // First ADX calculation: simple average of first 'period' DX values\n        const dxValues = dxHistory.getAll();\n        adx = dxValues.reduce((sum, val) => sum + val, 0) / period;\n      } else {\n        // Wilder's smoothing for ADX\n        adx = prevADX + (dx - prevADX) * wilderAlpha;\n      }\n    } else {\n      // Not enough DX data yet, return current DX as ADX\n      adx = dx;\n    }\n\n    adxSmoothed.set(adx);\n\n\n    return [\n      adx as ADXLine,\n      plusDI as PlusDILine,\n      minusDI as MinusDILine\n    ];\n  }\n\n  applyIndicatorData() {\n    const adxData: SingleValueData[] = [];\n    const plusDIData: SingleValueData[] = [];\n    const minusDIData: SingleValueData[] = [];\n    \n    for(const bar of this._executionContext.data) {\n      const value = bar.value;\n      if(!value) continue;\n      \n      const time = bar.time as Time;\n      adxData.push({time, value: value[0]});\n      plusDIData.push({time, value: value[1]});\n      minusDIData.push({time, value: value[2]});\n    }\n\n    this.adxSeries.setData(adxData);\n    this.plusDISeries.setData(plusDIData);\n    this.minusDISeries.setData(minusDIData);\n  }\n\n  remove() {\n    super.remove()\n    this.chart.removeSeries(this.adxSeries);\n    this.chart.removeSeries(this.plusDISeries);\n    this.chart.removeSeries(this.minusDISeries);\n  }\n\n  _applyOptions() {\n    this.adxSeries.applyOptions({color: this.options.adxColor});\n    this.plusDISeries.applyOptions({color: this.options.plusDIColor});\n    this.minusDISeries.applyOptions({color: this.options.minusDIColor});\n    this.applyIndicatorData();\n  }\n\n  setPaneIndex(paneIndex: number) {\n    this.adxSeries.moveToPane(paneIndex);\n    this.plusDISeries.moveToPane(paneIndex);\n    this.minusDISeries.moveToPane(paneIndex);\n  }\n\n  getPaneIndex(): number {\n    return this.adxSeries.getPane().paneIndex();\n  }\n}\n"], "names": ["defaultOptions", "DMIIndicator", "ChartIndicator", "chart", "options", "paneIndex", "__publicField", "LineSeries", "autoScaleInfoProviderCreator", "RegionPrimitive", "c", "period", "high", "low", "close", "highSeries", "lowSeries", "closeSeries", "currentHigh", "currentLow", "prevHigh", "prevLow", "prevClose", "upMove", "downMove", "plusDM", "minusDM", "tr1", "tr2", "tr3", "trueRang<PERSON>", "wilderAlpha", "plusDMSmoothed", "minusDMSmoothed", "trSmoothed", "plusDMHistory", "minusDMHistory", "trHistory", "smoothedPlusDM", "smoothedMinusDM", "smoothedTR", "prevSmoothedPlusDM", "prevSmoothedMinusDM", "prevSmoothedTR", "plusDMValues", "minusDMValues", "tr<PERSON><PERSON><PERSON>", "sum", "val", "plusDI", "minusDI", "diSum", "dx", "dxHistory", "adxSmoothed", "adx", "prevADX", "adxData", "plusDIData", "minusDIData", "bar", "value", "time"], "mappings": ";;;;;;;AAeO,MAAMA,KAAsC;AAAA,EACjD,UAAU;AAAA;AAAA,EACV,aAAa;AAAA;AAAA,EACb,cAAc;AAAA;AAAA,EACd,gBAAgB;AAAA,EAChB,iBAAiB;AAAA,EACjB,QAAQ;AAAA,EACR,SAAS;AACX;AAQA,MAAqBC,WAAqBC,EAA6C;AAAA,EAKrF,YAAYC,GAAkBC,GAAwCC,GAAoB;AACxF,UAAMF,GAAOC,CAAO;AALtB,IAAAE,EAAA;AACA,IAAAA,EAAA;AACA,IAAAA,EAAA;AAKO,SAAA,YAAYH,EAAM,UAAUI,GAAY;AAAA,MAC3C,OAAO,KAAK,QAAQ;AAAA,MACpB,WAAW;AAAA,MACX,kBAAkB;AAAA,MAClB,wBAAwB;AAAA,MACxB,cAAc;AAAA,MACd,uBAAuBC,EAA6B,EAAC,UAAU,KAAK,UAAU,EAAE,CAAA;AAAA,OAC/EH,CAAS,GAEP,KAAA,eAAeF,EAAM,UAAUI,GAAY;AAAA,MAC9C,OAAO,KAAK,QAAQ;AAAA,MACpB,WAAW;AAAA,MACX,kBAAkB;AAAA,MAClB,wBAAwB;AAAA,MACxB,cAAc;AAAA,MACd,uBAAuBC,EAA6B,EAAC,UAAU,KAAK,UAAU,EAAE,CAAA;AAAA,OAC/EH,CAAS,GAEP,KAAA,gBAAgBF,EAAM,UAAUI,GAAY;AAAA,MAC/C,OAAO,KAAK,QAAQ;AAAA,MACpB,WAAW;AAAA,MACX,kBAAkB;AAAA,MAClB,wBAAwB;AAAA,MACxB,cAAc;AAAA,MACd,uBAAuBC,EAA6B,EAAC,UAAU,KAAK,UAAU,EAAE,CAAA;AAAA,OAC/EH,CAAS,GAEZ,KAAK,UAAU;AAAA,MACb,IAAII,GAAgB;AAAA,QAClB,SAAS;AAAA,QACT,UAAU;AAAA,QACV,WAAW,KAAK,QAAQ;AAAA,QACxB,iBAAiB,KAAK,QAAQ;AAAA,MAC/B,CAAA;AAAA,IACH;AAAA,EAAA;AAAA,EAGF,oBAAyC;AAChC,WAAAT;AAAA,EAAA;AAAA,EAGT,QAAQU,GAAiC;AACjC,UAAAC,IAAS,KAAK,QAAQ,QACtBC,IAAOF,EAAE,OAAO,MAChBG,IAAMH,EAAE,OAAO,KACfI,IAAQJ,EAAE,OAAO,OAEjBK,IAAaL,EAAE,QAAQE,GAAMD,IAAS,CAAC,GACvCK,IAAYN,EAAE,QAAQG,GAAKF,IAAS,CAAC,GACrCM,IAAcP,EAAE,QAAQI,GAAOH,IAAS,CAAC;AAE3C,QAAA,CAACI,EAAW,WAAA,KAAgB,CAACC,EAAU,gBAAgB,CAACC,EAAY;AACtE;AAGF,UAAMC,IAAcN,GACdO,IAAaN,GACbO,IAAWL,EAAW,IAAI,CAAC,GAC3BM,IAAUL,EAAU,IAAI,CAAC,GACzBM,IAAYL,EAAY,IAAI,CAAC,GAG7BM,IAASL,IAAcE,GACvBI,IAAWH,IAAUF;AAE3B,QAAIM,IAAS,GACTC,IAAU;AAEV,IAAAH,IAASC,KAAYD,IAAS,IACvBE,IAAAF,IACAC,IAAWD,KAAUC,IAAW,MAC/BE,IAAAF;AAIZ,UAAMG,IAAMT,IAAcC,GACpBS,IAAM,KAAK,IAAIV,IAAcI,CAAS,GACtCO,IAAM,KAAK,IAAIV,IAAaG,CAAS,GACrCQ,IAAY,KAAK,IAAIH,GAAKC,GAAKC,CAAG,GAElCE,IAAc,IAAIpB,GAElBqB,IAAiBtB,EAAE,QAAQ,GAAGC,IAAS,CAAC,GACxCsB,IAAkBvB,EAAE,QAAQ,GAAGC,IAAS,CAAC,GACzCuB,IAAaxB,EAAE,QAAQ,GAAGC,IAAS,CAAC,GAGpCwB,IAAgBzB,EAAE,QAAQe,GAAQd,IAAS,CAAC,GAC5CyB,IAAiB1B,EAAE,QAAQgB,GAASf,IAAS,CAAC,GAC9C0B,IAAY3B,EAAE,QAAQoB,GAAWnB,IAAS,CAAC;AAE7C,QAAA2B,GACAC,GACAC;AAGA,QAAAL,EAAc,gBAAgBC,EAAe,gBAAgBC,EAAU,cAAc;AACjF,YAAAI,IAAqBT,EAAe,IAAI,CAAC,GACzCU,IAAsBT,EAAgB,IAAI,CAAC,GAC3CU,IAAiBT,EAAW,IAAI,CAAC;AAEnC,UAAA,MAAMO,CAAkB,KAAK,MAAMC,CAAmB,KAAK,MAAMC,CAAc,GAAG;AAC9E,cAAAC,IAAeT,EAAc,OAAO,GACpCU,IAAgBT,EAAe,OAAO,GACtCU,IAAWT,EAAU,OAAO;AAEjB,QAAAC,IAAAM,EAAa,OAAO,CAACG,GAAKC,MAAQD,IAAMC,GAAK,CAAC,IAAIrC,GACjD4B,IAAAM,EAAc,OAAO,CAACE,GAAKC,MAAQD,IAAMC,GAAK,CAAC,IAAIrC,GACxD6B,IAAAM,EAAS,OAAO,CAACC,GAAKC,MAAQD,IAAMC,GAAK,CAAC,IAAIrC;AAAA,MAAA;AAI1C,QAAA2B,IAAAG,KAAsBhB,IAASgB,KAAsBV,GACpDQ,IAAAG,KAAuBhB,IAAUgB,KAAuBX,GAC7DS,IAAAG,KAAkBb,IAAYa,KAAkBZ;AAAA,IAC/D;AAEA;AAGF,IAAAC,EAAe,IAAIM,CAAc,GACjCL,EAAgB,IAAIM,CAAe,GACnCL,EAAW,IAAIM,CAAU;AAGzB,UAAMS,IAAST,MAAe,IAAK,MAAMF,IAAkBE,IAAa,GAClEU,IAAUV,MAAe,IAAK,MAAMD,IAAmBC,IAAa,GAGpEW,IAAQF,IAASC,GACjBE,IAAKD,MAAU,IAAK,MAAM,KAAK,IAAIF,IAASC,CAAO,IAAKC,IAAQ,GAGhEE,IAAY3C,EAAE,QAAQ0C,GAAIzC,IAAS,CAAC,GACpC2C,IAAc5C,EAAE,QAAQ,GAAGC,IAAS,CAAC;AAEvC,QAAA4C;AAEA,QAAAF,EAAU,cAAc;AACpB,YAAAG,IAAUF,EAAY,IAAI,CAAC;AAE7B,MAAA,MAAME,CAAO,IAGTD,IADWF,EAAU,OAAO,EACnB,OAAO,CAACN,GAAKC,MAAQD,IAAMC,GAAK,CAAC,IAAIrC,IAG9C4C,IAAAC,KAAWJ,IAAKI,KAAWzB;AAAA,IACnC;AAGM,MAAAwB,IAAAH;AAGR,WAAAE,EAAY,IAAIC,CAAG,GAGZ;AAAA,MACLA;AAAA,MACAN;AAAA,MACAC;AAAA,IACF;AAAA,EAAA;AAAA,EAGF,qBAAqB;AACnB,UAAMO,IAA6B,CAAC,GAC9BC,IAAgC,CAAC,GACjCC,IAAiC,CAAC;AAE9B,eAAAC,KAAO,KAAK,kBAAkB,MAAM;AAC5C,YAAMC,IAAQD,EAAI;AAClB,UAAG,CAACC,EAAO;AAEX,YAAMC,IAAOF,EAAI;AACjB,MAAAH,EAAQ,KAAK,EAAC,MAAAK,GAAM,OAAOD,EAAM,CAAC,GAAE,GACpCH,EAAW,KAAK,EAAC,MAAAI,GAAM,OAAOD,EAAM,CAAC,GAAE,GACvCF,EAAY,KAAK,EAAC,MAAAG,GAAM,OAAOD,EAAM,CAAC,GAAE;AAAA,IAAA;AAGrC,SAAA,UAAU,QAAQJ,CAAO,GACzB,KAAA,aAAa,QAAQC,CAAU,GAC/B,KAAA,cAAc,QAAQC,CAAW;AAAA,EAAA;AAAA,EAGxC,SAAS;AACP,UAAM,OAAO,GACR,KAAA,MAAM,aAAa,KAAK,SAAS,GACjC,KAAA,MAAM,aAAa,KAAK,YAAY,GACpC,KAAA,MAAM,aAAa,KAAK,aAAa;AAAA,EAAA;AAAA,EAG5C,gBAAgB;AACd,SAAK,UAAU,aAAa,EAAC,OAAO,KAAK,QAAQ,UAAS,GAC1D,KAAK,aAAa,aAAa,EAAC,OAAO,KAAK,QAAQ,aAAY,GAChE,KAAK,cAAc,aAAa,EAAC,OAAO,KAAK,QAAQ,cAAa,GAClE,KAAK,mBAAmB;AAAA,EAAA;AAAA,EAG1B,aAAatD,GAAmB;AACzB,SAAA,UAAU,WAAWA,CAAS,GAC9B,KAAA,aAAa,WAAWA,CAAS,GACjC,KAAA,cAAc,WAAWA,CAAS;AAAA,EAAA;AAAA,EAGzC,eAAuB;AACrB,WAAO,KAAK,UAAU,QAAQ,EAAE,UAAU;AAAA,EAAA;AAE9C;"}