import "./chunk-5WRI5ZAA.js";

// ../node_modules/@0no-co/graphql.web/dist/graphql.web.mjs
var e = {
  NAME: "Name",
  DOCUMENT: "Document",
  OPERATION_DEFINITION: "OperationDefinition",
  VARIABLE_DEFINITION: "VariableDefinition",
  SELECTION_SET: "SelectionSet",
  FIELD: "Field",
  ARGUMENT: "Argument",
  FRAGMENT_SPREAD: "FragmentSpread",
  INLINE_FRAGMENT: "InlineFragment",
  FRAGMENT_DEFINITION: "FragmentDefinition",
  VARIABLE: "Variable",
  INT: "IntValue",
  FLOAT: "FloatValue",
  STRING: "StringValue",
  BOOLEAN: "BooleanValue",
  NULL: "NullValue",
  ENUM: "EnumValue",
  LIST: "ListValue",
  OBJECT: "ObjectValue",
  OBJECT_FIELD: "ObjectField",
  DIRECTIVE: "Directive",
  NAMED_TYPE: "NamedType",
  LIST_TYPE: "ListType",
  NON_NULL_TYPE: "NonNullType"
};
var GraphQLError = class extends Error {
  constructor(e3, r2, n2, i2, t2, a2, l3) {
    super(e3);
    this.name = "GraphQLError";
    this.message = e3;
    if (t2) {
      this.path = t2;
    }
    if (r2) {
      this.nodes = Array.isArray(r2) ? r2 : [r2];
    }
    if (n2) {
      this.source = n2;
    }
    if (i2) {
      this.positions = i2;
    }
    if (a2) {
      this.originalError = a2;
    }
    var o2 = l3;
    if (!o2 && a2) {
      var u3 = a2.extensions;
      if (u3 && "object" == typeof u3) {
        o2 = u3;
      }
    }
    this.extensions = o2 || {};
  }
  toJSON() {
    return {
      ...this,
      message: this.message
    };
  }
  toString() {
    return this.message;
  }
  get [Symbol.toStringTag]() {
    return "GraphQLError";
  }
};
var n;
var i;
function error(e3) {
  return new GraphQLError(`Syntax Error: Unexpected token at ${i} in ${e3}`);
}
function advance(e3) {
  e3.lastIndex = i;
  if (e3.test(n)) {
    return n.slice(i, i = e3.lastIndex);
  }
}
var t = / +(?=[^\s])/y;
function blockString(e3) {
  var r2 = e3.split("\n");
  var n2 = "";
  var i2 = 0;
  var a2 = 0;
  var l3 = r2.length - 1;
  for (var o2 = 0; o2 < r2.length; o2++) {
    t.lastIndex = 0;
    if (t.test(r2[o2])) {
      if (o2 && (!i2 || t.lastIndex < i2)) {
        i2 = t.lastIndex;
      }
      a2 = a2 || o2;
      l3 = o2;
    }
  }
  for (var u3 = a2; u3 <= l3; u3++) {
    if (u3 !== a2) {
      n2 += "\n";
    }
    n2 += r2[u3].slice(i2).replace(/\\"""/g, '"""');
  }
  return n2;
}
function ignored() {
  for (var e3 = 0 | n.charCodeAt(i++); 9 === e3 || 10 === e3 || 13 === e3 || 32 === e3 || 35 === e3 || 44 === e3 || 65279 === e3; e3 = 0 | n.charCodeAt(i++)) {
    if (35 === e3) {
      while (10 !== (e3 = n.charCodeAt(i++)) && 13 !== e3) {
      }
    }
  }
  i--;
}
var a = /[_A-Za-z]\w*/y;
var l = new RegExp("(?:(null|true|false)|\\$(" + a.source + ')|(-?\\d+)((?:\\.\\d+)?[eE][+-]?\\d+|\\.\\d+)?|("""(?:"""|(?:[\\s\\S]*?[^\\\\])"""))|("(?:"|[^\\r\\n]*?[^\\\\]"))|(' + a.source + "))", "y");
var o = function(e3) {
  e3[e3.Const = 1] = "Const";
  e3[e3.Var = 2] = "Var";
  e3[e3.Int = 3] = "Int";
  e3[e3.Float = 4] = "Float";
  e3[e3.BlockString = 5] = "BlockString";
  e3[e3.String = 6] = "String";
  e3[e3.Enum = 7] = "Enum";
  return e3;
}(o || {});
var u = /\\/;
function value(e3) {
  var r2;
  var t2;
  l.lastIndex = i;
  if (91 === n.charCodeAt(i)) {
    i++;
    ignored();
    var d3 = [];
    while (93 !== n.charCodeAt(i)) {
      d3.push(value(e3));
    }
    i++;
    ignored();
    return {
      kind: "ListValue",
      values: d3
    };
  } else if (123 === n.charCodeAt(i)) {
    i++;
    ignored();
    var s3 = [];
    while (125 !== n.charCodeAt(i)) {
      if (null == (r2 = advance(a))) {
        throw error("ObjectField");
      }
      ignored();
      if (58 !== n.charCodeAt(i++)) {
        throw error("ObjectField");
      }
      ignored();
      s3.push({
        kind: "ObjectField",
        name: {
          kind: "Name",
          value: r2
        },
        value: value(e3)
      });
    }
    i++;
    ignored();
    return {
      kind: "ObjectValue",
      fields: s3
    };
  } else if (null != (t2 = l.exec(n))) {
    i = l.lastIndex;
    ignored();
    if (null != (r2 = t2[o.Const])) {
      return "null" === r2 ? {
        kind: "NullValue"
      } : {
        kind: "BooleanValue",
        value: "true" === r2
      };
    } else if (null != (r2 = t2[o.Var])) {
      if (e3) {
        throw error("Variable");
      } else {
        return {
          kind: "Variable",
          name: {
            kind: "Name",
            value: r2
          }
        };
      }
    } else if (null != (r2 = t2[o.Int])) {
      var v3;
      if (null != (v3 = t2[o.Float])) {
        return {
          kind: "FloatValue",
          value: r2 + v3
        };
      } else {
        return {
          kind: "IntValue",
          value: r2
        };
      }
    } else if (null != (r2 = t2[o.BlockString])) {
      return {
        kind: "StringValue",
        value: blockString(r2.slice(3, -3)),
        block: true
      };
    } else if (null != (r2 = t2[o.String])) {
      return {
        kind: "StringValue",
        value: u.test(r2) ? JSON.parse(r2) : r2.slice(1, -1),
        block: false
      };
    } else if (null != (r2 = t2[o.Enum])) {
      return {
        kind: "EnumValue",
        value: r2
      };
    }
  }
  throw error("Value");
}
function arguments_(e3) {
  if (40 === n.charCodeAt(i)) {
    var r2 = [];
    i++;
    ignored();
    var t2;
    do {
      if (null == (t2 = advance(a))) {
        throw error("Argument");
      }
      ignored();
      if (58 !== n.charCodeAt(i++)) {
        throw error("Argument");
      }
      ignored();
      r2.push({
        kind: "Argument",
        name: {
          kind: "Name",
          value: t2
        },
        value: value(e3)
      });
    } while (41 !== n.charCodeAt(i));
    i++;
    ignored();
    return r2;
  }
}
function directives(e3) {
  if (64 === n.charCodeAt(i)) {
    var r2 = [];
    var t2;
    do {
      i++;
      if (null == (t2 = advance(a))) {
        throw error("Directive");
      }
      ignored();
      r2.push({
        kind: "Directive",
        name: {
          kind: "Name",
          value: t2
        },
        arguments: arguments_(e3)
      });
    } while (64 === n.charCodeAt(i));
    return r2;
  }
}
function type() {
  var e3;
  var r2 = 0;
  while (91 === n.charCodeAt(i)) {
    r2++;
    i++;
    ignored();
  }
  if (null == (e3 = advance(a))) {
    throw error("NamedType");
  }
  ignored();
  var t2 = {
    kind: "NamedType",
    name: {
      kind: "Name",
      value: e3
    }
  };
  do {
    if (33 === n.charCodeAt(i)) {
      i++;
      ignored();
      t2 = {
        kind: "NonNullType",
        type: t2
      };
    }
    if (r2) {
      if (93 !== n.charCodeAt(i++)) {
        throw error("NamedType");
      }
      ignored();
      t2 = {
        kind: "ListType",
        type: t2
      };
    }
  } while (r2--);
  return t2;
}
var d = new RegExp("(?:(\\.{3})|(" + a.source + "))", "y");
var s = function(e3) {
  e3[e3.Spread = 1] = "Spread";
  e3[e3.Name = 2] = "Name";
  return e3;
}(s || {});
function selectionSet() {
  var e3 = [];
  var r2;
  var t2;
  do {
    d.lastIndex = i;
    if (null != (t2 = d.exec(n))) {
      i = d.lastIndex;
      if (null != t2[s.Spread]) {
        ignored();
        var l3 = advance(a);
        if (null != l3 && "on" !== l3) {
          ignored();
          e3.push({
            kind: "FragmentSpread",
            name: {
              kind: "Name",
              value: l3
            },
            directives: directives(false)
          });
        } else {
          ignored();
          if ("on" === l3) {
            if (null == (l3 = advance(a))) {
              throw error("NamedType");
            }
            ignored();
          }
          var o2 = directives(false);
          if (123 !== n.charCodeAt(i++)) {
            throw error("InlineFragment");
          }
          ignored();
          e3.push({
            kind: "InlineFragment",
            typeCondition: l3 ? {
              kind: "NamedType",
              name: {
                kind: "Name",
                value: l3
              }
            } : void 0,
            directives: o2,
            selectionSet: selectionSet()
          });
        }
      } else if (null != (r2 = t2[s.Name])) {
        var u3 = void 0;
        ignored();
        if (58 === n.charCodeAt(i)) {
          i++;
          ignored();
          u3 = r2;
          if (null == (r2 = advance(a))) {
            throw error("Field");
          }
          ignored();
        }
        var v3 = arguments_(false);
        ignored();
        var c2 = directives(false);
        var f3 = void 0;
        if (123 === n.charCodeAt(i)) {
          i++;
          ignored();
          f3 = selectionSet();
        }
        e3.push({
          kind: "Field",
          alias: u3 ? {
            kind: "Name",
            value: u3
          } : void 0,
          name: {
            kind: "Name",
            value: r2
          },
          arguments: v3,
          directives: c2,
          selectionSet: f3
        });
      }
    } else {
      throw error("SelectionSet");
    }
  } while (125 !== n.charCodeAt(i));
  i++;
  ignored();
  return {
    kind: "SelectionSet",
    selections: e3
  };
}
function fragmentDefinition() {
  var e3;
  var r2;
  if (null == (e3 = advance(a))) {
    throw error("FragmentDefinition");
  }
  ignored();
  if ("on" !== advance(a)) {
    throw error("FragmentDefinition");
  }
  ignored();
  if (null == (r2 = advance(a))) {
    throw error("FragmentDefinition");
  }
  ignored();
  var t2 = directives(false);
  if (123 !== n.charCodeAt(i++)) {
    throw error("FragmentDefinition");
  }
  ignored();
  return {
    kind: "FragmentDefinition",
    name: {
      kind: "Name",
      value: e3
    },
    typeCondition: {
      kind: "NamedType",
      name: {
        kind: "Name",
        value: r2
      }
    },
    directives: t2,
    selectionSet: selectionSet()
  };
}
var v = /(?:query|mutation|subscription|fragment)/y;
function operationDefinition(e3) {
  var r2;
  var t2;
  var l3;
  if (e3) {
    ignored();
    r2 = advance(a);
    t2 = function variableDefinitions() {
      ignored();
      if (40 === n.charCodeAt(i)) {
        var e4 = [];
        i++;
        ignored();
        var r3;
        do {
          if (36 !== n.charCodeAt(i++)) {
            throw error("Variable");
          }
          if (null == (r3 = advance(a))) {
            throw error("Variable");
          }
          ignored();
          if (58 !== n.charCodeAt(i++)) {
            throw error("VariableDefinition");
          }
          ignored();
          var t3 = type();
          var l4 = void 0;
          if (61 === n.charCodeAt(i)) {
            i++;
            ignored();
            l4 = value(true);
          }
          ignored();
          e4.push({
            kind: "VariableDefinition",
            variable: {
              kind: "Variable",
              name: {
                kind: "Name",
                value: r3
              }
            },
            type: t3,
            defaultValue: l4,
            directives: directives(true)
          });
        } while (41 !== n.charCodeAt(i));
        i++;
        ignored();
        return e4;
      }
    }();
    l3 = directives(false);
  }
  if (123 === n.charCodeAt(i)) {
    i++;
    ignored();
    return {
      kind: "OperationDefinition",
      operation: e3 || "query",
      name: r2 ? {
        kind: "Name",
        value: r2
      } : void 0,
      variableDefinitions: t2,
      directives: l3,
      selectionSet: selectionSet()
    };
  }
}
function parse(e3, r2) {
  i = 0;
  return function document(e4, r3) {
    var n2;
    var t2;
    ignored();
    var a2 = [];
    do {
      if ("fragment" === (n2 = advance(v))) {
        ignored();
        a2.push(fragmentDefinition());
      } else if (null != (t2 = operationDefinition(n2))) {
        a2.push(t2);
      } else {
        throw error("Document");
      }
    } while (i < e4.length);
    if (!r3) {
      var l3;
      return {
        kind: "Document",
        definitions: a2,
        set loc(e5) {
          l3 = e5;
        },
        get loc() {
          if (!l3) {
            l3 = {
              start: 0,
              end: e4.length,
              startToken: void 0,
              endToken: void 0,
              source: {
                body: e4,
                name: "graphql.web",
                locationOffset: {
                  line: 1,
                  column: 1
                }
              }
            };
          }
          return l3;
        }
      };
    }
    return {
      kind: "Document",
      definitions: a2
    };
  }(n = "string" == typeof e3.body ? e3.body : e3, r2 && r2.noLocation);
}
function mapJoin(e3, r2, n2) {
  var i2 = "";
  for (var t2 = 0; t2 < e3.length; t2++) {
    if (t2) {
      i2 += r2;
    }
    i2 += n2(e3[t2]);
  }
  return i2;
}
function printString(e3) {
  return JSON.stringify(e3);
}
function printBlockString(e3) {
  return '"""\n' + e3.replace(/"""/g, '\\"""') + '\n"""';
}
var f = "\n";
var g = {
  OperationDefinition(e3) {
    var r2 = e3.operation;
    if (e3.name) {
      r2 += " " + e3.name.value;
    }
    if (e3.variableDefinitions && e3.variableDefinitions.length) {
      if (!e3.name) {
        r2 += " ";
      }
      r2 += "(" + mapJoin(e3.variableDefinitions, ", ", g.VariableDefinition) + ")";
    }
    if (e3.directives && e3.directives.length) {
      r2 += " " + mapJoin(e3.directives, " ", g.Directive);
    }
    return "query" !== r2 ? r2 + " " + g.SelectionSet(e3.selectionSet) : g.SelectionSet(e3.selectionSet);
  },
  VariableDefinition(e3) {
    var r2 = g.Variable(e3.variable) + ": " + _print(e3.type);
    if (e3.defaultValue) {
      r2 += " = " + _print(e3.defaultValue);
    }
    if (e3.directives && e3.directives.length) {
      r2 += " " + mapJoin(e3.directives, " ", g.Directive);
    }
    return r2;
  },
  Field(e3) {
    var r2 = e3.alias ? e3.alias.value + ": " + e3.name.value : e3.name.value;
    if (e3.arguments && e3.arguments.length) {
      var n2 = mapJoin(e3.arguments, ", ", g.Argument);
      if (r2.length + n2.length + 2 > 80) {
        r2 += "(" + (f += "  ") + mapJoin(e3.arguments, f, g.Argument) + (f = f.slice(0, -2)) + ")";
      } else {
        r2 += "(" + n2 + ")";
      }
    }
    if (e3.directives && e3.directives.length) {
      r2 += " " + mapJoin(e3.directives, " ", g.Directive);
    }
    if (e3.selectionSet && e3.selectionSet.selections.length) {
      r2 += " " + g.SelectionSet(e3.selectionSet);
    }
    return r2;
  },
  StringValue(e3) {
    if (e3.block) {
      return printBlockString(e3.value).replace(/\n/g, f);
    } else {
      return printString(e3.value);
    }
  },
  BooleanValue: (e3) => "" + e3.value,
  NullValue: (e3) => "null",
  IntValue: (e3) => e3.value,
  FloatValue: (e3) => e3.value,
  EnumValue: (e3) => e3.value,
  Name: (e3) => e3.value,
  Variable: (e3) => "$" + e3.name.value,
  ListValue: (e3) => "[" + mapJoin(e3.values, ", ", _print) + "]",
  ObjectValue: (e3) => "{" + mapJoin(e3.fields, ", ", g.ObjectField) + "}",
  ObjectField: (e3) => e3.name.value + ": " + _print(e3.value),
  Document(e3) {
    if (!e3.definitions || !e3.definitions.length) {
      return "";
    }
    return mapJoin(e3.definitions, "\n\n", _print);
  },
  SelectionSet: (e3) => "{" + (f += "  ") + mapJoin(e3.selections, f, _print) + (f = f.slice(0, -2)) + "}",
  Argument: (e3) => e3.name.value + ": " + _print(e3.value),
  FragmentSpread(e3) {
    var r2 = "..." + e3.name.value;
    if (e3.directives && e3.directives.length) {
      r2 += " " + mapJoin(e3.directives, " ", g.Directive);
    }
    return r2;
  },
  InlineFragment(e3) {
    var r2 = "...";
    if (e3.typeCondition) {
      r2 += " on " + e3.typeCondition.name.value;
    }
    if (e3.directives && e3.directives.length) {
      r2 += " " + mapJoin(e3.directives, " ", g.Directive);
    }
    return r2 += " " + g.SelectionSet(e3.selectionSet);
  },
  FragmentDefinition(e3) {
    var r2 = "fragment " + e3.name.value;
    r2 += " on " + e3.typeCondition.name.value;
    if (e3.directives && e3.directives.length) {
      r2 += " " + mapJoin(e3.directives, " ", g.Directive);
    }
    return r2 + " " + g.SelectionSet(e3.selectionSet);
  },
  Directive(e3) {
    var r2 = "@" + e3.name.value;
    if (e3.arguments && e3.arguments.length) {
      r2 += "(" + mapJoin(e3.arguments, ", ", g.Argument) + ")";
    }
    return r2;
  },
  NamedType: (e3) => e3.name.value,
  ListType: (e3) => "[" + _print(e3.type) + "]",
  NonNullType: (e3) => _print(e3.type) + "!"
};
var _print = (e3) => g[e3.kind](e3);
function print(e3) {
  f = "\n";
  return g[e3.kind] ? g[e3.kind](e3) : "";
}

// ../node_modules/wonka/dist/wonka.mjs
var teardownPlaceholder = () => {
};
var e2 = teardownPlaceholder;
function start(e3) {
  return {
    tag: 0,
    0: e3
  };
}
function push(e3) {
  return {
    tag: 1,
    0: e3
  };
}
var asyncIteratorSymbol = () => "function" == typeof Symbol && Symbol.asyncIterator || "@@asyncIterator";
var identity = (e3) => e3;
function filter(r2) {
  return (t2) => (i2) => {
    var a2 = e2;
    t2((e3) => {
      if (0 === e3) {
        i2(0);
      } else if (0 === e3.tag) {
        a2 = e3[0];
        i2(e3);
      } else if (!r2(e3[0])) {
        a2(0);
      } else {
        i2(e3);
      }
    });
  };
}
function map(e3) {
  return (r2) => (t2) => r2((r3) => {
    if (0 === r3 || 0 === r3.tag) {
      t2(r3);
    } else {
      t2(push(e3(r3[0])));
    }
  });
}
function mergeMap(r2) {
  return (t2) => (i2) => {
    var a2 = [];
    var f3 = e2;
    var n2 = false;
    var s3 = false;
    t2((t3) => {
      if (s3) {
      } else if (0 === t3) {
        s3 = true;
        if (!a2.length) {
          i2(0);
        }
      } else if (0 === t3.tag) {
        f3 = t3[0];
      } else {
        n2 = false;
        !function applyInnerSource(r3) {
          var t4 = e2;
          r3((e3) => {
            if (0 === e3) {
              if (a2.length) {
                var r4 = a2.indexOf(t4);
                if (r4 > -1) {
                  (a2 = a2.slice()).splice(r4, 1);
                }
                if (!a2.length) {
                  if (s3) {
                    i2(0);
                  } else if (!n2) {
                    n2 = true;
                    f3(0);
                  }
                }
              }
            } else if (0 === e3.tag) {
              a2.push(t4 = e3[0]);
              t4(0);
            } else if (a2.length) {
              i2(e3);
              t4(0);
            }
          });
        }(r2(t3[0]));
        if (!n2) {
          n2 = true;
          f3(0);
        }
      }
    });
    i2(start((e3) => {
      if (1 === e3) {
        if (!s3) {
          s3 = true;
          f3(1);
        }
        for (var r3 = 0, t3 = a2, i3 = a2.length; r3 < i3; r3++) {
          t3[r3](1);
        }
        a2.length = 0;
      } else {
        if (!s3 && !n2) {
          n2 = true;
          f3(0);
        } else {
          n2 = false;
        }
        for (var l3 = 0, u3 = a2, o2 = a2.length; l3 < o2; l3++) {
          u3[l3](0);
        }
      }
    }));
  };
}
function mergeAll(e3) {
  return mergeMap(identity)(e3);
}
function merge(e3) {
  return mergeAll(r(e3));
}
function onEnd(e3) {
  return (r2) => (t2) => {
    var i2 = false;
    r2((r3) => {
      if (i2) {
      } else if (0 === r3) {
        i2 = true;
        t2(0);
        e3();
      } else if (0 === r3.tag) {
        var a2 = r3[0];
        t2(start((r4) => {
          if (1 === r4) {
            i2 = true;
            a2(1);
            e3();
          } else {
            a2(r4);
          }
        }));
      } else {
        t2(r3);
      }
    });
  };
}
function onPush(e3) {
  return (r2) => (t2) => {
    var i2 = false;
    r2((r3) => {
      if (i2) {
      } else if (0 === r3) {
        i2 = true;
        t2(0);
      } else if (0 === r3.tag) {
        var a2 = r3[0];
        t2(start((e4) => {
          if (1 === e4) {
            i2 = true;
          }
          a2(e4);
        }));
      } else {
        e3(r3[0]);
        t2(r3);
      }
    });
  };
}
function onStart(e3) {
  return (r2) => (t2) => r2((r3) => {
    if (0 === r3) {
      t2(0);
    } else if (0 === r3.tag) {
      t2(r3);
      e3();
    } else {
      t2(r3);
    }
  });
}
function share(r2) {
  var t2 = [];
  var i2 = e2;
  var a2 = false;
  return (e3) => {
    t2.push(e3);
    if (1 === t2.length) {
      r2((e4) => {
        if (0 === e4) {
          for (var r3 = 0, f3 = t2, n2 = t2.length; r3 < n2; r3++) {
            f3[r3](0);
          }
          t2.length = 0;
        } else if (0 === e4.tag) {
          i2 = e4[0];
        } else {
          a2 = false;
          for (var s3 = 0, l3 = t2, u3 = t2.length; s3 < u3; s3++) {
            l3[s3](e4);
          }
        }
      });
    }
    e3(start((r3) => {
      if (1 === r3) {
        var f3 = t2.indexOf(e3);
        if (f3 > -1) {
          (t2 = t2.slice()).splice(f3, 1);
        }
        if (!t2.length) {
          i2(1);
        }
      } else if (!a2) {
        a2 = true;
        i2(0);
      }
    }));
  };
}
function switchMap(r2) {
  return (t2) => (i2) => {
    var a2 = e2;
    var f3 = e2;
    var n2 = false;
    var s3 = false;
    var l3 = false;
    var u3 = false;
    t2((t3) => {
      if (u3) {
      } else if (0 === t3) {
        u3 = true;
        if (!l3) {
          i2(0);
        }
      } else if (0 === t3.tag) {
        a2 = t3[0];
      } else {
        if (l3) {
          f3(1);
          f3 = e2;
        }
        if (!n2) {
          n2 = true;
          a2(0);
        } else {
          n2 = false;
        }
        !function applyInnerSource(e3) {
          l3 = true;
          e3((e4) => {
            if (!l3) {
            } else if (0 === e4) {
              l3 = false;
              if (u3) {
                i2(0);
              } else if (!n2) {
                n2 = true;
                a2(0);
              }
            } else if (0 === e4.tag) {
              s3 = false;
              (f3 = e4[0])(0);
            } else {
              i2(e4);
              if (!s3) {
                f3(0);
              } else {
                s3 = false;
              }
            }
          });
        }(r2(t3[0]));
      }
    });
    i2(start((e3) => {
      if (1 === e3) {
        if (!u3) {
          u3 = true;
          a2(1);
        }
        if (l3) {
          l3 = false;
          f3(1);
        }
      } else {
        if (!u3 && !n2) {
          n2 = true;
          a2(0);
        }
        if (l3 && !s3) {
          s3 = true;
          f3(0);
        }
      }
    }));
  };
}
function take(r2) {
  return (t2) => (i2) => {
    var a2 = e2;
    var f3 = false;
    var n2 = 0;
    t2((e3) => {
      if (f3) {
      } else if (0 === e3) {
        f3 = true;
        i2(0);
      } else if (0 === e3.tag) {
        if (r2 <= 0) {
          f3 = true;
          i2(0);
          e3[0](1);
        } else {
          a2 = e3[0];
        }
      } else if (n2++ < r2) {
        i2(e3);
        if (!f3 && n2 >= r2) {
          f3 = true;
          i2(0);
          a2(1);
        }
      } else {
        i2(e3);
      }
    });
    i2(start((e3) => {
      if (1 === e3 && !f3) {
        f3 = true;
        a2(1);
      } else if (0 === e3 && !f3 && n2 < r2) {
        a2(0);
      }
    }));
  };
}
function takeUntil(r2) {
  return (t2) => (i2) => {
    var a2 = e2;
    var f3 = e2;
    var n2 = false;
    t2((e3) => {
      if (n2) {
      } else if (0 === e3) {
        n2 = true;
        f3(1);
        i2(0);
      } else if (0 === e3.tag) {
        a2 = e3[0];
        r2((e4) => {
          if (0 === e4) {
          } else if (0 === e4.tag) {
            (f3 = e4[0])(0);
          } else {
            n2 = true;
            f3(1);
            a2(1);
            i2(0);
          }
        });
      } else {
        i2(e3);
      }
    });
    i2(start((e3) => {
      if (1 === e3 && !n2) {
        n2 = true;
        a2(1);
        f3(1);
      } else if (!n2) {
        a2(0);
      }
    }));
  };
}
function takeWhile(r2, t2) {
  return (i2) => (a2) => {
    var f3 = e2;
    var n2 = false;
    i2((e3) => {
      if (n2) {
      } else if (0 === e3) {
        n2 = true;
        a2(0);
      } else if (0 === e3.tag) {
        f3 = e3[0];
        a2(e3);
      } else if (!r2(e3[0])) {
        n2 = true;
        if (t2) {
          a2(e3);
        }
        a2(0);
        f3(1);
      } else {
        a2(e3);
      }
    });
  };
}
function lazy(e3) {
  return (r2) => e3()(r2);
}
function fromAsyncIterable(e3) {
  return (r2) => {
    var t2 = e3[asyncIteratorSymbol()] && e3[asyncIteratorSymbol()]() || e3;
    var i2 = false;
    var a2 = false;
    var f3 = false;
    var n2;
    r2(start(async (e4) => {
      if (1 === e4) {
        i2 = true;
        if (t2.return) {
          t2.return();
        }
      } else if (a2) {
        f3 = true;
      } else {
        for (f3 = a2 = true; f3 && !i2; ) {
          if ((n2 = await t2.next()).done) {
            i2 = true;
            if (t2.return) {
              await t2.return();
            }
            r2(0);
          } else {
            try {
              f3 = false;
              r2(push(n2.value));
            } catch (e5) {
              if (t2.throw) {
                if (i2 = !!(await t2.throw(e5)).done) {
                  r2(0);
                }
              } else {
                throw e5;
              }
            }
          }
        }
        a2 = false;
      }
    }));
  };
}
function fromIterable(e3) {
  if (e3[Symbol.asyncIterator]) {
    return fromAsyncIterable(e3);
  }
  return (r2) => {
    var t2 = e3[Symbol.iterator]();
    var i2 = false;
    var a2 = false;
    var f3 = false;
    var n2;
    r2(start((e4) => {
      if (1 === e4) {
        i2 = true;
        if (t2.return) {
          t2.return();
        }
      } else if (a2) {
        f3 = true;
      } else {
        for (f3 = a2 = true; f3 && !i2; ) {
          if ((n2 = t2.next()).done) {
            i2 = true;
            if (t2.return) {
              t2.return();
            }
            r2(0);
          } else {
            try {
              f3 = false;
              r2(push(n2.value));
            } catch (e5) {
              if (t2.throw) {
                if (i2 = !!t2.throw(e5).done) {
                  r2(0);
                }
              } else {
                throw e5;
              }
            }
          }
        }
        a2 = false;
      }
    }));
  };
}
var r = fromIterable;
function fromValue(e3) {
  return (r2) => {
    var t2 = false;
    r2(start((i2) => {
      if (1 === i2) {
        t2 = true;
      } else if (!t2) {
        t2 = true;
        r2(push(e3));
        r2(0);
      }
    }));
  };
}
function make(e3) {
  return (r2) => {
    var t2 = false;
    var i2 = e3({
      next(e4) {
        if (!t2) {
          r2(push(e4));
        }
      },
      complete() {
        if (!t2) {
          t2 = true;
          r2(0);
        }
      }
    });
    r2(start((e4) => {
      if (1 === e4 && !t2) {
        t2 = true;
        i2();
      }
    }));
  };
}
function makeSubject() {
  var e3;
  var r2;
  return {
    source: share(make((t2) => {
      e3 = t2.next;
      r2 = t2.complete;
      return teardownPlaceholder;
    })),
    next(r3) {
      if (e3) {
        e3(r3);
      }
    },
    complete() {
      if (r2) {
        r2();
      }
    }
  };
}
function fromPromise(e3) {
  return make((r2) => {
    e3.then((e4) => {
      Promise.resolve(e4).then(() => {
        r2.next(e4);
        r2.complete();
      });
    });
    return teardownPlaceholder;
  });
}
function subscribe(r2) {
  return (t2) => {
    var i2 = e2;
    var a2 = false;
    t2((e3) => {
      if (0 === e3) {
        a2 = true;
      } else if (0 === e3.tag) {
        (i2 = e3[0])(0);
      } else if (!a2) {
        r2(e3[0]);
        i2(0);
      }
    });
    return {
      unsubscribe() {
        if (!a2) {
          a2 = true;
          i2(1);
        }
      }
    };
  };
}
function publish(e3) {
  subscribe((e4) => {
  })(e3);
}
function toPromise(r2) {
  return new Promise((t2) => {
    var i2 = e2;
    var a2;
    r2((e3) => {
      if (0 === e3) {
        Promise.resolve(a2).then(t2);
      } else if (0 === e3.tag) {
        (i2 = e3[0])(0);
      } else {
        a2 = e3[0];
        i2(0);
      }
    });
  });
}

// ../node_modules/@urql/core/dist/urql-core-chunk.mjs
var rehydrateGraphQlError = (r2) => {
  if (r2 && "string" == typeof r2.message && (r2.extensions || "GraphQLError" === r2.name)) {
    return r2;
  } else if ("object" == typeof r2 && "string" == typeof r2.message) {
    return new GraphQLError(r2.message, r2.nodes, r2.source, r2.positions, r2.path, r2, r2.extensions || {});
  } else {
    return new GraphQLError(r2);
  }
};
var CombinedError = class extends Error {
  constructor(e3) {
    var r2 = (e3.graphQLErrors || []).map(rehydrateGraphQlError);
    var t2 = ((e4, r3) => {
      var t3 = "";
      if (e4) {
        return `[Network] ${e4.message}`;
      }
      if (r3) {
        for (var a2 = 0, n2 = r3.length; a2 < n2; a2++) {
          if (t3) {
            t3 += "\n";
          }
          t3 += `[GraphQL] ${r3[a2].message}`;
        }
      }
      return t3;
    })(e3.networkError, r2);
    super(t2);
    this.name = "CombinedError";
    this.message = t2;
    this.graphQLErrors = r2;
    this.networkError = e3.networkError;
    this.response = e3.response;
  }
  toString() {
    return this.message;
  }
};
var phash = (e3, r2) => {
  var t2 = 0 | (r2 || 5381);
  for (var a2 = 0, n2 = 0 | e3.length; a2 < n2; a2++) {
    t2 = (t2 << 5) + t2 + e3.charCodeAt(a2);
  }
  return t2;
};
var s2 = /* @__PURE__ */ new Set();
var f2 = /* @__PURE__ */ new WeakMap();
var stringify = (e3, r2) => {
  if (null === e3 || s2.has(e3)) {
    return "null";
  } else if ("object" != typeof e3) {
    return JSON.stringify(e3) || "";
  } else if (e3.toJSON) {
    return stringify(e3.toJSON(), r2);
  } else if (Array.isArray(e3)) {
    var t2 = "[";
    for (var a2 = 0, n2 = e3.length; a2 < n2; a2++) {
      if (t2.length > 1) {
        t2 += ",";
      }
      t2 += stringify(e3[a2], r2) || "null";
    }
    return t2 += "]";
  } else if (!r2 && (l2 !== NoopConstructor && e3 instanceof l2 || d2 !== NoopConstructor && e3 instanceof d2)) {
    return "null";
  }
  var o2 = Object.keys(e3).sort();
  if (!o2.length && e3.constructor && Object.getPrototypeOf(e3).constructor !== Object.prototype.constructor) {
    var i2 = f2.get(e3) || Math.random().toString(36).slice(2);
    f2.set(e3, i2);
    return stringify({
      __key: i2
    }, r2);
  }
  s2.add(e3);
  var c2 = "{";
  for (var v3 = 0, p2 = o2.length; v3 < p2; v3++) {
    var u3 = stringify(e3[o2[v3]], r2);
    if (u3) {
      if (c2.length > 1) {
        c2 += ",";
      }
      c2 += stringify(o2[v3], r2) + ":" + u3;
    }
  }
  s2.delete(e3);
  return c2 += "}";
};
var extract = (e3, r2, t2) => {
  if (null == t2 || "object" != typeof t2 || t2.toJSON || s2.has(t2)) {
  } else if (Array.isArray(t2)) {
    for (var a2 = 0, n2 = t2.length; a2 < n2; a2++) {
      extract(e3, `${r2}.${a2}`, t2[a2]);
    }
  } else if (t2 instanceof l2 || t2 instanceof d2) {
    e3.set(r2, t2);
  } else {
    s2.add(t2);
    for (var o2 in t2) {
      extract(e3, `${r2}.${o2}`, t2[o2]);
    }
  }
};
var stringifyVariables = (e3, r2) => {
  s2.clear();
  return stringify(e3, r2 || false);
};
var NoopConstructor = class {
};
var l2 = "undefined" != typeof File ? File : NoopConstructor;
var d2 = "undefined" != typeof Blob ? Blob : NoopConstructor;
var c = /("{3}[\s\S]*"{3}|"(?:\\.|[^"])*")/g;
var v2 = /(?:#[^\n\r]+)?(?:[\r\n]+|$)/g;
var replaceOutsideStrings = (e3, r2) => r2 % 2 == 0 ? e3.replace(v2, "\n") : e3;
var sanitizeDocument = (e3) => e3.split(c).map(replaceOutsideStrings).join("").trim();
var p = /* @__PURE__ */ new Map();
var u2 = /* @__PURE__ */ new Map();
var stringifyDocument = (e3) => {
  var t2;
  if ("string" == typeof e3) {
    t2 = sanitizeDocument(e3);
  } else if (e3.loc && u2.get(e3.__key) === e3) {
    t2 = e3.loc.source.body;
  } else {
    t2 = p.get(e3) || sanitizeDocument(print(e3));
    p.set(e3, t2);
  }
  if ("string" != typeof e3 && !e3.loc) {
    e3.loc = {
      start: 0,
      end: t2.length,
      source: {
        body: t2,
        name: "gql",
        locationOffset: {
          line: 1,
          column: 1
        }
      }
    };
  }
  return t2;
};
var hashDocument = (e3) => {
  var r2;
  if (e3.documentId) {
    r2 = phash(e3.documentId);
  } else {
    r2 = phash(stringifyDocument(e3));
    if (e3.definitions) {
      var t2 = getOperationName(e3);
      if (t2) {
        r2 = phash(`
# ${t2}`, r2);
      }
    }
  }
  return r2;
};
var keyDocument = (e3) => {
  var r2;
  var a2;
  if ("string" == typeof e3) {
    r2 = hashDocument(e3);
    a2 = u2.get(r2) || parse(e3, {
      noLocation: true
    });
  } else {
    r2 = e3.__key || hashDocument(e3);
    a2 = u2.get(r2) || e3;
  }
  if (!a2.loc) {
    stringifyDocument(a2);
  }
  a2.__key = r2;
  u2.set(r2, a2);
  return a2;
};
var createRequest = (e3, r2, t2) => {
  var a2 = r2 || {};
  var n2 = keyDocument(e3);
  var o2 = stringifyVariables(a2, true);
  var i2 = n2.__key;
  if ("{}" !== o2) {
    i2 = phash(o2, i2);
  }
  return {
    key: i2,
    query: n2,
    variables: a2,
    extensions: t2
  };
};
var getOperationName = (e3) => {
  for (var r2 = 0, t2 = e3.definitions.length; r2 < t2; r2++) {
    var n2 = e3.definitions[r2];
    if (n2.kind === e.OPERATION_DEFINITION) {
      return n2.name ? n2.name.value : void 0;
    }
  }
};
var getOperationType = (e3) => {
  for (var r2 = 0, t2 = e3.definitions.length; r2 < t2; r2++) {
    var n2 = e3.definitions[r2];
    if (n2.kind === e.OPERATION_DEFINITION) {
      return n2.operation;
    }
  }
};
var makeResult = (e3, r2, t2) => {
  if (!("data" in r2 || "errors" in r2 && Array.isArray(r2.errors))) {
    throw new Error("No Content");
  }
  var a2 = "subscription" === e3.kind;
  return {
    operation: e3,
    data: r2.data,
    error: Array.isArray(r2.errors) ? new CombinedError({
      graphQLErrors: r2.errors,
      response: t2
    }) : void 0,
    extensions: r2.extensions ? {
      ...r2.extensions
    } : void 0,
    hasNext: null == r2.hasNext ? a2 : r2.hasNext,
    stale: false
  };
};
var deepMerge = (e3, r2) => {
  if ("object" == typeof e3 && null != e3) {
    if (Array.isArray(e3)) {
      e3 = [...e3];
      for (var t2 = 0, a2 = r2.length; t2 < a2; t2++) {
        e3[t2] = deepMerge(e3[t2], r2[t2]);
      }
      return e3;
    }
    if (!e3.constructor || e3.constructor === Object) {
      e3 = {
        ...e3
      };
      for (var n2 in r2) {
        e3[n2] = deepMerge(e3[n2], r2[n2]);
      }
      return e3;
    }
  }
  return r2;
};
var mergeResultPatch = (e3, r2, t2, a2) => {
  var n2 = e3.error ? e3.error.graphQLErrors : [];
  var o2 = !!e3.extensions || !!(r2.payload || r2).extensions;
  var i2 = {
    ...e3.extensions,
    ...(r2.payload || r2).extensions
  };
  var s3 = r2.incremental;
  if ("path" in r2) {
    s3 = [r2];
  }
  var f3 = {
    data: e3.data
  };
  if (s3) {
    var _loop = function() {
      var e4 = s3[l3];
      if (Array.isArray(e4.errors)) {
        n2.push(...e4.errors);
      }
      if (e4.extensions) {
        Object.assign(i2, e4.extensions);
        o2 = true;
      }
      var r3 = "data";
      var t3 = f3;
      var d4 = [];
      if (e4.path) {
        d4 = e4.path;
      } else if (a2) {
        var c2 = a2.find((r4) => r4.id === e4.id);
        if (e4.subPath) {
          d4 = [...c2.path, ...e4.subPath];
        } else {
          d4 = c2.path;
        }
      }
      for (var v3 = 0, p2 = d4.length; v3 < p2; r3 = d4[v3++]) {
        t3 = t3[r3] = Array.isArray(t3[r3]) ? [...t3[r3]] : {
          ...t3[r3]
        };
      }
      if (e4.items) {
        var u3 = +r3 >= 0 ? r3 : 0;
        for (var y2 = 0, h2 = e4.items.length; y2 < h2; y2++) {
          t3[u3 + y2] = deepMerge(t3[u3 + y2], e4.items[y2]);
        }
      } else if (void 0 !== e4.data) {
        t3[r3] = deepMerge(t3[r3], e4.data);
      }
    };
    for (var l3 = 0, d3 = s3.length; l3 < d3; l3++) {
      _loop();
    }
  } else {
    f3.data = (r2.payload || r2).data || e3.data;
    n2 = r2.errors || r2.payload && r2.payload.errors || n2;
  }
  return {
    operation: e3.operation,
    data: f3.data,
    error: n2.length ? new CombinedError({
      graphQLErrors: n2,
      response: t2
    }) : void 0,
    extensions: o2 ? i2 : void 0,
    hasNext: null != r2.hasNext ? r2.hasNext : e3.hasNext,
    stale: false
  };
};
var makeErrorResult = (e3, r2, t2) => ({
  operation: e3,
  data: void 0,
  error: new CombinedError({
    networkError: r2,
    response: t2
  }),
  extensions: void 0,
  hasNext: false,
  stale: false
});
function makeFetchBody(e3) {
  var r2 = {
    query: void 0,
    documentId: void 0,
    operationName: getOperationName(e3.query),
    variables: e3.variables || void 0,
    extensions: e3.extensions
  };
  if ("documentId" in e3.query && e3.query.documentId && (!e3.query.definitions || !e3.query.definitions.length)) {
    r2.documentId = e3.query.documentId;
  } else if (!e3.extensions || !e3.extensions.persistedQuery || e3.extensions.persistedQuery.miss) {
    r2.query = stringifyDocument(e3.query);
  }
  return r2;
}
var makeFetchURL = (e3, r2) => {
  var t2 = "query" === e3.kind && e3.context.preferGetMethod;
  if (!t2 || !r2) {
    return e3.context.url;
  }
  var a2 = splitOutSearchParams(e3.context.url);
  for (var n2 in r2) {
    var o2 = r2[n2];
    if (o2) {
      a2[1].set(n2, "object" == typeof o2 ? stringifyVariables(o2) : o2);
    }
  }
  var i2 = a2.join("?");
  if (i2.length > 2047 && "force" !== t2) {
    e3.context.preferGetMethod = false;
    return e3.context.url;
  }
  return i2;
};
var splitOutSearchParams = (e3) => {
  var r2 = e3.indexOf("?");
  return r2 > -1 ? [e3.slice(0, r2), new URLSearchParams(e3.slice(r2 + 1))] : [e3, new URLSearchParams()];
};
var serializeBody = (e3, r2) => {
  if (r2 && !("query" === e3.kind && !!e3.context.preferGetMethod)) {
    var t2 = stringifyVariables(r2);
    var a2 = ((e4) => {
      var r3 = /* @__PURE__ */ new Map();
      if (l2 !== NoopConstructor || d2 !== NoopConstructor) {
        s2.clear();
        extract(r3, "variables", e4);
      }
      return r3;
    })(r2.variables);
    if (a2.size) {
      var n2 = new FormData();
      n2.append("operations", t2);
      n2.append("map", stringifyVariables({
        ...[...a2.keys()].map((e4) => [e4])
      }));
      var o2 = 0;
      for (var i2 of a2.values()) {
        n2.append("" + o2++, i2);
      }
      return n2;
    }
    return t2;
  }
};
var makeFetchOptions = (e3, r2) => {
  var t2 = {
    accept: "subscription" === e3.kind ? "text/event-stream, multipart/mixed" : "application/graphql-response+json, application/graphql+json, application/json, text/event-stream, multipart/mixed"
  };
  var a2 = ("function" == typeof e3.context.fetchOptions ? e3.context.fetchOptions() : e3.context.fetchOptions) || {};
  if (a2.headers) {
    if (((e4) => "has" in e4 && !Object.keys(e4).length)(a2.headers)) {
      a2.headers.forEach((e4, r3) => {
        t2[r3] = e4;
      });
    } else if (Array.isArray(a2.headers)) {
      a2.headers.forEach((e4, r3) => {
        if (Array.isArray(e4)) {
          if (t2[e4[0]]) {
            t2[e4[0]] = `${t2[e4[0]]},${e4[1]}`;
          } else {
            t2[e4[0]] = e4[1];
          }
        } else {
          t2[r3] = e4;
        }
      });
    } else {
      for (var n2 in a2.headers) {
        t2[n2.toLowerCase()] = a2.headers[n2];
      }
    }
  }
  var o2 = serializeBody(e3, r2);
  if ("string" == typeof o2 && !t2["content-type"]) {
    t2["content-type"] = "application/json";
  }
  return {
    ...a2,
    method: o2 ? "POST" : "GET",
    body: o2,
    headers: t2
  };
};
var y = "undefined" != typeof TextDecoder ? new TextDecoder() : null;
var h = /boundary="?([^=";]+)"?/i;
var m = /data: ?([^\n]+)/;
var toString = (e3) => "Buffer" === e3.constructor.name ? e3.toString() : y.decode(e3);
async function* streamBody(e3) {
  if (e3.body[Symbol.asyncIterator]) {
    for await (var r2 of e3.body) {
      yield toString(r2);
    }
  } else {
    var t2 = e3.body.getReader();
    var a2;
    try {
      while (!(a2 = await t2.read()).done) {
        yield toString(a2.value);
      }
    } finally {
      t2.cancel();
    }
  }
}
async function* split(e3, r2) {
  var t2 = "";
  var a2;
  for await (var n2 of e3) {
    t2 += n2;
    while ((a2 = t2.indexOf(r2)) > -1) {
      yield t2.slice(0, a2);
      t2 = t2.slice(a2 + r2.length);
    }
  }
}
async function* fetchOperation(e3, r2, t2) {
  var a2 = true;
  var n2 = null;
  var o2;
  try {
    yield await Promise.resolve();
    var i2 = (o2 = await (e3.context.fetch || fetch)(r2, t2)).headers.get("Content-Type") || "";
    var s3;
    if (/multipart\/mixed/i.test(i2)) {
      s3 = async function* parseMultipartMixed(e4, r3) {
        var t3 = e4.match(h);
        var a3 = "--" + (t3 ? t3[1] : "-");
        var n3 = true;
        var o3;
        for await (var i3 of split(streamBody(r3), "\r\n" + a3)) {
          if (n3) {
            n3 = false;
            var s4 = i3.indexOf(a3);
            if (s4 > -1) {
              i3 = i3.slice(s4 + a3.length);
            } else {
              continue;
            }
          }
          try {
            yield o3 = JSON.parse(i3.slice(i3.indexOf("\r\n\r\n") + 4));
          } catch (e5) {
            if (!o3) {
              throw e5;
            }
          }
          if (o3 && false === o3.hasNext) {
            break;
          }
        }
        if (o3 && false !== o3.hasNext) {
          yield {
            hasNext: false
          };
        }
      }(i2, o2);
    } else if (/text\/event-stream/i.test(i2)) {
      s3 = async function* parseEventStream(e4) {
        var r3;
        for await (var t3 of split(streamBody(e4), "\n\n")) {
          var a3 = t3.match(m);
          if (a3) {
            var n3 = a3[1];
            try {
              yield r3 = JSON.parse(n3);
            } catch (e5) {
              if (!r3) {
                throw e5;
              }
            }
            if (r3 && false === r3.hasNext) {
              break;
            }
          }
        }
        if (r3 && false !== r3.hasNext) {
          yield {
            hasNext: false
          };
        }
      }(o2);
    } else if (!/text\//i.test(i2)) {
      s3 = async function* parseJSON(e4) {
        yield JSON.parse(await e4.text());
      }(o2);
    } else {
      s3 = async function* parseMaybeJSON(e4) {
        var r3 = await e4.text();
        try {
          var t3 = JSON.parse(r3);
          if (true) {
            console.warn('Found response with content-type "text/plain" but it had a valid "application/json" response.');
          }
          yield t3;
        } catch (e5) {
          throw new Error(r3);
        }
      }(o2);
    }
    var f3;
    for await (var l3 of s3) {
      if (l3.pending && !n2) {
        f3 = l3.pending;
      } else if (l3.pending) {
        f3 = [...f3, ...l3.pending];
      }
      n2 = n2 ? mergeResultPatch(n2, l3, o2, f3) : makeResult(e3, l3, o2);
      a2 = false;
      yield n2;
      a2 = true;
    }
    if (!n2) {
      yield n2 = makeResult(e3, {}, o2);
    }
  } catch (r3) {
    if (!a2) {
      throw r3;
    }
    yield makeErrorResult(e3, o2 && (o2.status < 200 || o2.status >= 300) && o2.statusText ? new Error(o2.statusText) : r3, o2);
  }
}
function makeFetchSource(e3, r2, t2) {
  var a2;
  if ("undefined" != typeof AbortController) {
    t2.signal = (a2 = new AbortController()).signal;
  }
  return onEnd(() => {
    if (a2) {
      a2.abort();
    }
  })(filter((e4) => !!e4)(fromAsyncIterable(fetchOperation(e3, r2, t2))));
}

// ../node_modules/@urql/core/dist/urql-core.mjs
var collectTypes = (e3, r2) => {
  if (Array.isArray(e3)) {
    for (var t2 = 0, n2 = e3.length; t2 < n2; t2++) {
      collectTypes(e3[t2], r2);
    }
  } else if ("object" == typeof e3 && null !== e3) {
    for (var a2 in e3) {
      if ("__typename" === a2 && "string" == typeof e3[a2]) {
        r2.add(e3[a2]);
      } else {
        collectTypes(e3[a2], r2);
      }
    }
  }
  return r2;
};
var formatNode = (r2) => {
  if ("definitions" in r2) {
    var t2 = [];
    for (var n2 = 0, a2 = r2.definitions.length; n2 < a2; n2++) {
      var i2 = formatNode(r2.definitions[n2]);
      t2.push(i2);
    }
    return {
      ...r2,
      definitions: t2
    };
  }
  if ("directives" in r2 && r2.directives && r2.directives.length) {
    var o2 = [];
    var s3 = {};
    for (var c2 = 0, u3 = r2.directives.length; c2 < u3; c2++) {
      var p2 = r2.directives[c2];
      var d3 = p2.name.value;
      if ("_" !== d3[0]) {
        o2.push(p2);
      } else {
        d3 = d3.slice(1);
      }
      s3[d3] = p2;
    }
    r2 = {
      ...r2,
      directives: o2,
      _directives: s3
    };
  }
  if ("selectionSet" in r2) {
    var l3 = [];
    var v3 = r2.kind === e.OPERATION_DEFINITION;
    if (r2.selectionSet) {
      for (var f3 = 0, h2 = r2.selectionSet.selections.length; f3 < h2; f3++) {
        var k = r2.selectionSet.selections[f3];
        v3 = v3 || k.kind === e.FIELD && "__typename" === k.name.value && !k.alias;
        var y2 = formatNode(k);
        l3.push(y2);
      }
      if (!v3) {
        l3.push({
          kind: e.FIELD,
          name: {
            kind: e.NAME,
            value: "__typename"
          },
          _generated: true
        });
      }
      return {
        ...r2,
        selectionSet: {
          ...r2.selectionSet,
          selections: l3
        }
      };
    }
  }
  return r2;
};
var I = /* @__PURE__ */ new Map();
var formatDocument = (e3) => {
  var t2 = keyDocument(e3);
  var n2 = I.get(t2.__key);
  if (!n2) {
    I.set(t2.__key, n2 = formatNode(t2));
    Object.defineProperty(n2, "__key", {
      value: t2.__key,
      enumerable: false
    });
  }
  return n2;
};
function withPromise(e3) {
  var source$ = (r2) => e3(r2);
  source$.toPromise = () => toPromise(take(1)(filter((e4) => !e4.stale && !e4.hasNext)(source$)));
  source$.then = (e4, r2) => source$.toPromise().then(e4, r2);
  source$.subscribe = (e4) => subscribe(e4)(source$);
  return source$;
}
function makeOperation(e3, r2, t2) {
  return {
    ...r2,
    kind: e3,
    context: r2.context ? {
      ...r2.context,
      ...t2
    } : t2 || r2.context
  };
}
var addMetadata = (e3, r2) => makeOperation(e3.kind, e3, {
  meta: {
    ...e3.context.meta,
    ...r2
  }
});
var noop = () => {
};
function gql(n2) {
  var a2 = /* @__PURE__ */ new Map();
  var i2 = [];
  var o2 = [];
  var s3 = Array.isArray(n2) ? n2[0] : n2 || "";
  for (var c2 = 1; c2 < arguments.length; c2++) {
    var u3 = arguments[c2];
    if (u3 && u3.definitions) {
      o2.push(u3);
    } else {
      s3 += u3;
    }
    s3 += arguments[0][c2];
  }
  o2.unshift(keyDocument(s3));
  for (var p2 = 0; p2 < o2.length; p2++) {
    for (var d3 = 0; d3 < o2[p2].definitions.length; d3++) {
      var l3 = o2[p2].definitions[d3];
      if (l3.kind === e.FRAGMENT_DEFINITION) {
        var v3 = l3.name.value;
        var f3 = stringifyDocument(l3);
        if (!a2.has(v3)) {
          a2.set(v3, f3);
          i2.push(l3);
        } else if (a2.get(v3) !== f3) {
          console.warn("[WARNING: Duplicate Fragment] A fragment with name `" + v3 + "` already exists in this document.\nWhile fragment names may not be unique across your source, each name must be unique per document.");
        }
      } else {
        i2.push(l3);
      }
    }
  }
  return keyDocument({
    kind: e.DOCUMENT,
    definitions: i2
  });
}
var shouldSkip = ({ kind: e3 }) => "mutation" !== e3 && "query" !== e3;
var mapTypeNames = (e3) => {
  var r2 = formatDocument(e3.query);
  if (r2 !== e3.query) {
    var t2 = makeOperation(e3.kind, e3);
    t2.query = r2;
    return t2;
  } else {
    return e3;
  }
};
var cacheExchange = ({ forward: e3, client: r2, dispatchDebug: t2 }) => {
  var a2 = /* @__PURE__ */ new Map();
  var i2 = /* @__PURE__ */ new Map();
  var isOperationCached = (e4) => "query" === e4.kind && "network-only" !== e4.context.requestPolicy && ("cache-only" === e4.context.requestPolicy || a2.has(e4.key));
  return (o2) => {
    var s3 = map((e4) => {
      var i3 = a2.get(e4.key);
      t2({
        operation: e4,
        ...i3 ? {
          type: "cacheHit",
          message: "The result was successfully retried from the cache"
        } : {
          type: "cacheMiss",
          message: "The result could not be retrieved from the cache"
        },
        source: "cacheExchange"
      });
      var o3 = i3 || makeResult(e4, {
        data: null
      });
      o3 = {
        ...o3,
        operation: addMetadata(e4, {
          cacheOutcome: i3 ? "hit" : "miss"
        })
      };
      if ("cache-and-network" === e4.context.requestPolicy) {
        o3.stale = true;
        reexecuteOperation(r2, e4);
      }
      return o3;
    })(filter((e4) => !shouldSkip(e4) && isOperationCached(e4))(o2));
    var c2 = onPush((e4) => {
      var { operation: n2 } = e4;
      if (!n2) {
        return;
      }
      var o3 = n2.context.additionalTypenames || [];
      if ("subscription" !== e4.operation.kind) {
        o3 = ((e5) => [...collectTypes(e5, /* @__PURE__ */ new Set())])(e4.data).concat(o3);
      }
      if ("mutation" === e4.operation.kind || "subscription" === e4.operation.kind) {
        var s4 = /* @__PURE__ */ new Set();
        t2({
          type: "cacheInvalidation",
          message: `The following typenames have been invalidated: ${o3}`,
          operation: n2,
          data: {
            typenames: o3,
            response: e4
          },
          source: "cacheExchange"
        });
        for (var c3 = 0; c3 < o3.length; c3++) {
          var u3 = o3[c3];
          var p2 = i2.get(u3);
          if (!p2) {
            i2.set(u3, p2 = /* @__PURE__ */ new Set());
          }
          for (var d3 of p2.values()) {
            s4.add(d3);
          }
          p2.clear();
        }
        for (var l3 of s4.values()) {
          if (a2.has(l3)) {
            n2 = a2.get(l3).operation;
            a2.delete(l3);
            reexecuteOperation(r2, n2);
          }
        }
      } else if ("query" === n2.kind && e4.data) {
        a2.set(n2.key, e4);
        for (var v3 = 0; v3 < o3.length; v3++) {
          var f3 = o3[v3];
          var h2 = i2.get(f3);
          if (!h2) {
            i2.set(f3, h2 = /* @__PURE__ */ new Set());
          }
          h2.add(n2.key);
        }
      }
    })(e3(filter((e4) => "query" !== e4.kind || "cache-only" !== e4.context.requestPolicy)(map((e4) => addMetadata(e4, {
      cacheOutcome: "miss"
    }))(merge([map(mapTypeNames)(filter((e4) => !shouldSkip(e4) && !isOperationCached(e4))(o2)), filter((e4) => shouldSkip(e4))(o2)])))));
    return merge([s3, c2]);
  };
};
var reexecuteOperation = (e3, r2) => e3.reexecuteOperation(makeOperation(r2.kind, r2, {
  requestPolicy: "network-only"
}));
var T = /* @__PURE__ */ new Set();
var ssrExchange = (e3 = {}) => {
  var r2 = !!e3.staleWhileRevalidate;
  var t2 = !!e3.includeExtensions;
  var n2 = {};
  var i2 = [];
  var invalidate = (e4) => {
    i2.push(e4.operation.key);
    if (1 === i2.length) {
      Promise.resolve().then(() => {
        var e5;
        while (e5 = i2.shift()) {
          n2[e5] = null;
        }
      });
    }
  };
  var ssr = ({ client: i3, forward: o2 }) => (s3) => {
    var c2 = e3 && "boolean" == typeof e3.isClient ? !!e3.isClient : !i3.suspense;
    var u3 = o2(map(mapTypeNames)(filter((e4) => "teardown" === e4.kind || !n2[e4.key] || !!n2[e4.key].hasNext || "network-only" === e4.context.requestPolicy)(s3)));
    var p2 = map((e4) => {
      var o3 = ((e5, r3, t3) => ({
        operation: e5,
        data: r3.data ? JSON.parse(r3.data) : void 0,
        extensions: t3 && r3.extensions ? JSON.parse(r3.extensions) : void 0,
        error: r3.error ? new CombinedError({
          networkError: r3.error.networkError ? new Error(r3.error.networkError) : void 0,
          graphQLErrors: r3.error.graphQLErrors
        }) : void 0,
        stale: false,
        hasNext: !!r3.hasNext
      }))(e4, n2[e4.key], t2);
      if (r2 && !T.has(e4.key)) {
        o3.stale = true;
        T.add(e4.key);
        reexecuteOperation(i3, e4);
      }
      return {
        ...o3,
        operation: addMetadata(e4, {
          cacheOutcome: "hit"
        })
      };
    })(filter((e4) => "teardown" !== e4.kind && !!n2[e4.key] && "network-only" !== e4.context.requestPolicy)(s3));
    if (!c2) {
      u3 = onPush((e4) => {
        var { operation: r3 } = e4;
        if ("mutation" !== r3.kind) {
          var a2 = ((e5, r4) => {
            var t3 = {
              hasNext: e5.hasNext
            };
            if (void 0 !== e5.data) {
              t3.data = JSON.stringify(e5.data);
            }
            if (r4 && void 0 !== e5.extensions) {
              t3.extensions = JSON.stringify(e5.extensions);
            }
            if (e5.error) {
              t3.error = {
                graphQLErrors: e5.error.graphQLErrors.map((e6) => {
                  if (!e6.path && !e6.extensions) {
                    return e6.message;
                  }
                  return {
                    message: e6.message,
                    path: e6.path,
                    extensions: e6.extensions
                  };
                })
              };
              if (e5.error.networkError) {
                t3.error.networkError = "" + e5.error.networkError;
              }
            }
            return t3;
          })(e4, t2);
          n2[r3.key] = a2;
        }
      })(u3);
    } else {
      p2 = onPush(invalidate)(p2);
    }
    return merge([u3, p2]);
  };
  ssr.restoreData = (e4) => {
    for (var r3 in e4) {
      if (null !== n2[r3]) {
        n2[r3] = e4[r3];
      }
    }
  };
  ssr.extractData = () => {
    var e4 = {};
    for (var r3 in n2) {
      if (null != n2[r3]) {
        e4[r3] = n2[r3];
      }
    }
    return e4;
  };
  if (e3 && e3.initialState) {
    ssr.restoreData(e3.initialState);
  }
  return ssr;
};
var subscriptionExchange = ({ forwardSubscription: e3, enableAllOperations: r2, isSubscriptionOperation: t2 }) => ({ client: a2, forward: i2 }) => {
  var u3 = t2 || ((e4) => "subscription" === e4.kind || !!r2 && ("query" === e4.kind || "mutation" === e4.kind));
  return (r3) => {
    var t3 = mergeMap((t4) => {
      var { key: i3 } = t4;
      var u4 = filter((e4) => "teardown" === e4.kind && e4.key === i3)(r3);
      return takeUntil(u4)(((r4) => {
        var t5 = e3(makeFetchBody(r4), r4);
        return make((e4) => {
          var i4 = false;
          var o2;
          var u5;
          function nextResult(t6) {
            e4.next(u5 = u5 ? mergeResultPatch(u5, t6) : makeResult(r4, t6));
          }
          Promise.resolve().then(() => {
            if (i4) {
              return;
            }
            o2 = t5.subscribe({
              next: nextResult,
              error(t6) {
                if (Array.isArray(t6)) {
                  nextResult({
                    errors: t6
                  });
                } else {
                  e4.next(makeErrorResult(r4, t6));
                }
                e4.complete();
              },
              complete() {
                if (!i4) {
                  i4 = true;
                  if ("subscription" === r4.kind) {
                    a2.reexecuteOperation(makeOperation("teardown", r4, r4.context));
                  }
                  if (u5 && u5.hasNext) {
                    nextResult({
                      hasNext: false
                    });
                  }
                  e4.complete();
                }
              }
            });
          });
          return () => {
            i4 = true;
            if (o2) {
              o2.unsubscribe();
            }
          };
        });
      })(t4));
    })(filter((e4) => "teardown" !== e4.kind && u3(e4))(r3));
    var p2 = i2(filter((e4) => "teardown" === e4.kind || !u3(e4))(r3));
    return merge([t3, p2]);
  };
};
var debugExchange = ({ forward: e3 }) => {
  if (false) {
    return (r2) => e3(r2);
  } else {
    return (r2) => onPush((e4) => console.log("[Exchange debug]: Completed operation: ", e4))(e3(onPush((e4) => console.log("[Exchange debug]: Incoming operation: ", e4))(r2)));
  }
};
var fetchExchange = ({ forward: e3, dispatchDebug: r2 }) => (t2) => {
  var n2 = mergeMap((e4) => {
    var n3 = makeFetchBody(e4);
    var a3 = makeFetchURL(e4, n3);
    var i2 = makeFetchOptions(e4, n3);
    r2({
      type: "fetchRequest",
      message: "A fetch request is being executed.",
      operation: e4,
      data: {
        url: a3,
        fetchOptions: i2
      },
      source: "fetchExchange"
    });
    var s3 = takeUntil(filter((r3) => "teardown" === r3.kind && r3.key === e4.key)(t2))(makeFetchSource(e4, a3, i2));
    if (true) {
      return onPush((t3) => {
        var n4 = !t3.data ? t3.error : void 0;
        r2({
          type: n4 ? "fetchError" : "fetchSuccess",
          message: `A ${n4 ? "failed" : "successful"} fetch response has been returned.`,
          operation: e4,
          data: {
            url: a3,
            fetchOptions: i2,
            value: n4 || t3
          },
          source: "fetchExchange"
        });
      })(s3);
    }
    return s3;
  })(filter((e4) => "teardown" !== e4.kind && ("subscription" !== e4.kind || !!e4.context.fetchSubscriptions))(t2));
  var a2 = e3(filter((e4) => "teardown" === e4.kind || "subscription" === e4.kind && !e4.context.fetchSubscriptions)(t2));
  return merge([n2, a2]);
};
var composeExchanges = (e3) => ({ client: r2, forward: t2, dispatchDebug: n2 }) => e3.reduceRight((e4, t3) => {
  var a2 = false;
  return t3({
    client: r2,
    forward(r3) {
      if (true) {
        if (a2) {
          throw new Error("forward() must only be called once in each Exchange.");
        }
        a2 = true;
      }
      return share(e4(share(r3)));
    },
    dispatchDebug(e5) {
      n2({
        timestamp: Date.now(),
        source: t3.name,
        ...e5
      });
    }
  });
}, t2);
var mapExchange = ({ onOperation: e3, onResult: r2, onError: t2 }) => ({ forward: n2 }) => (a2) => mergeMap((e4) => {
  if (t2 && e4.error) {
    t2(e4.error, e4.operation);
  }
  var n3 = r2 && r2(e4) || e4;
  return "then" in n3 ? fromPromise(n3) : fromValue(n3);
})(n2(mergeMap((r3) => {
  var t3 = e3 && e3(r3) || r3;
  return "then" in t3 ? fromPromise(t3) : fromValue(t3);
})(a2)));
var fallbackExchange = ({ dispatchDebug: e3 }) => (r2) => {
  if (true) {
    r2 = onPush((r3) => {
      if ("teardown" !== r3.kind && true) {
        var t2 = `No exchange has handled operations of kind "${r3.kind}". Check whether you've added an exchange responsible for these operations.`;
        e3({
          type: "fallbackCatch",
          message: t2,
          operation: r3,
          source: "fallbackExchange"
        });
        console.warn(t2);
      }
    })(r2);
  }
  return filter((e4) => false)(r2);
};
var C = function Client(e3) {
  if (!e3.url) {
    throw new Error("You are creating an urql-client without a url.");
  }
  var r2 = 0;
  var t2 = /* @__PURE__ */ new Map();
  var n2 = /* @__PURE__ */ new Map();
  var a2 = /* @__PURE__ */ new Set();
  var i2 = [];
  var o2 = {
    url: e3.url,
    fetchSubscriptions: e3.fetchSubscriptions,
    fetchOptions: e3.fetchOptions,
    fetch: e3.fetch,
    preferGetMethod: e3.preferGetMethod,
    requestPolicy: e3.requestPolicy || "cache-first"
  };
  var s3 = makeSubject();
  function nextOperation(e4) {
    if ("mutation" === e4.kind || "teardown" === e4.kind || !a2.has(e4.key)) {
      if ("teardown" === e4.kind) {
        a2.delete(e4.key);
      } else if ("mutation" !== e4.kind) {
        a2.add(e4.key);
      }
      s3.next(e4);
    }
  }
  var c2 = false;
  function dispatchOperation(e4) {
    if (e4) {
      nextOperation(e4);
    }
    if (!c2) {
      c2 = true;
      while (c2 && (e4 = i2.shift())) {
        nextOperation(e4);
      }
      c2 = false;
    }
  }
  var makeResultSource = (e4) => {
    var r3 = takeUntil(filter((r4) => "teardown" === r4.kind && r4.key === e4.key)(s3.source))(filter((r4) => r4.operation.kind === e4.kind && r4.operation.key === e4.key && (!r4.operation.context._instance || r4.operation.context._instance === e4.context._instance))(E));
    if ("query" !== e4.kind) {
      r3 = takeWhile((e5) => !!e5.hasNext, true)(r3);
    } else {
      r3 = switchMap((r4) => {
        var t3 = fromValue(r4);
        return r4.stale || r4.hasNext ? t3 : merge([t3, map(() => {
          r4.stale = true;
          return r4;
        })(take(1)(filter((r5) => r5.key === e4.key)(s3.source)))]);
      })(r3);
    }
    if ("mutation" !== e4.kind) {
      r3 = onEnd(() => {
        a2.delete(e4.key);
        t2.delete(e4.key);
        n2.delete(e4.key);
        c2 = false;
        for (var r4 = i2.length - 1; r4 >= 0; r4--) {
          if (i2[r4].key === e4.key) {
            i2.splice(r4, 1);
          }
        }
        nextOperation(makeOperation("teardown", e4, e4.context));
      })(onPush((r4) => {
        if (r4.stale) {
          if (!r4.hasNext) {
            a2.delete(e4.key);
          } else {
            for (var n3 = 0; n3 < i2.length; n3++) {
              var o3 = i2[n3];
              if (o3.key === r4.operation.key) {
                a2.delete(o3.key);
                break;
              }
            }
          }
        } else if (!r4.hasNext) {
          a2.delete(e4.key);
        }
        t2.set(e4.key, r4);
      })(r3));
    } else {
      r3 = onStart(() => {
        nextOperation(e4);
      })(r3);
    }
    return share(r3);
  };
  var u3 = this instanceof Client ? this : Object.create(Client.prototype);
  var p2 = Object.assign(u3, {
    suspense: !!e3.suspense,
    operations$: s3.source,
    reexecuteOperation(e4) {
      if ("teardown" === e4.kind) {
        dispatchOperation(e4);
      } else if ("mutation" === e4.kind) {
        i2.push(e4);
        Promise.resolve().then(dispatchOperation);
      } else if (n2.has(e4.key)) {
        var r3 = false;
        for (var t3 = 0; t3 < i2.length; t3++) {
          if (i2[t3].key === e4.key) {
            i2[t3] = e4;
            r3 = true;
          }
        }
        if (!(r3 || a2.has(e4.key) && "network-only" !== e4.context.requestPolicy)) {
          i2.push(e4);
          Promise.resolve().then(dispatchOperation);
        } else {
          a2.delete(e4.key);
          Promise.resolve().then(dispatchOperation);
        }
      }
    },
    createRequestOperation(e4, t3, n3) {
      if (!n3) {
        n3 = {};
      }
      var a3;
      if ("teardown" !== e4 && (a3 = getOperationType(t3.query)) !== e4) {
        throw new Error(`Expected operation of type "${e4}" but found "${a3}"`);
      }
      return makeOperation(e4, t3, {
        _instance: "mutation" === e4 ? r2 = r2 + 1 | 0 : void 0,
        ...o2,
        ...n3,
        requestPolicy: n3.requestPolicy || o2.requestPolicy,
        suspense: n3.suspense || false !== n3.suspense && p2.suspense
      });
    },
    executeRequestOperation(e4) {
      if ("mutation" === e4.kind) {
        return withPromise(makeResultSource(e4));
      }
      return withPromise(lazy(() => {
        var r3 = n2.get(e4.key);
        if (!r3) {
          n2.set(e4.key, r3 = makeResultSource(e4));
        }
        r3 = onStart(() => {
          dispatchOperation(e4);
        })(r3);
        var a3 = t2.get(e4.key);
        if ("query" === e4.kind && a3 && (a3.stale || a3.hasNext)) {
          return switchMap(fromValue)(merge([r3, filter((r4) => r4 === t2.get(e4.key))(fromValue(a3))]));
        } else {
          return r3;
        }
      }));
    },
    executeQuery(e4, r3) {
      var t3 = p2.createRequestOperation("query", e4, r3);
      return p2.executeRequestOperation(t3);
    },
    executeSubscription(e4, r3) {
      var t3 = p2.createRequestOperation("subscription", e4, r3);
      return p2.executeRequestOperation(t3);
    },
    executeMutation(e4, r3) {
      var t3 = p2.createRequestOperation("mutation", e4, r3);
      return p2.executeRequestOperation(t3);
    },
    readQuery(e4, r3, t3) {
      var n3 = null;
      subscribe((e5) => {
        n3 = e5;
      })(p2.query(e4, r3, t3)).unsubscribe();
      return n3;
    },
    query: (e4, r3, t3) => p2.executeQuery(createRequest(e4, r3), t3),
    subscription: (e4, r3, t3) => p2.executeSubscription(createRequest(e4, r3), t3),
    mutation: (e4, r3, t3) => p2.executeMutation(createRequest(e4, r3), t3)
  });
  var d3 = noop;
  if (true) {
    var { next: f3, source: x } = makeSubject();
    p2.subscribeToDebugTarget = (e4) => subscribe(e4)(x);
    d3 = f3;
  }
  var w = composeExchanges(e3.exchanges);
  var E = share(w({
    client: p2,
    dispatchDebug: d3,
    forward: fallbackExchange({
      dispatchDebug: d3
    })
  })(s3.source));
  publish(E);
  return p2;
};
var Q = C;
export {
  C as Client,
  CombinedError,
  cacheExchange,
  composeExchanges,
  Q as createClient,
  createRequest,
  debugExchange,
  mapExchange as errorExchange,
  fetchExchange,
  formatDocument,
  gql,
  makeErrorResult,
  makeOperation,
  makeResult,
  mapExchange,
  mergeResultPatch,
  ssrExchange,
  stringifyDocument,
  stringifyVariables,
  subscriptionExchange
};
//# sourceMappingURL=@urql_core.js.map
