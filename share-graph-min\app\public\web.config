<?xml version="1.0" encoding="UTF-8"?>

<!--
  This web.config configures the request from client to handle versioning of
  embed.js and integration.js files. Place this file at the root of Integration/ virtual directory
-->
<configuration>
  <system.webServer>
    <rewrite>
      <rules>
        <rule name="ReactRouter Routes" stopProcessing="true">
          <match url=".*" />
          <conditions logicalGrouping="MatchAll">
            <add input="{REQUEST_FILENAME}" matchType="IsFile" negate="true" />
            <add input="{REQUEST_FILENAME}" matchType="IsDirectory" negate="true" />
            <add input="{REQUEST_URI}" pattern="^/(docs)" negate="true" />
            <add input="{URL}" negate="true" pattern="\.js$" />
            <add input="{URL}" negate="true" pattern="\.css$" />
          </conditions>
          <action type="Rewrite" url="index.html" />
        </rule>
      </rules>
    </rewrite>
  </system.webServer>
</configuration>
