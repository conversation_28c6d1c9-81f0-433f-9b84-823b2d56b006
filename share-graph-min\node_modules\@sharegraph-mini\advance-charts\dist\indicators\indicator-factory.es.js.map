{"version": 3, "file": "indicator-factory.es.js", "sources": ["../../src/indicators/indicator-factory.ts"], "sourcesContent": ["import {I<PERSON>hart<PERSON>pi} from \"lightweight-charts\";\r\nimport {ChartIndicator, ChartIndicatorOptions} from \"./abstract-indicator\";\r\nimport {merge} from \"es-toolkit\";\r\n\r\nexport interface IChartIndicatorConstructor<IOptions extends ChartIndicatorOptions = ChartIndicatorOptions, IIndicatorData extends readonly number[] = number[]> {\r\n  new (\r\n    chart: IChartApi,\r\n    options?: Partial<IOptions>,\r\n    paneIndex?: number\r\n  ): ChartIndicator<IOptions, IIndicatorData >;\r\n}\r\n\r\nexport class IndicatorFactory {\r\n  private static registry = new Map<string, [IChartIndicatorConstructor, object | undefined]>();\r\n\r\n  static registerIndicator<IOptions extends ChartIndicatorOptions, IIndicatorData extends readonly number[] = number[]>(name: string, indicatorClass: IChartIndicatorConstructor<IOptions, IIndicatorData>, defaultOptions?: Partial<IOptions>) {\r\n    this.registry.set(name, [indicatorClass as unknown as IChartIndicatorConstructor, defaultOptions]);\r\n  }\r\n\r\n  static indicatorRegistered(name: string) {\r\n    return this.registry.has(name);\r\n  }\r\n\r\n  static createIndicator(name: string, chartApi: IChartApi, options?: Partial<ChartIndicatorOptions>, paneIndex?: number) {\r\n    const result = this.registry.get(name);\r\n    if (!result) {\r\n      throw new Error(`Indicator \"${name}\" not registered. Available: ${Array.from(this.registry.keys()).join(', ')}`);\r\n    }\r\n\r\n    const [IndicatorClass, defaultSettings] = result;\r\n    return new IndicatorClass(chartApi, (options || defaultSettings) ? merge(structuredClone(defaultSettings) ?? {}, options ?? {}) : undefined, paneIndex);\r\n  }\r\n}\r\n"], "names": ["IndicatorFactory", "name", "indicatorClass", "defaultOptions", "chartApi", "options", "paneIndex", "result", "IndicatorClass", "defaultSettings", "merge", "__publicField"], "mappings": ";;;;AAYO,MAAMA,EAAiB;AAAA,EAG5B,OAAO,kBAA+GC,GAAcC,GAAsEC,GAAoC;AAC5O,SAAK,SAAS,IAAIF,GAAM,CAACC,GAAyDC,CAAc,CAAC;AAAA,EAAA;AAAA,EAGnG,OAAO,oBAAoBF,GAAc;AAChC,WAAA,KAAK,SAAS,IAAIA,CAAI;AAAA,EAAA;AAAA,EAG/B,OAAO,gBAAgBA,GAAcG,GAAqBC,GAA0CC,GAAoB;AACtH,UAAMC,IAAS,KAAK,SAAS,IAAIN,CAAI;AACrC,QAAI,CAACM;AACH,YAAM,IAAI,MAAM,cAAcN,CAAI,gCAAgC,MAAM,KAAK,KAAK,SAAS,KAAM,CAAA,EAAE,KAAK,IAAI,CAAC,EAAE;AAG3G,UAAA,CAACO,GAAgBC,CAAe,IAAIF;AAC1C,WAAO,IAAIC,EAAeJ,GAAWC,KAAWI,IAAmBC,EAAM,gBAAgBD,CAAe,KAAK,IAAIJ,KAAW,CAAE,CAAA,IAAI,QAAWC,CAAS;AAAA,EAAA;AAE1J;AAnBEK,EADWX,GACI,YAAW,oBAAI,IAA8D;"}