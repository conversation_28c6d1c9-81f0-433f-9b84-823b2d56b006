import clsx from 'clsx';
import React, { useEffect, useState } from 'react';
import { useChartContext } from '../context';
import useClickOutside from '../../../hooks/useClickOutside';

interface IEventPointProps {
  type: 'dividend' | 'earning';
  children: React.ReactNode;
  xPosition: number | null;
}

const EventPoint: React.FC<IEventPointProps> = ({
  xPosition,
  type,
  children,
}) => {
  const [isOpenTooltip, setIsOpenTooltip] = useState(false);
  const { getChart } = useChartContext();

  const ref = useClickOutside<HTMLDivElement>(() => {
    setIsOpenTooltip(false); 
  });


  useEffect(() => {
    const chart = getChart();
    if (!chart) return;

    const closeTooltip = () => {
      setIsOpenTooltip(false);
    };

    chart.chartApi.timeScale().subscribeVisibleLogicalRangeChange(closeTooltip);

    return () => {
      chart.chartApi
        .timeScale()
        .unsubscribeVisibleLogicalRangeChange(closeTooltip);
    };
  }, [getChart]);

  const getContent = () => {
    switch (type) {
      case 'dividend':
        return 'D';

      default:
        return 'E';
    }
  };

  if (!xPosition) return null;

  return (
    <div className="event-marker" style={{ left: xPosition }} ref={ref}>
      <button
        className={clsx(`event-marker__content ${type}`, {
          active: isOpenTooltip,
        })}
        onClick={() => setIsOpenTooltip((prev) => !prev)}
      >
        {getContent()}
      </button>
      {isOpenTooltip && (
        <div className={`event-marker__tooltip ${type}`}>
          <h2 className="event-marker__tooltip__title">
            <span className="event-marker__tooltip__icon">{getContent()}</span>
            <span>{type === 'dividend' ? 'Dividend' : 'Earning'}</span>
          </h2>
          <div>{children}</div>
        </div>
      )}
    </div>
  );
};

export default EventPoint;
