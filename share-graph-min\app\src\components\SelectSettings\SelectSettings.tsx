import ChartTypeDropdown from "../Dropdown/ChartTypeDropdown"
import IndicatorDropdown from "../Dropdown/IndicatorDropdown"
import SettingsDropdown from "../Dropdown/SettingsDropdown"
import OverlayDropdown from "../Dropdown/OverlayDropdown"
import MobileSelectSettings from "../MobileSelectSettings"
import useMobileDetect from "../../hooks/useMobileDetect"

const SelectSettings = () => {
  const isMobile = useMobileDetect();

  return (
    <div className='select-settings'>
      {!isMobile ? (
        <div className="select-settings__pc">
          <ChartTypeDropdown />
          <IndicatorDropdown />
          <OverlayDropdown />
          <SettingsDropdown />
        </div>
      ) : (
        <div className="select-settings__mobile">
          <MobileSelectSettings />
        </div>
      )}
    </div>
  )
}

export default SelectSettings;