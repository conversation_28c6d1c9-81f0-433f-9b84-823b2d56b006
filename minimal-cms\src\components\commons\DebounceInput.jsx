import { useEffect, useState } from "react";
import { Input } from "antd";

import useDebounce from "../../hooks/useDebounce";

const DebounceInput = ({ value, onChange, ...props }) => {
  const [val, setVal] = useState(value);

  const debouncedOnChange = useDebounce(() => {
    onChange(val);
  });

  const handleChange = (e) => {
    setVal(e.target.value);
    debouncedOnChange();
  };

  useEffect(() => {
    setVal(value);
  }, [value]);

  return <Input {...props} value={val} onChange={handleChange} />;
};

export default DebounceInput;
