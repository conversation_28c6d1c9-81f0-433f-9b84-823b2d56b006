import React, { useState } from "react";
import Modal from "./Modal";
import RangeDatePicker from "../RangeDatepicker";
import { TRangeDatePickerValue } from "../../types/datepicker";
import { useAppStore } from "../../stores/useAppStore";
import dayjs from "dayjs";
import { DATE_FORMAT } from "../../constants/common";
import { i18n } from "@euroland/libs";

interface CustomRangeChartModalProps {
  visible: boolean;
  onClose: () => void;
}

const CustomRangeChartModal: React.FC<CustomRangeChartModalProps> = ({
  visible,
  onClose,
}) => {
  const {fromDate, toDate} = useAppStore((state) => state.chartRange);
  const setChartRange = useAppStore((state) => state.setChartRange);

  const [value, setValue] = useState<TRangeDatePickerValue>({
    startDate: fromDate ? dayjs(fromDate).toDate() : null,
    endDate: toDate ? dayjs(toDate).toDate() : null,
  } as TRangeDatePickerValue);
  const { startDate, endDate } = value;


  const handleApplyCustomRange = () => {
    onClose();

    setChartRange({
      period: "Custom",
      interval: "1m", // in case of custom range, we don't care about interval
      fromDate: dayjs(startDate).format(DATE_FORMAT),
      toDate: dayjs(endDate).format(DATE_FORMAT),
    })
  }

  return (
    <Modal
      isOpen={visible}
      onClose={onClose}
      title={i18n.translate("dateRange")}
      okText={i18n.translate("go")}
      onOk={handleApplyCustomRange}
    >
      <RangeDatePicker onChange={setValue} startDate={startDate} endDate={endDate} />
    </Modal>
  );
};

export default CustomRangeChartModal;
