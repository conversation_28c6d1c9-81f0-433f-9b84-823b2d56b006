import { describe, expect, it } from "vitest";
import { convertObjectToPathMap, getQueryParameters, mergeSettings, parseDateRangesAndInterval, TQueryParams } from "../../utils/config";
import { IDefaultSettingConfigs } from "../../types/defaultConfig";
describe('getQueryParameters', () => {
    it('should return an empty object when there are no query parameters', () => {
        const url = '';
        const expected: TQueryParams = {};

        expect(getQueryParameters(url)).toEqual(expected);
    });

    it('should correctly parse a single query parameter', () => {
        const url = '?user=alice';
        const expected: TQueryParams = { user: 'alice' };

        expect(getQueryParameters(url)).toEqual(expected);
    });

    it('should correctly parse multiple query parameters', () => {
        const url = '?user=alice&age=30&country=USA';
        const expected: TQueryParams = {
            user: 'alice',
            age: '30',
            country: 'USA',
        };

        expect(getQueryParameters(url)).toEqual(expected);
    });

    it('should decode URL-encoded query parameters', () => {
        const url = '?name=John%20Doe&city=New%20York';
        const expected: TQueryParams = {
            name: 'John Doe',
            city: 'New York',
        };

        expect(getQueryParameters(url)).toEqual(expected);
    });

    it('should handle query parameters with no value', () => {
        const url = '?flag&user=alice';
        const expected: TQueryParams = {
            flag: '',
            user: 'alice',
        };

        expect(getQueryParameters(url)).toEqual(expected);
    });

    it('should handle query parameters with empty value', () => {
        const url = '?user=';
        const expected: TQueryParams = { user: '' };

        expect(getQueryParameters(url)).toEqual(expected);
    });

    it('should overwrite duplicate query parameters with the last occurrence', () => {
        const url = '?user=alice&user=bob';
        const expected: TQueryParams = { user: 'bob' };

        expect(getQueryParameters(url)).toEqual(expected);
    });

    it('should ignore pairs without a key', () => {
        const url = '?=value&user=alice';
        const expected: TQueryParams = { user: 'alice' };

        expect(getQueryParameters(url)).toEqual(expected);
    });


    it('should handle URLs with hash fragments', () => {
        const url = '?user=alice#section1';
        const expected: TQueryParams = { user: 'alice#section1' };

        // Note: The current implementation does not handle hash fragments separately.
        // The hash will be included in the last parameter's value.
        expect(getQueryParameters(url)).toEqual(expected);
    });


    it('should return an empty object for a URL with only "?"', () => {
        const url = '?';
        const expected: TQueryParams = {};

        expect(getQueryParameters(url)).toEqual(expected);
    });


    it('should handle query parameters with plus signs as spaces', () => {
        const url = '?name=John%2BDoe&city=New%2BYork';
        const expected: TQueryParams = {
            name: 'John+Doe', // decodeURIComponent does not convert '+' to ' '
            city: 'New+York',
        };

        // Note: In URL encoding, '+' is often used to represent spaces, but decodeURIComponent does not handle this.
        // If handling '+' as spaces is desired, additional processing is needed.
        expect(getQueryParameters(url)).toEqual(expected);
    });
});

describe('convertObjectToPathMap function', () => {
    it('should correctly convert a nested object to a path map', () => {
        const input = {
            size: {
                width: '100%',
                height: '100%',
            },
            instrumentId: '32864',
            dateRangesAndInterval: [],
            valueTracking: 'legend',
            tickerSettings: {
                dataFields: 'open,high,low,close,volume',
                template: 'ticker',
            },
        }
        const expected = {
            instrumentId: ['instrumentId'],
            valueTracking: ['valueTracking'],
            width: ['size', 'width'],
            height: ['size', 'height'],
            dataFields: ['tickerSettings', 'dataFields'],
            template: ['tickerSettings', 'template'],
        };

        const result = convertObjectToPathMap(input as unknown as IDefaultSettingConfigs);
        expect(result).toEqual(expected);
    })
});

describe('parseDateRangesAndInterval', () => {
    it('should parse valid period and intervals correctly', () => {
        const input = "1D|1m,5m;5D|15m,30m";
        const result = parseDateRangesAndInterval(input);
        expect(result).toEqual([
            { period: "1D", intervals: ["1m", "5m"] },
            { period: "5D", intervals: ["15m", "30m"] },
        ]);
    });

    it('should return default intervals if no valid intervals provided', () => {
        const input = "1D|invalidInterval;5D|";
        const result = parseDateRangesAndInterval(input);
        expect(result).toEqual([
            { period: "1D", intervals: ["1m", "5m", "10m", "15m", "30m", "1h"] },
            { period: "5D", intervals: ["1m", "5m", "10m", "15m", "30m", "1h"] },
        ]);
    });

    it('should ignore invalid periods', () => {
        const input = "InvalidPeriod|1m;5D|15m";
        const result = parseDateRangesAndInterval(input);
        expect(result).toEqual([
            { period: "5D", intervals: ["15m"] },
        ]);
    });

    it('should handle empty input gracefully', () => {
        const input = "";
        const result = parseDateRangesAndInterval(input);
        expect(result).toEqual([]);
    });

    it('should return default intervals for a valid period with no intervals provided', () => {
        const input = "1D;";
        const result = parseDateRangesAndInterval(input);
        expect(result).toEqual([
            { period: "1D", intervals: ["1m", "5m", "10m", "15m", "30m", "1h"] },
        ]);
    });

    it('should handle mixed valid and invalid intervals', () => {
        const input = "1D|1m,invalidInterval,5m";
        const result = parseDateRangesAndInterval(input);
        expect(result).toEqual([
            { period: "1D", intervals: ["1m", "5m"] },
        ]);
    });
});

describe('mergeSettings', () => {
    it('should handle with configuration correctly', () => {
        const input = {
          
            defaultSelectedInstrumentId: 32864,
            instrumentIds: [32864],
            dateRangesAndInterval: [
                {
                    period: '1D',
                    intervals: ["1m", "5m", "10m", "15m", "30m", "1h"],
                },
            ],
        }
        const expectedOutput = {
          
            defaultSelectedInstrumentId: 32864,
            instrumentIds: [32864],
            dateRangesAndInterval: [
                { period: "1D", intervals: ["1m", "5m"] },
                { period: "5D", intervals: ["15m", "30m"] },
            ],
        }
        const params = { width: '100px', dateRangesAndInterval: '1D|1m,5m;5D|15m,30m', defaultSelectedInstrumentId: '32864', instrumentIds: '32864' };
        const result = mergeSettings(input as IDefaultSettingConfigs, params);
        expect(result).toEqual(expectedOutput);
    });
});