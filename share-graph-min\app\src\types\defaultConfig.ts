import { IChartRangeStore, TChartRangePeriod, TChartSettingsStore, TChartType, TTimeIntervals } from "./store";

export interface IDateRangeAndInterval {
    period: TChartRangePeriod;
    intervals: TTimeIntervals[];
}

export type TDefaultRange = Pick<IChartRangeStore, 'period' | 'interval'>

export type TChartConfiguration = {
    gridColor: string;
    axesFontsize: string;
    axesColor: string;
    chartType: TChartType;
    height: number; // px
} & TChartSettingsStore;

export type TSettingPosition = 'top' | 'right';

export interface IDefaultSettingConfigs {
   
    defaultSelectedInstrumentId: number;
    instrumentIds: number[];
    realtimeIds: number[];
    companyCode: string;
    settingPosition?: TSettingPosition;
    dateRangesAndInterval: IDateRangeAndInterval[];
    defaultRange: TDefaultRange;
    general: {
        locale: string;
        fontFamily: string;
        fontSize: string;
        fontColor: string;
        upColor: string;
        downColor: string;
        primaryColor: string;
    };
    chartConfiguration: TChartConfiguration;
    tickerSettings: {
        dataFields: string;
        template: 'ticker' | 'table';
        refreshTickerTime: number;
    };
    valueTracking: 'legend' | 'tooltip';
    events: string;
    customRange: boolean;
    unControllerUI: string;
    footerText: string;
}