import {IChartApi, ISeriesApi, LineSeries, Nominal, SeriesType, SingleValueData, Time} from "lightweight-charts";
import {SMA} from "technicalindicators";
import {ChartIndicator, ChartIndicatorOptions} from "./abstract-indicator";
import {RegionPrimitive} from "../custom-primitive/primitive/region";
import {autoScaleInfoProviderCreator} from "../helpers/utils";
import {Context} from "../helpers/execution-indicator";
import {max, min} from "es-toolkit/compat";
import {ensureDefined} from "../helpers/assertions";

export interface StochasticIndicatorOptions extends ChartIndicatorOptions {
  color: string,
  signalColor: string,
  period: number
  priceLineColor: string
  backgroundColor: string
  signalPeriod: number
}

export const defaultOptions: StochasticIndicatorOptions = {
  color: "#2962ff",
  signalColor: '#ff6d00',
  priceLineColor: "rgba(150, 150, 150, 0.35)",
  backgroundColor: "#2196f31a",
  period: 14,
  overlay: false,
  signalPeriod: 3
}

export type KStochasticLine = Nominal<number, 'K_Stochastic'>
export type DStochasticLine = Nominal<number, 'D_Stochastic'>
export type StochasticData = [KStochasticLine, DStochasticLine]

export default class StochasticIndicator extends ChartIndicator<StochasticIndicatorOptions, StochasticData> {
  kSeries: ISeriesApi<SeriesType>
  dSeries: ISeriesApi<SeriesType>

  constructor(chart: IChartApi, options?: Partial<StochasticIndicatorOptions>, paneIndex?: number) {
    super(chart, options)
    this.kSeries = chart.addSeries(LineSeries, {
      color: this.options.color,
      lineWidth: 1,
      priceLineVisible: false,
      crosshairMarkerVisible: false,
      priceScaleId: 'Stochastic',
    }, paneIndex);

    this.dSeries = chart.addSeries(LineSeries, {
      color: this.options.signalColor,
      lineWidth: 1,
      priceLineVisible: false,
      crosshairMarkerVisible: false,
      priceScaleId: 'Stochastic',
      autoscaleInfoProvider: autoScaleInfoProviderCreator({maxValue: 90, minValue: 10})
    }, paneIndex);

    this.kSeries.attachPrimitive(
      new RegionPrimitive({
        upPrice: 80,
        lowPrice: 20,
        lineColor: this.options.priceLineColor,
        backgroundColor: this.options.backgroundColor,
      })
    );
  }

  getDefaultOptions(): StochasticIndicatorOptions {
    return defaultOptions
  }
  
  formula(c: Context): StochasticData | undefined {
    const highSeries = c.new_var(c.symbol.high, this.options.period)
    const lowSeries = c.new_var(c.symbol.low, this.options.period)
    const dSmaSeries = c.new_var(NaN, this.options.signalPeriod)

    if(!highSeries.calculable() || !lowSeries.calculable()) return;

    const highest = ensureDefined(max(highSeries.getAll()))
    const lowest = ensureDefined(min(lowSeries.getAll()));

    let k = (c.symbol.close - lowest) / (highest - lowest) * 100;
    k = isNaN(k) ? 0 : k

    dSmaSeries.set(k);

    if(!dSmaSeries.calculable()) return;

    const [d] = new SMA({
      period: this.options.signalPeriod, 
      values: dSmaSeries.getAll()
    }).result;

    return [k as KStochasticLine, d as DStochasticLine]
  }

  applyIndicatorData() {
    const kData: SingleValueData[] = []
    const dData: SingleValueData[] = []

    for(const bar of this._executionContext.data) {
      const value = bar.value;
      const time = bar.time as Time;
      if(!value) continue;

      kData.push({time, value: value[0]})
      dData.push({time, value: value[1]})
    }
    this.kSeries.setData(kData)
    this.dSeries.setData(dData)
  }

  remove() {
    super.remove()
    this.chart.removeSeries(this.kSeries);
    this.chart.removeSeries(this.dSeries);
  }

  _applyOptions() {
    this.kSeries.applyOptions({color: this.options.color})
    this.dSeries.applyOptions({color: this.options.signalColor})

    this.calcIndicatorData()
    this.applyIndicatorData();
  }


  setPaneIndex(paneIndex: number) {
    this.kSeries.moveToPane(paneIndex)
    this.dSeries.moveToPane(paneIndex)
  }

  getPaneIndex(): number {
    return this.kSeries.getPane().paneIndex()
  }
}