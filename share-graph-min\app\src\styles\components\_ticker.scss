@use "../common/mixins" as *;

.ticker {
    background: #fff;
    padding: 15px;
}

.ticker-table {
    display: flex;
    gap: 20px;


    &__info {
        display: flex;
        background: #fff;
        flex: 1;
        flex-shrink: 0;
        flex-wrap: wrap;
    }

    &__item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        gap: 10px;
        padding: 16px 15px;
        border-bottom: 1px solid var(--border-color);

        &:first-child {
            border-top: 1px solid var(--border-color);
        }

        &:nth-child(even) {
            background: var(--hover-color);
        }

        &__label {
            font-weight: 700;
        }

        &__content {
            transition: background-color 0.5s ease-in-out;
        }
    }

    &__column {
        width: 50%;
        flex-shrink: 0;

        @include respond-to(xs) {
            width: 100%;

            &:nth-child(1) .ticker-table__item:last-child {
                border-bottom: none;
            }
        }
    }

    &__price {
        display: flex;
        justify-content: flex-start;
        align-items: center;
        flex-direction: column;
        width: 30%;
        flex-shrink: 0;

        &.single {
            justify-content: center;
        }

        @include respond-to(md) {
            width: 100%;
        }
    }

    &__quote-price {
        font-size: 45px;
        font-weight: 700;
        color: var(--primary-color);
        margin-bottom: 7px;
        text-align: center;
    }

    &__change {
        &.up {
            color: var(--up-color);
        }

        &.down {
            color: var(--down-color);
        }
    }

    @include respond-to(md) {
        flex-direction: column;
    }
}

.value-updated {
    animation: highlight 0.1s ease-in-out;

    &.up {
        animation: highlight-up 0.1s ease-in-out;

    }

    &.down {
        animation: highlight-down 0.1s ease-in-out;

    }
}

@keyframes highlight {
    0% {
        background-color: gray;
    }

    100% {
        background-color: transparent;
    }
}

@keyframes highlight-up {
    0% {
        background-color: var(--up-color);
    }

    100% {
        background-color: transparent;
    }
}

@keyframes highlight-down {
    0% {
        background-color: var(--down-color);
    }

    100% {
        background-color: transparent;
    }
}