{"version": 3, "file": "ultimate-oscillator-indicator.es.js", "sources": ["../../src/indicators/ultimate-oscillator-indicator.ts"], "sourcesContent": ["import { IChartApi, ISeriesApi, LineSeries, Nominal, SeriesType, SingleValueData, Time } from \"lightweight-charts\";\nimport { ChartIndicator, ChartIndicatorOptions } from \"./abstract-indicator\";\nimport { RegionPrimitive } from \"../custom-primitive/primitive/region\";\nimport { autoScaleInfoProviderCreator } from \"../helpers/utils\";\nimport { Context } from \"../helpers/execution-indicator\";\n\nexport interface UltimateOscillatorIndicatorOptions extends ChartIndicatorOptions {\n  color: string,\n  period1: number,\n  period2: number,\n  period3: number,\n  weight1: number,\n  weight2: number,\n  weight3: number,\n  priceLineColor: string,\n  backgroundColor: string\n}\n\nexport const defaultOptions: UltimateOscillatorIndicatorOptions = {\n  color: \"#3179f5\",     // for Ultimate Oscillator\n  priceLineColor: \"#a1c3ff\",\n  backgroundColor: '#d2e0fa',\n  period1: 7,      // Short period as per TradingView standard\n  period2: 14,     // Medium period as per TradingView standard\n  period3: 28,     // Long period as per TradingView standard\n  weight1: 4,      // Weight for short period\n  weight2: 2,      // Weight for medium period\n  weight3: 1,      // Weight for long period\n  overlay: false\n}\n\nexport type UltimateOscillatorLine = Nominal<number, 'UltimateOscillator'>\n\nexport type UltimateOscillatorData = [UltimateOscillatorLine]\n\nexport default class UltimateOscillatorIndicator extends ChartIndicator<UltimateOscillatorIndicatorOptions, UltimateOscillatorData> {\n  ultimateOscillatorSeries: ISeriesApi<SeriesType>\n\n  constructor(chart: IChartApi, options?: Partial<UltimateOscillatorIndicatorOptions>, paneIndex?: number) {\n    super(chart, options)\n\n    this.ultimateOscillatorSeries = chart.addSeries(LineSeries, {\n      color: this.options.color,\n      lineWidth: 2,\n      priceLineVisible: false,\n      crosshairMarkerVisible: false,\n      priceScaleId: 'ultimateoscillator',\n      autoscaleInfoProvider: autoScaleInfoProviderCreator({ maxValue: 100, minValue: 0 })\n    }, paneIndex);\n\n    // Add region primitives for visual reference at 30 and 70 thresholds\n    this.ultimateOscillatorSeries.attachPrimitive(\n      new RegionPrimitive({\n        upPrice: 75,\n        lowPrice: 70,\n        lineColor: this.options.priceLineColor,\n        backgroundColor: this.options.backgroundColor\n      })\n    );\n\n    this.ultimateOscillatorSeries.attachPrimitive(\n      new RegionPrimitive({\n        upPrice: 30,\n        lowPrice: 25,\n        lineColor: this.options.priceLineColor,\n        backgroundColor: this.options.backgroundColor\n      })\n    );\n  }\n\n  getDefaultOptions(): UltimateOscillatorIndicatorOptions {\n    return defaultOptions\n  }\n\n  formula(c: Context): UltimateOscillatorData | undefined {\n    const period1 = this.options.period1;\n    const period2 = this.options.period2;\n    const period3 = this.options.period3;\n    const weight1 = this.options.weight1;\n    const weight2 = this.options.weight2;\n    const weight3 = this.options.weight3;\n\n    const high = c.symbol.high;\n    const low = c.symbol.low;\n    const close = c.symbol.close;\n\n    // Get MAX period needed first for proper series initialization\n    const maxPeriod = Math.max(period1, period2, period3);\n\n    // Create series to track previous close - need at least maxPeriod + 1 for lookback\n    const closeSeries = c.new_var(close, maxPeriod + 1);\n    if (!closeSeries.calculable()) return;\n\n    // Get previous close (handle first bar case)\n    const prevClose = closeSeries.get(1);\n\n    // BP = Close - Min(Low, PrevClose)\n    const minLowPrevClose = Math.min(low, prevClose);\n    const buyingPressure = close - minLowPrevClose;\n        \n    // TR = Max(High, PrevClose) - Min(Low, PrevClose)  \n    const maxHighPrevClose = Math.max(high, prevClose);\n    const trueRange = maxHighPrevClose - minLowPrevClose;\n\n    // Create series for BP and TR with proper period length\n    const bpSeries = c.new_var(buyingPressure, maxPeriod);\n    const trSeries = c.new_var(trueRange, maxPeriod);\n\n    if (!bpSeries.calculable() || !trSeries.calculable()) return;\n\n    // Check if we have enough data for the longest period\n    if (!bpSeries.calculable() || !trSeries.calculable()) return;\n\n    // Calculate period averages: Sum(BP) / Sum(TR) for each period\n    const getPeriodAverage = (period: number) => {\n        let bpSum = 0;\n        let trSum = 0;\n        \n        // Sum the most recent 'period' values\n        for (let i = 0; i < period; i++) {\n            bpSum += bpSeries.get(i);\n            trSum += trSeries.get(i);\n        }\n        \n        return trSum > 0 ? bpSum / trSum : 0;\n    };\n\n    const avg1 = getPeriodAverage(period1);  // 7-period average\n    const avg2 = getPeriodAverage(period2);  // 14-period average\n    const avg3 = getPeriodAverage(period3);  // 28-period average\n\n    // UO = 100 × [(4×Avg7) + (2×Avg14) + Avg28] / (4+2+1)\n    const weightedSum = (weight1 * avg1) + (weight2 * avg2) + (weight3 * avg3);\n    const totalWeight = weight1 + weight2 + weight3;\n    \n    const ultimateOscillator = totalWeight > 0 ? 100 * (weightedSum / totalWeight) : 0;\n\n    // Optional: Clamp to 0-100 range (TradingView doesn't explicitly clamp, but values should naturally stay in range)\n    // Remove this if you want exact TradingView behavior\n    // ultimateOscillator = Math.max(0, Math.min(100, ultimateOscillator));\n\n    return [ultimateOscillator as UltimateOscillatorLine];\n}\n\n  applyIndicatorData() {\n    const ultimateOscillatorData: SingleValueData[] = [];\n\n    for (const bar of this._executionContext.data) {\n      const value = bar.value;\n      if (!value) continue;\n\n      const time = bar.time as Time;\n      ultimateOscillatorData.push({ time, value: value[0] });\n    }\n\n    this.ultimateOscillatorSeries.setData(ultimateOscillatorData);\n  }\n\n  remove() {\n    super.remove()\n    this.chart.removeSeries(this.ultimateOscillatorSeries);\n  }\n\n  _applyOptions() {\n    this.ultimateOscillatorSeries.applyOptions({ color: this.options.color });\n    this.applyIndicatorData();\n  }\n\n  setPaneIndex(paneIndex: number) {\n    this.ultimateOscillatorSeries.moveToPane(paneIndex);\n  }\n\n  getPaneIndex(): number {\n    return this.ultimateOscillatorSeries.getPane().paneIndex();\n  }\n}\n"], "names": ["defaultOptions", "UltimateOscillatorIndicator", "ChartIndicator", "chart", "options", "paneIndex", "__publicField", "LineSeries", "autoScaleInfoProviderCreator", "RegionPrimitive", "c", "period1", "period2", "period3", "weight1", "weight2", "weight3", "high", "low", "close", "max<PERSON><PERSON><PERSON>", "closeSeries", "prevClose", "minLowPrevClose", "buyingPressure", "trueRang<PERSON>", "bpSeries", "trSeries", "getPeriodAverage", "period", "bpSum", "trSum", "i", "avg1", "avg2", "avg3", "weightedSum", "totalWeight", "ultimateOscillatorData", "bar", "value", "time"], "mappings": ";;;;;;;AAkBO,MAAMA,IAAqD;AAAA,EAChE,OAAO;AAAA;AAAA,EACP,gBAAgB;AAAA,EAChB,iBAAiB;AAAA,EACjB,SAAS;AAAA;AAAA,EACT,SAAS;AAAA;AAAA,EACT,SAAS;AAAA;AAAA,EACT,SAAS;AAAA;AAAA,EACT,SAAS;AAAA;AAAA,EACT,SAAS;AAAA;AAAA,EACT,SAAS;AACX;AAMA,MAAqBC,UAAoCC,EAA2E;AAAA,EAGlI,YAAYC,GAAkBC,GAAuDC,GAAoB;AACvG,UAAMF,GAAOC,CAAO;AAHtB,IAAAE,EAAA;AAKO,SAAA,2BAA2BH,EAAM,UAAUI,GAAY;AAAA,MAC1D,OAAO,KAAK,QAAQ;AAAA,MACpB,WAAW;AAAA,MACX,kBAAkB;AAAA,MAClB,wBAAwB;AAAA,MACxB,cAAc;AAAA,MACd,uBAAuBC,EAA6B,EAAE,UAAU,KAAK,UAAU,EAAG,CAAA;AAAA,OACjFH,CAAS,GAGZ,KAAK,yBAAyB;AAAA,MAC5B,IAAII,EAAgB;AAAA,QAClB,SAAS;AAAA,QACT,UAAU;AAAA,QACV,WAAW,KAAK,QAAQ;AAAA,QACxB,iBAAiB,KAAK,QAAQ;AAAA,MAC/B,CAAA;AAAA,IACH,GAEA,KAAK,yBAAyB;AAAA,MAC5B,IAAIA,EAAgB;AAAA,QAClB,SAAS;AAAA,QACT,UAAU;AAAA,QACV,WAAW,KAAK,QAAQ;AAAA,QACxB,iBAAiB,KAAK,QAAQ;AAAA,MAC/B,CAAA;AAAA,IACH;AAAA,EAAA;AAAA,EAGF,oBAAwD;AAC/C,WAAAT;AAAA,EAAA;AAAA,EAGT,QAAQU,GAAgD;AAChD,UAAAC,IAAU,KAAK,QAAQ,SACvBC,IAAU,KAAK,QAAQ,SACvBC,IAAU,KAAK,QAAQ,SACvBC,IAAU,KAAK,QAAQ,SACvBC,IAAU,KAAK,QAAQ,SACvBC,IAAU,KAAK,QAAQ,SAEvBC,IAAOP,EAAE,OAAO,MAChBQ,IAAMR,EAAE,OAAO,KACfS,IAAQT,EAAE,OAAO,OAGjBU,IAAY,KAAK,IAAIT,GAASC,GAASC,CAAO,GAG9CQ,IAAcX,EAAE,QAAQS,GAAOC,IAAY,CAAC;AAC9C,QAAA,CAACC,EAAY,aAAc;AAGzB,UAAAC,IAAYD,EAAY,IAAI,CAAC,GAG7BE,IAAkB,KAAK,IAAIL,GAAKI,CAAS,GACzCE,IAAiBL,IAAQI,GAIzBE,IADmB,KAAK,IAAIR,GAAMK,CAAS,IACZC,GAG/BG,IAAWhB,EAAE,QAAQc,GAAgBJ,CAAS,GAC9CO,IAAWjB,EAAE,QAAQe,GAAWL,CAAS;AAK/C,QAHI,CAACM,EAAS,WAAA,KAAgB,CAACC,EAAS,gBAGpC,CAACD,EAAS,WAAA,KAAgB,CAACC,EAAS,aAAc;AAGhD,UAAAC,IAAmB,CAACC,MAAmB;AACzC,UAAIC,IAAQ,GACRC,IAAQ;AAGZ,eAASC,IAAI,GAAGA,IAAIH,GAAQG;AACf,QAAAF,KAAAJ,EAAS,IAAIM,CAAC,GACdD,KAAAJ,EAAS,IAAIK,CAAC;AAGpB,aAAAD,IAAQ,IAAID,IAAQC,IAAQ;AAAA,IACvC,GAEME,IAAOL,EAAiBjB,CAAO,GAC/BuB,IAAON,EAAiBhB,CAAO,GAC/BuB,IAAOP,EAAiBf,CAAO,GAG/BuB,IAAetB,IAAUmB,IAASlB,IAAUmB,IAASlB,IAAUmB,GAC/DE,IAAcvB,IAAUC,IAAUC;AAQxC,WAAO,CANoBqB,IAAc,IAAI,OAAOD,IAAcC,KAAe,CAM7B;AAAA,EAAA;AAAA,EAGtD,qBAAqB;AACnB,UAAMC,IAA4C,CAAC;AAExC,eAAAC,KAAO,KAAK,kBAAkB,MAAM;AAC7C,YAAMC,IAAQD,EAAI;AAClB,UAAI,CAACC,EAAO;AAEZ,YAAMC,IAAOF,EAAI;AACjB,MAAAD,EAAuB,KAAK,EAAE,MAAAG,GAAM,OAAOD,EAAM,CAAC,GAAG;AAAA,IAAA;AAGlD,SAAA,yBAAyB,QAAQF,CAAsB;AAAA,EAAA;AAAA,EAG9D,SAAS;AACP,UAAM,OAAO,GACR,KAAA,MAAM,aAAa,KAAK,wBAAwB;AAAA,EAAA;AAAA,EAGvD,gBAAgB;AACd,SAAK,yBAAyB,aAAa,EAAE,OAAO,KAAK,QAAQ,OAAO,GACxE,KAAK,mBAAmB;AAAA,EAAA;AAAA,EAG1B,aAAajC,GAAmB;AACzB,SAAA,yBAAyB,WAAWA,CAAS;AAAA,EAAA;AAAA,EAGpD,eAAuB;AACrB,WAAO,KAAK,yBAAyB,QAAQ,EAAE,UAAU;AAAA,EAAA;AAE7D;"}