"use strict";var Z=Object.defineProperty;var $=(a,t,e)=>t in a?Z(a,t,{enumerable:!0,configurable:!0,writable:!0,value:e}):a[t]=e;var g=(a,t,e)=>$(a,typeof t!="symbol"?t+"":t,e);Object.defineProperties(exports,{__esModule:{value:!0},[Symbol.toStringTag]:{value:"Module"}});const _=require("lightweight-charts"),ee=require("./abstract-indicator.cjs.js"),se=require("../custom-primitive/primitive/region.cjs.js"),L=require("../helpers/utils.cjs.js"),E={adxColor:"#f23645",plusDIColor:"#2962ff",minusDIColor:"#ff6d00",priceLineColor:"rgba(150, 150, 150, 0.35)",backgroundColor:"#ff98001a",period:14,overlay:!1};class oe extends ee.ChartIndicator{constructor(e,s,o){super(e,s);g(this,"adxSeries");g(this,"plusDISeries");g(this,"minusDISeries");this.adxSeries=e.addSeries(_.LineSeries,{color:this.options.adxColor,lineWidth:2,priceLineVisible:!1,crosshairMarkerVisible:!1,priceScaleId:"dmi",autoscaleInfoProvider:L.autoScaleInfoProviderCreator({maxValue:100,minValue:0})},o),this.plusDISeries=e.addSeries(_.LineSeries,{color:this.options.plusDIColor,lineWidth:1,priceLineVisible:!1,crosshairMarkerVisible:!1,priceScaleId:"dmi",autoscaleInfoProvider:L.autoScaleInfoProviderCreator({maxValue:100,minValue:0})},o),this.minusDISeries=e.addSeries(_.LineSeries,{color:this.options.minusDIColor,lineWidth:1,priceLineVisible:!1,crosshairMarkerVisible:!1,priceScaleId:"dmi",autoscaleInfoProvider:L.autoScaleInfoProviderCreator({maxValue:100,minValue:0})},o),this.adxSeries.attachPrimitive(new se.RegionPrimitive({upPrice:25,lowPrice:20,lineColor:this.options.priceLineColor,backgroundColor:this.options.backgroundColor}))}getDefaultOptions(){return E}formula(e){const s=this.options.period,o=e.symbol.high,c=e.symbol.low,l=e.symbol.close,n=e.new_var(o,s+1),N=e.new_var(c,s+1),O=e.new_var(l,s+1);if(!n.calculable()||!N.calculable()||!O.calculable())return;const b=o,M=c,F=n.get(1),G=N.get(1),k=O.get(1),m=b-F,S=G-M;let x=0,C=0;m>S&&m>0?x=m:S>m&&S>0&&(C=S);const J=b-M,K=Math.abs(b-k),Q=Math.abs(M-k),A=Math.max(J,K,Q),D=1/s,H=e.new_var(0,s+1),T=e.new_var(0,s+1),q=e.new_var(0,s+1),R=e.new_var(x,s+1),W=e.new_var(C,s+1),j=e.new_var(A,s+1);let v,f,i;if(R.calculable()&&W.calculable()&&j.calculable()){const r=H.get(1),I=T.get(1),d=q.get(1);if(isNaN(r)||isNaN(I)||isNaN(d)){const y=R.getAll(),U=W.getAll(),Y=j.getAll();v=y.reduce((h,p)=>h+p,0)/s,f=U.reduce((h,p)=>h+p,0)/s,i=Y.reduce((h,p)=>h+p,0)/s}else v=r+(x-r)*D,f=I+(C-I)*D,i=d+(A-d)*D}else return;H.set(v),T.set(f),q.set(i);const w=i!==0?100*v/i:0,P=i!==0?100*f/i:0,X=w+P,V=X!==0?100*Math.abs(w-P)/X:0,z=e.new_var(V,s+1),B=e.new_var(0,s+1);let u;if(z.calculable()){const r=B.get(1);isNaN(r)?u=z.getAll().reduce((d,y)=>d+y,0)/s:u=r+(V-r)*D}else u=V;return B.set(u),[u,w,P]}applyIndicatorData(){const e=[],s=[],o=[];for(const c of this._executionContext.data){const l=c.value;if(!l)continue;const n=c.time;e.push({time:n,value:l[0]}),s.push({time:n,value:l[1]}),o.push({time:n,value:l[2]})}this.adxSeries.setData(e),this.plusDISeries.setData(s),this.minusDISeries.setData(o)}remove(){super.remove(),this.chart.removeSeries(this.adxSeries),this.chart.removeSeries(this.plusDISeries),this.chart.removeSeries(this.minusDISeries)}_applyOptions(){this.adxSeries.applyOptions({color:this.options.adxColor}),this.plusDISeries.applyOptions({color:this.options.plusDIColor}),this.minusDISeries.applyOptions({color:this.options.minusDIColor}),this.applyIndicatorData()}setPaneIndex(e){this.adxSeries.moveToPane(e),this.plusDISeries.moveToPane(e),this.minusDISeries.moveToPane(e)}getPaneIndex(){return this.adxSeries.getPane().paneIndex()}}exports.default=oe;exports.defaultOptions=E;
//# sourceMappingURL=dmi-indicator.cjs.js.map
