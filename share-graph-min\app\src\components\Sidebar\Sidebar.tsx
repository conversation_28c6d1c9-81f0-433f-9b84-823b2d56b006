import { useState } from 'react';
import ChartPreferenceOptionList from '../OptionList/ChartPreferenceOptionList';
import ChartTypeOptionList from '../OptionList/ChartTypeOptionList';
import EventsOptionList from '../OptionList/EventsOptionList';
import IndicatorOptionList from '../OptionList/IndicatorOptionList';
import LastCloseOptionList from '../OptionList/LastCloseOptionList';
import OverlaysOptionList from '../OptionList/OverlaysOptionList';
import VolumeOptionList from '../OptionList/VolumeOptionList';
import YAxisPreferenceOptionList from '../OptionList/YAxisPreferenceOptionList';
import clsx from 'clsx';
import RightCollapseIcon from '../../icons/RightCollapseIcon';
import SettingIcon from '../../icons/SettingIcon';

const Sidebar = () => {
  const [isCollapse, setIsCollapse] = useState(false);

  return (
    <div className={clsx('sidebar', { collapsed: isCollapse })}>
      <div className="sidebar__settings">
        <ChartTypeOptionList />
        <IndicatorOptionList />
        <OverlaysOptionList />
        <VolumeOptionList />
        <YAxisPreferenceOptionList />
        <ChartPreferenceOptionList />
        <LastCloseOptionList />
        <EventsOptionList />
      </div>
      <div className="sidebar__collapsed-menu">
        <button onClick={() => setIsCollapse(false)}>
          <SettingIcon />
        </button>
      </div>

      <button
        className="sidebar__collapsed-icon"
        onClick={() => setIsCollapse((prev) => !prev)}
      >
        <RightCollapseIcon width={20} height={20} />
      </button>
    </div>
  );
};

export default Sidebar;
