import { Select } from "antd";
import { useImperative<PERSON>andle, useLayoutEffect, useMemo, useRef, useState } from "react";
import { useSearchParams } from "react-router-dom";
import { useAvailableTools } from "../../../hooks/useSettings";

export default function ToolSelect({ handlePreviewDataRef, value, onChange }) {
  const [searchParams] = useSearchParams();
  const [{ availableTools }] = useAvailableTools();
  const [toolOptions, setToolOptions] = useState([]);

  const handleRef = useRef();
  handlePreviewDataRef.current["tool"] = handleRef;
  useImperativeHandle(
    handleRef,
    () => ({
      onReceiveData: data => {
        if (data.tools?.length) {
          setToolOptions(data.tools);
        }
      },
    }),
    []
  );

  const availableToolsObj = useMemo(
    () =>
      availableTools.reduce((s, tool) => {
        s[tool.toolNameIntegration] = { ...tool, value: tool.toolNameIntegration, label: tool.shortName };
        return s;
      }, {}),
    [availableTools]
  );

  const toolOptionData = useMemo(() => {
    if (toolOptions.length) {
      return toolOptions.map(tool => availableToolsObj[tool.toolNameIntegration]).filter(tool => !!tool);
    }
    const result = (searchParams.get("tools") || "")
      .split(",")
      .map(tool => availableToolsObj[tool])
      .filter(tool => !!tool);
    return result;
  }, [searchParams, availableToolsObj, toolOptions]);

  const defaultTool = toolOptionData[0]?.toolNameIntegration;

  useLayoutEffect(() => {
    if (defaultTool) {
      onChange(defaultTool);
    }
  }, [defaultTool, onChange]);

  return <Select value={value} style={{ width: 200 }} onChange={onChange} options={toolOptionData} />;
}
