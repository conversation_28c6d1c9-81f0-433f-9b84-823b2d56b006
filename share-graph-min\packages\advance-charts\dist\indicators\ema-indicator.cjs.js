"use strict";var h=Object.defineProperty;var P=(t,i,e)=>i in t?h(t,i,{enumerable:!0,configurable:!0,writable:!0,value:e}):t[i]=e;var l=(t,i,e)=>P(t,typeof i!="symbol"?i+"":i,e);Object.defineProperties(exports,{__esModule:{value:!0},[Symbol.toStringTag]:{value:"Module"}});const f=require("../custom-primitive/primitive-base.cjs.js"),S=require("../custom-primitive/pane-view/line.cjs.js"),_=require("./abstract-indicator.cjs.js"),m={color:"#d26400",period:9,overlay:!0};class v extends f.SeriesPrimitiveBase{constructor(e){super();l(this,"linePrimitive");this.source=e,this.linePrimitive=new S.LinePrimitivePaneView({lineColor:this.source.options.color}),this._paneViews=[this.linePrimitive]}update(e){const s=[];for(const o of e){const a=o.value;a&&s.push({time:o.time,price:a[0]})}this.linePrimitive.update(s)}}class b extends _.ChartIndicator{constructor(){super(...arguments);l(this,"emaPrimitive",new v(this))}getDefaultOptions(){return m}_mainSeriesChanged(e){e.attachPrimitive(this.emaPrimitive)}remove(){var e;super.remove(),(e=this.mainSeries)==null||e.detachPrimitive(this.emaPrimitive)}applyIndicatorData(){this.emaPrimitive.update(this._executionContext.data)}formula(e){const s=this.options.period,o=e.symbol.close,a=2/(s+1),c=e.new_var(o,s),n=e.new_var(NaN,2);if(!c.calculable())return;const u=n.get(1);let r;return isNaN(u)?(r=c.getAll().reduce((d,p)=>d+p,0)/s,n.set(r),[r]):(r=a*o+(1-a)*u,n.set(r),[r])}}exports.EMAPrimitive=v;exports.default=b;exports.defaultOptions=m;
//# sourceMappingURL=ema-indicator.cjs.js.map
