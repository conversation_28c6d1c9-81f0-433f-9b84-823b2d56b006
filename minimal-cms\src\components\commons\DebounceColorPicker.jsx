import { useEffect, useState } from "react";
import { ColorPicker } from "antd";

import useDebounce from "../../hooks/useDebounce";

const DebounceColorPicker = ({ value, onChange }) => {
  const [selectedColor, setSelectedColor] = useState(value);

  const debouncedOnColorChange = useDebounce(() => {
    onChange(selectedColor);
  });

  const handleColorChange = (color) => {
    setSelectedColor(color);
    debouncedOnColorChange();
  };

  useEffect(() => {
    setSelectedColor(value);
  }, [value]);

  return (
    <ColorPicker
      disabledAlpha
      value={selectedColor}
      onChange={handleColorChange}
      size="middle"
      showText
      allowClear
    />
  );
};

export function ColorPickerWrapper(props) {
  return (
    <DebounceColorPicker
      {...props}
      onChange={(value) => props.onChange(value.toHexString())}
    />
  );
}

export default DebounceColorPicker;
