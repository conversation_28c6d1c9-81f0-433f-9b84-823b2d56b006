import base64Decode  from 'fast-base64-decode';

/**
 * @link https://en.wikipedia.org/wiki/Base64
 * decode base64 string to text
 * normally the size of text will less than 33% of the size of the base64
 * @param base64 - base64 string
 * @returns text
 */
export function decodeBase64(base64: string) {
  // base64 133% than text so text will be 100/133=~75% of the size of the base64
  const result = new Uint8Array(base64.length * 0.75)
  base64Decode(base64, result);
  return new TextDecoder().decode(result);
}
