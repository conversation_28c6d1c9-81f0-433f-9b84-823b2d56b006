/* eslint-disable react/prop-types */
import { forwardRef, useImperative<PERSON>andle } from "react";
import { AutoComplete, Flex, Form, Input } from "antd";

import CompanyCode from "./CompanyCode";
import AvailableTool from "./AvailableTool";

const AvailableToolSetting = forwardRef(
  ({ handleRef, onDraftChange, initState }, ref) => {
    const [form] = Form.useForm();

    const handleSubmit = async () => {
      try {
        const values = await form.validateFields();
        const data = form.getFieldsValue();
        return true;
      } catch (errorInfo) {
        console.log("Failed:", errorInfo);
        return false;
      }
    };

    useImperativeHandle(
      handleRef,
      () => {
        return {
          onSubmit: handleSubmit,
        };
      },
      []
    );

    return (
      <Form
        layout={"vertical"}
        form={form}
        initialValues={initState}
        onValuesChange={(state, values) => {
          onDraftChange(structuredClone(values));
        }}
      >
        <Flex gap={20} className="flex-wrap">
          <Form.Item
            label="Company name"
            name={["company"]}
            required
            rules={[{ required: true }]}
            style={{ marginBottom: 0 }}
          >
            <CompanyCode />
          </Form.Item>
          {/* <Form.Item
            label="Version"
            name="version"
            // required
            style={{ marginBottom: 0 }}
            // rules={[{ required: true }]}
          >
            <Input placeholder="Version" style={{ width: 200 }} />
          </Form.Item>
          <Form.Item
            label="Available tools"
            name="selectedTools"
            style={{ marginBottom: 0 }}
            // required
            // rules={[{ required: true }]}
          >
            <AvailableTool />
          </Form.Item> */}
        </Flex>
      </Form>
    );
  }
);
export default AvailableToolSetting;
