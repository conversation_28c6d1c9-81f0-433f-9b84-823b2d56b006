/**
 * Abstract base class for handling paginated data resources.
 * @template TContext - Type of the context object
 * @template TOptions - Type of the options object
 * @template TData - Type of the data being loaded
 */
export abstract class PaginatedResourceHandler<TContext, TOptions, TData> {
  protected stx: TContext;
  public loaders: PaginatedLoader<TContext, TOptions, TData>[] = [];
  protected dataNode: ResourceNode<TData> | undefined = undefined;
  protected options: TOptions;
  protected isSleep: boolean = false;
  protected range?: [Date, Date];

  constructor(stx: TContext, options: TOptions) {
    this.stx = stx;
    this.options = options;
  }

  /**
   * Creates a resource node (abstract method to be implemented by subclasses).
   */
  protected resourceNodeCreator?(): ResourceNode<TData>;

  /**
   * Creates a paginated loader (abstract method to be implemented by subclasses).
   */
  protected abstract paginatedLoaderCreator(from: Date, to: Date): Paginated<PERSON>oader<TContext, TOptions, TData>;

  protected initializeResourceNode(): void {
    if (this.dataNode) return;
    this.dataNode = this.resourceNodeCreator?.();
    this.isSleep = false;
    
    const existingData = this.loaders
      .filter((loader) => loader.data.length > 0)
      .flatMap((loader) => loader.data);

    if (existingData.length > 0) {
      this.dataNode?.onLoad(existingData);
    }
  }

  /**
   * Loads paginated data for a given date range.
   */
  public load(from: Date, to: Date): void {
    // If new range doesn't intersect with current range, reset everything
    if (this.range && !this.isIntersectionTime(from, to)) {
      this.reset();
    }

    this.initializeResourceNode();
    
    if (!this.range) {
      this.range = [from, to];
      this.loaders.push(this.paginatedLoaderCreator(from, to));
      return;
    }

    const { leftJoin, rightJoin } = this.intersectionTime([from, to]);

    if (leftJoin) this.loaders.push(this.paginatedLoaderCreator(leftJoin[0], leftJoin[1]));
    if (rightJoin) this.loaders.push(this.paginatedLoaderCreator(rightJoin[0], rightJoin[1]));

    this.range[0] = this.range[0] > from ? from : this.range[0];
    this.range[1] = this.range[1] < to ? to : this.range[1];
  }

  /**
   * Checks if the given date range intersects with the current range.
   */
  public isIntersectionTime(from: Date, to: Date): boolean {
    if (!this.range) return false;
    
    const [currentFrom, currentTo] = this.range;
    // Two ranges intersect if: max(start1, start2) <= min(end1, end2)
    return Math.max(from.getTime(), currentFrom.getTime()) <= Math.min(to.getTime(), currentTo.getTime());
  }

  /**
   * Resets the handler by destroying and removing all data and loaders.
   */
  public reset(): void {
    // Destroy all loaders
    this.loaders.forEach((loader) => loader.destroy());
    this.loaders = [];
    
    // Destroy data node
    this.dataNode?.destroy();
    this.dataNode = undefined;
    
    // Reset range
    this.range = undefined;
    
    // Reset sleep state
    this.isSleep = false;
  }

  /**
   * Calculates the intersection of time ranges.
   */
  protected intersectionTime(compareRange: [Date, Date]): { leftJoin?: [Date, Date]; rightJoin?: [Date, Date] } {
    const [leftRange, rightRange] = this.range!;
    const [leftCompare, rightCompare] = compareRange;
    const result: { leftJoin?: [Date, Date]; rightJoin?: [Date, Date] } = {};

    if (leftCompare < leftRange) {
      result.leftJoin = [leftCompare, leftRange];
    }

    if (rightCompare > rightRange) {
      result.rightJoin = [rightRange, rightCompare];
    }

    return result;
  }

  getData() {
    return this.loaders.map((item) => item.data).flat()
  }

  /**
   * Puts the resource handler to sleep, stopping processing and destroying resources.
   */
  public sleep(): void {
    this.loaders.forEach((loader) => loader.destroy());
    this.dataNode?.destroy();
    this.dataNode = undefined;
    this.isSleep = true;

    if (this.loaders.length === 0) return;

    if (this.loaders.length === 1) {
      if (this.loaders[0].forceStop) this.range = undefined;
      return;
    }

    const groupedLoaders = this.loaders.reduce<{ forceStop: boolean; loaders: PaginatedLoader<TContext, TOptions, TData>[] }[]>(
      (acc, loader) => {
        if (acc.length === 0 || acc[acc.length - 1].forceStop !== loader.forceStop) {
          acc.push({ forceStop: loader.forceStop, loaders: [loader] });
        } else {
          acc[acc.length - 1].loaders.push(loader);
        }
        return acc;
      },
      []
    );

    const validGroup = groupedLoaders
      .reverse()
      .find((group) => !group.forceStop);

    if (validGroup) {
      const from = validGroup.loaders.map((loader) => loader.from).reduce((a, b) => (a < b ? a : b));
      const to = validGroup.loaders.map((loader) => loader.to).reduce((a, b) => (a > b ? a : b));
      this.loaders = validGroup.loaders;
      this.range = [from, to];
    }
  }
}

/**
 * Abstract base class for paginated data loading.
 * @template TContext - Type of the context object
 * @template TOptions - Type of the options object
 * @template TData - Type of the data being loaded
 */
export abstract class PaginatedLoader<TContext, TOptions, TData> {
  protected options: TOptions;
  protected stx: TContext;
  public from: Date;
  public to: Date;
  public initLoad: boolean = false;
  public loading: boolean = false;
  public forceStop: boolean = false;
  public data: TData[] = [];
  private abortController: AbortController = new AbortController();

  constructor(from: Date, to: Date, options: TOptions, stx: TContext) {
    this.stx = stx;
    this.from = from;
    this.to = to;
    this.options = options;
  }

  /**
   * Fetches paginated data with cursor-based pagination.
   */
  public abstract fetch(cursor?: string): Promise<{ endCursor?: string; data: TData[] }>;

  /**
   * Loads paginated data and calls the provided callback for each batch.
   */
  public async load(onLoad: (data: TData[]) => void): Promise<void> {
    try {
      if (this.initLoad) return;

      this.initLoad = true;
      this.loading = true;
      let cursor: string | undefined;

      do {
        this.abortController.signal.throwIfAborted();
        const { data, endCursor } = await this.fetch(cursor);
        if (!data) break;
        this.data.push(...data);
        onLoad(data);
        cursor = endCursor;
      } while (cursor);

      this.loading = false;
    } catch (e) {
      this.forceStop = true;
      if (!(e instanceof DOMException && e.name === "AbortError")) {
        console.error(e);
      }
    } finally {
      this.loading = false;
    }
  }

  /**
   * Destroys the loader and stops any ongoing loading.
   */
  public destroy(): void {
    this.cancel();
  }

  /**
   * Cancels the ongoing loading process.
   */
  public cancel(): void {
    this.abortController.abort();
    if (this.loading) {
      this.forceStop = true;
    }
  }

  /**
   * Static method to create and start loading paginated data.
   */
  public static create<TContext, TOptions, TData>(
    from: Date,
    to: Date,
    onLoad: (data: TData[]) => void,
    options?: TOptions,
    stx?: TContext
  ): PaginatedLoader<TContext, TOptions, TData> {
    const instance = new (this as unknown as { new(from: Date, to: Date, options?: TOptions, stx?: TContext): PaginatedLoader<TContext, TOptions, TData> })(from, to, options, stx);
    instance.load(onLoad);
    return instance;
  }
}

/**
 * Interface representing a ResourceNode that handles loaded data.
 * @template TData - Type of the data being handled
 */
export interface ResourceNode<TData> {
  onLoad(data: TData[]): void;
  destroy(): void;
}