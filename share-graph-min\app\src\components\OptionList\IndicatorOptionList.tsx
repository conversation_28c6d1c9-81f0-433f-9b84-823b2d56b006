import { CHART_INDICATOR_KEYS } from '../../constants/chartConstant';
import OptionList from './OptionList';
import { IOptionList } from '../../types/OptionList';
import { useAppStore } from '../../stores/useAppStore';
import { TChartType } from '../../types/store';
import { i18n } from '@euroland/libs';

const IndicatorOptionList = () => {
  const menuData: IOptionList[] = [
    {
      id: CHART_INDICATOR_KEYS.MACD.key,
      label: CHART_INDICATOR_KEYS.MACD.label,
    },
    {
      id: CHART_INDICATOR_KEYS.RSI.key,
      label: CHART_INDICATOR_KEYS.RSI.label,
    },
    {
      id: CHART_INDICATOR_KEYS.STOCHASTIC.key,
      label: CHART_INDICATOR_KEYS.STOCHASTIC.label,
    },
    {
      id: CHART_INDICATOR_KEYS.MM.key,
      label: CHART_INDICATOR_KEYS.MM.label,
    },
    {
      id: CHART_INDICATOR_KEYS.WILLIAMS.key,
      label: CHART_INDICATOR_KEYS.WILLIAMS.label,
    },
    {
      id: CHART_INDICATOR_KEYS.DMI.key,
      label: CHART_INDICATOR_KEYS.DMI.label,
    },
    {
      id: CHART_INDICATOR_KEYS.MASS_INDEX.key,
      label: CHART_INDICATOR_KEYS.MASS_INDEX.label,
    },
    {
      id: CHART_INDICATOR_KEYS.ULTIMATE_OSCILLATOR.key,
      label: CHART_INDICATOR_KEYS.ULTIMATE_OSCILLATOR.label,
    },
    {
      id: CHART_INDICATOR_KEYS.VROC.key,
      label: CHART_INDICATOR_KEYS.VROC.label,
    },
    {
      id: CHART_INDICATOR_KEYS.CHAIKINS_VOLATILITY.key,
      label: CHART_INDICATOR_KEYS.CHAIKINS_VOLATILITY.label,
    },
  ];

  const indicators = useAppStore((state) => state.indicators);
  const setChartIndicators = useAppStore((state) => state.setChartIndicators);

  const handleChangeValue = (optionId: string) => {
    setChartIndicators(optionId as TChartType);

  };

  return (
    <OptionList
      title={i18n.translate("indicator")}
      options={menuData}
      onChange={handleChangeValue}
      value={indicators}
    />
  );
};

export default IndicatorOptionList;
