import { gql } from "../utils";


export const TICKER_INIT_QUERY = gql(/* GraphQL */`
  query TickerInit($ids: [Int!]!, $toCurrency: String, $adjClose: Boolean) {
    instrumentByIds(ids: $ids, exchangeCurrency: $toCurrency, adjClose: $adjClose) {
      ...TickerDataFragment
    }
  }

  fragment TickerDataFragment on Instrument {
    shareName
    id
    symbol
    low52W
    high52W
    currency {
      code
      name
    }
    market {
      status {
        isOpened
        remainingTime
      }
      openTimeLocal
      closeTimeLocal

      timezone {
        nameIANA
      }
    }
    currentPrice {
      open
      prevClose
      volume
      officialClose
      officialCloseDate
      last
      change
      changePercentage
      low
      date
      bid
      ask
      high
    }
  
  }
`);

export const TICKER_UPDATE_QUERY = gql(/* GraphQL */`query TickerUpdate(
  $ids: [Int!]!
  $adjClose: Boolean
  $additionalRealtimeIds: [Int!]!
  $toCurrency: String
) {
  instrumentByIds(ids: $ids, exchangeCurrency: $toCurrency, adjClose: $adjClose) {
    ...TickerData
  }

  additionalRealtime: instrumentByIds(ids: $additionalRealtimeIds) {
    id
    currentPrice {
      officialClose
      officialCloseDate
    }
  }
}

fragment TickerData on Instrument {
  shareName
    id
    symbol
    low52W
    high52W
    currency {
      code
      name
    }
    market {
      status {
        isOpened
        remainingTime
      }
      openTimeLocal
      closeTimeLocal

      timezone {
        nameIANA
      }
    }
    currentPrice {
      open
      prevClose
      volume
      officialClose
      officialCloseDate
      last
      change
      changePercentage
      low
      date
      bid
      ask
      high
    }
}
`);