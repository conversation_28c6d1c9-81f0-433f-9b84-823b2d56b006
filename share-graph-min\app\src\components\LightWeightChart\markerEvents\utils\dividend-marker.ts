import { Delegate } from "@sharegraph-mini/advance-charts";
import { getDividendEvents } from "../../../../services/common";
import { useAppStore } from "../../../../stores/useAppStore";
import { IDividendEvent } from "../../../../types/events";
import { PaginatedLoader, PaginatedResourceHandler } from "./data-loader";

class DividendLoader extends PaginatedLoader<unknown, unknown, IDividendEvent> {
  async fetch(cursor) {
    const { selectedInstrumentId } = useAppStore.getState();

    const result = await getDividendEvents({
      insId: selectedInstrumentId,
      fromDate: this.from,
      toDate: this.to,
      cursor
    });
    const edges = result?.data?.instrumentById?.dividends?.edges || [];
    const data = edges.map(item => ({
      exDate: item.node.exDate,
      grossDivAdj: item.node.grossDivAdj,
      currency: item.node.currency
    } as IDividendEvent))
    
    // console.log("DividendLoader ~ fetch ~ result:", result)
    return {
      cursor: result?.data?.instrumentById?.dividends?.pageInfo?.endCursor || undefined,
      data: data
    }
  }
}

class DividendHandler extends PaginatedResourceHandler<unknown, unknown, IDividendEvent> {
  dataLoaded = new Delegate();

  paginatedLoaderCreator(from, to) {
    return DividendLoader.create(from, to, (data: IDividendEvent[]) => {
      this.dataNode?.onLoad(data);
      this.dataLoaded.fire()
    }, this.options);
  }
}

// const dividendInstance = new DividendHandler(undefined, undefined);
export default DividendHandler;
