@use "../common/mixins" as *;

.dropdown {
    position: relative;
    display: inline-block;
    
    &-trigger {
      padding: 0 12px;
      background: none;
      border: none;
      cursor: pointer;
      display: flex;
      align-items: center;
      gap: 8px;
      border-radius: 6px;
      border: 1px solid rgb(228, 228, 231);
      background: #fff;
      height: 32px;
      
      &:hover {
        background: var(--hover-color);
      }
    }
  
    &-menu {
      position: absolute;
      top: calc(100% + 8px);
      background: white;
      // box-shadow: 0 2px 4px #0003;
      box-shadow: 
  0 4px 6px -1px rgba(0, 0, 0, 0.1),
  0 2px 4px -2px rgba(0, 0, 0, 0.1);
      border: 1px solid var(--border-color);
      min-width: 180px;
      display: none;
      z-index: 1000;
      border-radius: 6px;
      overflow: hidden;
      max-height: calc(50vh - 190px);
      overflow-y: auto;
      overscroll-behavior: contain;
      &::-webkit-scrollbar {
        display: none;
      }
      &::-webkit-scrollbar-thumb {
        display: none;
      }
      &:hover::-webkit-scrollbar {
        display: block;
      }
      &:hover::-webkit-scrollbar-thumb {
        display: block;
      }

      &.left {
        left: 0;
      }
      
      &.right {
        right: 0;
      }

      &.show {
        display: block;
      }

    @include style-scroll-bar;
    }

    svg {
      width: 20px;
      height: 20px;
    }
  
    &-item {
      padding: 6px 12px;
      white-space: nowrap;
      font-size: 14px;
      line-height: 20px;
      display: flex;
      align-items: center;

      &__description {
        margin-bottom: 3px;
        border-bottom: 1px solid var(--border-color);
        padding: 10px 12px;

        &-label {
          font-size: 14px;
          font-weight: 700;
          color: var(--text-color);
          line-height: 1.4;
        }
      }

      &:not(.dropdown-item__description):not(.dropdown-item__switcher):not(.dropdown-item__heading) {
        cursor: pointer;
        user-select: none;

        &:hover {
          background: var(--hover-color);
        }
      }

      &__switcher {
        display: flex;
        align-items: center;
        justify-content: space-between;
        gap: 10px;
      }

      &__icon {
        // margin-right: 6px;
        padding: 0 12px 0 0;
        display: flex;
        align-items: center;

        > svg {
          width: 17px;
          height: 17px;
        }
      }

      // &:not(.dropdown-item__description):not(.dropdown-item__switcher):hover {
      //   background: var(--hover-color);
      // }
  
      &.selected {
        // background-color: var(--active-color);
        font-weight: 700;
        // color: #fff;
      }

      &.disabled {
        cursor: not-allowed;
        opacity: 0.4;
      }

      &__separator {
        height: 1px;
        background: #e0e3eb;
        margin-top: 0;
        margin-bottom: 10px;
        width: 100%;
      }

      &__heading {
        display: flex;
        flex-direction: column;
        align-items: unset;
        margin: 6px 0;
        padding: 0;

        &:hover {
          background: none;
          cursor: default;
        }
      }

      &__label {
        padding: 0 12px;
        font-size: 14px;
        color: var(--text-color) ;
        line-height: 1.4;
        font-weight: 700;
      }
    }

    .setting-icon {
      width: 15px;
      height: 15px;
    }
  }

  .radio-icon {
    fill: currentColor;

    &.active {
      fill: transparent;
      stroke: currentColor;
    }
  }

  .checkbox-icon {
    stroke: currentColor;

    &__active path {
      stroke: currentColor;
    }
  }