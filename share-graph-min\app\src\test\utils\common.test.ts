import { beforeEach, describe, expect, it } from "vitest";
import { updateCSSVariables } from "../../utils/common";
import { TCSSVariables } from "../../types/common";

describe('updateCSSVariables', () => {
    beforeEach(() => {
      // Clear any existing styles before each test to avoid side effects
      document.documentElement.style.cssText = '';
    });
  
    it('should set CSS variables on document.documentElement', () => {
      const variables: TCSSVariables = {
        'primary-color': '#ff0000',
        'secondary-color': '#00ff00',
        'font-size': '16px',
      };
  
      updateCSSVariables(variables);
  
      // Iterate over each variable and assert it's correctly set
      Object.entries(variables).forEach(([key, value]) => {
        expect(document.documentElement).toHaveStyle(`--${key}: ${value}`);
      });
    });
  
    it('should overwrite existing CSS variables', () => {
      // Set initial variables
      const initialVariables: TCSSVariables = {
        'primary-color': '#0000ff',
        'margin': '10px',
      };
      updateCSSVariables(initialVariables);
  
      // Update variables
      const updatedVariables: TCSSVariables = {
        'primary-color': '#ff00ff', // Changed value
        'padding': '20px',          // New variable
      };
      updateCSSVariables(updatedVariables);
  
      // Assertions
      expect(document.documentElement).toHaveStyle('--primary-color: #ff00ff');
      expect(document.documentElement).toHaveStyle('--margin: 10px');
      expect(document.documentElement).toHaveStyle('--padding: 20px');
    });
  
    it('should correctly handle CSS variable names with special characters', () => {
      const specialVariables: TCSSVariables = {
        'font-family': 'Arial, sans-serif',
        'box-shadow': '0 4px 6px rgba(0, 0, 0, 0.1)',
      };
  
      updateCSSVariables(specialVariables);
  
      expect(document.documentElement).toHaveStyle('--font-family: Arial, sans-serif');
      expect(document.documentElement).toHaveStyle('--box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1)');
    });
  });