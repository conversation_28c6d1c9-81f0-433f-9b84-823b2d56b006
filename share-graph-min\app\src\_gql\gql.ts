/* eslint-disable */
import * as types from './graphql';
import { TypedDocumentNode as DocumentNode } from '@graphql-typed-document-node/core';

/**
 * Map of all GraphQL operations in the project.
 *
 * This map has several performance disadvantages:
 * 1. It is not tree-shakeable, so it will include all operations in the project.
 * 2. It is not minifiable, so the string of a GraphQL query will be multiple times inside the bundle.
 * 3. It does not support dead code elimination, so it will add unused operations.
 *
 * Therefore it is highly recommended to use the babel or swc plugin for production.
 * Learn more about it here: https://the-guild.dev/graphql/codegen/plugins/presets/preset-client#reducing-bundle-size
 */
const documents = {
    "query ChartHistory($id: Int!, $fromDate: DateTime, $toDate: DateTime, $timeIntervalGrouping: Int) {\n  instrumentById(id: $id) {\n    market {\n      timezone {\n        nameIANA\n      }\n      openTimeLocal\n      closeTimeLocal\n    }\n    intraday(\n      where: {dateTime: {gte: $fromDate, lte: $toDate}}\n      first: 9999999\n      order: {dateTime: ASC}\n      timeIntervalGrouping: $timeIntervalGrouping\n    ) {\n      nodes {\n        close\n        dateTime\n        high\n        low\n        open\n        volume\n        instrumentId\n      }\n    }\n  }\n}": types.ChartHistoryDocument,
    "query ChartHistoricalData(\n  $id: Int!\n  $fromDate: DateTime\n  $toDate: DateTime\n) {\n  instrumentById(id: $id) {\n   \n    historicals(\n      where: { dateTime: { gte: $fromDate, lte: $toDate } }\n      first: 9999999\n      order: { dateTime: ASC }\n    ) {\n      nodes {\n        close\n        dateTime\n        high\n        low\n        open\n        volume\n        instrumentId\n      }\n    }\n  }\n}\n": types.ChartHistoricalDataDocument,
    "query DividendEvents($id: Int!, $fromDate: DateTime!, $toDate: DateTime!) {\n    instrumentById(id: $id) {\n      dividends(where: {exDate: { gte: $fromDate, lt: $toDate}}, first: 99999999) {\n        nodes {\n          exDate\n          grossDivAdj\n          currency\n        }\n      }\n    }\n  }\n  ": types.DividendEventsDocument,
    "query DividendEventsPaging(\n  $id: Int!\n  $fromDate: DateTime!\n  $toDate: DateTime!\n  $cursor: String\n) {\n  instrumentById(id: $id) {\n    dividends(\n      where: { exDate: { gte: $fromDate, lt: $toDate } }\n      first: 200\n      after: $cursor\n    ) {\n      edges {\n        node {\n          exDate\n          grossDivAdj\n          currency\n        }\n      }\n\n      pageInfo {\n        hasNextPage\n        endCursor\n      }\n    }\n  }\n}\n": types.DividendEventsPagingDocument,
    "query EarningEvents($companyCode: String!, $fromDate: DateTime!, $toDate: DateTime!) {\n    company(code: $companyCode) {\n      fcEventsByTypes(\n        fcEventTypeNames: \"Results\"\n        where: {\n          dateTime: {\n            gte: $fromDate\n            lt: $toDate\n          }\n        }\n        order: { dateTime: ASC }\n        first: 99999999\n      ) {\n        nodes {\n          eventName\n          dateTime\n        }\n      }\n    }\n  }\n  ": types.EarningEventsDocument,
    "query EarningEventsPaging(\n  $companyCode: String!\n  $fromDate: DateTime!\n  $toDate: DateTime!\n  $cursor: String\n) {\n  company(code: $companyCode) {\n    fcEventsByTypes(\n      fcEventTypeNames: \"Results\"\n      where: { dateTime: { gte: $fromDate, lt: $toDate } }\n      order: { dateTime: DESC }\n      first: 200\n      after: $cursor\n    ) {\n      edges {\n        node {\n          eventName\n          dateTime\n        }\n      }\n      pageInfo {\n        hasNextPage\n        endCursor\n      }\n    }\n  }\n}\n": types.EarningEventsPagingDocument,
    "query MarketOfInstrument($id: Int!) {\n  instrumentById(id: $id) {\n    market {\n      status {\n        isOpened\n        remainingTime\n      }\n      openTimeLocal\n      closeTimeLocal\n\n      timezone {\n        nameIANA\n      }\n    }\n  }\n}": types.MarketOfInstrumentDocument,
    "\n  query TickerQuery ($id: Int!) {\n    instrumentById(id: $id) {\n      symbol\n    }\n  }\n": types.TickerQueryDocument,
    "\n  query TickerInit($ids: [Int!]!, $toCurrency: String, $adjClose: Boolean) {\n    instrumentByIds(ids: $ids, exchangeCurrency: $toCurrency, adjClose: $adjClose) {\n      ...TickerDataFragment\n    }\n  }\n\n  fragment TickerDataFragment on Instrument {\n    shareName\n    id\n    symbol\n    low52W\n    high52W\n    currency {\n      code\n      name\n    }\n    market {\n      status {\n        isOpened\n        remainingTime\n      }\n      openTimeLocal\n      closeTimeLocal\n\n      timezone {\n        nameIANA\n      }\n    }\n    currentPrice {\n      open\n      prevClose\n      volume\n      officialClose\n      officialCloseDate\n      last\n      change\n      changePercentage\n      low\n      date\n      bid\n      ask\n      high\n    }\n  \n  }\n": types.TickerInitDocument,
    "query TickerUpdate(\n  $ids: [Int!]!\n  $adjClose: Boolean\n  $additionalRealtimeIds: [Int!]!\n  $toCurrency: String\n) {\n  instrumentByIds(ids: $ids, exchangeCurrency: $toCurrency, adjClose: $adjClose) {\n    ...TickerData\n  }\n\n  additionalRealtime: instrumentByIds(ids: $additionalRealtimeIds) {\n    id\n    currentPrice {\n      officialClose\n      officialCloseDate\n    }\n  }\n}\n\nfragment TickerData on Instrument {\n  shareName\n    id\n    symbol\n    low52W\n    high52W\n    currency {\n      code\n      name\n    }\n    market {\n      status {\n        isOpened\n        remainingTime\n      }\n      openTimeLocal\n      closeTimeLocal\n\n      timezone {\n        nameIANA\n      }\n    }\n    currentPrice {\n      open\n      prevClose\n      volume\n      officialClose\n      officialCloseDate\n      last\n      change\n      changePercentage\n      low\n      date\n      bid\n      ask\n      high\n    }\n}\n": types.TickerUpdateDocument,
};

/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 *
 *
 * @example
 * ```ts
 * const query = graphql(`query GetUser($id: ID!) { user(id: $id) { name } }`);
 * ```
 *
 * The query argument is unknown!
 * Please regenerate the types.
 */
export function graphql(source: string): unknown;

/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "query ChartHistory($id: Int!, $fromDate: DateTime, $toDate: DateTime, $timeIntervalGrouping: Int) {\n  instrumentById(id: $id) {\n    market {\n      timezone {\n        nameIANA\n      }\n      openTimeLocal\n      closeTimeLocal\n    }\n    intraday(\n      where: {dateTime: {gte: $fromDate, lte: $toDate}}\n      first: 9999999\n      order: {dateTime: ASC}\n      timeIntervalGrouping: $timeIntervalGrouping\n    ) {\n      nodes {\n        close\n        dateTime\n        high\n        low\n        open\n        volume\n        instrumentId\n      }\n    }\n  }\n}"): (typeof documents)["query ChartHistory($id: Int!, $fromDate: DateTime, $toDate: DateTime, $timeIntervalGrouping: Int) {\n  instrumentById(id: $id) {\n    market {\n      timezone {\n        nameIANA\n      }\n      openTimeLocal\n      closeTimeLocal\n    }\n    intraday(\n      where: {dateTime: {gte: $fromDate, lte: $toDate}}\n      first: 9999999\n      order: {dateTime: ASC}\n      timeIntervalGrouping: $timeIntervalGrouping\n    ) {\n      nodes {\n        close\n        dateTime\n        high\n        low\n        open\n        volume\n        instrumentId\n      }\n    }\n  }\n}"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "query ChartHistoricalData(\n  $id: Int!\n  $fromDate: DateTime\n  $toDate: DateTime\n) {\n  instrumentById(id: $id) {\n   \n    historicals(\n      where: { dateTime: { gte: $fromDate, lte: $toDate } }\n      first: 9999999\n      order: { dateTime: ASC }\n    ) {\n      nodes {\n        close\n        dateTime\n        high\n        low\n        open\n        volume\n        instrumentId\n      }\n    }\n  }\n}\n"): (typeof documents)["query ChartHistoricalData(\n  $id: Int!\n  $fromDate: DateTime\n  $toDate: DateTime\n) {\n  instrumentById(id: $id) {\n   \n    historicals(\n      where: { dateTime: { gte: $fromDate, lte: $toDate } }\n      first: 9999999\n      order: { dateTime: ASC }\n    ) {\n      nodes {\n        close\n        dateTime\n        high\n        low\n        open\n        volume\n        instrumentId\n      }\n    }\n  }\n}\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "query DividendEvents($id: Int!, $fromDate: DateTime!, $toDate: DateTime!) {\n    instrumentById(id: $id) {\n      dividends(where: {exDate: { gte: $fromDate, lt: $toDate}}, first: 99999999) {\n        nodes {\n          exDate\n          grossDivAdj\n          currency\n        }\n      }\n    }\n  }\n  "): (typeof documents)["query DividendEvents($id: Int!, $fromDate: DateTime!, $toDate: DateTime!) {\n    instrumentById(id: $id) {\n      dividends(where: {exDate: { gte: $fromDate, lt: $toDate}}, first: 99999999) {\n        nodes {\n          exDate\n          grossDivAdj\n          currency\n        }\n      }\n    }\n  }\n  "];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "query DividendEventsPaging(\n  $id: Int!\n  $fromDate: DateTime!\n  $toDate: DateTime!\n  $cursor: String\n) {\n  instrumentById(id: $id) {\n    dividends(\n      where: { exDate: { gte: $fromDate, lt: $toDate } }\n      first: 200\n      after: $cursor\n    ) {\n      edges {\n        node {\n          exDate\n          grossDivAdj\n          currency\n        }\n      }\n\n      pageInfo {\n        hasNextPage\n        endCursor\n      }\n    }\n  }\n}\n"): (typeof documents)["query DividendEventsPaging(\n  $id: Int!\n  $fromDate: DateTime!\n  $toDate: DateTime!\n  $cursor: String\n) {\n  instrumentById(id: $id) {\n    dividends(\n      where: { exDate: { gte: $fromDate, lt: $toDate } }\n      first: 200\n      after: $cursor\n    ) {\n      edges {\n        node {\n          exDate\n          grossDivAdj\n          currency\n        }\n      }\n\n      pageInfo {\n        hasNextPage\n        endCursor\n      }\n    }\n  }\n}\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "query EarningEvents($companyCode: String!, $fromDate: DateTime!, $toDate: DateTime!) {\n    company(code: $companyCode) {\n      fcEventsByTypes(\n        fcEventTypeNames: \"Results\"\n        where: {\n          dateTime: {\n            gte: $fromDate\n            lt: $toDate\n          }\n        }\n        order: { dateTime: ASC }\n        first: 99999999\n      ) {\n        nodes {\n          eventName\n          dateTime\n        }\n      }\n    }\n  }\n  "): (typeof documents)["query EarningEvents($companyCode: String!, $fromDate: DateTime!, $toDate: DateTime!) {\n    company(code: $companyCode) {\n      fcEventsByTypes(\n        fcEventTypeNames: \"Results\"\n        where: {\n          dateTime: {\n            gte: $fromDate\n            lt: $toDate\n          }\n        }\n        order: { dateTime: ASC }\n        first: 99999999\n      ) {\n        nodes {\n          eventName\n          dateTime\n        }\n      }\n    }\n  }\n  "];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "query EarningEventsPaging(\n  $companyCode: String!\n  $fromDate: DateTime!\n  $toDate: DateTime!\n  $cursor: String\n) {\n  company(code: $companyCode) {\n    fcEventsByTypes(\n      fcEventTypeNames: \"Results\"\n      where: { dateTime: { gte: $fromDate, lt: $toDate } }\n      order: { dateTime: DESC }\n      first: 200\n      after: $cursor\n    ) {\n      edges {\n        node {\n          eventName\n          dateTime\n        }\n      }\n      pageInfo {\n        hasNextPage\n        endCursor\n      }\n    }\n  }\n}\n"): (typeof documents)["query EarningEventsPaging(\n  $companyCode: String!\n  $fromDate: DateTime!\n  $toDate: DateTime!\n  $cursor: String\n) {\n  company(code: $companyCode) {\n    fcEventsByTypes(\n      fcEventTypeNames: \"Results\"\n      where: { dateTime: { gte: $fromDate, lt: $toDate } }\n      order: { dateTime: DESC }\n      first: 200\n      after: $cursor\n    ) {\n      edges {\n        node {\n          eventName\n          dateTime\n        }\n      }\n      pageInfo {\n        hasNextPage\n        endCursor\n      }\n    }\n  }\n}\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "query MarketOfInstrument($id: Int!) {\n  instrumentById(id: $id) {\n    market {\n      status {\n        isOpened\n        remainingTime\n      }\n      openTimeLocal\n      closeTimeLocal\n\n      timezone {\n        nameIANA\n      }\n    }\n  }\n}"): (typeof documents)["query MarketOfInstrument($id: Int!) {\n  instrumentById(id: $id) {\n    market {\n      status {\n        isOpened\n        remainingTime\n      }\n      openTimeLocal\n      closeTimeLocal\n\n      timezone {\n        nameIANA\n      }\n    }\n  }\n}"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query TickerQuery ($id: Int!) {\n    instrumentById(id: $id) {\n      symbol\n    }\n  }\n"): (typeof documents)["\n  query TickerQuery ($id: Int!) {\n    instrumentById(id: $id) {\n      symbol\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query TickerInit($ids: [Int!]!, $toCurrency: String, $adjClose: Boolean) {\n    instrumentByIds(ids: $ids, exchangeCurrency: $toCurrency, adjClose: $adjClose) {\n      ...TickerDataFragment\n    }\n  }\n\n  fragment TickerDataFragment on Instrument {\n    shareName\n    id\n    symbol\n    low52W\n    high52W\n    currency {\n      code\n      name\n    }\n    market {\n      status {\n        isOpened\n        remainingTime\n      }\n      openTimeLocal\n      closeTimeLocal\n\n      timezone {\n        nameIANA\n      }\n    }\n    currentPrice {\n      open\n      prevClose\n      volume\n      officialClose\n      officialCloseDate\n      last\n      change\n      changePercentage\n      low\n      date\n      bid\n      ask\n      high\n    }\n  \n  }\n"): (typeof documents)["\n  query TickerInit($ids: [Int!]!, $toCurrency: String, $adjClose: Boolean) {\n    instrumentByIds(ids: $ids, exchangeCurrency: $toCurrency, adjClose: $adjClose) {\n      ...TickerDataFragment\n    }\n  }\n\n  fragment TickerDataFragment on Instrument {\n    shareName\n    id\n    symbol\n    low52W\n    high52W\n    currency {\n      code\n      name\n    }\n    market {\n      status {\n        isOpened\n        remainingTime\n      }\n      openTimeLocal\n      closeTimeLocal\n\n      timezone {\n        nameIANA\n      }\n    }\n    currentPrice {\n      open\n      prevClose\n      volume\n      officialClose\n      officialCloseDate\n      last\n      change\n      changePercentage\n      low\n      date\n      bid\n      ask\n      high\n    }\n  \n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "query TickerUpdate(\n  $ids: [Int!]!\n  $adjClose: Boolean\n  $additionalRealtimeIds: [Int!]!\n  $toCurrency: String\n) {\n  instrumentByIds(ids: $ids, exchangeCurrency: $toCurrency, adjClose: $adjClose) {\n    ...TickerData\n  }\n\n  additionalRealtime: instrumentByIds(ids: $additionalRealtimeIds) {\n    id\n    currentPrice {\n      officialClose\n      officialCloseDate\n    }\n  }\n}\n\nfragment TickerData on Instrument {\n  shareName\n    id\n    symbol\n    low52W\n    high52W\n    currency {\n      code\n      name\n    }\n    market {\n      status {\n        isOpened\n        remainingTime\n      }\n      openTimeLocal\n      closeTimeLocal\n\n      timezone {\n        nameIANA\n      }\n    }\n    currentPrice {\n      open\n      prevClose\n      volume\n      officialClose\n      officialCloseDate\n      last\n      change\n      changePercentage\n      low\n      date\n      bid\n      ask\n      high\n    }\n}\n"): (typeof documents)["query TickerUpdate(\n  $ids: [Int!]!\n  $adjClose: Boolean\n  $additionalRealtimeIds: [Int!]!\n  $toCurrency: String\n) {\n  instrumentByIds(ids: $ids, exchangeCurrency: $toCurrency, adjClose: $adjClose) {\n    ...TickerData\n  }\n\n  additionalRealtime: instrumentByIds(ids: $additionalRealtimeIds) {\n    id\n    currentPrice {\n      officialClose\n      officialCloseDate\n    }\n  }\n}\n\nfragment TickerData on Instrument {\n  shareName\n    id\n    symbol\n    low52W\n    high52W\n    currency {\n      code\n      name\n    }\n    market {\n      status {\n        isOpened\n        remainingTime\n      }\n      openTimeLocal\n      closeTimeLocal\n\n      timezone {\n        nameIANA\n      }\n    }\n    currentPrice {\n      open\n      prevClose\n      volume\n      officialClose\n      officialCloseDate\n      last\n      change\n      changePercentage\n      low\n      date\n      bid\n      ask\n      high\n    }\n}\n"];

export function graphql(source: string) {
  return (documents as any)[source] ?? {};
}

export type DocumentType<TDocumentNode extends DocumentNode<any, any>> = TDocumentNode extends DocumentNode<  infer TType,  any>  ? TType  : never;