import { useImperativeHandle } from "react";
import { Flex, Form, InputNumber, Select, Switch } from "antd";

import DEFAULT_SETTING from "../../../configs/defaultSetting";
import { useInputStyle } from "../../../hooks/useInputStyle";
import { getArrayFieldValue } from "../../../utils";
import { CHART_SETTING_KEYS, CHART_TYPE_KEYS } from "../../../constants/chartConstant";
import {
  CHART_EVENT_KEYS_OPTIONS,
  CHART_PREFERENCES_OPTIONS,
  DATA_FIELDS_OPTIONS,
  SHOW_LAST_CLOSE_LINE_OPTIONS,
  TABLE_VIEW_OPTIONS,
  TICKER_TEMPLATE_OPTIONS,
  TOOLTIP_OPTIONS,
  VALUE_TRACKING_OPTIONS,
  VOLUME_OPTIONS,
  Y_AXIS_PREFERENCES_OPTIONS,
} from "../../../configs/options";
import { ColorPickerWrapper } from "../../commons/DebounceColorPicker";
import DateRangeIntervalTags from "./DateRangeIntervalTags";
import DefaultRangeInterval from "./DefaultRangeInterval";
import { i18n } from "@euroland/libs";
import DebounceInputNumber from "../../commons/DebounceInputNumber";
import { CHART_KEYS } from "../../../constants/common";

export default function ShareGraphSetting({
  handleRef,
  initState,
  onDraftChange,
}) {
  const [form] = Form.useForm();

  const { inputStyle } = useInputStyle();

  const tickerSettingsFields = DATA_FIELDS_OPTIONS.map((item) => ({
    ...item,
    label: i18n.translate(item.value),
  }));

  const tickerTemplateOptions = TICKER_TEMPLATE_OPTIONS.map((item) => ({
    ...item,
    label: i18n.translate(item.value),
  }));
  const valueTrackingOptions = VALUE_TRACKING_OPTIONS.map((item) => ({
    ...item,
    label: i18n.translate(item.value),
  }));

  const chartTypeOptions = Object.values(CHART_TYPE_KEYS).map((item) => ({
    value: item.key,
    label: i18n.translate(item.label),
  }));
  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();

      return true;
    } catch (errorInfo) {
      console.log("Failed: minimal sharegraph", errorInfo);
      return false;
    }
  };

  useImperativeHandle(handleRef, () => {
    return { onSubmit: handleSubmit };
  });

  const onFormLayoutChange = (_, values) => {
    onDraftChange(form.getFieldsValue(true));
  };

  return (
    <Form
      form={form}
      initialValues={initState}
      onValuesChange={onFormLayoutChange}
      style={{ width: "100%", paddingBottom: 20 }}
      layout="vertical"
    >
      <Flex gap={10} className="flex-wrap">
        <Form.Item
          label="Date ranges and intervals"
          name={["dateRangesAndInterval"]}
          style={{ marginBottom: 0 }}
        >
          <DateRangeIntervalTags />
        </Form.Item>
      </Flex>
      <Flex gap={10} className="flex-wrap mt-5">
        <Form.Item name={["defaultRange"]} noStyle>
          <DefaultRangeInterval />
        </Form.Item>
        <Form.Item
          label="Custom range"
          name={["customRange"]}
          layout="vertical"
          style={{ marginBottom: 0 }}
        >
          <Switch />
        </Form.Item>
      </Flex>

      <Form.Item
        className="mt-5"
        label={
          <span className="font-semibold text-[16px]">Ticker settings</span>
        }
        style={{ marginBottom: 0 }}
      >
        <Flex gap={10} className="flex-wrap">
          <Form.Item
            label="Refresh time (second)"
            name={["tickerSettings", "refreshTickerTime"]}
            style={{ marginBottom: 0 }}
          >
            <DebounceInputNumber
              min={0}
              className="w-[120px]"
              placeholder="Refresh time"
              addonAfter="s"
            />
          </Form.Item>
          {/* <Form.Item
            label="Data fields"
            name={["tickerSettings", "dataFields"]}
            getValueProps={(value) => ({ value: getArrayFieldValue(value) })}
            getValueFromEvent={(selected) => selected.join(",")}
            style={{ marginBottom: 0 }}
          >
            <Select
              mode="multiple"
              style={inputStyle}
              options={tickerSettingsFields}
            />
          </Form.Item>
          <Form.Item label="Template" name={["tickerSettings", "template"]}>
            <Select style={inputStyle} options={tickerTemplateOptions} />
          </Form.Item> */}
        </Flex>
      </Form.Item>

      <Form.Item
        label={
          <span className="font-semibold text-[16px] mt-5">
            Chart configuration
          </span>
        }
        style={{ marginBottom: 0 }}
      >
        <Flex gap={10} className="flex-wrap mt-2">
          <Form.Item
            label="Axes fontsize"
            name={["chartConfiguration", "axesFontsize"]}
            style={{ marginBottom: 0 }}
          >
            <DebounceInputNumber
              min={0}
              className="w-[120px]"
              placeholder="Axes fontsize"
              addonAfter="px"
            />
          </Form.Item>
          <Form.Item
            label="Axes color"
            name={["chartConfiguration", "axesColor"]}
            style={{ marginBottom: 0 }}
          >
            <ColorPickerWrapper showText allowClear />
          </Form.Item>
          <Form.Item
            label="Grid color"
            name={["chartConfiguration", "gridColor"]}
            style={{ marginBottom: 0 }}
          >
            <ColorPickerWrapper showText allowClear />
          </Form.Item>
          {/* <Form.Item
            label="Value tracking"
            name={["valueTracking"]}
            style={{ marginBottom: 0 }}
          >
            <Select style={inputStyle} options={valueTrackingOptions} />
          </Form.Item> */}
        </Flex>
        {/* <p className="mt-5 font-semibold text-[14px]">Controller UI</p>
        <Flex gap={20} className="flex-wrap mt-4">
          <Form.Item noStyle name={["controllerUI", VOLUME_OPTIONS.key]}>
            <SwitchWrapper label="Show/Hide volume" />
          </Form.Item>
          <Form.Item
            noStyle
            name={["controllerUI", Y_AXIS_PREFERENCES_OPTIONS.key]}
          >
            <SwitchWrapper label="Show/Hide y-axis preferences" />
          </Form.Item>
        </Flex> */}
        <p className="mt-5 font-semibold text-[14px]">Default values</p>
        <Flex gap={20} className="flex-wrap mt-5">
          <Form.Item
            noStyle
            layout="horizontal"
            name={["chartConfiguration", SHOW_LAST_CLOSE_LINE_OPTIONS.key]}
            label={i18n.translate(SHOW_LAST_CLOSE_LINE_OPTIONS.label)}
            getValueFromEvent={(selected) =>
              selected ? SHOW_LAST_CLOSE_LINE_OPTIONS.key : ""
            }
            style={{ marginBottom: 0 }}
          >
            <SwitchWrapper
              label={i18n.translate(SHOW_LAST_CLOSE_LINE_OPTIONS.label)}
            />
          </Form.Item>
          <Form.Item
            noStyle
            layout="horizontal"
            name={["chartConfiguration", CHART_PREFERENCES_OPTIONS.key]}
            label={i18n.translate(CHART_SETTING_KEYS.CHART_PREFERENCES.HIGH_LOW_VALUE.label)}
            getValueFromEvent={(selected) =>
              selected ? CHART_SETTING_KEYS.CHART_PREFERENCES.HIGH_LOW_VALUE.key : ""
            }
            style={{ marginBottom: 0 }}
          >
            <SwitchWrapper
              label={i18n.translate(CHART_SETTING_KEYS.CHART_PREFERENCES.HIGH_LOW_VALUE.label)}
            />
          </Form.Item>
          {/* <Form.Item
            noStyle
            layout="horizontal"
            name={["chartConfiguration", TABLE_VIEW_OPTIONS.key]}
            label={i18n.translate(TABLE_VIEW_OPTIONS.label)}
            getValueFromEvent={(selected) =>
              selected ? TABLE_VIEW_OPTIONS.key : ""
            }
          >
            <SwitchWrapper label={i18n.translate(TABLE_VIEW_OPTIONS.label)} />
          </Form.Item>
          <Form.Item
            noStyle
            layout="horizontal"
            name={["chartConfiguration", TOOLTIP_OPTIONS.key]}
            label={i18n.translate(TOOLTIP_OPTIONS.label)}
            getValueFromEvent={(selected) =>
              selected ? TOOLTIP_OPTIONS.key : ""
            }
          >
            <SwitchWrapper label={i18n.translate(TOOLTIP_OPTIONS.label)} />
          </Form.Item> */}
        </Flex>

        <Flex gap={10} className="flex-wrap mt-5">
          <Form.Item
            name={["chartConfiguration", "chartType"]}
            label={"Chart type"}
            style={{ marginBottom: 0 }}
          >
            <Select style={inputStyle} options={chartTypeOptions} />
          </Form.Item>
          {/* <Form.Item
            name={["chartConfiguration", CHART_PREFERENCES_OPTIONS.key]}
            label={i18n.translate(CHART_PREFERENCES_OPTIONS.label)}
            style={{ marginBottom: 0 }}
          >
            <Select
              mode="multiple"
              style={inputStyle}
              options={CHART_PREFERENCES_OPTIONS.options.map((opt) => ({
                ...opt,
                label: i18n.translate(opt.label),
              }))}
            />
          </Form.Item> */}
          {/* <Form.Item
            name={["chartConfiguration", Y_AXIS_PREFERENCES_OPTIONS.key]}
            label={i18n.translate(Y_AXIS_PREFERENCES_OPTIONS.label)}
            style={{ marginBottom: 0 }}
          >
            <Select
              style={inputStyle}
              options={Y_AXIS_PREFERENCES_OPTIONS.options.map((opt) => ({
                ...opt,
                label: i18n.translate(opt.label),
              }))}
            />
          </Form.Item> */}
          <Form.Item
            name={[CHART_KEYS.EVENTS]}
            label={i18n.translate(CHART_KEYS.EVENTS)}
            getValueProps={(value) => ({ value: getArrayFieldValue(value) })}
            getValueFromEvent={(selected) => selected.join(",")}
            style={{ marginBottom: 0 }}
          >
            <Select
              mode="multiple"
              style={inputStyle}
              options={CHART_EVENT_KEYS_OPTIONS.map((opt) => ({
                ...opt,
                label: i18n.translate(opt.label),
              }))}
            />
          </Form.Item>
        </Flex>
        {/* <Flex gap={10} className="flex-wrap mt-5">
          <Form.Item
            name={["chartConfiguration", "chartType"]}
            label={"Chart type"}
            style={{ marginBottom: 0 }}
          >
            <Select style={inputStyle} options={chartTypeOptions} />
          </Form.Item>
          <Form.Item
            name={["chartConfiguration", VOLUME_OPTIONS.key]}
            label={i18n.translate(VOLUME_OPTIONS.label)}
            getValueProps={(value) => ({ value: getArrayFieldValue(value) })}
            getValueFromEvent={(selected) => selected.join(",")}
            style={{ marginBottom: 0 }}
          >
            <Select
              mode="multiple"
              style={inputStyle}
              options={VOLUME_OPTIONS.options.map((opt) => ({
                ...opt,
                label: i18n.translate(opt.label),
              }))}
            />
          </Form.Item>
        </Flex> */}
      </Form.Item>
    </Form>
  );
}

const SwitchWrapper = ({ label, ...props }) => {
  return (
    <Flex gap={10}>
      <span>{label}</span>
      <Switch {...props} />
    </Flex>
  );
};
