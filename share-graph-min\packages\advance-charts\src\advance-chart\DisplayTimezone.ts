import { Delegate } from '../helpers/delegate';
import { IAdvanceChart, Period } from './i-advance-chart';
import {TickMarkType} from 'lightweight-charts';
import dayjs from '../helpers/dayjs-setup'
export class DisplayTimezone {
  _timezoneChanged = new Delegate<string>();
  _timezone: string
  _browserTimezone = Intl.DateTimeFormat().resolvedOptions().timeZone
  constructor(
    private _source: IAdvanceChart
  ) {
    this._timezone = this._source.options.tzDisplay

    // const now = dayjs(new Date).tz(this._timezone).format('YYYY-MM-DD HH:mm:ss Z');
    // console.log('timezone display now: ' + now)
  }

  formatDateTime(date: Date) {
    date = this.convertDateToTimezoneDate(date);
    return new Intl.DateTimeFormat(this._source.options.locale, {
      dateStyle: 'medium',
      timeStyle: 'medium',
      hourCycle: 'h23', 
    }).format(date);
  }
  formatDate(date: Date) {
    date = this.convertDateToTimezoneDate(date);
    return new Intl.DateTimeFormat(this._source.options.locale, {
      dateStyle: 'medium',
    }).format(date);
  }

  convertDateToTimezoneDate(date: Date) {
    if(this._timezone === this._browserTimezone) return date;
    return dayjs.tz(date, this._timezone).tz(this._browserTimezone, true).toDate()
  }

  format(date: Date) {
    switch(this._source.dataInterval.period) {
      case Period.day:
      case Period.month:
      case Period.week:
        return this.formatDate(date);
      default:
        return this.formatDateTime(date)
    }
  }

  tickMarkFormatter(date: Date, tickMarkType: TickMarkType) {
    date = this.convertDateToTimezoneDate(date);
    const formatOptions: Intl.DateTimeFormatOptions = {};

    switch (tickMarkType) {
      case TickMarkType.Year:
        formatOptions.year = 'numeric';
        break;

      case TickMarkType.Month:
        formatOptions.month = 'short';
        break;

      case TickMarkType.DayOfMonth:
        formatOptions.day = 'numeric';
        break;

      case TickMarkType.Time:
        formatOptions.hour12 = false;
        formatOptions.hour = '2-digit';
        formatOptions.minute = '2-digit';
        break;

      case TickMarkType.TimeWithSeconds:
        formatOptions.hour12 = false;
        formatOptions.hour = '2-digit';
        formatOptions.minute = '2-digit';
        formatOptions.second = '2-digit';
        break;
    }

    return date.toLocaleString(this._source.options.locale, formatOptions)
  }
}
