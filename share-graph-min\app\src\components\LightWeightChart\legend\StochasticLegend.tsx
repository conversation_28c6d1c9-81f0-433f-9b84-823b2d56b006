import { FC } from 'react';
import { Legend } from './Legend';
import { AdvanceChart } from "@sharegraph-mini/advance-charts";
import {StochasticIndicator} from "@sharegraph-mini/advance-charts";

const StochasticLegend: FC<{
  indicator: StochasticIndicator;
  advanceChart: AdvanceChart;
}> = ({ indicator, advanceChart }) => {
  return (
    <Legend
      name="Stoch"
      indicator={indicator}
      renderer={(d) => d && d.value && (
        <>
          {' '}
          <span style={{ color: indicator.options.color }}>
            {advanceChart.numberFormatter.decimal(d.value[1])}
          </span>{' '}
          <span style={{ color: indicator.options.signalColor }}>
            {advanceChart.numberFormatter.decimal(d.value[0])}
          </span>
        </>
      )}
    />
  );
};

export default StochasticLegend;
