import Dropdown from './Dropdown';
import { DropdownMenuItem, OnChangeDropdown } from '../../types/common';
import { CHART_INDICATOR_KEYS } from '../../constants/chartConstant';
import { useAppStore } from '../../stores/useAppStore';
import { CHART_KEYS } from '../../constants/common';
import { i18n } from '@euroland/libs';
import { selectCheckIcon } from './common';
import OverlayIcon from '../../icons/OverlayIcon';



const OverlayDropdown = () => {
  const indicators = useAppStore((state) => state.indicators);
  const overlays = useAppStore((state) => state.overlays);
  const setChartIndicators = useAppStore((state) => state.setChartIndicators);
  const setChartOverlays = useAppStore((state) => state.setChartOverlays);
  const value = {
    [CHART_KEYS.INDICATORS]: indicators,
    [CHART_KEYS.OVERLAYS]: overlays,
  };

  const menuData: DropdownMenuItem[] = [
    {
      id: CHART_KEYS.OVERLAYS,
      label: i18n.translate('overlays'),
      hideGroupLabel: true,
      items: [
        {
          id: CHART_INDICATOR_KEYS.SMA.key,
          label: CHART_INDICATOR_KEYS.SMA.label,
          ...selectCheckIcon,
          // icon: <CheckboxIcon />,
          // activeIcon: <CheckboxIcon active />,
        },
        {
          id: CHART_INDICATOR_KEYS.EMA.key,
          label: CHART_INDICATOR_KEYS.EMA.label,
          ...selectCheckIcon,
          // icon: <CheckboxIcon />,
          // activeIcon: <CheckboxIcon active />,
        },
        {
          id: CHART_INDICATOR_KEYS.WMA.key,
          label: CHART_INDICATOR_KEYS.WMA.label,
          ...selectCheckIcon,
          // icon: <CheckboxIcon />,
          // activeIcon: <CheckboxIcon active />,
        },
        {
          id: CHART_INDICATOR_KEYS.BOLLINGER_BANDS.key,
          label: CHART_INDICATOR_KEYS.BOLLINGER_BANDS.label,
          ...selectCheckIcon,
          // icon: <CheckboxIcon />,
          // activeIcon: <CheckboxIcon active />,
        },
      ],
    },
  ];

  const handleChange: OnChangeDropdown = (val, parentId) => {
    if (parentId === CHART_KEYS.INDICATORS) setChartIndicators(val);
    else setChartOverlays(val);
  };

  return (
    <Dropdown
      menuData={menuData}
      onChange={handleChange}
      value={value}
      description={i18n.translate('chart-overlays')}
      placeholder={
        <>
          <OverlayIcon /> <span>{i18n.translate("overlays")}</span>
        </>
      }
    />
  );
};

export default OverlayDropdown;
