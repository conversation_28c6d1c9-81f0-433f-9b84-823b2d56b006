import React, { useState } from 'react';
import SettingIcon from '../../icons/SettingIcon';
import Button from '../Button';
import { i18n } from '@euroland/libs';
import { SettingDrawer } from '../Drawer';

const MobileSettings = () => {
  const [isSettingDrawerOpen, setIsSettingDrawerOpen] = useState(false);

  const toggleSettingDrawer = () => {
    setIsSettingDrawerOpen(!isSettingDrawerOpen);
  };

  return (
    <>
      <Button onClick={toggleSettingDrawer} className="mobile-button">
        <SettingIcon className="setting-icon" /> <span>{i18n.translate('settings')}</span>
      </Button>
      <SettingDrawer
        isOpen={isSettingDrawerOpen}
        onClose={toggleSettingDrawer}
      />
    </>
  );
};

export default React.memo(MobileSettings); 