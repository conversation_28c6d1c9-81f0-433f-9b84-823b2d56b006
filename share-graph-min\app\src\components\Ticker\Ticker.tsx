import { useEffect } from 'react';
import TableLayoutTicker from './TableLayoutTicker';
import { useAppStore } from '../../stores/useAppStore';
import TableLayoutTickerSkeleton from './TableLayoutTickerSkeleton';
const Ticker = () => {
  const selectedInstrumentId = useAppStore((state) => state.selectedInstrumentId);
  const tickers = useAppStore((state) => state.tickers);
  const refreshTickerTime = useAppStore(
    (state) => state.appSettings.tickerSettings.refreshTickerTime
  );
  const fetchInitTickers = useAppStore((state) => state.fetchInitTickers);
  const fetchUpdateTickers = useAppStore((state) => state.fetchUpdateTickers);

  const tickerData = tickers[selectedInstrumentId];

  useEffect(() => {
    fetchInitTickers();
  }, [fetchInitTickers]);

  useEffect(() => {
    const intervalId: ReturnType<typeof setInterval> = setInterval(() => {
      fetchUpdateTickers();
    }, Number(refreshTickerTime) * 1000);

    return () => {
      clearInterval(intervalId);
    };
  }, [fetchUpdateTickers, refreshTickerTime]);
  if (!tickerData) {
    return <TableLayoutTickerSkeleton />;
  }

  return (
    <div className="ticker">
      <TableLayoutTicker />
    </div>
  );
};

export default Ticker;
