import React, { useState } from 'react';
import Switcher from './Switcher';

const SwitcherExample: React.FC = () => {
  const [isEnabled, setIsEnabled] = useState(false);
  
  const handleChange = (checked: boolean) => {
    setIsEnabled(checked);
  };
  
  return (
    <div className="switcher-example">
      <h3>Switcher Example</h3>
      
      <div className="example-row">
        <span>Default Switcher:</span>
        <Switcher checked={isEnabled} onChange={handleChange} />
      </div>
      
      <div className="example-row">
        <span>Disabled Switcher:</span>
        <Switcher checked={true} onChange={() => {}} disabled={true} />
      </div>
      
      <div className="example-row">
        <span>Current State:</span>
        <span>{isEnabled ? 'ON' : 'OFF'}</span>
      </div>
    </div>
  );
};

export default SwitcherExample; 