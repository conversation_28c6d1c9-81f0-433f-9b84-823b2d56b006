import {FC} from 'react'
import {Legend} from './Legend'
import {VolumeIndicator} from "@sharegraph-mini/advance-charts"
import {AdvanceChart} from "@sharegraph-mini/advance-charts"

const VolumeLegend: FC<{indicator: VolumeIndicator, advanceChart: AdvanceChart}> = ({indicator, advanceChart}) => {
  return <Legend
    name="Volume"
    indicator={indicator}
    renderer={(d) =>
      d && d.value ? (
        <span style={{ color: d.value[1] ? indicator.options.upColor : indicator.options.downColor }}>
          {advanceChart.numberFormatter.volume(d.value[0])}
        </span>
      ) : null
    }
  />
}

export default VolumeLegend