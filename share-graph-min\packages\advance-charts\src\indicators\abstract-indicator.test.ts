import { IChartApi } from 'lightweight-charts';
import { expect, it, describe, vi } from 'vitest';
import { ChartIndicator } from './abstract-indicator';

describe('ChartIndicator', () => {
  // Helper to create mock chart (reuse pattern)
  const createMockChart = (overrides = {}) => ({
    subscribeCrosshairMove: vi.fn(),
    unsubscribeCrosshairMove: vi.fn(),
    ...overrides
  } as unknown as IChartApi);

  // Helper test indicator class (avoid repetition)
  class TestIndicator extends ChartIndicator {
    getDefaultOptions() {
      return { overlay: false };
    }
    calcIndicatorData = vi.fn();
    applyIndicatorData = vi.fn();
    _applyOptions() {}
    setPaneIndex() {}
    getPaneIndex() {
      return 0;
    }
  }

  describe('initialization', () => {
    it('should setup chart subscription and store options', () => {
      const mockChart = createMockChart();
      const options = { someOption: 'test', overlay: false };

      const indicator = new TestIndicator(mockChart, options);

      // Test core behavior: subscribes to crosshair and stores options
      expect(mockChart.subscribeCrosshairMove).toHaveBeenCalledWith(
        indicator.onCrosshairMove
      );
      expect(indicator.options).toEqual(options);
    });
  });

  describe('data handling', () => {
    it('should process data and trigger calculations', () => {
      const mockChart = createMockChart();
      const indicator = new TestIndicator(mockChart);

      indicator.setData([]);

      // Test core behavior: processes data and triggers indicator calculations
      expect(indicator.getData()).toEqual([]);
      expect(indicator.calcIndicatorData).toHaveBeenCalled();
      expect(indicator.applyIndicatorData).toHaveBeenCalled();
    });

    it('should return undefined for non-existent time data', () => {
      const mockChart = createMockChart();
      const indicator = new TestIndicator(mockChart);

      const result = indicator.dataByTime('2023-10-10');

      // Test core behavior: handles missing data gracefully
      expect(result).toBeUndefined();
    });

    it('should return undefined for last point when no data exists', () => {
      const mockChart = createMockChart();
      const indicator = new TestIndicator(mockChart);

      const result = indicator.lastPoint();

      // Test core behavior: handles empty data state
      expect(result).toBeUndefined();
    });
  });
});
