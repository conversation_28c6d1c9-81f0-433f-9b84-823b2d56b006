import { configureStore } from "@reduxjs/toolkit";
import formsSlice from "./slice/forms";
import settingsSlice from "./slice/settings";

const rootReducer = {
  settings: settingsSlice.reducer,
  forms: formsSlice.reducer,
};
export const store = configureStore({
  reducer: rootReducer,
  devTools: true,
});

/**
 * @typedef {typeof store} AppStore
 * @typedef {ReturnType<AppStore['getState']>} RootState
 * @typedef {AppStore['dispatch']} AppDispatch
 */
