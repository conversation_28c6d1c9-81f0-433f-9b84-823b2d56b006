
.modal {
    &__content {
        background: white;
        border-radius: 8px;
        max-width: 500px;
        width: 100%;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        z-index: 1000;
    }
    

    &__overlay {
        background: transparent;
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        z-index: 999;
    }

    &__header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        border-bottom: 1px solid #ddd;
        padding: 16px;
    }

    &__title {
        margin: 0;
        font-size: 18px;
    }

    &__close {
        background: none;
        border: none;
        cursor: pointer;
        padding: 6px;
        border-radius: 5px;
        display: flex;
        align-items: center;
        justify-content: center;

        &:hover {
            background: var(--hover-color);
        }
    }

    &__body {
        max-height: 400px;
        padding: 16px;

    }

    &__footer {
        padding: 16px;
        display: flex;
        justify-content: flex-end;
        gap: 15px;
    }
}