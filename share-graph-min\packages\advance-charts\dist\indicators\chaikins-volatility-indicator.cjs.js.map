{"version": 3, "file": "chaikins-volatility-indicator.cjs.js", "sources": ["../../src/indicators/chaikins-volatility-indicator.ts"], "sourcesContent": ["import { IChartApi, ISeriesApi, LineSeries, Nominal, SeriesType, SingleValueData, Time } from \"lightweight-charts\";\nimport { ChartIndicator, ChartIndicatorOptions } from \"./abstract-indicator\";\nimport { RegionPrimitive } from \"../custom-primitive/primitive/region\";\nimport { autoScaleInfoProviderCreator } from \"../helpers/utils\";\nimport { Context } from \"../helpers/execution-indicator\";\n\nexport interface ChaikinsVolatilityIndicatorOptions extends ChartIndicatorOptions {\n  color: string;\n  period: number;        // EMA period for High-Low spread (typically 10)\n  rocPeriod: number;     // Rate of Change lookback period (typically 10)\n  priceLineColor: string;\n  backgroundColor: string;\n}\n\nexport const defaultOptions: ChaikinsVolatilityIndicatorOptions = {\n  color: \"rgba(255, 152, 0, 1)\",     // Orange for Chaikin's Volatility\n  priceLineColor: \"rgba(150, 150, 150, 0.35)\",\n  backgroundColor: '#ff98001a',\n  period: 10,        // Default EMA period for High-Low spread\n  rocPeriod: 12,     // Default Rate of Change lookback period\n  overlay: false\n}\n\nexport type ChaikinsVolatilityLine = Nominal<number, 'ChaikinsVolatility'>\n\nexport type ChaikinsVolatilityData = readonly [ChaikinsVolatilityLine]\n\nexport default class ChaikinsVolatilityIndicator extends ChartIndicator<ChaikinsVolatilityIndicatorOptions, ChaikinsVolatilityData> {\n  chaikinsVolatilitySeries: ISeriesApi<SeriesType>\n\n  constructor(chart: IChartApi, options?: Partial<ChaikinsVolatilityIndicatorOptions>, paneIndex?: number) {\n    super(chart, options)\n\n    this.chaikinsVolatilitySeries = chart.addSeries(LineSeries, {\n      color: this.options.color,\n      lineWidth: 2,\n      priceLineVisible: false,\n      crosshairMarkerVisible: false,\n      priceScaleId: 'chaikinsvolatility',\n      autoscaleInfoProvider: autoScaleInfoProviderCreator({ maxValue: 80, minValue: -80 })\n    }, paneIndex);\n\n    this.chaikinsVolatilitySeries.attachPrimitive(\n      new RegionPrimitive({\n        upPrice: 5,\n        lowPrice: -5,\n        lineColor: this.options.priceLineColor,\n        backgroundColor: this.options.backgroundColor\n      })\n    );\n  }\n\n  getDefaultOptions(): ChaikinsVolatilityIndicatorOptions {\n    return defaultOptions\n  }\n\n  formula(c: Context): ChaikinsVolatilityData | undefined {\n    const emaPeriod = this.options.period;      // EMA period for High-Low spread (typically 10)\n    const rocPeriod = this.options.rocPeriod;   // Rate of Change lookback period (typically 10)\n\n    const high = c.symbol.high;\n    const low = c.symbol.low;\n\n    // Step 1: Calculate High-Low spread (not close price!)\n    const highLowSpread = high - low;\n\n    const alpha = 2 / (emaPeriod + 1);\n\n    const hlSeries = c.new_var(highLowSpread, emaPeriod);\n    const emaSeries = c.new_var(NaN, rocPeriod + 1);\n\n    if (!hlSeries.calculable()) return;\n\n    const previousEMA = emaSeries.get(1);\n\n    let currentEMA;\n    if (isNaN(previousEMA)) {\n      const hlValues = hlSeries.getAll();\n      currentEMA = hlValues.reduce((sum, val) => sum + val, 0) / emaPeriod;\n    } else {\n      currentEMA = alpha * highLowSpread + (1 - alpha) * previousEMA;\n    }\n\n    emaSeries.set(currentEMA);\n\n    const pastEMA = emaSeries.get(rocPeriod);\n\n    if (isNaN(pastEMA) || pastEMA === 0) {\n      return [0 as ChaikinsVolatilityLine];\n    }\n\n    // Chaikin Volatility Value = (Current EMA value - EMA value rocPeriod periods ago) / EMA value rocPeriod periods ago * 100\n    const chaikinVolatility = ((currentEMA - pastEMA) / pastEMA) * 100;\n\n    return [chaikinVolatility as ChaikinsVolatilityLine];\n  }\n\n\n  applyIndicatorData() {\n    const chaikinsVolatilityData: SingleValueData[] = [];\n\n    for (const bar of this._executionContext.data) {\n      const value = bar.value;\n      if (!value) continue;\n\n      const time = bar.time as Time;\n      chaikinsVolatilityData.push({ time, value: value[0] });\n    }\n\n    this.chaikinsVolatilitySeries.setData(chaikinsVolatilityData);\n  }\n\n  remove() {\n    super.remove()\n    this.chart.removeSeries(this.chaikinsVolatilitySeries);\n  }\n\n  _applyOptions() {\n    this.chaikinsVolatilitySeries.applyOptions({ color: this.options.color });\n    this.applyIndicatorData();\n  }\n\n  setPaneIndex(paneIndex: number) {\n    this.chaikinsVolatilitySeries.moveToPane(paneIndex);\n  }\n\n  getPaneIndex(): number {\n    return this.chaikinsVolatilitySeries.getPane().paneIndex();\n  }\n}"], "names": ["defaultOptions", "ChaikinsVolatilityIndicator", "ChartIndicator", "chart", "options", "paneIndex", "__publicField", "LineSeries", "autoScaleInfoProviderCreator", "RegionPrimitive", "c", "emaPeriod", "rocPeriod", "high", "low", "highLowSpread", "alpha", "hlSeries", "emaSeries", "previousEMA", "currentEMA", "sum", "val", "pastEMA", "chaikinsVolatilityData", "bar", "value", "time"], "mappings": "6bAcaA,EAAqD,CAChE,MAAO,uBACP,eAAgB,4BAChB,gBAAiB,YACjB,OAAQ,GACR,UAAW,GACX,QAAS,EACX,EAMA,MAAqBC,UAAoCC,EAAAA,cAA2E,CAGlI,YAAYC,EAAkBC,EAAuDC,EAAoB,CACvG,MAAMF,EAAOC,CAAO,EAHtBE,EAAA,iCAKO,KAAA,yBAA2BH,EAAM,UAAUI,EAAAA,WAAY,CAC1D,MAAO,KAAK,QAAQ,MACpB,UAAW,EACX,iBAAkB,GAClB,uBAAwB,GACxB,aAAc,qBACd,sBAAuBC,EAA6B,6BAAA,CAAE,SAAU,GAAI,SAAU,GAAK,CAAA,GAClFH,CAAS,EAEZ,KAAK,yBAAyB,gBAC5B,IAAII,kBAAgB,CAClB,QAAS,EACT,SAAU,GACV,UAAW,KAAK,QAAQ,eACxB,gBAAiB,KAAK,QAAQ,eAC/B,CAAA,CACH,CAAA,CAGF,mBAAwD,CAC/C,OAAAT,CAAA,CAGT,QAAQU,EAAgD,CAChD,MAAAC,EAAY,KAAK,QAAQ,OACzBC,EAAY,KAAK,QAAQ,UAEzBC,EAAOH,EAAE,OAAO,KAChBI,EAAMJ,EAAE,OAAO,IAGfK,EAAgBF,EAAOC,EAEvBE,EAAQ,GAAKL,EAAY,GAEzBM,EAAWP,EAAE,QAAQK,EAAeJ,CAAS,EAC7CO,EAAYR,EAAE,QAAQ,IAAKE,EAAY,CAAC,EAE1C,GAAA,CAACK,EAAS,aAAc,OAEtB,MAAAE,EAAcD,EAAU,IAAI,CAAC,EAE/B,IAAAE,EACA,MAAMD,CAAW,EAENC,EADIH,EAAS,OAAO,EACX,OAAO,CAACI,EAAKC,IAAQD,EAAMC,EAAK,CAAC,EAAIX,EAE9CS,EAAAJ,EAAQD,GAAiB,EAAIC,GAASG,EAGrDD,EAAU,IAAIE,CAAU,EAElB,MAAAG,EAAUL,EAAU,IAAIN,CAAS,EAEvC,OAAI,MAAMW,CAAO,GAAKA,IAAY,EACzB,CAAC,CAA2B,EAM9B,EAFqBH,EAAaG,GAAWA,EAAW,GAEZ,CAAA,CAIrD,oBAAqB,CACnB,MAAMC,EAA4C,CAAC,EAExC,UAAAC,KAAO,KAAK,kBAAkB,KAAM,CAC7C,MAAMC,EAAQD,EAAI,MAClB,GAAI,CAACC,EAAO,SAEZ,MAAMC,EAAOF,EAAI,KACjBD,EAAuB,KAAK,CAAE,KAAAG,EAAM,MAAOD,EAAM,CAAC,EAAG,CAAA,CAGlD,KAAA,yBAAyB,QAAQF,CAAsB,CAAA,CAG9D,QAAS,CACP,MAAM,OAAO,EACR,KAAA,MAAM,aAAa,KAAK,wBAAwB,CAAA,CAGvD,eAAgB,CACd,KAAK,yBAAyB,aAAa,CAAE,MAAO,KAAK,QAAQ,MAAO,EACxE,KAAK,mBAAmB,CAAA,CAG1B,aAAanB,EAAmB,CACzB,KAAA,yBAAyB,WAAWA,CAAS,CAAA,CAGpD,cAAuB,CACrB,OAAO,KAAK,yBAAyB,QAAQ,EAAE,UAAU,CAAA,CAE7D"}