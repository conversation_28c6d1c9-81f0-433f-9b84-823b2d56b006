import {CanvasRenderingTarget2D} from "fancy-canvas";
import {PrimitivePaneViewBase} from "../primitive-base";
import {Coordinate} from "lightweight-charts";
import {ensureDefined} from "../../helpers/assertions";

export interface RegionPrimitivePaneViewOptions {
  backgroundColor: string,
  lineWidth: number, 
  lineColor: string;
  lineStyle: number[]
}

export const RegionPrimitiveOptionsDefault: RegionPrimitivePaneViewOptions = {
  backgroundColor: '#2196f31a',
  lineColor: '#787b86',
  lineWidth: 1,
  lineStyle: []
}

interface RegionPrimitiveData {
  x: Coordinate, 
  points: Coordinate[]
}

export class RegionPrimitivePaneView extends PrimitivePaneViewBase<RegionPrimitivePaneViewOptions, RegionPrimitiveData> {
  drawBackground(target: CanvasRenderingTarget2D): void {
    const data = this.data
    const visibleLogicalRange = this.getVisibleLogicalRange()
    if(data.length < 2) return;
    if(!visibleLogicalRange) return;
    target.useBitmapCoordinateSpace(scope => {
      const ctx = scope.context;
      ctx.scale(scope.horizontalPixelRatio, scope.verticalPixelRatio);

      ctx.beginPath();
      const region = new Path2D();
      const from = 0;
      const to = data.length
      const firstBar = data[from];
      region.moveTo(
        firstBar.x, 
        ensureDefined(firstBar.points.at(0))
      );

      for (let i = from + 1; i < to; i++) {
        const point = data[i];
        region.lineTo(
          point.x,
          point.points.at(0) as number
        );
      }

      for (let i = to - 1; i >= from; i--) {
        const point = data[i];
        region.lineTo(point.x, point.points.at(-1) as number);
      }

      region.lineTo(
        firstBar.x, 
        ensureDefined(firstBar.points.at(0))
      );
      region.closePath();
      ctx.fillStyle = this.options.backgroundColor
      ctx.fill(region)

      const paths = firstBar.points.map(() => new Path2D)

      firstBar.points.map((point, index) => paths[index].moveTo(firstBar.x, point))

      for (let i = from + 1; i < to; i++) {
        const bar = data[i]
        bar.points.map((point, index) => paths[index].lineTo(bar.x, point))
      }

      ctx.setLineDash(this.options.lineStyle);
      ctx.lineWidth = this.options.lineWidth
      ctx.strokeStyle = this.options.lineColor
      paths.map(path => ctx.stroke(path))
      ctx.setLineDash([]);
    })
    // const points: Coordinate[] = this.points
    // if(points.length < 2) return;
    // target.useBitmapCoordinateSpace(scope => {
    //   const ctx = scope.context;
    //   ctx.scale(scope.horizontalPixelRatio, scope.verticalPixelRatio);

    //   ctx.lineWidth = this.options.lineWidth;
    //   ctx.strokeStyle = this.options.lineColor;
      
    //   ctx.beginPath();

    //   const topLine = points[0]
    //   const bottomLine = points[points.length - 1];

    //   const width = ctx.canvas.width

    //   const region = new Path2D();
    //   const lines = new Path2D();

      
    //   for(const point of points) {
    //     lines.moveTo(0, point)
    //     lines.lineTo(width, point)
    //   }

    //   region.moveTo(0, topLine)
    //   region.lineTo(width, topLine)
    //   region.lineTo(width, bottomLine)
    //   region.lineTo(0, bottomLine)
    //   region.lineTo(0, topLine)
    //   region.closePath();

    //   ctx.setLineDash(LINE_DASH)
    //   ctx.stroke(lines)
    //   ctx.setLineDash([])
    //   ctx.fillStyle = this.options.backgroundColor;
    //   ctx.fill(region)
    // })
  }
  defaultOptions(): RegionPrimitivePaneViewOptions {
    return RegionPrimitiveOptionsDefault
  }
}