import { Form, InputNumber, Space } from "antd";
import { FormItemSelectFontFamily } from "./commons/FormItemSelectFontFamily";
import DebounceInputNumber from "./commons/DebounceInputNumber";

export default function MainFont() {
  return (
    <Form.Item label="Font style" style={{ marginBottom: 0 }}>
      <Space direction="vertical" size="middle">
        <Space.Compact>
          <FormItemSelectFontFamily name={["general", "fontFamily"]} />
          <Form.Item name={["general", "fontSize"]} style={{ marginBottom: 0 }}>
            <DebounceInputNumber
              min={0}
              className="w-[100px]"
              placeholder="Font size"
              addonAfter="px"
            />
          </Form.Item>
        </Space.Compact>
      </Space>
    </Form.Item>
  );
}
