---
description: 
globs: **/*.test.ts
alwaysApply: false
---
# Testing Best Practices

## 🎯 **Core Principle**
**Test the contract, not the implementation.** Write the minimum tests for maximum confidence.

> **Quality over Quantity:** 1-2 essential tests > 20 edge case tests  
> **Don't hesitate** to write just 1 test if it covers the core behavior  
> **Failing tests are normal** - they often reveal implementation bugs, not test bugs

## ✅ **What TO Test**

### **Public Interface Behaviors**
```typescript
// ✅ Test the contract
it('should convert user input to uppercase', () => {
  expect(formatName('john')).toBe('JOHN')
})
```

### **Critical Side Effects & Business Logic**
- Resource cleanup (connections, memory)
- Required external calls
- Core business rules that break the app if they fail

## ❌ **What NOT to Test**

### **Implementation Details**
```typescript
// ❌ Don't test how it works
it('should call Array.map and then Array.filter', () => {
  const mapSpy = vi.spyOn(Array.prototype, 'map')
  processData([1, 2, 3])
  expect(mapSpy).toHaveBeenCalled()
})
```

### **Framework Features & Over-Specific Mocks**
- TypeScript interface compliance
- Built-in library behaviors
- Exact call counts: `expect(fn).toHaveBeenCalledTimes(3)`

## 📝 **The Decision Framework**
Before writing any test, ask:
1. "If this fails, is something broken for users?"
2. "Can I refactor internals without breaking this?"
3. "Does this verify user-facing behavior?"

**If any answer is "no" → skip the test.**

### **Stop When You Have Confidence**
```typescript
// ❌ Testing every possible scenario (15+ tests)
describe('UserValidator', () => {
  it('should validate email format')
  it('should validate email with dots')
  it('should validate email with plus signs')
  // ... 12 more similar tests
})

// ✅ Test core behavior only (2-3 tests)
describe('UserValidator', () => {
  it('should accept valid email formats')
  it('should reject invalid email formats')
})
```

## 📝 **Clean Test Code Principles**

### **Descriptive Names & Clear Structure**
```typescript
// ❌ Vague
it('should work correctly', () => {})

// ✅ Clear behavior description
it('should reject users under 18 years old', () => {
  // Arrange - Setup (what you need)
  const minorUser = { age: 16 }
  
  // Act - Execute (what you're testing)
  const result = validateUser(minorUser)
  
  // Assert - Verify (what you expect)
  expect(result.isValid).toBe(false)
})
```

### **Meaningful Test Data & Helpers**
```typescript
// ❌ Magic numbers/repeated setup
const user = { age: 25, type: 'A', status: 1 }

// ✅ Clear data + reusable builders
const createUser = (overrides = {}) => ({
  age: 25,
  type: 'premium',
  status: 'active',
  ...overrides
})

const minorUser = createUser({ age: 16 })
```

### **Avoid Repetitive Comments**
```typescript
// ❌ Obvious comments
// Create user object
const user = createUser()
// Call function
const result = process(user)

// ✅ Comments only when adding value
const result = process(user)
expect(result.discount).toBe(10) // 10% standard discount
```

## 🎯 **Mock Strategy**
- **Mock external dependencies** (APIs, DBs, filesystem)
- **Don't mock what you're testing**
- **Mock at boundaries** (service layer)

```typescript
// ✅ Good - Mock externals only
const mockApi = { fetchUser: vi.fn().mockResolvedValue(userData) }

// ❌ Bad - Mock internals
const processSpy = vi.spyOn(service, 'processInternal')
```

## 💡 **Common Anti-Patterns**

### **Testing Implementation vs Outcome**
```typescript
// ❌ Tests algorithm choice
it('should use bubble sort', () => {
  const spy = vi.spyOn(utils, 'bubbleSort')
  sortArray([3, 1, 2])
  expect(spy).toHaveBeenCalled()
})

// ✅ Tests outcome
it('should sort array ascending', () => {
  expect(sortArray([3, 1, 2])).toEqual([1, 2, 3])
})
```

## 🏆 **Quality Indicators**

### ✅ **Good Tests**
- **Fast** (milliseconds) & **Focused** (one behavior)
- **Stable** (survive refactoring) & **Clear** (behavior in name)
- **Minimal** (1-3 tests per function is often enough)

### ❌ **Bad Tests**
- Break during internal refactoring
- Test multiple behaviors in one test
- Full of implementation-specific assertions

## 🔄 **TDD Mindset**
1. **Write failing test** for behavior (failing is expected!)
2. **Minimal code** to pass
3. **Refactor** (tests still pass)
4. **Stop** when confident

## 📊 **Metrics That Actually Matter**
- ✅ **Behavior coverage** - Core behaviors tested?
- ✅ **Test stability** - Survive refactoring?
- ✅ **Test speed** - Run fast?
- ❌ **Line coverage** - Misleading metric
- ❌ **Test count** - More ≠ better

## 🚀 **Key Benefits**
- **Faster development** - Less test maintenance
- **Confident refactoring** - Tests won't break during improvements
- **Clear documentation** - Tests show actual behaviors
- **Reliable CI/CD** - Fewer false positives

---

**Remember:** If you can refactor internals without breaking tests, you're testing the right things.  
**Be comfortable with minimal tests** - 1 good test > 10 brittle ones.

