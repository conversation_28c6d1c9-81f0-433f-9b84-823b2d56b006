
import React from 'react';
import clsx from 'clsx';

interface ButtonProps extends Omit<React.ButtonHTMLAttributes<HTMLButtonElement>, 'type'> {
  type?: 'primary' | 'secondary' | 'default';
  htmlType?: 'button' | 'submit' | 'reset';
}

const Button: React.FC<ButtonProps> = ({
  type = 'default',
  className,
  htmlType = 'button',
  children,
  ...rest
}) => {
  const buttonClass = clsx('button', className, {
    [`button--${type}`]: type,
  });

  return (
    <button className={buttonClass} type={htmlType} {...rest}>
      {children}
    </button>
  );
};

export default Button;
