import { FC } from 'react';
import { Legend } from './Legend';
import { MassIndexIndicator } from "@sharegraph-mini/advance-charts";
import { AdvanceChart } from "@sharegraph-mini/advance-charts";

const MassIndexLegend: FC<{
  indicator: MassIndexIndicator;
  advanceChart: AdvanceChart;
}> = ({ indicator, advanceChart }) => {
  return (
    <Legend
      name="Mass Index"
      indicator={indicator}
      renderer={(d) =>
        d && d.value ? (
          <span style={{ color: indicator.options.color }}>
            {advanceChart.numberFormatter.decimal(d.value[0])}
          </span>
        ) : null
      }
    />
  );
};

export default MassIndexLegend;
