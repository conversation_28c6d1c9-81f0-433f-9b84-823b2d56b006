import { FC } from 'react';
import { Legend } from './Legend';
import { AdvanceChart } from "@sharegraph-mini/advance-charts";
import { MACDIndicator } from "@sharegraph-mini/advance-charts";

const MACDLegend: FC<{
  indicator: MACDIndicator;
  advanceChart: AdvanceChart;
}> = ({ indicator, advanceChart }) => {
  return (
    <Legend
      name="MACD"
      indicator={indicator}
      renderer={(d) => d && d.value ? (
        <>
          <span
            style={{
              color:
                (d.value[2] ?? 0) > 0
                  ? indicator.options.upColor
                  : indicator.options.downColor,
            }}
          >
            {advanceChart.numberFormatter.decimal(d.value[2])}
          </span>{' '}
          <span style={{ color: indicator.options.macdLineColor }}>
            {advanceChart.numberFormatter.decimal(d.value[0])}
          </span>{' '}
          <span
            style={{
              color: indicator.options.signalLineColor,
            }}
          >
            {advanceChart.numberFormatter.decimal(d.value[1])}
          </span>
        </>
      ) : null}
    />
  );
};

export default MACDLegend;
