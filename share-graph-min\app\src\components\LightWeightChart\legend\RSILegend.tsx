import { FC } from 'react';
import { Legend } from './Legend';
import { RSIIndicator } from "@sharegraph-mini/advance-charts";
import { AdvanceChart } from "@sharegraph-mini/advance-charts";

const RSILegend: FC<{
  indicator: RSIIndicator;
  advanceChart: AdvanceChart;
}> = ({ indicator, advanceChart }) => {
  return (
    <Legend
      name="RSI"
      indicator={indicator}
      renderer={(d) =>
        d && d.value ? (
          <span style={{ color: indicator.options.color }}>
            {advanceChart.numberFormatter.decimal(d.value[0])}
          </span>
        ) : null
      }
    />
  );
};

export default RSILegend;
