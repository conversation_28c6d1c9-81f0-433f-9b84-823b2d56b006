import {IChartApi, ISeriesApi, LineSeries, Nominal, SeriesType, SingleValueData, Time} from "lightweight-charts";
import {ChartIndicator, ChartIndicatorOptions} from "./abstract-indicator";
import {RegionPrimitive} from "../custom-primitive/primitive/region";
import {autoScaleInfoProviderCreator} from "../helpers/utils";
import {Context} from "../helpers/execution-indicator";

export interface DMIIndicatorOptions extends ChartIndicatorOptions {
  adxColor: string,
  plusDIColor: string,
  minusDIColor: string,
  period: number,
  priceLineColor: string,
  backgroundColor: string
}

export const defaultOptions: DMIIndicatorOptions = {
  adxColor: "#f23645",     // Orange for ADX
  plusDIColor: "#2962ff",   // Green for +DI
  minusDIColor: "#ff6d00",  // Red for -DI
  priceLineColor: "rgba(150, 150, 150, 0.35)",
  backgroundColor: '#ff98001a',
  period: 14,
  overlay: false
}

export type ADXLine = Nominal<number, 'ADX'>
export type PlusDILine = Nominal<number, 'PlusDI'>
export type MinusDILine = Nominal<number, 'MinusDI'>

export type DMIData = [ADXLine, PlusDILine, MinusDILine]

export default class DMIIndicator extends ChartIndicator<DMIIndicatorOptions, DMIData> {
  adxSeries: ISeriesApi<SeriesType>
  plusDISeries: ISeriesApi<SeriesType>
  minusDISeries: ISeriesApi<SeriesType>

  constructor(chart: IChartApi, options?: Partial<DMIIndicatorOptions>, paneIndex?: number) {
    super(chart, options)
    
    this.adxSeries = chart.addSeries(LineSeries, {
      color: this.options.adxColor,
      lineWidth: 2,
      priceLineVisible: false,
      crosshairMarkerVisible: false,
      priceScaleId: 'dmi',
      autoscaleInfoProvider: autoScaleInfoProviderCreator({maxValue: 100, minValue: 0})
    }, paneIndex);
    
    this.plusDISeries = chart.addSeries(LineSeries, {
      color: this.options.plusDIColor,
      lineWidth: 1,
      priceLineVisible: false,
      crosshairMarkerVisible: false,
      priceScaleId: 'dmi',
      autoscaleInfoProvider: autoScaleInfoProviderCreator({maxValue: 100, minValue: 0})
    }, paneIndex);
    
    this.minusDISeries = chart.addSeries(LineSeries, {
      color: this.options.minusDIColor,
      lineWidth: 1,
      priceLineVisible: false,
      crosshairMarkerVisible: false,
      priceScaleId: 'dmi',
      autoscaleInfoProvider: autoScaleInfoProviderCreator({maxValue: 100, minValue: 0})
    }, paneIndex);
    
    this.adxSeries.attachPrimitive(
      new RegionPrimitive({
        upPrice: 25,
        lowPrice: 20,
        lineColor: this.options.priceLineColor,
        backgroundColor: this.options.backgroundColor
      })
    );
  }

  getDefaultOptions(): DMIIndicatorOptions {
    return defaultOptions
  }

  formula(c: Context): DMIData | undefined {
    const period = this.options.period;
    const high = c.symbol.high;
    const low = c.symbol.low;
    const close = c.symbol.close;

    const highSeries = c.new_var(high, period + 1);
    const lowSeries = c.new_var(low, period + 1);
    const closeSeries = c.new_var(close, period + 1);

    if (!highSeries.calculable() || !lowSeries.calculable() || !closeSeries.calculable()) {
      return;
    }

    const currentHigh = high;
    const currentLow = low;
    const prevHigh = highSeries.get(1);
    const prevLow = lowSeries.get(1);
    const prevClose = closeSeries.get(1);

    // Step 1: Calculate +DM and -DM (Directional Movement) using TradingView logic
    const upMove = currentHigh - prevHigh;
    const downMove = prevLow - currentLow;

    let plusDM = 0;
    let minusDM = 0;

    if (upMove > downMove && upMove > 0) {
      plusDM = upMove;
    } else if (downMove > upMove && downMove > 0) {
      minusDM = downMove;
    }

    // Step 2: Calculate True Range (TR)
    const tr1 = currentHigh - currentLow;
    const tr2 = Math.abs(currentHigh - prevClose);
    const tr3 = Math.abs(currentLow - prevClose);
    const trueRange = Math.max(tr1, tr2, tr3);

    const wilderAlpha = 1 / period;

    const plusDMSmoothed = c.new_var(0, period + 1);
    const minusDMSmoothed = c.new_var(0, period + 1);
    const trSmoothed = c.new_var(0, period + 1);

    // Get historical data for initialization
    const plusDMHistory = c.new_var(plusDM, period + 1);
    const minusDMHistory = c.new_var(minusDM, period + 1);
    const trHistory = c.new_var(trueRange, period + 1);

    let smoothedPlusDM: number;
    let smoothedMinusDM: number;
    let smoothedTR: number;

    // Check if we have enough data for proper Wilder's smoothing
    if (plusDMHistory.calculable() && minusDMHistory.calculable() && trHistory.calculable()) {
      const prevSmoothedPlusDM = plusDMSmoothed.get(1);
      const prevSmoothedMinusDM = minusDMSmoothed.get(1);
      const prevSmoothedTR = trSmoothed.get(1);

      if (isNaN(prevSmoothedPlusDM) || isNaN(prevSmoothedMinusDM) || isNaN(prevSmoothedTR)) {
        const plusDMValues = plusDMHistory.getAll();
        const minusDMValues = minusDMHistory.getAll();
        const trValues = trHistory.getAll();

        smoothedPlusDM = plusDMValues.reduce((sum, val) => sum + val, 0) / period;
        smoothedMinusDM = minusDMValues.reduce((sum, val) => sum + val, 0) / period;
        smoothedTR = trValues.reduce((sum, val) => sum + val, 0) / period;
      } else {
        // Wilder's smoothing formula: Previous + (Current - Previous) / Period
        // This is equivalent to: (Previous * (Period-1) + Current) / Period
        smoothedPlusDM = prevSmoothedPlusDM + (plusDM - prevSmoothedPlusDM) * wilderAlpha;
        smoothedMinusDM = prevSmoothedMinusDM + (minusDM - prevSmoothedMinusDM) * wilderAlpha;
        smoothedTR = prevSmoothedTR + (trueRange - prevSmoothedTR) * wilderAlpha;
      }
    } else {
      return;
    }

    plusDMSmoothed.set(smoothedPlusDM);
    minusDMSmoothed.set(smoothedMinusDM);
    trSmoothed.set(smoothedTR);

    // Step 3: Calculate +DI and -DI (Directional Indicators)
    const plusDI = smoothedTR !== 0 ? (100 * smoothedPlusDM) / smoothedTR : 0;
    const minusDI = smoothedTR !== 0 ? (100 * smoothedMinusDM) / smoothedTR : 0;

    // Step 4: Calculate DX
    const diSum = plusDI + minusDI;
    const dx = diSum !== 0 ? (100 * Math.abs(plusDI - minusDI)) / diSum : 0;

    // Step 5: Calculate ADX using Wilder's smoothing on DX values
    const dxHistory = c.new_var(dx, period + 1);
    const adxSmoothed = c.new_var(0, period + 1);

    let adx: number;

    if (dxHistory.calculable()) {
      const prevADX = adxSmoothed.get(1);

      if (isNaN(prevADX)) {
        // First ADX calculation: simple average of first 'period' DX values
        const dxValues = dxHistory.getAll();
        adx = dxValues.reduce((sum, val) => sum + val, 0) / period;
      } else {
        // Wilder's smoothing for ADX
        adx = prevADX + (dx - prevADX) * wilderAlpha;
      }
    } else {
      // Not enough DX data yet, return current DX as ADX
      adx = dx;
    }

    adxSmoothed.set(adx);


    return [
      adx as ADXLine,
      plusDI as PlusDILine,
      minusDI as MinusDILine
    ];
  }

  applyIndicatorData() {
    const adxData: SingleValueData[] = [];
    const plusDIData: SingleValueData[] = [];
    const minusDIData: SingleValueData[] = [];
    
    for(const bar of this._executionContext.data) {
      const value = bar.value;
      if(!value) continue;
      
      const time = bar.time as Time;
      adxData.push({time, value: value[0]});
      plusDIData.push({time, value: value[1]});
      minusDIData.push({time, value: value[2]});
    }

    this.adxSeries.setData(adxData);
    this.plusDISeries.setData(plusDIData);
    this.minusDISeries.setData(minusDIData);
  }

  remove() {
    super.remove()
    this.chart.removeSeries(this.adxSeries);
    this.chart.removeSeries(this.plusDISeries);
    this.chart.removeSeries(this.minusDISeries);
  }

  _applyOptions() {
    this.adxSeries.applyOptions({color: this.options.adxColor});
    this.plusDISeries.applyOptions({color: this.options.plusDIColor});
    this.minusDISeries.applyOptions({color: this.options.minusDIColor});
    this.applyIndicatorData();
  }

  setPaneIndex(paneIndex: number) {
    this.adxSeries.moveToPane(paneIndex);
    this.plusDISeries.moveToPane(paneIndex);
    this.minusDISeries.moveToPane(paneIndex);
  }

  getPaneIndex(): number {
    return this.adxSeries.getPane().paneIndex();
  }
}
