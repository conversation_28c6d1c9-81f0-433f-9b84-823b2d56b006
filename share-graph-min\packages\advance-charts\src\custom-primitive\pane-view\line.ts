import {BitmapCoordinatesRenderingScope} from 'fancy-canvas';
import {ensureNotNull} from '../../helpers/assertions';
import {PrimitivePaneViewBase} from '../primitive-base';
import {Coordinate, LineStyle, Time} from 'lightweight-charts';
import {setLineStyle} from '../../helpers/line-style';

interface LinePrimitiveOptions {
  lineColor: string,
  lineWidth: number,
  lineDash: LineStyle
}

export const LinePrimitiveOptionsDefault: LinePrimitiveOptions = {
  lineColor: '#eee',
  lineWidth: 1,
  lineDash: LineStyle.Solid
}

export type LineData = {
  x: Coordinate,
  price: number,
} | {
  time: Time,
  price: number,
}

export class LinePrimitivePaneView extends PrimitivePaneViewBase<LinePrimitiveOptions, LineData> {
  getXCoordinate(data: LineData) {
    return 'time' in data ? this.timeToCoordinate(data.time) : data.x;
  }
  _drawImpl(renderingScope: BitmapCoordinatesRenderingScope): void {
    if(this.data.length === 0) return;
    const ctx = renderingScope.context;
		ctx.scale(renderingScope.horizontalPixelRatio, renderingScope.verticalPixelRatio);
    ctx.lineWidth = this.options.lineWidth
    ctx.strokeStyle = this.options.lineColor
    const lines = new Path2D();
    ctx.beginPath();
    const points = this.data.map((item) => ({
      x: this.getXCoordinate(item),
      y: this.priceToCoordinate(item.price),
    }));
    const [firstPoint, ...restPoints] = points;
    lines.moveTo(ensureNotNull(firstPoint.x), ensureNotNull(firstPoint.y));
    for(const point of restPoints) {
      if(!point.x || !point.y) continue;
      lines.lineTo(point.x, point.y);
    }

    setLineStyle(ctx, this.options.lineDash)
    ctx.stroke(lines)
  }

  defaultOptions() {
    return LinePrimitiveOptionsDefault
  }
}