import { FC } from 'react';
import { Legend } from './Legend';
import { AdvanceChart } from "@sharegraph-mini/advance-charts";
import { DMIIndicator } from "@sharegraph-mini/advance-charts";

const DMILegend: FC<{
  indicator: DMIIndicator;
  advanceChart: AdvanceChart;
}> = ({ indicator, advanceChart }) => {
  return (
    <Legend
      name="DMI"
      indicator={indicator}
      renderer={(d) => d && d.value ? (
        <>
          <span style={{ color: indicator.options.adxColor }}>
            ADX: {advanceChart.numberFormatter.decimal(d.value[0])}
          </span>{' '}
          <span style={{ color: indicator.options.plusDIColor }}>
            +DI: {advanceChart.numberFormatter.decimal(d.value[1])}
          </span>{' '}
          <span style={{ color: indicator.options.minusDIColor }}>
            -DI: {advanceChart.numberFormatter.decimal(d.value[2])}
          </span>
        </>
      ) : null}
    />
  );
};

export default DMILegend;
