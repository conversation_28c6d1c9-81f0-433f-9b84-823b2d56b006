import { StrictMode } from "react";
import { createRoot } from "react-dom/client";
import { Provider } from "react-redux";
import { ConfigProvider } from "antd";

import App from "./App.jsx";
import { store } from "./store/index.js";
import { theme } from "./configs/theme.js";
import { updateTranslation } from "./utils/appConfig.js";

import "./index.scss";

const bootstrapApp = async () => {
  await updateTranslation("en");
  createRoot(document.getElementById("root")).render(
    <Provider store={store}>
      <ConfigProvider theme={theme}>
        <App />
      </ConfigProvider>
    </Provider>
  );
};

bootstrapApp();
