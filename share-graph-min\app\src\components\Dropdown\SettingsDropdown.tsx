import Dropdown from ".";
import { DropdownMenuItem, OnChangeDropdown } from "../../types/common";
import {
  CHART_EVENT_KEYS,
  CHART_SETTING_KEYS,
} from "../../constants/chartConstant";
import { useAppStore } from "../../stores/useAppStore";
import { TChartSetting } from "../../types/store";
import SettingIcon from "../../icons/SettingIcon";
import { i18n } from "@euroland/libs";
import { selectCheckIcon } from "./common";
import { CHART_KEYS } from "../../constants/common";
import { useMemo } from "react";


const SettingsDropdown = () => {
  const chartSettings = useAppStore((state) => state.chartSettings);
  const setChartSettings = useAppStore((state) => state.setChartSettings);
  const setChartEvents = useAppStore((state) => state.setChartEvents);
  const enableEvents = useAppStore((state) => state.enableEvents);


  const menuData: DropdownMenuItem[] = [
    
    {
      id: 'group-checkbox',
      hideGroupLabel: true,
      items: [
        {
          id: CHART_SETTING_KEYS.CHART_PREFERENCES.HIGH_LOW_VALUE.key,
          label: i18n.translate(CHART_SETTING_KEYS.CHART_PREFERENCES.HIGH_LOW_VALUE.label),
          switcher: true,
          parentId: CHART_SETTING_KEYS.CHART_PREFERENCES.key,
          // ...selectRadioIcon,
          // icon: <RadioIcon />,
          // activeIcon: <RadioIcon active />,
        },
        {
          id: CHART_SETTING_KEYS.SHOW_LAST_CLOSE_LINE.key,
          label: i18n.translate(CHART_SETTING_KEYS.SHOW_LAST_CLOSE_LINE.label),
          // ...selectRadioIcon,
          switcher: true,
          parentId: CHART_SETTING_KEYS.SHOW_LAST_CLOSE_LINE.key,
    
          // icon: <RadioIcon />,
          // activeIcon: <RadioIcon active />,
        },
      ],
    },
  
    {
      id: CHART_KEYS.EVENTS,
      label: i18n.translate('events'),
      items: [
        {
          id: CHART_EVENT_KEYS.DIVIDEND.key,
          label: i18n.translate(CHART_EVENT_KEYS.DIVIDEND.label),
          ...selectCheckIcon,
        },
        {
          id: CHART_EVENT_KEYS.EARNING.key,
          label: i18n.translate(CHART_EVENT_KEYS.EARNING.label),
          ...selectCheckIcon,
        },
      ],
    },
  ];

  const eventValue = useMemo(() => {
    const value: string[] = [];
    Object.keys(enableEvents).forEach((key) => {
      if (enableEvents[key]) {
        value.push(key);
      }
    });
    return value;
  }, [enableEvents]);

  const value = {
    ...chartSettings,
    [CHART_KEYS.EVENTS]: eventValue,
  };
  

  const handleChange: OnChangeDropdown = (val, parentId) => {
    if (parentId === CHART_KEYS.EVENTS) {
      setChartEvents(val);
    } else {
      setChartSettings(parentId as TChartSetting, val);
    }
  };

  return (
    <Dropdown
      menuData={menuData}
      onChange={handleChange}
      value={value}
      className="settings-dropdown"
      placeholder={<><SettingIcon className="setting-icon" /> <span>{i18n.translate("settings")}</span></>}
      description={i18n.translate('chart-settings')}
    />
  );
};

export default SettingsDropdown;
