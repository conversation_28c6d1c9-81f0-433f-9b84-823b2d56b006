import { IChartApi, ISeriesApi, Nominal, SeriesType } from 'lightweight-charts';
import { ChartIndicator, ChartIndicatorOptions } from './abstract-indicator';
import { Context } from '../helpers/execution-indicator';

export interface UltimateOscillatorIndicatorOptions extends ChartIndicatorOptions {
    color: string;
    period1: number;
    period2: number;
    period3: number;
    weight1: number;
    weight2: number;
    weight3: number;
    priceLineColor: string;
    backgroundColor: string;
}
export declare const defaultOptions: UltimateOscillatorIndicatorOptions;
export type UltimateOscillatorLine = Nominal<number, 'UltimateOscillator'>;
export type UltimateOscillatorData = [UltimateOscillatorLine];
export default class UltimateOscillatorIndicator extends ChartIndicator<UltimateOscillatorIndicatorOptions, UltimateOscillatorData> {
    ultimateOscillatorSeries: ISeriesApi<SeriesType>;
    constructor(chart: IChartApi, options?: Partial<UltimateOscillatorIndicatorOptions>, paneIndex?: number);
    getDefaultOptions(): UltimateOscillatorIndicatorOptions;
    formula(c: Context): UltimateOscillatorData | undefined;
    applyIndicatorData(): void;
    remove(): void;
    _applyOptions(): void;
    setPaneIndex(paneIndex: number): void;
    getPaneIndex(): number;
}
