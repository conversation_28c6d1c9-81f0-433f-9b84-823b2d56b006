import { cloneDeep } from 'es-toolkit'
import { set } from 'es-toolkit/compat'

import { IDateRangeAndInterval, IDefaultSettingConfigs, TDefaultRange } from '../types/defaultConfig';
import DEFAULT_SETTING, { HAS_BOOLEAN_VALUE_KEYS } from '../configs/defaultSetting';
import { TChartRangePeriod, TTimeIntervals } from '../types/store';
import { DEFAULT_STORE_CHART_SETTINGS, RANGE_CHART_INFO } from '../constants/chartConstant';
import { addErrorToRootElement } from './appConfig';
import { getJsonAppSettingApi } from '../services/getJsonAppSetting';

export type TQueryParams = Record<string, string>

export type ConvertedObject = {
    [key: string]: string[];
};

export const getJsonAppSetting = async () => {
    const params = getQueryParameters(location.search);
    const { companyCode, v } = params;
    const isPreview = 'isPreview' in params;

    if (!companyCode) {
        addErrorToRootElement("Missing companyCode");
        return;
    }

    if (isPreview) {
        return getAppConfiguration();
    }

    try {
        const response = await getJsonAppSettingApi(companyCode, v);
        const appSetting = response.chartSetting;
        appSetting.companyCode = companyCode;

        return appSetting
    } catch {
        return getAppConfiguration();
    }

}

export const getAppConfiguration = (): IDefaultSettingConfigs => {
    const params = getQueryParameters(location.search);


    const mergedSettings = mergeSettings(DEFAULT_SETTING, params);
    return mergedSettings
}

// Get query parameters from URL
export function getQueryParameters(url: string): TQueryParams {
    const searchQuery = new URLSearchParams(url)
    const params = Object.fromEntries(Array.from(searchQuery.entries()).filter(([key]) => key));
    const { companycode, ...rest } = params;

    return {
        ...rest,
        companyCode: companycode || params.companyCode
    }
}

// Convert object to path map
// Only support 2 levels of nested objects
// Example: { a: { b: 1, c: 2 } } -> { b: ['a', 'b'], c: ['a', 'c'] }
export function convertObjectToPathMap(obj: IDefaultSettingConfigs): ConvertedObject {
    const result: ConvertedObject = {};

    for (const key in obj) {
        if (key === 'dateRangesAndInterval') continue;

        const value = obj[key];
        const isPlainObject = typeof value === 'object' && !Array.isArray(value) && value !== null;
        if (!isPlainObject) {
            result[key] = [key];
            continue;
        }

        for (const subKey in value) {
            result[subKey] = [key, subKey];
        }
    }

    return result;
}

const checkValidPeriod = (period: string): boolean => Boolean(RANGE_CHART_INFO[period]);
const checkValidIntervalByPeriod = (interval: string, period: string): boolean => {
    const defaultIntervalsByPeriod = RANGE_CHART_INFO[period].intervals;
    return defaultIntervalsByPeriod.includes(interval);
};

export function parseDateRangesAndInterval(value: string): IDateRangeAndInterval[] {
    return value.split(';').map(periodInterval => {
        const [period, intervalsStr] = periodInterval.split('|');

        // Check valid period
        const isValidPeriod = checkValidPeriod(period);
        // const isValidPeriod = period && RANGE_CHART_INFO[period];
        if (!isValidPeriod) return undefined;

        // Check valid intervals
        const defaultIntervalsByPeriod = RANGE_CHART_INFO[period].intervals
        const queryIntervals = intervalsStr ? intervalsStr.split(',') : [];
        const validIntervals = queryIntervals.filter(interval => defaultIntervalsByPeriod.includes(interval));

        // If intervalsStr is undefined or hasn't any valid interval, use default intervals
        const intervals = validIntervals.length > 0 ? validIntervals : defaultIntervalsByPeriod;
        return { period: period as TChartRangePeriod, intervals: intervals as TTimeIntervals[] };
    }).filter(Boolean) as IDateRangeAndInterval[];
}

export function parseDefaultRange(value: string): TDefaultRange {
    const [period, interval] = value.split(',');
    const defaultRange = { ...DEFAULT_SETTING.defaultRange }

    if (!period || !checkValidPeriod(period)) return defaultRange;
    defaultRange.period = period as TChartRangePeriod;

    if (interval && checkValidIntervalByPeriod(interval, period)) {
        defaultRange.interval = interval as TTimeIntervals;
    }

    return defaultRange;

}

export function getRealtimeIds(value: string, instrumentIds: number[]): number[] | undefined {
    if (!value) return undefined;
    const realtimeIds = value.split(',').map(Number);
    const defaultRealtimeIds = instrumentIds.filter(instrumentId => realtimeIds.includes(instrumentId));
    return defaultRealtimeIds;
}

const getDefaultInstrumentId = (defaultSelectedInstrumentId: number, instrumentIds: number[] = []): number => {
    if (!defaultSelectedInstrumentId || !instrumentIds.includes(defaultSelectedInstrumentId)) return instrumentIds[0];
    return defaultSelectedInstrumentId;
}

export function mergeSettings(defaultSettings: IDefaultSettingConfigs, params: TQueryParams): IDefaultSettingConfigs {
    const mergedSettings: IDefaultSettingConfigs = cloneDeep(defaultSettings);

    const keyPathMap = convertObjectToPathMap(mergedSettings);
    for (const paramKey in params) {
        const paramValue = params[paramKey];
        // handle dateRangesAndInterval
        if (paramKey === 'dateRangesAndInterval') {
            const newDateRangesAndInterval = parseDateRangesAndInterval(paramValue);
            const hasDateRangesAndInterval = newDateRangesAndInterval.length > 0;
            if (!hasDateRangesAndInterval) continue;
            mergedSettings.dateRangesAndInterval = newDateRangesAndInterval;
            continue;
        }

        // handle defaultRange
        if (paramKey === 'defaultRange') {
            const newDefaultDateRange = parseDefaultRange(paramValue);
            mergedSettings.defaultRange = newDefaultDateRange;
            continue;
        }
        if (paramKey === 'instrumentIds') {
            const newInstrumentIds = paramValue.split(',').map(Number);
            mergedSettings.instrumentIds = newInstrumentIds;
            continue;
        }

        // check if paramKey exists in keyPathMap
        const isExistKey = Object.prototype.hasOwnProperty.call(keyPathMap, paramKey);
        if (!isExistKey) continue;
        const path = keyPathMap[paramKey];

        // parse Boolean Value
        if (HAS_BOOLEAN_VALUE_KEYS.includes(paramKey)) {
            const newValue = paramValue === 'true';
            set(mergedSettings, path, newValue);
            continue;
        }

        // chartSetting 
        const isKeyInChartSetting = Object.prototype.hasOwnProperty.call(DEFAULT_STORE_CHART_SETTINGS, paramKey);
        if (isKeyInChartSetting) {
            const newChartSettingValue = paramValue.split(',');

            set(mergedSettings, path, newChartSettingValue);
            continue;
        }

        set(mergedSettings, path, paramValue);
    }
    const { defaultSelectedInstrumentId, instrumentIds } = mergedSettings
    mergedSettings.defaultSelectedInstrumentId = getDefaultInstrumentId(Number(defaultSelectedInstrumentId), instrumentIds);
    const realtimeIds = getRealtimeIds(params.realtimeIds, instrumentIds);
    if (realtimeIds) mergedSettings.realtimeIds = realtimeIds;

    // update chartConfiguration height
    if (mergedSettings?.chartConfiguration?.height) {
        mergedSettings.chartConfiguration.height = Number(mergedSettings.chartConfiguration.height);
    }


    return mergedSettings
}
