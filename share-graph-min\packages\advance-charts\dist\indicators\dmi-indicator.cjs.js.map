{"version": 3, "file": "dmi-indicator.cjs.js", "sources": ["../../src/indicators/dmi-indicator.ts"], "sourcesContent": ["import {IChartApi, ISeriesApi, LineSeries, Nominal, SeriesType, SingleValueData, Time} from \"lightweight-charts\";\nimport {ChartIndicator, ChartIndicatorOptions} from \"./abstract-indicator\";\nimport {RegionPrimitive} from \"../custom-primitive/primitive/region\";\nimport {autoScaleInfoProviderCreator} from \"../helpers/utils\";\nimport {Context} from \"../helpers/execution-indicator\";\n\nexport interface DMIIndicatorOptions extends ChartIndicatorOptions {\n  adxColor: string,\n  plusDIColor: string,\n  minusDIColor: string,\n  period: number,\n  priceLineColor: string,\n  backgroundColor: string\n}\n\nexport const defaultOptions: DMIIndicatorOptions = {\n  adxColor: \"#f23645\",     // Orange for ADX\n  plusDIColor: \"#2962ff\",   // Green for +DI\n  minusDIColor: \"#ff6d00\",  // Red for -DI\n  priceLineColor: \"rgba(150, 150, 150, 0.35)\",\n  backgroundColor: '#ff98001a',\n  period: 14,\n  overlay: false\n}\n\nexport type ADXLine = Nominal<number, 'ADX'>\nexport type PlusDILine = Nominal<number, 'PlusDI'>\nexport type MinusDILine = Nominal<number, 'MinusDI'>\n\nexport type DMIData = [ADXLine, PlusDILine, MinusDILine]\n\nexport default class DMIIndicator extends ChartIndicator<DMIIndicatorOptions, DMIData> {\n  adxSeries: ISeriesApi<SeriesType>\n  plusDISeries: ISeriesApi<SeriesType>\n  minusDISeries: ISeriesApi<SeriesType>\n\n  constructor(chart: IChartApi, options?: Partial<DMIIndicatorOptions>, paneIndex?: number) {\n    super(chart, options)\n    \n    this.adxSeries = chart.addSeries(LineSeries, {\n      color: this.options.adxColor,\n      lineWidth: 2,\n      priceLineVisible: false,\n      crosshairMarkerVisible: false,\n      priceScaleId: 'dmi',\n      autoscaleInfoProvider: autoScaleInfoProviderCreator({maxValue: 100, minValue: 0})\n    }, paneIndex);\n    \n    this.plusDISeries = chart.addSeries(LineSeries, {\n      color: this.options.plusDIColor,\n      lineWidth: 1,\n      priceLineVisible: false,\n      crosshairMarkerVisible: false,\n      priceScaleId: 'dmi',\n      autoscaleInfoProvider: autoScaleInfoProviderCreator({maxValue: 100, minValue: 0})\n    }, paneIndex);\n    \n    this.minusDISeries = chart.addSeries(LineSeries, {\n      color: this.options.minusDIColor,\n      lineWidth: 1,\n      priceLineVisible: false,\n      crosshairMarkerVisible: false,\n      priceScaleId: 'dmi',\n      autoscaleInfoProvider: autoScaleInfoProviderCreator({maxValue: 100, minValue: 0})\n    }, paneIndex);\n    \n    this.adxSeries.attachPrimitive(\n      new RegionPrimitive({\n        upPrice: 25,\n        lowPrice: 20,\n        lineColor: this.options.priceLineColor,\n        backgroundColor: this.options.backgroundColor\n      })\n    );\n  }\n\n  getDefaultOptions(): DMIIndicatorOptions {\n    return defaultOptions\n  }\n\n  formula(c: Context): DMIData | undefined {\n    const period = this.options.period;\n    const high = c.symbol.high;\n    const low = c.symbol.low;\n    const close = c.symbol.close;\n\n    const highSeries = c.new_var(high, period + 1);\n    const lowSeries = c.new_var(low, period + 1);\n    const closeSeries = c.new_var(close, period + 1);\n\n    if (!highSeries.calculable() || !lowSeries.calculable() || !closeSeries.calculable()) {\n      return;\n    }\n\n    const currentHigh = high;\n    const currentLow = low;\n    const prevHigh = highSeries.get(1);\n    const prevLow = lowSeries.get(1);\n    const prevClose = closeSeries.get(1);\n\n    // Step 1: Calculate +DM and -DM (Directional Movement) using TradingView logic\n    const upMove = currentHigh - prevHigh;\n    const downMove = prevLow - currentLow;\n\n    let plusDM = 0;\n    let minusDM = 0;\n\n    if (upMove > downMove && upMove > 0) {\n      plusDM = upMove;\n    } else if (downMove > upMove && downMove > 0) {\n      minusDM = downMove;\n    }\n\n    // Step 2: Calculate True Range (TR)\n    const tr1 = currentHigh - currentLow;\n    const tr2 = Math.abs(currentHigh - prevClose);\n    const tr3 = Math.abs(currentLow - prevClose);\n    const trueRange = Math.max(tr1, tr2, tr3);\n\n    const wilderAlpha = 1 / period;\n\n    const plusDMSmoothed = c.new_var(0, period + 1);\n    const minusDMSmoothed = c.new_var(0, period + 1);\n    const trSmoothed = c.new_var(0, period + 1);\n\n    // Get historical data for initialization\n    const plusDMHistory = c.new_var(plusDM, period + 1);\n    const minusDMHistory = c.new_var(minusDM, period + 1);\n    const trHistory = c.new_var(trueRange, period + 1);\n\n    let smoothedPlusDM: number;\n    let smoothedMinusDM: number;\n    let smoothedTR: number;\n\n    // Check if we have enough data for proper Wilder's smoothing\n    if (plusDMHistory.calculable() && minusDMHistory.calculable() && trHistory.calculable()) {\n      const prevSmoothedPlusDM = plusDMSmoothed.get(1);\n      const prevSmoothedMinusDM = minusDMSmoothed.get(1);\n      const prevSmoothedTR = trSmoothed.get(1);\n\n      if (isNaN(prevSmoothedPlusDM) || isNaN(prevSmoothedMinusDM) || isNaN(prevSmoothedTR)) {\n        const plusDMValues = plusDMHistory.getAll();\n        const minusDMValues = minusDMHistory.getAll();\n        const trValues = trHistory.getAll();\n\n        smoothedPlusDM = plusDMValues.reduce((sum, val) => sum + val, 0) / period;\n        smoothedMinusDM = minusDMValues.reduce((sum, val) => sum + val, 0) / period;\n        smoothedTR = trValues.reduce((sum, val) => sum + val, 0) / period;\n      } else {\n        // Wilder's smoothing formula: Previous + (Current - Previous) / Period\n        // This is equivalent to: (Previous * (Period-1) + Current) / Period\n        smoothedPlusDM = prevSmoothedPlusDM + (plusDM - prevSmoothedPlusDM) * wilderAlpha;\n        smoothedMinusDM = prevSmoothedMinusDM + (minusDM - prevSmoothedMinusDM) * wilderAlpha;\n        smoothedTR = prevSmoothedTR + (trueRange - prevSmoothedTR) * wilderAlpha;\n      }\n    } else {\n      return;\n    }\n\n    plusDMSmoothed.set(smoothedPlusDM);\n    minusDMSmoothed.set(smoothedMinusDM);\n    trSmoothed.set(smoothedTR);\n\n    // Step 3: Calculate +DI and -DI (Directional Indicators)\n    const plusDI = smoothedTR !== 0 ? (100 * smoothedPlusDM) / smoothedTR : 0;\n    const minusDI = smoothedTR !== 0 ? (100 * smoothedMinusDM) / smoothedTR : 0;\n\n    // Step 4: Calculate DX\n    const diSum = plusDI + minusDI;\n    const dx = diSum !== 0 ? (100 * Math.abs(plusDI - minusDI)) / diSum : 0;\n\n    // Step 5: Calculate ADX using Wilder's smoothing on DX values\n    const dxHistory = c.new_var(dx, period + 1);\n    const adxSmoothed = c.new_var(0, period + 1);\n\n    let adx: number;\n\n    if (dxHistory.calculable()) {\n      const prevADX = adxSmoothed.get(1);\n\n      if (isNaN(prevADX)) {\n        // First ADX calculation: simple average of first 'period' DX values\n        const dxValues = dxHistory.getAll();\n        adx = dxValues.reduce((sum, val) => sum + val, 0) / period;\n      } else {\n        // Wilder's smoothing for ADX\n        adx = prevADX + (dx - prevADX) * wilderAlpha;\n      }\n    } else {\n      // Not enough DX data yet, return current DX as ADX\n      adx = dx;\n    }\n\n    adxSmoothed.set(adx);\n\n\n    return [\n      adx as ADXLine,\n      plusDI as PlusDILine,\n      minusDI as MinusDILine\n    ];\n  }\n\n  applyIndicatorData() {\n    const adxData: SingleValueData[] = [];\n    const plusDIData: SingleValueData[] = [];\n    const minusDIData: SingleValueData[] = [];\n    \n    for(const bar of this._executionContext.data) {\n      const value = bar.value;\n      if(!value) continue;\n      \n      const time = bar.time as Time;\n      adxData.push({time, value: value[0]});\n      plusDIData.push({time, value: value[1]});\n      minusDIData.push({time, value: value[2]});\n    }\n\n    this.adxSeries.setData(adxData);\n    this.plusDISeries.setData(plusDIData);\n    this.minusDISeries.setData(minusDIData);\n  }\n\n  remove() {\n    super.remove()\n    this.chart.removeSeries(this.adxSeries);\n    this.chart.removeSeries(this.plusDISeries);\n    this.chart.removeSeries(this.minusDISeries);\n  }\n\n  _applyOptions() {\n    this.adxSeries.applyOptions({color: this.options.adxColor});\n    this.plusDISeries.applyOptions({color: this.options.plusDIColor});\n    this.minusDISeries.applyOptions({color: this.options.minusDIColor});\n    this.applyIndicatorData();\n  }\n\n  setPaneIndex(paneIndex: number) {\n    this.adxSeries.moveToPane(paneIndex);\n    this.plusDISeries.moveToPane(paneIndex);\n    this.minusDISeries.moveToPane(paneIndex);\n  }\n\n  getPaneIndex(): number {\n    return this.adxSeries.getPane().paneIndex();\n  }\n}\n"], "names": ["defaultOptions", "DMIIndicator", "ChartIndicator", "chart", "options", "paneIndex", "__publicField", "LineSeries", "autoScaleInfoProviderCreator", "RegionPrimitive", "c", "period", "high", "low", "close", "highSeries", "lowSeries", "closeSeries", "currentHigh", "currentLow", "prevHigh", "prevLow", "prevClose", "upMove", "downMove", "plusDM", "minusDM", "tr1", "tr2", "tr3", "trueRang<PERSON>", "wilderAlpha", "plusDMSmoothed", "minusDMSmoothed", "trSmoothed", "plusDMHistory", "minusDMHistory", "trHistory", "smoothedPlusDM", "smoothedMinusDM", "smoothedTR", "prevSmoothedPlusDM", "prevSmoothedMinusDM", "prevSmoothedTR", "plusDMValues", "minusDMValues", "tr<PERSON><PERSON><PERSON>", "sum", "val", "plusDI", "minusDI", "diSum", "dx", "dxHistory", "adxSmoothed", "adx", "prevADX", "adxData", "plusDIData", "minusDIData", "bar", "value", "time"], "mappings": "+bAeaA,EAAsC,CACjD,SAAU,UACV,YAAa,UACb,aAAc,UACd,eAAgB,4BAChB,gBAAiB,YACjB,OAAQ,GACR,QAAS,EACX,EAQA,MAAqBC,WAAqBC,GAAAA,cAA6C,CAKrF,YAAYC,EAAkBC,EAAwCC,EAAoB,CACxF,MAAMF,EAAOC,CAAO,EALtBE,EAAA,kBACAA,EAAA,qBACAA,EAAA,sBAKO,KAAA,UAAYH,EAAM,UAAUI,EAAAA,WAAY,CAC3C,MAAO,KAAK,QAAQ,SACpB,UAAW,EACX,iBAAkB,GAClB,uBAAwB,GACxB,aAAc,MACd,sBAAuBC,EAA6B,6BAAA,CAAC,SAAU,IAAK,SAAU,CAAE,CAAA,GAC/EH,CAAS,EAEP,KAAA,aAAeF,EAAM,UAAUI,EAAAA,WAAY,CAC9C,MAAO,KAAK,QAAQ,YACpB,UAAW,EACX,iBAAkB,GAClB,uBAAwB,GACxB,aAAc,MACd,sBAAuBC,EAA6B,6BAAA,CAAC,SAAU,IAAK,SAAU,CAAE,CAAA,GAC/EH,CAAS,EAEP,KAAA,cAAgBF,EAAM,UAAUI,EAAAA,WAAY,CAC/C,MAAO,KAAK,QAAQ,aACpB,UAAW,EACX,iBAAkB,GAClB,uBAAwB,GACxB,aAAc,MACd,sBAAuBC,EAA6B,6BAAA,CAAC,SAAU,IAAK,SAAU,CAAE,CAAA,GAC/EH,CAAS,EAEZ,KAAK,UAAU,gBACb,IAAII,mBAAgB,CAClB,QAAS,GACT,SAAU,GACV,UAAW,KAAK,QAAQ,eACxB,gBAAiB,KAAK,QAAQ,eAC/B,CAAA,CACH,CAAA,CAGF,mBAAyC,CAChC,OAAAT,CAAA,CAGT,QAAQU,EAAiC,CACjC,MAAAC,EAAS,KAAK,QAAQ,OACtBC,EAAOF,EAAE,OAAO,KAChBG,EAAMH,EAAE,OAAO,IACfI,EAAQJ,EAAE,OAAO,MAEjBK,EAAaL,EAAE,QAAQE,EAAMD,EAAS,CAAC,EACvCK,EAAYN,EAAE,QAAQG,EAAKF,EAAS,CAAC,EACrCM,EAAcP,EAAE,QAAQI,EAAOH,EAAS,CAAC,EAE3C,GAAA,CAACI,EAAW,WAAA,GAAgB,CAACC,EAAU,cAAgB,CAACC,EAAY,aACtE,OAGF,MAAMC,EAAcN,EACdO,EAAaN,EACbO,EAAWL,EAAW,IAAI,CAAC,EAC3BM,EAAUL,EAAU,IAAI,CAAC,EACzBM,EAAYL,EAAY,IAAI,CAAC,EAG7BM,EAASL,EAAcE,EACvBI,EAAWH,EAAUF,EAE3B,IAAIM,EAAS,EACTC,EAAU,EAEVH,EAASC,GAAYD,EAAS,EACvBE,EAAAF,EACAC,EAAWD,GAAUC,EAAW,IAC/BE,EAAAF,GAIZ,MAAMG,EAAMT,EAAcC,EACpBS,EAAM,KAAK,IAAIV,EAAcI,CAAS,EACtCO,EAAM,KAAK,IAAIV,EAAaG,CAAS,EACrCQ,EAAY,KAAK,IAAIH,EAAKC,EAAKC,CAAG,EAElCE,EAAc,EAAIpB,EAElBqB,EAAiBtB,EAAE,QAAQ,EAAGC,EAAS,CAAC,EACxCsB,EAAkBvB,EAAE,QAAQ,EAAGC,EAAS,CAAC,EACzCuB,EAAaxB,EAAE,QAAQ,EAAGC,EAAS,CAAC,EAGpCwB,EAAgBzB,EAAE,QAAQe,EAAQd,EAAS,CAAC,EAC5CyB,EAAiB1B,EAAE,QAAQgB,EAASf,EAAS,CAAC,EAC9C0B,EAAY3B,EAAE,QAAQoB,EAAWnB,EAAS,CAAC,EAE7C,IAAA2B,EACAC,EACAC,EAGA,GAAAL,EAAc,cAAgBC,EAAe,cAAgBC,EAAU,aAAc,CACjF,MAAAI,EAAqBT,EAAe,IAAI,CAAC,EACzCU,EAAsBT,EAAgB,IAAI,CAAC,EAC3CU,EAAiBT,EAAW,IAAI,CAAC,EAEnC,GAAA,MAAMO,CAAkB,GAAK,MAAMC,CAAmB,GAAK,MAAMC,CAAc,EAAG,CAC9E,MAAAC,EAAeT,EAAc,OAAO,EACpCU,EAAgBT,EAAe,OAAO,EACtCU,EAAWT,EAAU,OAAO,EAEjBC,EAAAM,EAAa,OAAO,CAACG,EAAKC,IAAQD,EAAMC,EAAK,CAAC,EAAIrC,EACjD4B,EAAAM,EAAc,OAAO,CAACE,EAAKC,IAAQD,EAAMC,EAAK,CAAC,EAAIrC,EACxD6B,EAAAM,EAAS,OAAO,CAACC,EAAKC,IAAQD,EAAMC,EAAK,CAAC,EAAIrC,CAAA,MAI1C2B,EAAAG,GAAsBhB,EAASgB,GAAsBV,EACpDQ,EAAAG,GAAuBhB,EAAUgB,GAAuBX,EAC7DS,EAAAG,GAAkBb,EAAYa,GAAkBZ,CAC/D,KAEA,QAGFC,EAAe,IAAIM,CAAc,EACjCL,EAAgB,IAAIM,CAAe,EACnCL,EAAW,IAAIM,CAAU,EAGzB,MAAMS,EAAST,IAAe,EAAK,IAAMF,EAAkBE,EAAa,EAClEU,EAAUV,IAAe,EAAK,IAAMD,EAAmBC,EAAa,EAGpEW,EAAQF,EAASC,EACjBE,EAAKD,IAAU,EAAK,IAAM,KAAK,IAAIF,EAASC,CAAO,EAAKC,EAAQ,EAGhEE,EAAY3C,EAAE,QAAQ0C,EAAIzC,EAAS,CAAC,EACpC2C,EAAc5C,EAAE,QAAQ,EAAGC,EAAS,CAAC,EAEvC,IAAA4C,EAEA,GAAAF,EAAU,aAAc,CACpB,MAAAG,EAAUF,EAAY,IAAI,CAAC,EAE7B,MAAME,CAAO,EAGTD,EADWF,EAAU,OAAO,EACnB,OAAO,CAACN,EAAKC,IAAQD,EAAMC,EAAK,CAAC,EAAIrC,EAG9C4C,EAAAC,GAAWJ,EAAKI,GAAWzB,CACnC,MAGMwB,EAAAH,EAGR,OAAAE,EAAY,IAAIC,CAAG,EAGZ,CACLA,EACAN,EACAC,CACF,CAAA,CAGF,oBAAqB,CACnB,MAAMO,EAA6B,CAAC,EAC9BC,EAAgC,CAAC,EACjCC,EAAiC,CAAC,EAE9B,UAAAC,KAAO,KAAK,kBAAkB,KAAM,CAC5C,MAAMC,EAAQD,EAAI,MAClB,GAAG,CAACC,EAAO,SAEX,MAAMC,EAAOF,EAAI,KACjBH,EAAQ,KAAK,CAAC,KAAAK,EAAM,MAAOD,EAAM,CAAC,EAAE,EACpCH,EAAW,KAAK,CAAC,KAAAI,EAAM,MAAOD,EAAM,CAAC,EAAE,EACvCF,EAAY,KAAK,CAAC,KAAAG,EAAM,MAAOD,EAAM,CAAC,EAAE,CAAA,CAGrC,KAAA,UAAU,QAAQJ,CAAO,EACzB,KAAA,aAAa,QAAQC,CAAU,EAC/B,KAAA,cAAc,QAAQC,CAAW,CAAA,CAGxC,QAAS,CACP,MAAM,OAAO,EACR,KAAA,MAAM,aAAa,KAAK,SAAS,EACjC,KAAA,MAAM,aAAa,KAAK,YAAY,EACpC,KAAA,MAAM,aAAa,KAAK,aAAa,CAAA,CAG5C,eAAgB,CACd,KAAK,UAAU,aAAa,CAAC,MAAO,KAAK,QAAQ,SAAS,EAC1D,KAAK,aAAa,aAAa,CAAC,MAAO,KAAK,QAAQ,YAAY,EAChE,KAAK,cAAc,aAAa,CAAC,MAAO,KAAK,QAAQ,aAAa,EAClE,KAAK,mBAAmB,CAAA,CAG1B,aAAatD,EAAmB,CACzB,KAAA,UAAU,WAAWA,CAAS,EAC9B,KAAA,aAAa,WAAWA,CAAS,EACjC,KAAA,cAAc,WAAWA,CAAS,CAAA,CAGzC,cAAuB,CACrB,OAAO,KAAK,UAAU,QAAQ,EAAE,UAAU,CAAA,CAE9C"}