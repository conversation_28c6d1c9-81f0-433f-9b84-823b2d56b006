{"version": 3, "file": "chaikins-volatility-indicator.es.js", "sources": ["../../src/indicators/chaikins-volatility-indicator.ts"], "sourcesContent": ["import { IChartApi, ISeriesApi, LineSeries, Nominal, SeriesType, SingleValueData, Time } from \"lightweight-charts\";\nimport { ChartIndicator, ChartIndicatorOptions } from \"./abstract-indicator\";\nimport { RegionPrimitive } from \"../custom-primitive/primitive/region\";\nimport { autoScaleInfoProviderCreator } from \"../helpers/utils\";\nimport { Context } from \"../helpers/execution-indicator\";\n\nexport interface ChaikinsVolatilityIndicatorOptions extends ChartIndicatorOptions {\n  color: string;\n  period: number;        // EMA period for High-Low spread (typically 10)\n  rocPeriod: number;     // Rate of Change lookback period (typically 10)\n  priceLineColor: string;\n  backgroundColor: string;\n}\n\nexport const defaultOptions: ChaikinsVolatilityIndicatorOptions = {\n  color: \"rgba(255, 152, 0, 1)\",     // Orange for Chaikin's Volatility\n  priceLineColor: \"rgba(150, 150, 150, 0.35)\",\n  backgroundColor: '#ff98001a',\n  period: 10,        // Default EMA period for High-Low spread\n  rocPeriod: 12,     // Default Rate of Change lookback period\n  overlay: false\n}\n\nexport type ChaikinsVolatilityLine = Nominal<number, 'ChaikinsVolatility'>\n\nexport type ChaikinsVolatilityData = readonly [ChaikinsVolatilityLine]\n\nexport default class ChaikinsVolatilityIndicator extends ChartIndicator<ChaikinsVolatilityIndicatorOptions, ChaikinsVolatilityData> {\n  chaikinsVolatilitySeries: ISeriesApi<SeriesType>\n\n  constructor(chart: IChartApi, options?: Partial<ChaikinsVolatilityIndicatorOptions>, paneIndex?: number) {\n    super(chart, options)\n\n    this.chaikinsVolatilitySeries = chart.addSeries(LineSeries, {\n      color: this.options.color,\n      lineWidth: 2,\n      priceLineVisible: false,\n      crosshairMarkerVisible: false,\n      priceScaleId: 'chaikinsvolatility',\n      autoscaleInfoProvider: autoScaleInfoProviderCreator({ maxValue: 80, minValue: -80 })\n    }, paneIndex);\n\n    this.chaikinsVolatilitySeries.attachPrimitive(\n      new RegionPrimitive({\n        upPrice: 5,\n        lowPrice: -5,\n        lineColor: this.options.priceLineColor,\n        backgroundColor: this.options.backgroundColor\n      })\n    );\n  }\n\n  getDefaultOptions(): ChaikinsVolatilityIndicatorOptions {\n    return defaultOptions\n  }\n\n  formula(c: Context): ChaikinsVolatilityData | undefined {\n    const emaPeriod = this.options.period;      // EMA period for High-Low spread (typically 10)\n    const rocPeriod = this.options.rocPeriod;   // Rate of Change lookback period (typically 10)\n\n    const high = c.symbol.high;\n    const low = c.symbol.low;\n\n    // Step 1: Calculate High-Low spread (not close price!)\n    const highLowSpread = high - low;\n\n    const alpha = 2 / (emaPeriod + 1);\n\n    const hlSeries = c.new_var(highLowSpread, emaPeriod);\n    const emaSeries = c.new_var(NaN, rocPeriod + 1);\n\n    if (!hlSeries.calculable()) return;\n\n    const previousEMA = emaSeries.get(1);\n\n    let currentEMA;\n    if (isNaN(previousEMA)) {\n      const hlValues = hlSeries.getAll();\n      currentEMA = hlValues.reduce((sum, val) => sum + val, 0) / emaPeriod;\n    } else {\n      currentEMA = alpha * highLowSpread + (1 - alpha) * previousEMA;\n    }\n\n    emaSeries.set(currentEMA);\n\n    const pastEMA = emaSeries.get(rocPeriod);\n\n    if (isNaN(pastEMA) || pastEMA === 0) {\n      return [0 as ChaikinsVolatilityLine];\n    }\n\n    // Chaikin Volatility Value = (Current EMA value - EMA value rocPeriod periods ago) / EMA value rocPeriod periods ago * 100\n    const chaikinVolatility = ((currentEMA - pastEMA) / pastEMA) * 100;\n\n    return [chaikinVolatility as ChaikinsVolatilityLine];\n  }\n\n\n  applyIndicatorData() {\n    const chaikinsVolatilityData: SingleValueData[] = [];\n\n    for (const bar of this._executionContext.data) {\n      const value = bar.value;\n      if (!value) continue;\n\n      const time = bar.time as Time;\n      chaikinsVolatilityData.push({ time, value: value[0] });\n    }\n\n    this.chaikinsVolatilitySeries.setData(chaikinsVolatilityData);\n  }\n\n  remove() {\n    super.remove()\n    this.chart.removeSeries(this.chaikinsVolatilitySeries);\n  }\n\n  _applyOptions() {\n    this.chaikinsVolatilitySeries.applyOptions({ color: this.options.color });\n    this.applyIndicatorData();\n  }\n\n  setPaneIndex(paneIndex: number) {\n    this.chaikinsVolatilitySeries.moveToPane(paneIndex);\n  }\n\n  getPaneIndex(): number {\n    return this.chaikinsVolatilitySeries.getPane().paneIndex();\n  }\n}"], "names": ["defaultOptions", "ChaikinsVolatilityIndicator", "ChartIndicator", "chart", "options", "paneIndex", "__publicField", "LineSeries", "autoScaleInfoProviderCreator", "RegionPrimitive", "c", "emaPeriod", "rocPeriod", "high", "low", "highLowSpread", "alpha", "hlSeries", "emaSeries", "previousEMA", "currentEMA", "sum", "val", "pastEMA", "chaikinsVolatilityData", "bar", "value", "time"], "mappings": ";;;;;;;AAcO,MAAMA,IAAqD;AAAA,EAChE,OAAO;AAAA;AAAA,EACP,gBAAgB;AAAA,EAChB,iBAAiB;AAAA,EACjB,QAAQ;AAAA;AAAA,EACR,WAAW;AAAA;AAAA,EACX,SAAS;AACX;AAMA,MAAqBC,UAAoCC,EAA2E;AAAA,EAGlI,YAAYC,GAAkBC,GAAuDC,GAAoB;AACvG,UAAMF,GAAOC,CAAO;AAHtB,IAAAE,EAAA;AAKO,SAAA,2BAA2BH,EAAM,UAAUI,GAAY;AAAA,MAC1D,OAAO,KAAK,QAAQ;AAAA,MACpB,WAAW;AAAA,MACX,kBAAkB;AAAA,MAClB,wBAAwB;AAAA,MACxB,cAAc;AAAA,MACd,uBAAuBC,EAA6B,EAAE,UAAU,IAAI,UAAU,IAAK,CAAA;AAAA,OAClFH,CAAS,GAEZ,KAAK,yBAAyB;AAAA,MAC5B,IAAII,EAAgB;AAAA,QAClB,SAAS;AAAA,QACT,UAAU;AAAA,QACV,WAAW,KAAK,QAAQ;AAAA,QACxB,iBAAiB,KAAK,QAAQ;AAAA,MAC/B,CAAA;AAAA,IACH;AAAA,EAAA;AAAA,EAGF,oBAAwD;AAC/C,WAAAT;AAAA,EAAA;AAAA,EAGT,QAAQU,GAAgD;AAChD,UAAAC,IAAY,KAAK,QAAQ,QACzBC,IAAY,KAAK,QAAQ,WAEzBC,IAAOH,EAAE,OAAO,MAChBI,IAAMJ,EAAE,OAAO,KAGfK,IAAgBF,IAAOC,GAEvBE,IAAQ,KAAKL,IAAY,IAEzBM,IAAWP,EAAE,QAAQK,GAAeJ,CAAS,GAC7CO,IAAYR,EAAE,QAAQ,KAAKE,IAAY,CAAC;AAE1C,QAAA,CAACK,EAAS,aAAc;AAEtB,UAAAE,IAAcD,EAAU,IAAI,CAAC;AAE/B,QAAAE;AACA,IAAA,MAAMD,CAAW,IAENC,IADIH,EAAS,OAAO,EACX,OAAO,CAACI,GAAKC,MAAQD,IAAMC,GAAK,CAAC,IAAIX,IAE9CS,IAAAJ,IAAQD,KAAiB,IAAIC,KAASG,GAGrDD,EAAU,IAAIE,CAAU;AAElB,UAAAG,IAAUL,EAAU,IAAIN,CAAS;AAEvC,WAAI,MAAMW,CAAO,KAAKA,MAAY,IACzB,CAAC,CAA2B,IAM9B,EAFqBH,IAAaG,KAAWA,IAAW,GAEZ;AAAA,EAAA;AAAA,EAIrD,qBAAqB;AACnB,UAAMC,IAA4C,CAAC;AAExC,eAAAC,KAAO,KAAK,kBAAkB,MAAM;AAC7C,YAAMC,IAAQD,EAAI;AAClB,UAAI,CAACC,EAAO;AAEZ,YAAMC,IAAOF,EAAI;AACjB,MAAAD,EAAuB,KAAK,EAAE,MAAAG,GAAM,OAAOD,EAAM,CAAC,GAAG;AAAA,IAAA;AAGlD,SAAA,yBAAyB,QAAQF,CAAsB;AAAA,EAAA;AAAA,EAG9D,SAAS;AACP,UAAM,OAAO,GACR,KAAA,MAAM,aAAa,KAAK,wBAAwB;AAAA,EAAA;AAAA,EAGvD,gBAAgB;AACd,SAAK,yBAAyB,aAAa,EAAE,OAAO,KAAK,QAAQ,OAAO,GACxE,KAAK,mBAAmB;AAAA,EAAA;AAAA,EAG1B,aAAanB,GAAmB;AACzB,SAAA,yBAAyB,WAAWA,CAAS;AAAA,EAAA;AAAA,EAGpD,eAAuB;AACrB,WAAO,KAAK,yBAAyB,QAAQ,EAAE,UAAU;AAAA,EAAA;AAE7D;"}