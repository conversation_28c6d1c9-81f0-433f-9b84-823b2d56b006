import { StrictMode } from 'react';
import { createRoot } from 'react-dom/client';
import './styles/index.scss';
import App from './App.tsx';
import { getJsonAppSetting} from './utils/config.ts';
import {
  getRequiredDataToShowApp,
  updateAppSettingsToStore,
  updateAppStyle,
  updateFormatNumber,
  validateAppAndAddError,
} from './utils/appConfig.ts';
import {useAppStore} from './stores/useAppStore.ts';
import {integrateTool} from './integrateTool.ts';

const bootstrapApp = async () => {
  const appConfig = await getJsonAppSetting();
  if (!appConfig) return;
  const isValid = validateAppAndAddError(appConfig);
  if (!isValid) return;
  
  updateAppSettingsToStore(appConfig);
  updateAppStyle(appConfig);
  updateFormatNumber(appConfig.general.locale);

  const canShowApp = await getRequiredDataToShowApp(appConfig);
  if (!canShowApp) return;
  const {realtimeIds} = useAppStore.getState().appSettings;
  if(realtimeIds.length > 0) {
    import("./realtime/realtimeSetup.ts").then(({default: initRealtime}) => {
      initRealtime(realtimeIds.map(String))
    })
  }

  createRoot(document.getElementById('root')!).render(
    <StrictMode>
      <App />
    </StrictMode>
  );
};

bootstrapApp().then(integrateTool);
