import React from 'react';
import { IDateRangeAndInterval } from '../../types/defaultConfig';
import { IChartRangeStore } from '../../types/store';
import { useAppStore } from '../../stores/useAppStore';
import {
  RANGE_CHART_INFO,
  TIME_INTERVALS,
} from '../../constants/chartConstant';
import { ITimelineOption } from '../../types/common';
import clsx from 'clsx';
import { i18n } from '@euroland/libs';

interface IRangeChartItemProps {
  option: IDateRangeAndInterval;
  isActivePeriod: boolean;
}

const RangeChartItem: React.FC<IRangeChartItemProps> = ({
  option,
  isActivePeriod,
}) => {
  const setChartRange = useAppStore((state) => state.setChartRange);
  const chartRange = useAppStore((state) => state.chartRange);
  const { interval } = chartRange;

  const periodValue = option.period;
  const intervalsFromOption = option.intervals;
  const { defaultSelectedInterval, label } = RANGE_CHART_INFO[
    periodValue
  ] as ITimelineOption;


  const handleChangeChartRange = (updateValue: IChartRangeStore) => {
    setChartRange(updateValue);
  };

  const getIntervalLabel = (translationKey: string, times: number) => {
    const translationLabel = i18n.translate(translationKey);
    switch (translationKey) {
      case 'minute':
      case 'minutes':
      case 'hour':
        return `${times} ${translationLabel}`;
    
      default:
        break;
    }
    return translationLabel;
  }

  return (
    <li className="chart-range__timeline-option">
      <button
        className={`chart-range__timeline-button ${
          isActivePeriod ? 'active' : ''
        }`}
        onClick={() =>
          handleChangeChartRange({
            period: periodValue,
            interval: intervalsFromOption.includes(defaultSelectedInterval) ? defaultSelectedInterval : intervalsFromOption[0],
          })
        }
      >
        {label}
      </button>
      <div className="chart-range__interval-buttons">
        {intervalsFromOption.map((inter) => {
          const intervalOption = TIME_INTERVALS[inter];
          const intervalValue = intervalOption.value;

          return (
            <button
              key={intervalValue}
              className={clsx('chart-range__interval-button', {
                active: intervalValue === interval && isActivePeriod,
              })}
              onClick={() =>
                handleChangeChartRange({
                  period: periodValue,
                  interval: intervalValue,
                })
              }
            >
              {getIntervalLabel(intervalOption.label, intervalOption.interval.times)}
            </button>
          );
        })}
      </div>
    </li>
  );
};

export default React.memo(RangeChartItem);
