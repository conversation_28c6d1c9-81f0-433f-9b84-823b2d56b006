{"version": 3, "file": "index.es.js", "sources": ["../../src/indicators/index.ts"], "sourcesContent": ["import BBIndicator from \"./bb-indicator\";\r\nimport MACDIndicator from \"./macd-indicator\";\r\nimport RSIIndicator from \"./rsi-indicator\";\r\nimport VolumeIndicator from \"./volume-indicator\";\r\nimport {IndicatorFactory} from \"./indicator-factory\";\r\nimport SMAIndicator from \"./sma-indicator\";\r\nimport StochasticIndicator from \"./stochastic-indicator\";\r\nimport EMAIndicator from \"./ema-indicator\";\r\nimport WMAIndicator from \"./wma-indicator\";\r\nimport MomentumIndicator from \"./momentum-indicator\";\r\nimport WilliamsIndicator from \"./williams-indicator\";\r\nimport DMIIndicator from \"./dmi-indicator\";\r\nimport MassIndexIndicator from \"./mass-index-indicator\";\r\nimport UltimateOscillatorIndicator from \"./ultimate-oscillator-indicator\";\r\nimport VROCIndicator from \"./vroc-indicator\";\r\nimport ChaikinsVolatilityIndicator from \"./chaikins-volatility-indicator\";\r\n\r\nIndicatorFactory.registerIndicator('bb', BBIndicator)\r\nIndicatorFactory.registerIndicator('rsi', RSIIndicator)\r\nIndicatorFactory.registerIndicator('macd', MACDIndicator)\r\nIndicatorFactory.registerIndicator('volume_overlay', VolumeIndicator, { overlay: true })\r\nIndicatorFactory.registerIndicator('volume', VolumeIndicator)\r\nIndicatorFactory.registerIndicator('sma', SMAIndicator)\r\nIndicatorFactory.registerIndicator('stochastic', StochasticIndicator)\r\nIndicatorFactory.registerIndicator('ema', EMAIndicator)\r\nIndicatorFactory.registerIndicator('wma', WMAIndicator)\r\nIndicatorFactory.registerIndicator('momentum', MomentumIndicator)\r\nIndicatorFactory.registerIndicator('williams', WilliamsIndicator)\r\nIndicatorFactory.registerIndicator('dmi', DMIIndicator)\r\nIndicatorFactory.registerIndicator('massindex', MassIndexIndicator)\r\nIndicatorFactory.registerIndicator('ultimateoscillator', UltimateOscillatorIndicator)\r\nIndicatorFactory.registerIndicator('vroc', VROCIndicator)\r\nIndicatorFactory.registerIndicator('chaikinsvolatility', ChaikinsVolatilityIndicator)\r\n\r\nexport { IndicatorFactory } from './indicator-factory'\r\nexport {default as BBIndicator} from \"./bb-indicator\";\r\nexport {default as MACDIndicator} from \"./macd-indicator\";\r\nexport {default as RSIIndicator} from \"./rsi-indicator\";\r\nexport {default as VolumeIndicator} from \"./volume-indicator\";\r\nexport {default as SMAIndicator} from \"./sma-indicator\";\r\nexport { default as StochasticIndicator } from \"./stochastic-indicator\";\r\nexport { default as EMAIndicator } from \"./ema-indicator\";\r\nexport { default as WMAIndicator } from \"./wma-indicator\";\r\nexport { default as MomentumIndicator } from \"./momentum-indicator\";\r\nexport { default as WilliamsIndicator } from \"./williams-indicator\";\r\nexport { default as DMIIndicator } from \"./dmi-indicator\";\r\nexport { default as MassIndexIndicator } from \"./mass-index-indicator\";\r\nexport { default as UltimateOscillatorIndicator } from \"./ultimate-oscillator-indicator\";\r\nexport { default as VROCIndicator } from \"./vroc-indicator\";\r\nexport { default as ChaikinsVolatilityIndicator } from \"./chaikins-volatility-indicator\";\r\n"], "names": ["IndicatorFactory", "BBIndicator", "RSIIndicator", "MACDIndicator", "VolumeIndicator", "SMAIndicator", "StochasticIndicator", "EMAIndicator", "WMAIndicator", "MomentumIndicator", "WilliamsIndicator", "DMIIndicator", "MassIndexIndicator", "UltimateOscillatorIndicator", "VROCIndicator", "ChaikinsVolatilityIndicator"], "mappings": ";;;;;;;;;;;;;;;;AAiBAA,EAAiB,kBAAkB,MAAMC,CAAW;AACpDD,EAAiB,kBAAkB,OAAOE,CAAY;AACtDF,EAAiB,kBAAkB,QAAQG,CAAa;AACxDH,EAAiB,kBAAkB,kBAAkBI,GAAiB,EAAE,SAAS,IAAM;AACvFJ,EAAiB,kBAAkB,UAAUI,CAAe;AAC5DJ,EAAiB,kBAAkB,OAAOK,CAAY;AACtDL,EAAiB,kBAAkB,cAAcM,CAAmB;AACpEN,EAAiB,kBAAkB,OAAOO,CAAY;AACtDP,EAAiB,kBAAkB,OAAOQ,CAAY;AACtDR,EAAiB,kBAAkB,YAAYS,CAAiB;AAChET,EAAiB,kBAAkB,YAAYU,CAAiB;AAChEV,EAAiB,kBAAkB,OAAOW,CAAY;AACtDX,EAAiB,kBAAkB,aAAaY,CAAkB;AAClEZ,EAAiB,kBAAkB,sBAAsBa,CAA2B;AACpFb,EAAiB,kBAAkB,QAAQc,CAAa;AACxDd,EAAiB,kBAAkB,sBAAsBe,CAA2B;"}