{"version": 3, "file": "vroc-indicator.es.js", "sources": ["../../src/indicators/vroc-indicator.ts"], "sourcesContent": ["import {IChartApi, ISeriesApi, LineSeries, Nominal, SeriesType, SingleValueData, Time} from \"lightweight-charts\";\nimport {ChartIndicator, ChartIndicatorOptions} from \"./abstract-indicator\";\nimport {RegionPrimitive} from \"../custom-primitive/primitive/region\";\nimport {autoScaleInfoProviderCreator} from \"../helpers/utils\";\nimport {Context} from \"../helpers/execution-indicator\";\n\nexport interface VROCIndicatorOptions extends ChartIndicatorOptions {\n  color: string,\n  period: number,\n  priceLineColor: string,\n  backgroundColor: string\n}\n\nexport const defaultOptions: VROCIndicatorOptions = {\n  color: \"rgba(255, 152, 0, 1)\",     // Orange for VROC\n  priceLineColor: \"rgba(150, 150, 150, 0.35)\",\n  backgroundColor: '#ff98001a',\n  period: 14,      // Default period for VROC\n  overlay: false\n}\n\nexport type VROCLine = Nominal<number, 'VROC'>\n\nexport type VROCData = [VROCLine]\n\nexport default class VROCIndicator extends ChartIndicator<VROCIndicatorOptions, VROCData> {\n  vrocSeries: ISeriesApi<SeriesType>\n\n  constructor(chart: IChartApi, options?: Partial<VROCIndicatorOptions>, paneIndex?: number) {\n    super(chart, options)\n    \n    this.vrocSeries = chart.addSeries(LineSeries, {\n      color: this.options.color,\n      lineWidth: 2,\n      priceLineVisible: false,\n      crosshairMarkerVisible: false,\n      priceScaleId: 'vroc',\n      autoscaleInfoProvider: autoScaleInfoProviderCreator({minValue: -100, maxValue: 100})\n    }, paneIndex);\n    \n    // Add zero line for reference\n    this.vrocSeries.attachPrimitive(\n      new RegionPrimitive({\n        upPrice: 5,\n        lowPrice: -5,\n        lineColor: this.options.priceLineColor,\n        backgroundColor: this.options.backgroundColor\n      })\n    );\n  }\n\n  getDefaultOptions(): VROCIndicatorOptions {\n    return defaultOptions\n  }\n\n  formula(c: Context): VROCData | undefined {\n    const period = this.options.period;\n    const volume = c.symbol.volume;\n\n    // We need enough data for the period calculation plus current volume\n    const volumeSeries = c.new_var(volume, period + 1);\n\n    if (!volumeSeries.calculable()) {\n        return;\n    }\n\n    // Get current volume (index 0) and volume from 'period' periods ago (index period)\n    const currentVolume = volumeSeries.get(0);\n    const pastVolume = volumeSeries.get(period);\n\n    // Calculate Volume Rate of Change\n    // VROC = ((Current Volume - Past Volume) / Past Volume) * 100\n    if (pastVolume === 0) {\n        return [0 as VROCLine]; // Avoid division by zero\n    }\n\n    const vroc = ((currentVolume - pastVolume) / pastVolume) * 100;\n\n    return [vroc as VROCLine];\n}\n\n  applyIndicatorData() {\n    const vrocData: SingleValueData[] = [];\n    \n    for(const bar of this._executionContext.data) {\n      const value = bar.value;\n      if(!value) continue;\n      \n      const time = bar.time as Time;\n      vrocData.push({time, value: value[0]});\n    }\n\n    this.vrocSeries.setData(vrocData);\n  }\n\n  remove() {\n    super.remove()\n    this.chart.removeSeries(this.vrocSeries);\n  }\n\n  _applyOptions() {\n    this.vrocSeries.applyOptions({color: this.options.color});\n    this.applyIndicatorData();\n  }\n\n  setPaneIndex(paneIndex: number) {\n    this.vrocSeries.moveToPane(paneIndex);\n  }\n\n  getPaneIndex(): number {\n    return this.vrocSeries.getPane().paneIndex();\n  }\n}\n"], "names": ["defaultOptions", "VROCIndicator", "ChartIndicator", "chart", "options", "paneIndex", "__publicField", "LineSeries", "autoScaleInfoProviderCreator", "RegionPrimitive", "c", "period", "volume", "volumeSeries", "currentVolume", "pastVolume", "vrocData", "bar", "value", "time"], "mappings": ";;;;;;;AAaO,MAAMA,IAAuC;AAAA,EAClD,OAAO;AAAA;AAAA,EACP,gBAAgB;AAAA,EAChB,iBAAiB;AAAA,EACjB,QAAQ;AAAA;AAAA,EACR,SAAS;AACX;AAMA,MAAqBC,UAAsBC,EAA+C;AAAA,EAGxF,YAAYC,GAAkBC,GAAyCC,GAAoB;AACzF,UAAMF,GAAOC,CAAO;AAHtB,IAAAE,EAAA;AAKO,SAAA,aAAaH,EAAM,UAAUI,GAAY;AAAA,MAC5C,OAAO,KAAK,QAAQ;AAAA,MACpB,WAAW;AAAA,MACX,kBAAkB;AAAA,MAClB,wBAAwB;AAAA,MACxB,cAAc;AAAA,MACd,uBAAuBC,EAA6B,EAAC,UAAU,MAAM,UAAU,IAAI,CAAA;AAAA,OAClFH,CAAS,GAGZ,KAAK,WAAW;AAAA,MACd,IAAII,EAAgB;AAAA,QAClB,SAAS;AAAA,QACT,UAAU;AAAA,QACV,WAAW,KAAK,QAAQ;AAAA,QACxB,iBAAiB,KAAK,QAAQ;AAAA,MAC/B,CAAA;AAAA,IACH;AAAA,EAAA;AAAA,EAGF,oBAA0C;AACjC,WAAAT;AAAA,EAAA;AAAA,EAGT,QAAQU,GAAkC;AAClC,UAAAC,IAAS,KAAK,QAAQ,QACtBC,IAASF,EAAE,OAAO,QAGlBG,IAAeH,EAAE,QAAQE,GAAQD,IAAS,CAAC;AAE7C,QAAA,CAACE,EAAa;AACd;AAIE,UAAAC,IAAgBD,EAAa,IAAI,CAAC,GAClCE,IAAaF,EAAa,IAAIF,CAAM;AAI1C,WAAII,MAAe,IACR,CAAC,CAAa,IAKlB,EAFQD,IAAgBC,KAAcA,IAAc,GAEnC;AAAA,EAAA;AAAA,EAG1B,qBAAqB;AACnB,UAAMC,IAA8B,CAAC;AAE3B,eAAAC,KAAO,KAAK,kBAAkB,MAAM;AAC5C,YAAMC,IAAQD,EAAI;AAClB,UAAG,CAACC,EAAO;AAEX,YAAMC,IAAOF,EAAI;AACjB,MAAAD,EAAS,KAAK,EAAC,MAAAG,GAAM,OAAOD,EAAM,CAAC,GAAE;AAAA,IAAA;AAGlC,SAAA,WAAW,QAAQF,CAAQ;AAAA,EAAA;AAAA,EAGlC,SAAS;AACP,UAAM,OAAO,GACR,KAAA,MAAM,aAAa,KAAK,UAAU;AAAA,EAAA;AAAA,EAGzC,gBAAgB;AACd,SAAK,WAAW,aAAa,EAAC,OAAO,KAAK,QAAQ,OAAM,GACxD,KAAK,mBAAmB;AAAA,EAAA;AAAA,EAG1B,aAAaX,GAAmB;AACzB,SAAA,WAAW,WAAWA,CAAS;AAAA,EAAA;AAAA,EAGtC,eAAuB;AACrB,WAAO,KAAK,WAAW,QAAQ,EAAE,UAAU;AAAA,EAAA;AAE/C;"}