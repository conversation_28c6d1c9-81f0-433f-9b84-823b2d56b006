import { FC } from 'react';
import { Legend } from './Legend';
import { UltimateOscillatorIndicator } from "@sharegraph-mini/advance-charts";
import { AdvanceChart } from "@sharegraph-mini/advance-charts";

const UltimateOscillatorLegend: FC<{
  indicator: UltimateOscillatorIndicator;
  advanceChart: AdvanceChart;
}> = ({ indicator, advanceChart }) => {
  return (
    <Legend
      name="Ultimate Oscillator"
      indicator={indicator}
      renderer={(d) =>
        d && d.value ? (
          <span style={{ color: indicator.options.color }}>
            {advanceChart.numberFormatter.decimal(d.value[0])}
          </span>
        ) : null
      }
    />
  );
};

export default UltimateOscillatorLegend;
