"use strict";Object.defineProperty(exports,Symbol.toStringTag,{value:"Module"});const a=require("./bb-indicator.cjs.js"),i=require("./macd-indicator.cjs.js"),o=require("./rsi-indicator.cjs.js"),r=require("./volume-indicator.cjs.js"),t=require("./indicator-factory.cjs.js"),e=require("./sma-indicator.cjs.js"),c=require("./stochastic-indicator.cjs.js"),d=require("./ema-indicator.cjs.js"),n=require("./wma-indicator.cjs.js"),I=require("./momentum-indicator.cjs.js"),l=require("./williams-indicator.cjs.js"),u=require("./dmi-indicator.cjs.js"),s=require("./mass-index-indicator.cjs.js"),f=require("./ultimate-oscillator-indicator.cjs.js"),m=require("./vroc-indicator.cjs.js"),y=require("./chaikins-volatility-indicator.cjs.js");t.IndicatorFactory.registerIndicator("bb",a.default);t.IndicatorFactory.registerIndicator("rsi",o.default);t.IndicatorFactory.registerIndicator("macd",i.default);t.IndicatorFactory.registerIndicator("volume_overlay",r.default,{overlay:!0});t.IndicatorFactory.registerIndicator("volume",r.default);t.IndicatorFactory.registerIndicator("sma",e.default);t.IndicatorFactory.registerIndicator("stochastic",c.default);t.IndicatorFactory.registerIndicator("ema",d.default);t.IndicatorFactory.registerIndicator("wma",n.default);t.IndicatorFactory.registerIndicator("momentum",I.default);t.IndicatorFactory.registerIndicator("williams",l.default);t.IndicatorFactory.registerIndicator("dmi",u.default);t.IndicatorFactory.registerIndicator("massindex",s.default);t.IndicatorFactory.registerIndicator("ultimateoscillator",f.default);t.IndicatorFactory.registerIndicator("vroc",m.default);t.IndicatorFactory.registerIndicator("chaikinsvolatility",y.default);exports.BBIndicator=a.default;exports.MACDIndicator=i.default;exports.RSIIndicator=o.default;exports.VolumeIndicator=r.default;exports.IndicatorFactory=t.IndicatorFactory;exports.SMAIndicator=e.default;exports.StochasticIndicator=c.default;exports.EMAIndicator=d.default;exports.WMAIndicator=n.default;exports.MomentumIndicator=I.default;exports.WilliamsIndicator=l.default;exports.DMIIndicator=u.default;exports.MassIndexIndicator=s.default;exports.UltimateOscillatorIndicator=f.default;exports.VROCIndicator=m.default;exports.ChaikinsVolatilityIndicator=y.default;
//# sourceMappingURL=index.cjs.js.map
