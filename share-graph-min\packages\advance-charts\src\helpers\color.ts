import {memoize} from "es-toolkit";

/**
 * For colors which fall within the sRGB space, the browser can
 * be used to convert the color string into a rgb /rgba string.
 *
 * For other colors, it will be returned as specified (i.e. for
 * newer formats like display-p3)
 *
 * See: https://www.w3.org/TR/css-color-4/#serializing-sRGB-values
 */
function getRgbStringViaBrowser(color: string): string {
	const element = document.createElement('div');
	element.style.display = 'none';
	// We append to the body as it is the most reliable way to get a color reading
	// appending to the chart container or similar element can result in the following
	// getComputedStyle returning empty strings on each check.
	document.body.appendChild(element);
	element.style.color = color;
	const computed = window.getComputedStyle(element).color;
	document.body.removeChild(element);
	return computed;
}

export const parseColor = memoize((color: string) => {
  const computed = getRgbStringViaBrowser(color);

  const match = computed.match(
    /^rgba?\s*\((\d+),\s*(\d+),\s*(\d+)(?:,\s*(\d*\.?\d+))?\)$/
  );

  if (!match) {
    throw new Error(`Failed to parse color: ${color}`);
  }

  return [
    parseInt(match[1], 10) as number,
    parseInt(match[2], 10) as number,
    parseInt(match[3], 10) as number,
    (match[4] ? parseFloat(match[4]) : 1) as number,
  ];
})

export class Color {
  /**
  * We fallback to RGBA here since supporting alpha transformations
  * on wider color gamuts would currently be a lot of extra code
  * for very little benefit due to actual usage.
  */
  static applyAlpha(color: string, alpha: number): string {
   // special case optimization
   if (color === 'transparent') {
     return color;
   }
   if(alpha === 1) return color;

   const originRgba = parseColor(color);
   const originAlpha = originRgba[3];
   return `rgba(${originRgba[0]}, ${originRgba[1]}, ${originRgba[2]}, ${
     alpha * originAlpha
   })`;
 }

 static parseColor(color: string) {
  return parseColor(color)
 }
}
