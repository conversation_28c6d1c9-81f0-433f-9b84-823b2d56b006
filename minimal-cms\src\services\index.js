import api from './api';
import { API } from '../constants/service';

/**
 *
 * @param {Record<string, any>} params
 * @returns {Promise<{
 *  data: {Record<string, any>}
 * }>}
 */

export const getInstruments = params => api.GET(API.GET_INSTRUMENT, params);

export const getInstrumentsByCompanyCode = params =>
  api.GET(API.GET_INSTRUMENT_BY_COMPANY_CODE, params);

export const getCompanyCodesById = params =>
  api.GET(API.GET_COMPANY_CODES, params);

export const getAvailableLanguages = params =>
  api.GET(API.GET_AVAILABLE_LANGUAGES, params);

export const getAvailableTools = params =>
  api.GET(API.GET_AVAILABLE_TOOLS, params);

export const getTimeZones = params => api.GET(API.GET_TIME_ZONE, params);

export const getCurrencies = params => api.GET(API.GET_CURRENCIES, params);

export const getSessionState = params => api.GET(API.GET_SESSION_STATE, params);

export const saveSessionState = params =>
  api.POST(API.SAVE_SESSION_STATE, params);

export const applySettings = params => api.POST(API.APPLY_SETTINGS, params);

export const uploadFont = params => api.POST(API.UPLOAD_FONT, params);

export const getFont = params => api.GET(API.GET_FONT, params);

export const deleteFontById = params =>
  api.DELETE(API.DELETE_FONT_BY_ID, params);

export const deleteFontByFontFamily = params =>
  api.DELETE(API.DELETE_FONT_BY_FONT_FAMILY, params);

export const generateLink = params => api.POST(API.GENERATE_LINK, params);

export const getXML = params => api.GET(API.GET_XML, params);

export const getVersionsByCompanyCode = params =>
  api.GET(API.GET_VERSIONS_BY_COMPANY_CODE, params);

export const getCssVersionsByCompanyCode = params =>
  api.GET(API.GET_CSS_VERSIONS_BY_COMPANY_CODE, params);

export const getInstrumentsByIds = params =>
  api.GET(API.GET_INSTRUMENT_BY_IDS, params);

export const getOldSolutionsAndVersionsToolData = params =>
  api.GET(API.GET_OLD_SOLUTIONS_AND_VERSIONS_TOOL_DATA, params);

export const migrateSolution = params => api.GET(API.MIGRATE_SOLUTION, params);

export const getListFiles = params => api.GET(API.GET_LIST_FILES, params);

export const getToolsCenterLink = params =>
  api.GET(API.GET_TOOL_CENTER_LINK, params);

export const getContentFiles = params => api.GET(API.GET_CONTENT_FILE, params);

export const saveFile = params => api.POST(API.SAVE_FILE, params);

export const getCaptcha = params => api.GET(API.GET_CAPTCHA, params);

export const migration = params => api.POST(API.MIGRATION, params);

export const getUserNames = params => api.GET(API.GET_USERNAMES, params);

export const getEventTypes = params => api.GET(API.GET_EVENT_TYPES, params);

export const getEventDescriptions = params =>
  api.GET(API.GET_EVENT_DESCRIPTIONS, params);

export const getEventStatus = params => api.GET(API.GET_EVENT_STATUS, params);

export const getActivityLog = params => api.GET(API.GET_ACTIVITY_LOG, params);

export const confirmMigration = params =>
  api.POST(API.CONFIRM_MIGRATION, params);

export const getCustomePhraseApi = params =>
  api.POST(API.GET_CUSTOME_PHRASES, params);

export const saveSettingFile = params =>
  api.POST(API.SAVE_SETTING_FILE, params);

export const uploadImages = params => api.POST(API.UPLOAD_IMAGES, params);

export const getImages = params => api.GET(API.GET_IMAGES, params);

export const deleteImage = params => api.DELETE(API.DELETE_IMAGE, params);

export const savePackage = params => api.POST(API.SAVE_PACKAGE, params);

export const getSavedPackages = params =>
  api.GET(API.GET_SAVED_PACKAGES, params);

export const getUserPermissions = params =>
  api.GET(API.GET_USER_PERMISSIONS, params);

export const getRoles = params => api.GET(API.ROLE, params);

export const createRole = params => api.POST(API.ROLE, params);

export const updateRole = params => api.PUT(API.ROLE, params);

export const deleteRole = params => api.DELETE(API.ROLE, params);

export const getPermissions = params => api.GET(API.GET_PERMISSIONS, params);

export const assignRole = params => api.PUT(API.ASSIGN_ROLE, params);

export const searchUserByEmail = params =>
  api.GET(API.SEARCH_USER_BY_EMAIL, params);

export const getAllUserRoles = params =>
  api.GET(API.GET_ALL_USER_ROLES, params);
