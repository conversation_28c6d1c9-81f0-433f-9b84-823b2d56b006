{"version": 3, "sources": ["../../../../node_modules/events/events.js", "../../../../node_modules/base64-js/index.js", "../../../../node_modules/ieee754/index.js", "../../../../node_modules/buffer/index.js", "../../../../node_modules/@azure/abort-controller/src/AbortSignal.ts", "../../../../node_modules/@azure/abort-controller/src/AbortController.ts", "../../../../node_modules/@azure/core-util/node_modules/@azure/abort-controller/src/AbortError.ts", "../../../../node_modules/@azure/core-util/src/createAbortablePromise.ts", "../../../../node_modules/@azure/core-util/src/delay.ts", "../../../../node_modules/@azure/core-util/src/bytesEncoding.common.ts", "../../../../node_modules/@azure/core-util/src/uuidUtils.common.ts", "../../../../node_modules/@azure/core-util/src/uuidUtils-browser.mts", "../../../../node_modules/@azure/core-util/src/checkEnvironment.ts", "../../../../node_modules/@azure/web-pubsub-client/src/webPubSubClient.ts", "../../../../node_modules/@azure/web-pubsub-client/src/errors/index.ts", "../../../../node_modules/@azure/logger/src/log.common.ts", "../../../../node_modules/@azure/logger/src/debug.ts", "../../../../node_modules/@azure/logger/src/index.ts", "../../../../node_modules/@azure/web-pubsub-client/src/logger.ts", "../../../../node_modules/@azure/web-pubsub-client/src/protocols/jsonProtocolBase.ts", "../../../../node_modules/@azure/web-pubsub-client/src/protocols/webPubSubJsonProtocol.ts", "../../../../node_modules/@azure/web-pubsub-client/src/protocols/webPubSubJsonReliableProtocol.ts", "../../../../node_modules/@azure/web-pubsub-client/src/protocols/index.ts", "../../../../node_modules/@azure/web-pubsub-client/src/websocket/websocketClient.browser.ts", "../../../../node_modules/@azure/web-pubsub-client/src/utils/abortablePromise.ts"], "sourcesContent": ["// Copyright Joyent, Inc. and other Node contributors.\n//\n// Permission is hereby granted, free of charge, to any person obtaining a\n// copy of this software and associated documentation files (the\n// \"Software\"), to deal in the Software without restriction, including\n// without limitation the rights to use, copy, modify, merge, publish,\n// distribute, sublicense, and/or sell copies of the Software, and to permit\n// persons to whom the Software is furnished to do so, subject to the\n// following conditions:\n//\n// The above copyright notice and this permission notice shall be included\n// in all copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS\n// OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n// MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN\n// NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM,\n// DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR\n// OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE\n// USE OR OTHER DEALINGS IN THE SOFTWARE.\n\n'use strict';\n\nvar R = typeof Reflect === 'object' ? Reflect : null\nvar ReflectApply = R && typeof R.apply === 'function'\n  ? R.apply\n  : function ReflectApply(target, receiver, args) {\n    return Function.prototype.apply.call(target, receiver, args);\n  }\n\nvar ReflectOwnKeys\nif (R && typeof R.ownKeys === 'function') {\n  ReflectOwnKeys = R.ownKeys\n} else if (Object.getOwnPropertySymbols) {\n  ReflectOwnKeys = function ReflectOwnKeys(target) {\n    return Object.getOwnPropertyNames(target)\n      .concat(Object.getOwnPropertySymbols(target));\n  };\n} else {\n  ReflectOwnKeys = function ReflectOwnKeys(target) {\n    return Object.getOwnPropertyNames(target);\n  };\n}\n\nfunction ProcessEmitWarning(warning) {\n  if (console && console.warn) console.warn(warning);\n}\n\nvar NumberIsNaN = Number.isNaN || function NumberIsNaN(value) {\n  return value !== value;\n}\n\nfunction EventEmitter() {\n  EventEmitter.init.call(this);\n}\nmodule.exports = EventEmitter;\nmodule.exports.once = once;\n\n// Backwards-compat with node 0.10.x\nEventEmitter.EventEmitter = EventEmitter;\n\nEventEmitter.prototype._events = undefined;\nEventEmitter.prototype._eventsCount = 0;\nEventEmitter.prototype._maxListeners = undefined;\n\n// By default EventEmitters will print a warning if more than 10 listeners are\n// added to it. This is a useful default which helps finding memory leaks.\nvar defaultMaxListeners = 10;\n\nfunction checkListener(listener) {\n  if (typeof listener !== 'function') {\n    throw new TypeError('The \"listener\" argument must be of type Function. Received type ' + typeof listener);\n  }\n}\n\nObject.defineProperty(EventEmitter, 'defaultMaxListeners', {\n  enumerable: true,\n  get: function() {\n    return defaultMaxListeners;\n  },\n  set: function(arg) {\n    if (typeof arg !== 'number' || arg < 0 || NumberIsNaN(arg)) {\n      throw new RangeError('The value of \"defaultMaxListeners\" is out of range. It must be a non-negative number. Received ' + arg + '.');\n    }\n    defaultMaxListeners = arg;\n  }\n});\n\nEventEmitter.init = function() {\n\n  if (this._events === undefined ||\n      this._events === Object.getPrototypeOf(this)._events) {\n    this._events = Object.create(null);\n    this._eventsCount = 0;\n  }\n\n  this._maxListeners = this._maxListeners || undefined;\n};\n\n// Obviously not all Emitters should be limited to 10. This function allows\n// that to be increased. Set to zero for unlimited.\nEventEmitter.prototype.setMaxListeners = function setMaxListeners(n) {\n  if (typeof n !== 'number' || n < 0 || NumberIsNaN(n)) {\n    throw new RangeError('The value of \"n\" is out of range. It must be a non-negative number. Received ' + n + '.');\n  }\n  this._maxListeners = n;\n  return this;\n};\n\nfunction _getMaxListeners(that) {\n  if (that._maxListeners === undefined)\n    return EventEmitter.defaultMaxListeners;\n  return that._maxListeners;\n}\n\nEventEmitter.prototype.getMaxListeners = function getMaxListeners() {\n  return _getMaxListeners(this);\n};\n\nEventEmitter.prototype.emit = function emit(type) {\n  var args = [];\n  for (var i = 1; i < arguments.length; i++) args.push(arguments[i]);\n  var doError = (type === 'error');\n\n  var events = this._events;\n  if (events !== undefined)\n    doError = (doError && events.error === undefined);\n  else if (!doError)\n    return false;\n\n  // If there is no 'error' event listener then throw.\n  if (doError) {\n    var er;\n    if (args.length > 0)\n      er = args[0];\n    if (er instanceof Error) {\n      // Note: The comments on the `throw` lines are intentional, they show\n      // up in Node's output if this results in an unhandled exception.\n      throw er; // Unhandled 'error' event\n    }\n    // At least give some kind of context to the user\n    var err = new Error('Unhandled error.' + (er ? ' (' + er.message + ')' : ''));\n    err.context = er;\n    throw err; // Unhandled 'error' event\n  }\n\n  var handler = events[type];\n\n  if (handler === undefined)\n    return false;\n\n  if (typeof handler === 'function') {\n    ReflectApply(handler, this, args);\n  } else {\n    var len = handler.length;\n    var listeners = arrayClone(handler, len);\n    for (var i = 0; i < len; ++i)\n      ReflectApply(listeners[i], this, args);\n  }\n\n  return true;\n};\n\nfunction _addListener(target, type, listener, prepend) {\n  var m;\n  var events;\n  var existing;\n\n  checkListener(listener);\n\n  events = target._events;\n  if (events === undefined) {\n    events = target._events = Object.create(null);\n    target._eventsCount = 0;\n  } else {\n    // To avoid recursion in the case that type === \"newListener\"! Before\n    // adding it to the listeners, first emit \"newListener\".\n    if (events.newListener !== undefined) {\n      target.emit('newListener', type,\n                  listener.listener ? listener.listener : listener);\n\n      // Re-assign `events` because a newListener handler could have caused the\n      // this._events to be assigned to a new object\n      events = target._events;\n    }\n    existing = events[type];\n  }\n\n  if (existing === undefined) {\n    // Optimize the case of one listener. Don't need the extra array object.\n    existing = events[type] = listener;\n    ++target._eventsCount;\n  } else {\n    if (typeof existing === 'function') {\n      // Adding the second element, need to change to array.\n      existing = events[type] =\n        prepend ? [listener, existing] : [existing, listener];\n      // If we've already got an array, just append.\n    } else if (prepend) {\n      existing.unshift(listener);\n    } else {\n      existing.push(listener);\n    }\n\n    // Check for listener leak\n    m = _getMaxListeners(target);\n    if (m > 0 && existing.length > m && !existing.warned) {\n      existing.warned = true;\n      // No error code for this since it is a Warning\n      // eslint-disable-next-line no-restricted-syntax\n      var w = new Error('Possible EventEmitter memory leak detected. ' +\n                          existing.length + ' ' + String(type) + ' listeners ' +\n                          'added. Use emitter.setMaxListeners() to ' +\n                          'increase limit');\n      w.name = 'MaxListenersExceededWarning';\n      w.emitter = target;\n      w.type = type;\n      w.count = existing.length;\n      ProcessEmitWarning(w);\n    }\n  }\n\n  return target;\n}\n\nEventEmitter.prototype.addListener = function addListener(type, listener) {\n  return _addListener(this, type, listener, false);\n};\n\nEventEmitter.prototype.on = EventEmitter.prototype.addListener;\n\nEventEmitter.prototype.prependListener =\n    function prependListener(type, listener) {\n      return _addListener(this, type, listener, true);\n    };\n\nfunction onceWrapper() {\n  if (!this.fired) {\n    this.target.removeListener(this.type, this.wrapFn);\n    this.fired = true;\n    if (arguments.length === 0)\n      return this.listener.call(this.target);\n    return this.listener.apply(this.target, arguments);\n  }\n}\n\nfunction _onceWrap(target, type, listener) {\n  var state = { fired: false, wrapFn: undefined, target: target, type: type, listener: listener };\n  var wrapped = onceWrapper.bind(state);\n  wrapped.listener = listener;\n  state.wrapFn = wrapped;\n  return wrapped;\n}\n\nEventEmitter.prototype.once = function once(type, listener) {\n  checkListener(listener);\n  this.on(type, _onceWrap(this, type, listener));\n  return this;\n};\n\nEventEmitter.prototype.prependOnceListener =\n    function prependOnceListener(type, listener) {\n      checkListener(listener);\n      this.prependListener(type, _onceWrap(this, type, listener));\n      return this;\n    };\n\n// Emits a 'removeListener' event if and only if the listener was removed.\nEventEmitter.prototype.removeListener =\n    function removeListener(type, listener) {\n      var list, events, position, i, originalListener;\n\n      checkListener(listener);\n\n      events = this._events;\n      if (events === undefined)\n        return this;\n\n      list = events[type];\n      if (list === undefined)\n        return this;\n\n      if (list === listener || list.listener === listener) {\n        if (--this._eventsCount === 0)\n          this._events = Object.create(null);\n        else {\n          delete events[type];\n          if (events.removeListener)\n            this.emit('removeListener', type, list.listener || listener);\n        }\n      } else if (typeof list !== 'function') {\n        position = -1;\n\n        for (i = list.length - 1; i >= 0; i--) {\n          if (list[i] === listener || list[i].listener === listener) {\n            originalListener = list[i].listener;\n            position = i;\n            break;\n          }\n        }\n\n        if (position < 0)\n          return this;\n\n        if (position === 0)\n          list.shift();\n        else {\n          spliceOne(list, position);\n        }\n\n        if (list.length === 1)\n          events[type] = list[0];\n\n        if (events.removeListener !== undefined)\n          this.emit('removeListener', type, originalListener || listener);\n      }\n\n      return this;\n    };\n\nEventEmitter.prototype.off = EventEmitter.prototype.removeListener;\n\nEventEmitter.prototype.removeAllListeners =\n    function removeAllListeners(type) {\n      var listeners, events, i;\n\n      events = this._events;\n      if (events === undefined)\n        return this;\n\n      // not listening for removeListener, no need to emit\n      if (events.removeListener === undefined) {\n        if (arguments.length === 0) {\n          this._events = Object.create(null);\n          this._eventsCount = 0;\n        } else if (events[type] !== undefined) {\n          if (--this._eventsCount === 0)\n            this._events = Object.create(null);\n          else\n            delete events[type];\n        }\n        return this;\n      }\n\n      // emit removeListener for all listeners on all events\n      if (arguments.length === 0) {\n        var keys = Object.keys(events);\n        var key;\n        for (i = 0; i < keys.length; ++i) {\n          key = keys[i];\n          if (key === 'removeListener') continue;\n          this.removeAllListeners(key);\n        }\n        this.removeAllListeners('removeListener');\n        this._events = Object.create(null);\n        this._eventsCount = 0;\n        return this;\n      }\n\n      listeners = events[type];\n\n      if (typeof listeners === 'function') {\n        this.removeListener(type, listeners);\n      } else if (listeners !== undefined) {\n        // LIFO order\n        for (i = listeners.length - 1; i >= 0; i--) {\n          this.removeListener(type, listeners[i]);\n        }\n      }\n\n      return this;\n    };\n\nfunction _listeners(target, type, unwrap) {\n  var events = target._events;\n\n  if (events === undefined)\n    return [];\n\n  var evlistener = events[type];\n  if (evlistener === undefined)\n    return [];\n\n  if (typeof evlistener === 'function')\n    return unwrap ? [evlistener.listener || evlistener] : [evlistener];\n\n  return unwrap ?\n    unwrapListeners(evlistener) : arrayClone(evlistener, evlistener.length);\n}\n\nEventEmitter.prototype.listeners = function listeners(type) {\n  return _listeners(this, type, true);\n};\n\nEventEmitter.prototype.rawListeners = function rawListeners(type) {\n  return _listeners(this, type, false);\n};\n\nEventEmitter.listenerCount = function(emitter, type) {\n  if (typeof emitter.listenerCount === 'function') {\n    return emitter.listenerCount(type);\n  } else {\n    return listenerCount.call(emitter, type);\n  }\n};\n\nEventEmitter.prototype.listenerCount = listenerCount;\nfunction listenerCount(type) {\n  var events = this._events;\n\n  if (events !== undefined) {\n    var evlistener = events[type];\n\n    if (typeof evlistener === 'function') {\n      return 1;\n    } else if (evlistener !== undefined) {\n      return evlistener.length;\n    }\n  }\n\n  return 0;\n}\n\nEventEmitter.prototype.eventNames = function eventNames() {\n  return this._eventsCount > 0 ? ReflectOwnKeys(this._events) : [];\n};\n\nfunction arrayClone(arr, n) {\n  var copy = new Array(n);\n  for (var i = 0; i < n; ++i)\n    copy[i] = arr[i];\n  return copy;\n}\n\nfunction spliceOne(list, index) {\n  for (; index + 1 < list.length; index++)\n    list[index] = list[index + 1];\n  list.pop();\n}\n\nfunction unwrapListeners(arr) {\n  var ret = new Array(arr.length);\n  for (var i = 0; i < ret.length; ++i) {\n    ret[i] = arr[i].listener || arr[i];\n  }\n  return ret;\n}\n\nfunction once(emitter, name) {\n  return new Promise(function (resolve, reject) {\n    function errorListener(err) {\n      emitter.removeListener(name, resolver);\n      reject(err);\n    }\n\n    function resolver() {\n      if (typeof emitter.removeListener === 'function') {\n        emitter.removeListener('error', errorListener);\n      }\n      resolve([].slice.call(arguments));\n    };\n\n    eventTargetAgnosticAddListener(emitter, name, resolver, { once: true });\n    if (name !== 'error') {\n      addErrorHandlerIfEventEmitter(emitter, errorListener, { once: true });\n    }\n  });\n}\n\nfunction addErrorHandlerIfEventEmitter(emitter, handler, flags) {\n  if (typeof emitter.on === 'function') {\n    eventTargetAgnosticAddListener(emitter, 'error', handler, flags);\n  }\n}\n\nfunction eventTargetAgnosticAddListener(emitter, name, listener, flags) {\n  if (typeof emitter.on === 'function') {\n    if (flags.once) {\n      emitter.once(name, listener);\n    } else {\n      emitter.on(name, listener);\n    }\n  } else if (typeof emitter.addEventListener === 'function') {\n    // EventTarget does not have `error` event semantics like Node\n    // EventEmitters, we do not listen for `error` events here.\n    emitter.addEventListener(name, function wrapListener(arg) {\n      // IE does not have builtin `{ once: true }` support so we\n      // have to do it manually.\n      if (flags.once) {\n        emitter.removeEventListener(name, wrapListener);\n      }\n      listener(arg);\n    });\n  } else {\n    throw new TypeError('The \"emitter\" argument must be of type EventEmitter. Received type ' + typeof emitter);\n  }\n}\n", "'use strict'\n\nexports.byteLength = byteLength\nexports.toByteArray = toByteArray\nexports.fromByteArray = fromByteArray\n\nvar lookup = []\nvar revLookup = []\nvar Arr = typeof Uint8Array !== 'undefined' ? Uint8Array : Array\n\nvar code = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/'\nfor (var i = 0, len = code.length; i < len; ++i) {\n  lookup[i] = code[i]\n  revLookup[code.charCodeAt(i)] = i\n}\n\n// Support decoding URL-safe base64 strings, as Node.js does.\n// See: https://en.wikipedia.org/wiki/Base64#URL_applications\nrevLookup['-'.charCodeAt(0)] = 62\nrevLookup['_'.charCodeAt(0)] = 63\n\nfunction getLens (b64) {\n  var len = b64.length\n\n  if (len % 4 > 0) {\n    throw new Error('Invalid string. Length must be a multiple of 4')\n  }\n\n  // Trim off extra bytes after placeholder bytes are found\n  // See: https://github.com/beatgammit/base64-js/issues/42\n  var validLen = b64.indexOf('=')\n  if (validLen === -1) validLen = len\n\n  var placeHoldersLen = validLen === len\n    ? 0\n    : 4 - (validLen % 4)\n\n  return [validLen, placeHoldersLen]\n}\n\n// base64 is 4/3 + up to two characters of the original data\nfunction byteLength (b64) {\n  var lens = getLens(b64)\n  var validLen = lens[0]\n  var placeHoldersLen = lens[1]\n  return ((validLen + placeHoldersLen) * 3 / 4) - placeHoldersLen\n}\n\nfunction _byteLength (b64, validLen, placeHoldersLen) {\n  return ((validLen + placeHoldersLen) * 3 / 4) - placeHoldersLen\n}\n\nfunction toByteArray (b64) {\n  var tmp\n  var lens = getLens(b64)\n  var validLen = lens[0]\n  var placeHoldersLen = lens[1]\n\n  var arr = new Arr(_byteLength(b64, validLen, placeHoldersLen))\n\n  var curByte = 0\n\n  // if there are placeholders, only get up to the last complete 4 chars\n  var len = placeHoldersLen > 0\n    ? validLen - 4\n    : validLen\n\n  var i\n  for (i = 0; i < len; i += 4) {\n    tmp =\n      (revLookup[b64.charCodeAt(i)] << 18) |\n      (revLookup[b64.charCodeAt(i + 1)] << 12) |\n      (revLookup[b64.charCodeAt(i + 2)] << 6) |\n      revLookup[b64.charCodeAt(i + 3)]\n    arr[curByte++] = (tmp >> 16) & 0xFF\n    arr[curByte++] = (tmp >> 8) & 0xFF\n    arr[curByte++] = tmp & 0xFF\n  }\n\n  if (placeHoldersLen === 2) {\n    tmp =\n      (revLookup[b64.charCodeAt(i)] << 2) |\n      (revLookup[b64.charCodeAt(i + 1)] >> 4)\n    arr[curByte++] = tmp & 0xFF\n  }\n\n  if (placeHoldersLen === 1) {\n    tmp =\n      (revLookup[b64.charCodeAt(i)] << 10) |\n      (revLookup[b64.charCodeAt(i + 1)] << 4) |\n      (revLookup[b64.charCodeAt(i + 2)] >> 2)\n    arr[curByte++] = (tmp >> 8) & 0xFF\n    arr[curByte++] = tmp & 0xFF\n  }\n\n  return arr\n}\n\nfunction tripletToBase64 (num) {\n  return lookup[num >> 18 & 0x3F] +\n    lookup[num >> 12 & 0x3F] +\n    lookup[num >> 6 & 0x3F] +\n    lookup[num & 0x3F]\n}\n\nfunction encodeChunk (uint8, start, end) {\n  var tmp\n  var output = []\n  for (var i = start; i < end; i += 3) {\n    tmp =\n      ((uint8[i] << 16) & 0xFF0000) +\n      ((uint8[i + 1] << 8) & 0xFF00) +\n      (uint8[i + 2] & 0xFF)\n    output.push(tripletToBase64(tmp))\n  }\n  return output.join('')\n}\n\nfunction fromByteArray (uint8) {\n  var tmp\n  var len = uint8.length\n  var extraBytes = len % 3 // if we have 1 byte left, pad 2 bytes\n  var parts = []\n  var maxChunkLength = 16383 // must be multiple of 3\n\n  // go through the array every three bytes, we'll deal with trailing stuff later\n  for (var i = 0, len2 = len - extraBytes; i < len2; i += maxChunkLength) {\n    parts.push(encodeChunk(uint8, i, (i + maxChunkLength) > len2 ? len2 : (i + maxChunkLength)))\n  }\n\n  // pad the end with zeros, but make sure to not forget the extra bytes\n  if (extraBytes === 1) {\n    tmp = uint8[len - 1]\n    parts.push(\n      lookup[tmp >> 2] +\n      lookup[(tmp << 4) & 0x3F] +\n      '=='\n    )\n  } else if (extraBytes === 2) {\n    tmp = (uint8[len - 2] << 8) + uint8[len - 1]\n    parts.push(\n      lookup[tmp >> 10] +\n      lookup[(tmp >> 4) & 0x3F] +\n      lookup[(tmp << 2) & 0x3F] +\n      '='\n    )\n  }\n\n  return parts.join('')\n}\n", "/*! ieee754. BSD-3-Clause License. Feross A<PERSON> <https://feross.org/opensource> */\nexports.read = function (buffer, offset, isLE, mLen, nBytes) {\n  var e, m\n  var eLen = (nBytes * 8) - mLen - 1\n  var eMax = (1 << eLen) - 1\n  var eBias = eMax >> 1\n  var nBits = -7\n  var i = isLE ? (nBytes - 1) : 0\n  var d = isLE ? -1 : 1\n  var s = buffer[offset + i]\n\n  i += d\n\n  e = s & ((1 << (-nBits)) - 1)\n  s >>= (-nBits)\n  nBits += eLen\n  for (; nBits > 0; e = (e * 256) + buffer[offset + i], i += d, nBits -= 8) {}\n\n  m = e & ((1 << (-nBits)) - 1)\n  e >>= (-nBits)\n  nBits += mLen\n  for (; nBits > 0; m = (m * 256) + buffer[offset + i], i += d, nBits -= 8) {}\n\n  if (e === 0) {\n    e = 1 - eBias\n  } else if (e === eMax) {\n    return m ? NaN : ((s ? -1 : 1) * Infinity)\n  } else {\n    m = m + Math.pow(2, mLen)\n    e = e - eBias\n  }\n  return (s ? -1 : 1) * m * Math.pow(2, e - mLen)\n}\n\nexports.write = function (buffer, value, offset, isLE, mLen, nBytes) {\n  var e, m, c\n  var eLen = (nBytes * 8) - mLen - 1\n  var eMax = (1 << eLen) - 1\n  var eBias = eMax >> 1\n  var rt = (mLen === 23 ? Math.pow(2, -24) - Math.pow(2, -77) : 0)\n  var i = isLE ? 0 : (nBytes - 1)\n  var d = isLE ? 1 : -1\n  var s = value < 0 || (value === 0 && 1 / value < 0) ? 1 : 0\n\n  value = Math.abs(value)\n\n  if (isNaN(value) || value === Infinity) {\n    m = isNaN(value) ? 1 : 0\n    e = eMax\n  } else {\n    e = Math.floor(Math.log(value) / Math.LN2)\n    if (value * (c = Math.pow(2, -e)) < 1) {\n      e--\n      c *= 2\n    }\n    if (e + eBias >= 1) {\n      value += rt / c\n    } else {\n      value += rt * Math.pow(2, 1 - eBias)\n    }\n    if (value * c >= 2) {\n      e++\n      c /= 2\n    }\n\n    if (e + eBias >= eMax) {\n      m = 0\n      e = eMax\n    } else if (e + eBias >= 1) {\n      m = ((value * c) - 1) * Math.pow(2, mLen)\n      e = e + eBias\n    } else {\n      m = value * Math.pow(2, eBias - 1) * Math.pow(2, mLen)\n      e = 0\n    }\n  }\n\n  for (; mLen >= 8; buffer[offset + i] = m & 0xff, i += d, m /= 256, mLen -= 8) {}\n\n  e = (e << mLen) | m\n  eLen += mLen\n  for (; eLen > 0; buffer[offset + i] = e & 0xff, i += d, e /= 256, eLen -= 8) {}\n\n  buffer[offset + i - d] |= s * 128\n}\n", "/*!\n * The buffer module from node.js, for the browser.\n *\n * <AUTHOR> <https://feross.org>\n * @license  MIT\n */\n/* eslint-disable no-proto */\n\n'use strict'\n\nconst base64 = require('base64-js')\nconst ieee754 = require('ieee754')\nconst customInspectSymbol =\n  (typeof Symbol === 'function' && typeof Symbol['for'] === 'function') // eslint-disable-line dot-notation\n    ? Symbol['for']('nodejs.util.inspect.custom') // eslint-disable-line dot-notation\n    : null\n\nexports.Buffer = Buffer\nexports.SlowBuffer = SlowBuffer\nexports.INSPECT_MAX_BYTES = 50\n\nconst K_MAX_LENGTH = 0x7fffffff\nexports.kMaxLength = K_MAX_LENGTH\n\n/**\n * If `Buffer.TYPED_ARRAY_SUPPORT`:\n *   === true    Use Uint8Array implementation (fastest)\n *   === false   Print warning and recommend using `buffer` v4.x which has an Object\n *               implementation (most compatible, even IE6)\n *\n * Browsers that support typed arrays are IE 10+, Firefox 4+, Chrome 7+, Safari 5.1+,\n * Opera 11.6+, iOS 4.2+.\n *\n * We report that the browser does not support typed arrays if the are not subclassable\n * using __proto__. Firefox 4-29 lacks support for adding new properties to `Uint8Array`\n * (See: https://bugzilla.mozilla.org/show_bug.cgi?id=695438). IE 10 lacks support\n * for __proto__ and has a buggy typed array implementation.\n */\nBuffer.TYPED_ARRAY_SUPPORT = typedArraySupport()\n\nif (!Buffer.TYPED_ARRAY_SUPPORT && typeof console !== 'undefined' &&\n    typeof console.error === 'function') {\n  console.error(\n    'This browser lacks typed array (Uint8Array) support which is required by ' +\n    '`buffer` v5.x. Use `buffer` v4.x if you require old browser support.'\n  )\n}\n\nfunction typedArraySupport () {\n  // Can typed array instances can be augmented?\n  try {\n    const arr = new Uint8Array(1)\n    const proto = { foo: function () { return 42 } }\n    Object.setPrototypeOf(proto, Uint8Array.prototype)\n    Object.setPrototypeOf(arr, proto)\n    return arr.foo() === 42\n  } catch (e) {\n    return false\n  }\n}\n\nObject.defineProperty(Buffer.prototype, 'parent', {\n  enumerable: true,\n  get: function () {\n    if (!Buffer.isBuffer(this)) return undefined\n    return this.buffer\n  }\n})\n\nObject.defineProperty(Buffer.prototype, 'offset', {\n  enumerable: true,\n  get: function () {\n    if (!Buffer.isBuffer(this)) return undefined\n    return this.byteOffset\n  }\n})\n\nfunction createBuffer (length) {\n  if (length > K_MAX_LENGTH) {\n    throw new RangeError('The value \"' + length + '\" is invalid for option \"size\"')\n  }\n  // Return an augmented `Uint8Array` instance\n  const buf = new Uint8Array(length)\n  Object.setPrototypeOf(buf, Buffer.prototype)\n  return buf\n}\n\n/**\n * The Buffer constructor returns instances of `Uint8Array` that have their\n * prototype changed to `Buffer.prototype`. Furthermore, `Buffer` is a subclass of\n * `Uint8Array`, so the returned instances will have all the node `Buffer` methods\n * and the `Uint8Array` methods. Square bracket notation works as expected -- it\n * returns a single octet.\n *\n * The `Uint8Array` prototype remains unmodified.\n */\n\nfunction Buffer (arg, encodingOrOffset, length) {\n  // Common case.\n  if (typeof arg === 'number') {\n    if (typeof encodingOrOffset === 'string') {\n      throw new TypeError(\n        'The \"string\" argument must be of type string. Received type number'\n      )\n    }\n    return allocUnsafe(arg)\n  }\n  return from(arg, encodingOrOffset, length)\n}\n\nBuffer.poolSize = 8192 // not used by this implementation\n\nfunction from (value, encodingOrOffset, length) {\n  if (typeof value === 'string') {\n    return fromString(value, encodingOrOffset)\n  }\n\n  if (ArrayBuffer.isView(value)) {\n    return fromArrayView(value)\n  }\n\n  if (value == null) {\n    throw new TypeError(\n      'The first argument must be one of type string, Buffer, ArrayBuffer, Array, ' +\n      'or Array-like Object. Received type ' + (typeof value)\n    )\n  }\n\n  if (isInstance(value, ArrayBuffer) ||\n      (value && isInstance(value.buffer, ArrayBuffer))) {\n    return fromArrayBuffer(value, encodingOrOffset, length)\n  }\n\n  if (typeof SharedArrayBuffer !== 'undefined' &&\n      (isInstance(value, SharedArrayBuffer) ||\n      (value && isInstance(value.buffer, SharedArrayBuffer)))) {\n    return fromArrayBuffer(value, encodingOrOffset, length)\n  }\n\n  if (typeof value === 'number') {\n    throw new TypeError(\n      'The \"value\" argument must not be of type number. Received type number'\n    )\n  }\n\n  const valueOf = value.valueOf && value.valueOf()\n  if (valueOf != null && valueOf !== value) {\n    return Buffer.from(valueOf, encodingOrOffset, length)\n  }\n\n  const b = fromObject(value)\n  if (b) return b\n\n  if (typeof Symbol !== 'undefined' && Symbol.toPrimitive != null &&\n      typeof value[Symbol.toPrimitive] === 'function') {\n    return Buffer.from(value[Symbol.toPrimitive]('string'), encodingOrOffset, length)\n  }\n\n  throw new TypeError(\n    'The first argument must be one of type string, Buffer, ArrayBuffer, Array, ' +\n    'or Array-like Object. Received type ' + (typeof value)\n  )\n}\n\n/**\n * Functionally equivalent to Buffer(arg, encoding) but throws a TypeError\n * if value is a number.\n * Buffer.from(str[, encoding])\n * Buffer.from(array)\n * Buffer.from(buffer)\n * Buffer.from(arrayBuffer[, byteOffset[, length]])\n **/\nBuffer.from = function (value, encodingOrOffset, length) {\n  return from(value, encodingOrOffset, length)\n}\n\n// Note: Change prototype *after* Buffer.from is defined to workaround Chrome bug:\n// https://github.com/feross/buffer/pull/148\nObject.setPrototypeOf(Buffer.prototype, Uint8Array.prototype)\nObject.setPrototypeOf(Buffer, Uint8Array)\n\nfunction assertSize (size) {\n  if (typeof size !== 'number') {\n    throw new TypeError('\"size\" argument must be of type number')\n  } else if (size < 0) {\n    throw new RangeError('The value \"' + size + '\" is invalid for option \"size\"')\n  }\n}\n\nfunction alloc (size, fill, encoding) {\n  assertSize(size)\n  if (size <= 0) {\n    return createBuffer(size)\n  }\n  if (fill !== undefined) {\n    // Only pay attention to encoding if it's a string. This\n    // prevents accidentally sending in a number that would\n    // be interpreted as a start offset.\n    return typeof encoding === 'string'\n      ? createBuffer(size).fill(fill, encoding)\n      : createBuffer(size).fill(fill)\n  }\n  return createBuffer(size)\n}\n\n/**\n * Creates a new filled Buffer instance.\n * alloc(size[, fill[, encoding]])\n **/\nBuffer.alloc = function (size, fill, encoding) {\n  return alloc(size, fill, encoding)\n}\n\nfunction allocUnsafe (size) {\n  assertSize(size)\n  return createBuffer(size < 0 ? 0 : checked(size) | 0)\n}\n\n/**\n * Equivalent to Buffer(num), by default creates a non-zero-filled Buffer instance.\n * */\nBuffer.allocUnsafe = function (size) {\n  return allocUnsafe(size)\n}\n/**\n * Equivalent to SlowBuffer(num), by default creates a non-zero-filled Buffer instance.\n */\nBuffer.allocUnsafeSlow = function (size) {\n  return allocUnsafe(size)\n}\n\nfunction fromString (string, encoding) {\n  if (typeof encoding !== 'string' || encoding === '') {\n    encoding = 'utf8'\n  }\n\n  if (!Buffer.isEncoding(encoding)) {\n    throw new TypeError('Unknown encoding: ' + encoding)\n  }\n\n  const length = byteLength(string, encoding) | 0\n  let buf = createBuffer(length)\n\n  const actual = buf.write(string, encoding)\n\n  if (actual !== length) {\n    // Writing a hex string, for example, that contains invalid characters will\n    // cause everything after the first invalid character to be ignored. (e.g.\n    // 'abxxcd' will be treated as 'ab')\n    buf = buf.slice(0, actual)\n  }\n\n  return buf\n}\n\nfunction fromArrayLike (array) {\n  const length = array.length < 0 ? 0 : checked(array.length) | 0\n  const buf = createBuffer(length)\n  for (let i = 0; i < length; i += 1) {\n    buf[i] = array[i] & 255\n  }\n  return buf\n}\n\nfunction fromArrayView (arrayView) {\n  if (isInstance(arrayView, Uint8Array)) {\n    const copy = new Uint8Array(arrayView)\n    return fromArrayBuffer(copy.buffer, copy.byteOffset, copy.byteLength)\n  }\n  return fromArrayLike(arrayView)\n}\n\nfunction fromArrayBuffer (array, byteOffset, length) {\n  if (byteOffset < 0 || array.byteLength < byteOffset) {\n    throw new RangeError('\"offset\" is outside of buffer bounds')\n  }\n\n  if (array.byteLength < byteOffset + (length || 0)) {\n    throw new RangeError('\"length\" is outside of buffer bounds')\n  }\n\n  let buf\n  if (byteOffset === undefined && length === undefined) {\n    buf = new Uint8Array(array)\n  } else if (length === undefined) {\n    buf = new Uint8Array(array, byteOffset)\n  } else {\n    buf = new Uint8Array(array, byteOffset, length)\n  }\n\n  // Return an augmented `Uint8Array` instance\n  Object.setPrototypeOf(buf, Buffer.prototype)\n\n  return buf\n}\n\nfunction fromObject (obj) {\n  if (Buffer.isBuffer(obj)) {\n    const len = checked(obj.length) | 0\n    const buf = createBuffer(len)\n\n    if (buf.length === 0) {\n      return buf\n    }\n\n    obj.copy(buf, 0, 0, len)\n    return buf\n  }\n\n  if (obj.length !== undefined) {\n    if (typeof obj.length !== 'number' || numberIsNaN(obj.length)) {\n      return createBuffer(0)\n    }\n    return fromArrayLike(obj)\n  }\n\n  if (obj.type === 'Buffer' && Array.isArray(obj.data)) {\n    return fromArrayLike(obj.data)\n  }\n}\n\nfunction checked (length) {\n  // Note: cannot use `length < K_MAX_LENGTH` here because that fails when\n  // length is NaN (which is otherwise coerced to zero.)\n  if (length >= K_MAX_LENGTH) {\n    throw new RangeError('Attempt to allocate Buffer larger than maximum ' +\n                         'size: 0x' + K_MAX_LENGTH.toString(16) + ' bytes')\n  }\n  return length | 0\n}\n\nfunction SlowBuffer (length) {\n  if (+length != length) { // eslint-disable-line eqeqeq\n    length = 0\n  }\n  return Buffer.alloc(+length)\n}\n\nBuffer.isBuffer = function isBuffer (b) {\n  return b != null && b._isBuffer === true &&\n    b !== Buffer.prototype // so Buffer.isBuffer(Buffer.prototype) will be false\n}\n\nBuffer.compare = function compare (a, b) {\n  if (isInstance(a, Uint8Array)) a = Buffer.from(a, a.offset, a.byteLength)\n  if (isInstance(b, Uint8Array)) b = Buffer.from(b, b.offset, b.byteLength)\n  if (!Buffer.isBuffer(a) || !Buffer.isBuffer(b)) {\n    throw new TypeError(\n      'The \"buf1\", \"buf2\" arguments must be one of type Buffer or Uint8Array'\n    )\n  }\n\n  if (a === b) return 0\n\n  let x = a.length\n  let y = b.length\n\n  for (let i = 0, len = Math.min(x, y); i < len; ++i) {\n    if (a[i] !== b[i]) {\n      x = a[i]\n      y = b[i]\n      break\n    }\n  }\n\n  if (x < y) return -1\n  if (y < x) return 1\n  return 0\n}\n\nBuffer.isEncoding = function isEncoding (encoding) {\n  switch (String(encoding).toLowerCase()) {\n    case 'hex':\n    case 'utf8':\n    case 'utf-8':\n    case 'ascii':\n    case 'latin1':\n    case 'binary':\n    case 'base64':\n    case 'ucs2':\n    case 'ucs-2':\n    case 'utf16le':\n    case 'utf-16le':\n      return true\n    default:\n      return false\n  }\n}\n\nBuffer.concat = function concat (list, length) {\n  if (!Array.isArray(list)) {\n    throw new TypeError('\"list\" argument must be an Array of Buffers')\n  }\n\n  if (list.length === 0) {\n    return Buffer.alloc(0)\n  }\n\n  let i\n  if (length === undefined) {\n    length = 0\n    for (i = 0; i < list.length; ++i) {\n      length += list[i].length\n    }\n  }\n\n  const buffer = Buffer.allocUnsafe(length)\n  let pos = 0\n  for (i = 0; i < list.length; ++i) {\n    let buf = list[i]\n    if (isInstance(buf, Uint8Array)) {\n      if (pos + buf.length > buffer.length) {\n        if (!Buffer.isBuffer(buf)) buf = Buffer.from(buf)\n        buf.copy(buffer, pos)\n      } else {\n        Uint8Array.prototype.set.call(\n          buffer,\n          buf,\n          pos\n        )\n      }\n    } else if (!Buffer.isBuffer(buf)) {\n      throw new TypeError('\"list\" argument must be an Array of Buffers')\n    } else {\n      buf.copy(buffer, pos)\n    }\n    pos += buf.length\n  }\n  return buffer\n}\n\nfunction byteLength (string, encoding) {\n  if (Buffer.isBuffer(string)) {\n    return string.length\n  }\n  if (ArrayBuffer.isView(string) || isInstance(string, ArrayBuffer)) {\n    return string.byteLength\n  }\n  if (typeof string !== 'string') {\n    throw new TypeError(\n      'The \"string\" argument must be one of type string, Buffer, or ArrayBuffer. ' +\n      'Received type ' + typeof string\n    )\n  }\n\n  const len = string.length\n  const mustMatch = (arguments.length > 2 && arguments[2] === true)\n  if (!mustMatch && len === 0) return 0\n\n  // Use a for loop to avoid recursion\n  let loweredCase = false\n  for (;;) {\n    switch (encoding) {\n      case 'ascii':\n      case 'latin1':\n      case 'binary':\n        return len\n      case 'utf8':\n      case 'utf-8':\n        return utf8ToBytes(string).length\n      case 'ucs2':\n      case 'ucs-2':\n      case 'utf16le':\n      case 'utf-16le':\n        return len * 2\n      case 'hex':\n        return len >>> 1\n      case 'base64':\n        return base64ToBytes(string).length\n      default:\n        if (loweredCase) {\n          return mustMatch ? -1 : utf8ToBytes(string).length // assume utf8\n        }\n        encoding = ('' + encoding).toLowerCase()\n        loweredCase = true\n    }\n  }\n}\nBuffer.byteLength = byteLength\n\nfunction slowToString (encoding, start, end) {\n  let loweredCase = false\n\n  // No need to verify that \"this.length <= MAX_UINT32\" since it's a read-only\n  // property of a typed array.\n\n  // This behaves neither like String nor Uint8Array in that we set start/end\n  // to their upper/lower bounds if the value passed is out of range.\n  // undefined is handled specially as per ECMA-262 6th Edition,\n  // Section 13.3.3.7 Runtime Semantics: KeyedBindingInitialization.\n  if (start === undefined || start < 0) {\n    start = 0\n  }\n  // Return early if start > this.length. Done here to prevent potential uint32\n  // coercion fail below.\n  if (start > this.length) {\n    return ''\n  }\n\n  if (end === undefined || end > this.length) {\n    end = this.length\n  }\n\n  if (end <= 0) {\n    return ''\n  }\n\n  // Force coercion to uint32. This will also coerce falsey/NaN values to 0.\n  end >>>= 0\n  start >>>= 0\n\n  if (end <= start) {\n    return ''\n  }\n\n  if (!encoding) encoding = 'utf8'\n\n  while (true) {\n    switch (encoding) {\n      case 'hex':\n        return hexSlice(this, start, end)\n\n      case 'utf8':\n      case 'utf-8':\n        return utf8Slice(this, start, end)\n\n      case 'ascii':\n        return asciiSlice(this, start, end)\n\n      case 'latin1':\n      case 'binary':\n        return latin1Slice(this, start, end)\n\n      case 'base64':\n        return base64Slice(this, start, end)\n\n      case 'ucs2':\n      case 'ucs-2':\n      case 'utf16le':\n      case 'utf-16le':\n        return utf16leSlice(this, start, end)\n\n      default:\n        if (loweredCase) throw new TypeError('Unknown encoding: ' + encoding)\n        encoding = (encoding + '').toLowerCase()\n        loweredCase = true\n    }\n  }\n}\n\n// This property is used by `Buffer.isBuffer` (and the `is-buffer` npm package)\n// to detect a Buffer instance. It's not possible to use `instanceof Buffer`\n// reliably in a browserify context because there could be multiple different\n// copies of the 'buffer' package in use. This method works even for Buffer\n// instances that were created from another copy of the `buffer` package.\n// See: https://github.com/feross/buffer/issues/154\nBuffer.prototype._isBuffer = true\n\nfunction swap (b, n, m) {\n  const i = b[n]\n  b[n] = b[m]\n  b[m] = i\n}\n\nBuffer.prototype.swap16 = function swap16 () {\n  const len = this.length\n  if (len % 2 !== 0) {\n    throw new RangeError('Buffer size must be a multiple of 16-bits')\n  }\n  for (let i = 0; i < len; i += 2) {\n    swap(this, i, i + 1)\n  }\n  return this\n}\n\nBuffer.prototype.swap32 = function swap32 () {\n  const len = this.length\n  if (len % 4 !== 0) {\n    throw new RangeError('Buffer size must be a multiple of 32-bits')\n  }\n  for (let i = 0; i < len; i += 4) {\n    swap(this, i, i + 3)\n    swap(this, i + 1, i + 2)\n  }\n  return this\n}\n\nBuffer.prototype.swap64 = function swap64 () {\n  const len = this.length\n  if (len % 8 !== 0) {\n    throw new RangeError('Buffer size must be a multiple of 64-bits')\n  }\n  for (let i = 0; i < len; i += 8) {\n    swap(this, i, i + 7)\n    swap(this, i + 1, i + 6)\n    swap(this, i + 2, i + 5)\n    swap(this, i + 3, i + 4)\n  }\n  return this\n}\n\nBuffer.prototype.toString = function toString () {\n  const length = this.length\n  if (length === 0) return ''\n  if (arguments.length === 0) return utf8Slice(this, 0, length)\n  return slowToString.apply(this, arguments)\n}\n\nBuffer.prototype.toLocaleString = Buffer.prototype.toString\n\nBuffer.prototype.equals = function equals (b) {\n  if (!Buffer.isBuffer(b)) throw new TypeError('Argument must be a Buffer')\n  if (this === b) return true\n  return Buffer.compare(this, b) === 0\n}\n\nBuffer.prototype.inspect = function inspect () {\n  let str = ''\n  const max = exports.INSPECT_MAX_BYTES\n  str = this.toString('hex', 0, max).replace(/(.{2})/g, '$1 ').trim()\n  if (this.length > max) str += ' ... '\n  return '<Buffer ' + str + '>'\n}\nif (customInspectSymbol) {\n  Buffer.prototype[customInspectSymbol] = Buffer.prototype.inspect\n}\n\nBuffer.prototype.compare = function compare (target, start, end, thisStart, thisEnd) {\n  if (isInstance(target, Uint8Array)) {\n    target = Buffer.from(target, target.offset, target.byteLength)\n  }\n  if (!Buffer.isBuffer(target)) {\n    throw new TypeError(\n      'The \"target\" argument must be one of type Buffer or Uint8Array. ' +\n      'Received type ' + (typeof target)\n    )\n  }\n\n  if (start === undefined) {\n    start = 0\n  }\n  if (end === undefined) {\n    end = target ? target.length : 0\n  }\n  if (thisStart === undefined) {\n    thisStart = 0\n  }\n  if (thisEnd === undefined) {\n    thisEnd = this.length\n  }\n\n  if (start < 0 || end > target.length || thisStart < 0 || thisEnd > this.length) {\n    throw new RangeError('out of range index')\n  }\n\n  if (thisStart >= thisEnd && start >= end) {\n    return 0\n  }\n  if (thisStart >= thisEnd) {\n    return -1\n  }\n  if (start >= end) {\n    return 1\n  }\n\n  start >>>= 0\n  end >>>= 0\n  thisStart >>>= 0\n  thisEnd >>>= 0\n\n  if (this === target) return 0\n\n  let x = thisEnd - thisStart\n  let y = end - start\n  const len = Math.min(x, y)\n\n  const thisCopy = this.slice(thisStart, thisEnd)\n  const targetCopy = target.slice(start, end)\n\n  for (let i = 0; i < len; ++i) {\n    if (thisCopy[i] !== targetCopy[i]) {\n      x = thisCopy[i]\n      y = targetCopy[i]\n      break\n    }\n  }\n\n  if (x < y) return -1\n  if (y < x) return 1\n  return 0\n}\n\n// Finds either the first index of `val` in `buffer` at offset >= `byteOffset`,\n// OR the last index of `val` in `buffer` at offset <= `byteOffset`.\n//\n// Arguments:\n// - buffer - a Buffer to search\n// - val - a string, Buffer, or number\n// - byteOffset - an index into `buffer`; will be clamped to an int32\n// - encoding - an optional encoding, relevant is val is a string\n// - dir - true for indexOf, false for lastIndexOf\nfunction bidirectionalIndexOf (buffer, val, byteOffset, encoding, dir) {\n  // Empty buffer means no match\n  if (buffer.length === 0) return -1\n\n  // Normalize byteOffset\n  if (typeof byteOffset === 'string') {\n    encoding = byteOffset\n    byteOffset = 0\n  } else if (byteOffset > 0x7fffffff) {\n    byteOffset = 0x7fffffff\n  } else if (byteOffset < -0x80000000) {\n    byteOffset = -0x80000000\n  }\n  byteOffset = +byteOffset // Coerce to Number.\n  if (numberIsNaN(byteOffset)) {\n    // byteOffset: it it's undefined, null, NaN, \"foo\", etc, search whole buffer\n    byteOffset = dir ? 0 : (buffer.length - 1)\n  }\n\n  // Normalize byteOffset: negative offsets start from the end of the buffer\n  if (byteOffset < 0) byteOffset = buffer.length + byteOffset\n  if (byteOffset >= buffer.length) {\n    if (dir) return -1\n    else byteOffset = buffer.length - 1\n  } else if (byteOffset < 0) {\n    if (dir) byteOffset = 0\n    else return -1\n  }\n\n  // Normalize val\n  if (typeof val === 'string') {\n    val = Buffer.from(val, encoding)\n  }\n\n  // Finally, search either indexOf (if dir is true) or lastIndexOf\n  if (Buffer.isBuffer(val)) {\n    // Special case: looking for empty string/buffer always fails\n    if (val.length === 0) {\n      return -1\n    }\n    return arrayIndexOf(buffer, val, byteOffset, encoding, dir)\n  } else if (typeof val === 'number') {\n    val = val & 0xFF // Search for a byte value [0-255]\n    if (typeof Uint8Array.prototype.indexOf === 'function') {\n      if (dir) {\n        return Uint8Array.prototype.indexOf.call(buffer, val, byteOffset)\n      } else {\n        return Uint8Array.prototype.lastIndexOf.call(buffer, val, byteOffset)\n      }\n    }\n    return arrayIndexOf(buffer, [val], byteOffset, encoding, dir)\n  }\n\n  throw new TypeError('val must be string, number or Buffer')\n}\n\nfunction arrayIndexOf (arr, val, byteOffset, encoding, dir) {\n  let indexSize = 1\n  let arrLength = arr.length\n  let valLength = val.length\n\n  if (encoding !== undefined) {\n    encoding = String(encoding).toLowerCase()\n    if (encoding === 'ucs2' || encoding === 'ucs-2' ||\n        encoding === 'utf16le' || encoding === 'utf-16le') {\n      if (arr.length < 2 || val.length < 2) {\n        return -1\n      }\n      indexSize = 2\n      arrLength /= 2\n      valLength /= 2\n      byteOffset /= 2\n    }\n  }\n\n  function read (buf, i) {\n    if (indexSize === 1) {\n      return buf[i]\n    } else {\n      return buf.readUInt16BE(i * indexSize)\n    }\n  }\n\n  let i\n  if (dir) {\n    let foundIndex = -1\n    for (i = byteOffset; i < arrLength; i++) {\n      if (read(arr, i) === read(val, foundIndex === -1 ? 0 : i - foundIndex)) {\n        if (foundIndex === -1) foundIndex = i\n        if (i - foundIndex + 1 === valLength) return foundIndex * indexSize\n      } else {\n        if (foundIndex !== -1) i -= i - foundIndex\n        foundIndex = -1\n      }\n    }\n  } else {\n    if (byteOffset + valLength > arrLength) byteOffset = arrLength - valLength\n    for (i = byteOffset; i >= 0; i--) {\n      let found = true\n      for (let j = 0; j < valLength; j++) {\n        if (read(arr, i + j) !== read(val, j)) {\n          found = false\n          break\n        }\n      }\n      if (found) return i\n    }\n  }\n\n  return -1\n}\n\nBuffer.prototype.includes = function includes (val, byteOffset, encoding) {\n  return this.indexOf(val, byteOffset, encoding) !== -1\n}\n\nBuffer.prototype.indexOf = function indexOf (val, byteOffset, encoding) {\n  return bidirectionalIndexOf(this, val, byteOffset, encoding, true)\n}\n\nBuffer.prototype.lastIndexOf = function lastIndexOf (val, byteOffset, encoding) {\n  return bidirectionalIndexOf(this, val, byteOffset, encoding, false)\n}\n\nfunction hexWrite (buf, string, offset, length) {\n  offset = Number(offset) || 0\n  const remaining = buf.length - offset\n  if (!length) {\n    length = remaining\n  } else {\n    length = Number(length)\n    if (length > remaining) {\n      length = remaining\n    }\n  }\n\n  const strLen = string.length\n\n  if (length > strLen / 2) {\n    length = strLen / 2\n  }\n  let i\n  for (i = 0; i < length; ++i) {\n    const parsed = parseInt(string.substr(i * 2, 2), 16)\n    if (numberIsNaN(parsed)) return i\n    buf[offset + i] = parsed\n  }\n  return i\n}\n\nfunction utf8Write (buf, string, offset, length) {\n  return blitBuffer(utf8ToBytes(string, buf.length - offset), buf, offset, length)\n}\n\nfunction asciiWrite (buf, string, offset, length) {\n  return blitBuffer(asciiToBytes(string), buf, offset, length)\n}\n\nfunction base64Write (buf, string, offset, length) {\n  return blitBuffer(base64ToBytes(string), buf, offset, length)\n}\n\nfunction ucs2Write (buf, string, offset, length) {\n  return blitBuffer(utf16leToBytes(string, buf.length - offset), buf, offset, length)\n}\n\nBuffer.prototype.write = function write (string, offset, length, encoding) {\n  // Buffer#write(string)\n  if (offset === undefined) {\n    encoding = 'utf8'\n    length = this.length\n    offset = 0\n  // Buffer#write(string, encoding)\n  } else if (length === undefined && typeof offset === 'string') {\n    encoding = offset\n    length = this.length\n    offset = 0\n  // Buffer#write(string, offset[, length][, encoding])\n  } else if (isFinite(offset)) {\n    offset = offset >>> 0\n    if (isFinite(length)) {\n      length = length >>> 0\n      if (encoding === undefined) encoding = 'utf8'\n    } else {\n      encoding = length\n      length = undefined\n    }\n  } else {\n    throw new Error(\n      'Buffer.write(string, encoding, offset[, length]) is no longer supported'\n    )\n  }\n\n  const remaining = this.length - offset\n  if (length === undefined || length > remaining) length = remaining\n\n  if ((string.length > 0 && (length < 0 || offset < 0)) || offset > this.length) {\n    throw new RangeError('Attempt to write outside buffer bounds')\n  }\n\n  if (!encoding) encoding = 'utf8'\n\n  let loweredCase = false\n  for (;;) {\n    switch (encoding) {\n      case 'hex':\n        return hexWrite(this, string, offset, length)\n\n      case 'utf8':\n      case 'utf-8':\n        return utf8Write(this, string, offset, length)\n\n      case 'ascii':\n      case 'latin1':\n      case 'binary':\n        return asciiWrite(this, string, offset, length)\n\n      case 'base64':\n        // Warning: maxLength not taken into account in base64Write\n        return base64Write(this, string, offset, length)\n\n      case 'ucs2':\n      case 'ucs-2':\n      case 'utf16le':\n      case 'utf-16le':\n        return ucs2Write(this, string, offset, length)\n\n      default:\n        if (loweredCase) throw new TypeError('Unknown encoding: ' + encoding)\n        encoding = ('' + encoding).toLowerCase()\n        loweredCase = true\n    }\n  }\n}\n\nBuffer.prototype.toJSON = function toJSON () {\n  return {\n    type: 'Buffer',\n    data: Array.prototype.slice.call(this._arr || this, 0)\n  }\n}\n\nfunction base64Slice (buf, start, end) {\n  if (start === 0 && end === buf.length) {\n    return base64.fromByteArray(buf)\n  } else {\n    return base64.fromByteArray(buf.slice(start, end))\n  }\n}\n\nfunction utf8Slice (buf, start, end) {\n  end = Math.min(buf.length, end)\n  const res = []\n\n  let i = start\n  while (i < end) {\n    const firstByte = buf[i]\n    let codePoint = null\n    let bytesPerSequence = (firstByte > 0xEF)\n      ? 4\n      : (firstByte > 0xDF)\n          ? 3\n          : (firstByte > 0xBF)\n              ? 2\n              : 1\n\n    if (i + bytesPerSequence <= end) {\n      let secondByte, thirdByte, fourthByte, tempCodePoint\n\n      switch (bytesPerSequence) {\n        case 1:\n          if (firstByte < 0x80) {\n            codePoint = firstByte\n          }\n          break\n        case 2:\n          secondByte = buf[i + 1]\n          if ((secondByte & 0xC0) === 0x80) {\n            tempCodePoint = (firstByte & 0x1F) << 0x6 | (secondByte & 0x3F)\n            if (tempCodePoint > 0x7F) {\n              codePoint = tempCodePoint\n            }\n          }\n          break\n        case 3:\n          secondByte = buf[i + 1]\n          thirdByte = buf[i + 2]\n          if ((secondByte & 0xC0) === 0x80 && (thirdByte & 0xC0) === 0x80) {\n            tempCodePoint = (firstByte & 0xF) << 0xC | (secondByte & 0x3F) << 0x6 | (thirdByte & 0x3F)\n            if (tempCodePoint > 0x7FF && (tempCodePoint < 0xD800 || tempCodePoint > 0xDFFF)) {\n              codePoint = tempCodePoint\n            }\n          }\n          break\n        case 4:\n          secondByte = buf[i + 1]\n          thirdByte = buf[i + 2]\n          fourthByte = buf[i + 3]\n          if ((secondByte & 0xC0) === 0x80 && (thirdByte & 0xC0) === 0x80 && (fourthByte & 0xC0) === 0x80) {\n            tempCodePoint = (firstByte & 0xF) << 0x12 | (secondByte & 0x3F) << 0xC | (thirdByte & 0x3F) << 0x6 | (fourthByte & 0x3F)\n            if (tempCodePoint > 0xFFFF && tempCodePoint < 0x110000) {\n              codePoint = tempCodePoint\n            }\n          }\n      }\n    }\n\n    if (codePoint === null) {\n      // we did not generate a valid codePoint so insert a\n      // replacement char (U+FFFD) and advance only 1 byte\n      codePoint = 0xFFFD\n      bytesPerSequence = 1\n    } else if (codePoint > 0xFFFF) {\n      // encode to utf16 (surrogate pair dance)\n      codePoint -= 0x10000\n      res.push(codePoint >>> 10 & 0x3FF | 0xD800)\n      codePoint = 0xDC00 | codePoint & 0x3FF\n    }\n\n    res.push(codePoint)\n    i += bytesPerSequence\n  }\n\n  return decodeCodePointsArray(res)\n}\n\n// Based on http://stackoverflow.com/a/22747272/680742, the browser with\n// the lowest limit is Chrome, with 0x10000 args.\n// We go 1 magnitude less, for safety\nconst MAX_ARGUMENTS_LENGTH = 0x1000\n\nfunction decodeCodePointsArray (codePoints) {\n  const len = codePoints.length\n  if (len <= MAX_ARGUMENTS_LENGTH) {\n    return String.fromCharCode.apply(String, codePoints) // avoid extra slice()\n  }\n\n  // Decode in chunks to avoid \"call stack size exceeded\".\n  let res = ''\n  let i = 0\n  while (i < len) {\n    res += String.fromCharCode.apply(\n      String,\n      codePoints.slice(i, i += MAX_ARGUMENTS_LENGTH)\n    )\n  }\n  return res\n}\n\nfunction asciiSlice (buf, start, end) {\n  let ret = ''\n  end = Math.min(buf.length, end)\n\n  for (let i = start; i < end; ++i) {\n    ret += String.fromCharCode(buf[i] & 0x7F)\n  }\n  return ret\n}\n\nfunction latin1Slice (buf, start, end) {\n  let ret = ''\n  end = Math.min(buf.length, end)\n\n  for (let i = start; i < end; ++i) {\n    ret += String.fromCharCode(buf[i])\n  }\n  return ret\n}\n\nfunction hexSlice (buf, start, end) {\n  const len = buf.length\n\n  if (!start || start < 0) start = 0\n  if (!end || end < 0 || end > len) end = len\n\n  let out = ''\n  for (let i = start; i < end; ++i) {\n    out += hexSliceLookupTable[buf[i]]\n  }\n  return out\n}\n\nfunction utf16leSlice (buf, start, end) {\n  const bytes = buf.slice(start, end)\n  let res = ''\n  // If bytes.length is odd, the last 8 bits must be ignored (same as node.js)\n  for (let i = 0; i < bytes.length - 1; i += 2) {\n    res += String.fromCharCode(bytes[i] + (bytes[i + 1] * 256))\n  }\n  return res\n}\n\nBuffer.prototype.slice = function slice (start, end) {\n  const len = this.length\n  start = ~~start\n  end = end === undefined ? len : ~~end\n\n  if (start < 0) {\n    start += len\n    if (start < 0) start = 0\n  } else if (start > len) {\n    start = len\n  }\n\n  if (end < 0) {\n    end += len\n    if (end < 0) end = 0\n  } else if (end > len) {\n    end = len\n  }\n\n  if (end < start) end = start\n\n  const newBuf = this.subarray(start, end)\n  // Return an augmented `Uint8Array` instance\n  Object.setPrototypeOf(newBuf, Buffer.prototype)\n\n  return newBuf\n}\n\n/*\n * Need to make sure that buffer isn't trying to write out of bounds.\n */\nfunction checkOffset (offset, ext, length) {\n  if ((offset % 1) !== 0 || offset < 0) throw new RangeError('offset is not uint')\n  if (offset + ext > length) throw new RangeError('Trying to access beyond buffer length')\n}\n\nBuffer.prototype.readUintLE =\nBuffer.prototype.readUIntLE = function readUIntLE (offset, byteLength, noAssert) {\n  offset = offset >>> 0\n  byteLength = byteLength >>> 0\n  if (!noAssert) checkOffset(offset, byteLength, this.length)\n\n  let val = this[offset]\n  let mul = 1\n  let i = 0\n  while (++i < byteLength && (mul *= 0x100)) {\n    val += this[offset + i] * mul\n  }\n\n  return val\n}\n\nBuffer.prototype.readUintBE =\nBuffer.prototype.readUIntBE = function readUIntBE (offset, byteLength, noAssert) {\n  offset = offset >>> 0\n  byteLength = byteLength >>> 0\n  if (!noAssert) {\n    checkOffset(offset, byteLength, this.length)\n  }\n\n  let val = this[offset + --byteLength]\n  let mul = 1\n  while (byteLength > 0 && (mul *= 0x100)) {\n    val += this[offset + --byteLength] * mul\n  }\n\n  return val\n}\n\nBuffer.prototype.readUint8 =\nBuffer.prototype.readUInt8 = function readUInt8 (offset, noAssert) {\n  offset = offset >>> 0\n  if (!noAssert) checkOffset(offset, 1, this.length)\n  return this[offset]\n}\n\nBuffer.prototype.readUint16LE =\nBuffer.prototype.readUInt16LE = function readUInt16LE (offset, noAssert) {\n  offset = offset >>> 0\n  if (!noAssert) checkOffset(offset, 2, this.length)\n  return this[offset] | (this[offset + 1] << 8)\n}\n\nBuffer.prototype.readUint16BE =\nBuffer.prototype.readUInt16BE = function readUInt16BE (offset, noAssert) {\n  offset = offset >>> 0\n  if (!noAssert) checkOffset(offset, 2, this.length)\n  return (this[offset] << 8) | this[offset + 1]\n}\n\nBuffer.prototype.readUint32LE =\nBuffer.prototype.readUInt32LE = function readUInt32LE (offset, noAssert) {\n  offset = offset >>> 0\n  if (!noAssert) checkOffset(offset, 4, this.length)\n\n  return ((this[offset]) |\n      (this[offset + 1] << 8) |\n      (this[offset + 2] << 16)) +\n      (this[offset + 3] * 0x1000000)\n}\n\nBuffer.prototype.readUint32BE =\nBuffer.prototype.readUInt32BE = function readUInt32BE (offset, noAssert) {\n  offset = offset >>> 0\n  if (!noAssert) checkOffset(offset, 4, this.length)\n\n  return (this[offset] * 0x1000000) +\n    ((this[offset + 1] << 16) |\n    (this[offset + 2] << 8) |\n    this[offset + 3])\n}\n\nBuffer.prototype.readBigUInt64LE = defineBigIntMethod(function readBigUInt64LE (offset) {\n  offset = offset >>> 0\n  validateNumber(offset, 'offset')\n  const first = this[offset]\n  const last = this[offset + 7]\n  if (first === undefined || last === undefined) {\n    boundsError(offset, this.length - 8)\n  }\n\n  const lo = first +\n    this[++offset] * 2 ** 8 +\n    this[++offset] * 2 ** 16 +\n    this[++offset] * 2 ** 24\n\n  const hi = this[++offset] +\n    this[++offset] * 2 ** 8 +\n    this[++offset] * 2 ** 16 +\n    last * 2 ** 24\n\n  return BigInt(lo) + (BigInt(hi) << BigInt(32))\n})\n\nBuffer.prototype.readBigUInt64BE = defineBigIntMethod(function readBigUInt64BE (offset) {\n  offset = offset >>> 0\n  validateNumber(offset, 'offset')\n  const first = this[offset]\n  const last = this[offset + 7]\n  if (first === undefined || last === undefined) {\n    boundsError(offset, this.length - 8)\n  }\n\n  const hi = first * 2 ** 24 +\n    this[++offset] * 2 ** 16 +\n    this[++offset] * 2 ** 8 +\n    this[++offset]\n\n  const lo = this[++offset] * 2 ** 24 +\n    this[++offset] * 2 ** 16 +\n    this[++offset] * 2 ** 8 +\n    last\n\n  return (BigInt(hi) << BigInt(32)) + BigInt(lo)\n})\n\nBuffer.prototype.readIntLE = function readIntLE (offset, byteLength, noAssert) {\n  offset = offset >>> 0\n  byteLength = byteLength >>> 0\n  if (!noAssert) checkOffset(offset, byteLength, this.length)\n\n  let val = this[offset]\n  let mul = 1\n  let i = 0\n  while (++i < byteLength && (mul *= 0x100)) {\n    val += this[offset + i] * mul\n  }\n  mul *= 0x80\n\n  if (val >= mul) val -= Math.pow(2, 8 * byteLength)\n\n  return val\n}\n\nBuffer.prototype.readIntBE = function readIntBE (offset, byteLength, noAssert) {\n  offset = offset >>> 0\n  byteLength = byteLength >>> 0\n  if (!noAssert) checkOffset(offset, byteLength, this.length)\n\n  let i = byteLength\n  let mul = 1\n  let val = this[offset + --i]\n  while (i > 0 && (mul *= 0x100)) {\n    val += this[offset + --i] * mul\n  }\n  mul *= 0x80\n\n  if (val >= mul) val -= Math.pow(2, 8 * byteLength)\n\n  return val\n}\n\nBuffer.prototype.readInt8 = function readInt8 (offset, noAssert) {\n  offset = offset >>> 0\n  if (!noAssert) checkOffset(offset, 1, this.length)\n  if (!(this[offset] & 0x80)) return (this[offset])\n  return ((0xff - this[offset] + 1) * -1)\n}\n\nBuffer.prototype.readInt16LE = function readInt16LE (offset, noAssert) {\n  offset = offset >>> 0\n  if (!noAssert) checkOffset(offset, 2, this.length)\n  const val = this[offset] | (this[offset + 1] << 8)\n  return (val & 0x8000) ? val | 0xFFFF0000 : val\n}\n\nBuffer.prototype.readInt16BE = function readInt16BE (offset, noAssert) {\n  offset = offset >>> 0\n  if (!noAssert) checkOffset(offset, 2, this.length)\n  const val = this[offset + 1] | (this[offset] << 8)\n  return (val & 0x8000) ? val | 0xFFFF0000 : val\n}\n\nBuffer.prototype.readInt32LE = function readInt32LE (offset, noAssert) {\n  offset = offset >>> 0\n  if (!noAssert) checkOffset(offset, 4, this.length)\n\n  return (this[offset]) |\n    (this[offset + 1] << 8) |\n    (this[offset + 2] << 16) |\n    (this[offset + 3] << 24)\n}\n\nBuffer.prototype.readInt32BE = function readInt32BE (offset, noAssert) {\n  offset = offset >>> 0\n  if (!noAssert) checkOffset(offset, 4, this.length)\n\n  return (this[offset] << 24) |\n    (this[offset + 1] << 16) |\n    (this[offset + 2] << 8) |\n    (this[offset + 3])\n}\n\nBuffer.prototype.readBigInt64LE = defineBigIntMethod(function readBigInt64LE (offset) {\n  offset = offset >>> 0\n  validateNumber(offset, 'offset')\n  const first = this[offset]\n  const last = this[offset + 7]\n  if (first === undefined || last === undefined) {\n    boundsError(offset, this.length - 8)\n  }\n\n  const val = this[offset + 4] +\n    this[offset + 5] * 2 ** 8 +\n    this[offset + 6] * 2 ** 16 +\n    (last << 24) // Overflow\n\n  return (BigInt(val) << BigInt(32)) +\n    BigInt(first +\n    this[++offset] * 2 ** 8 +\n    this[++offset] * 2 ** 16 +\n    this[++offset] * 2 ** 24)\n})\n\nBuffer.prototype.readBigInt64BE = defineBigIntMethod(function readBigInt64BE (offset) {\n  offset = offset >>> 0\n  validateNumber(offset, 'offset')\n  const first = this[offset]\n  const last = this[offset + 7]\n  if (first === undefined || last === undefined) {\n    boundsError(offset, this.length - 8)\n  }\n\n  const val = (first << 24) + // Overflow\n    this[++offset] * 2 ** 16 +\n    this[++offset] * 2 ** 8 +\n    this[++offset]\n\n  return (BigInt(val) << BigInt(32)) +\n    BigInt(this[++offset] * 2 ** 24 +\n    this[++offset] * 2 ** 16 +\n    this[++offset] * 2 ** 8 +\n    last)\n})\n\nBuffer.prototype.readFloatLE = function readFloatLE (offset, noAssert) {\n  offset = offset >>> 0\n  if (!noAssert) checkOffset(offset, 4, this.length)\n  return ieee754.read(this, offset, true, 23, 4)\n}\n\nBuffer.prototype.readFloatBE = function readFloatBE (offset, noAssert) {\n  offset = offset >>> 0\n  if (!noAssert) checkOffset(offset, 4, this.length)\n  return ieee754.read(this, offset, false, 23, 4)\n}\n\nBuffer.prototype.readDoubleLE = function readDoubleLE (offset, noAssert) {\n  offset = offset >>> 0\n  if (!noAssert) checkOffset(offset, 8, this.length)\n  return ieee754.read(this, offset, true, 52, 8)\n}\n\nBuffer.prototype.readDoubleBE = function readDoubleBE (offset, noAssert) {\n  offset = offset >>> 0\n  if (!noAssert) checkOffset(offset, 8, this.length)\n  return ieee754.read(this, offset, false, 52, 8)\n}\n\nfunction checkInt (buf, value, offset, ext, max, min) {\n  if (!Buffer.isBuffer(buf)) throw new TypeError('\"buffer\" argument must be a Buffer instance')\n  if (value > max || value < min) throw new RangeError('\"value\" argument is out of bounds')\n  if (offset + ext > buf.length) throw new RangeError('Index out of range')\n}\n\nBuffer.prototype.writeUintLE =\nBuffer.prototype.writeUIntLE = function writeUIntLE (value, offset, byteLength, noAssert) {\n  value = +value\n  offset = offset >>> 0\n  byteLength = byteLength >>> 0\n  if (!noAssert) {\n    const maxBytes = Math.pow(2, 8 * byteLength) - 1\n    checkInt(this, value, offset, byteLength, maxBytes, 0)\n  }\n\n  let mul = 1\n  let i = 0\n  this[offset] = value & 0xFF\n  while (++i < byteLength && (mul *= 0x100)) {\n    this[offset + i] = (value / mul) & 0xFF\n  }\n\n  return offset + byteLength\n}\n\nBuffer.prototype.writeUintBE =\nBuffer.prototype.writeUIntBE = function writeUIntBE (value, offset, byteLength, noAssert) {\n  value = +value\n  offset = offset >>> 0\n  byteLength = byteLength >>> 0\n  if (!noAssert) {\n    const maxBytes = Math.pow(2, 8 * byteLength) - 1\n    checkInt(this, value, offset, byteLength, maxBytes, 0)\n  }\n\n  let i = byteLength - 1\n  let mul = 1\n  this[offset + i] = value & 0xFF\n  while (--i >= 0 && (mul *= 0x100)) {\n    this[offset + i] = (value / mul) & 0xFF\n  }\n\n  return offset + byteLength\n}\n\nBuffer.prototype.writeUint8 =\nBuffer.prototype.writeUInt8 = function writeUInt8 (value, offset, noAssert) {\n  value = +value\n  offset = offset >>> 0\n  if (!noAssert) checkInt(this, value, offset, 1, 0xff, 0)\n  this[offset] = (value & 0xff)\n  return offset + 1\n}\n\nBuffer.prototype.writeUint16LE =\nBuffer.prototype.writeUInt16LE = function writeUInt16LE (value, offset, noAssert) {\n  value = +value\n  offset = offset >>> 0\n  if (!noAssert) checkInt(this, value, offset, 2, 0xffff, 0)\n  this[offset] = (value & 0xff)\n  this[offset + 1] = (value >>> 8)\n  return offset + 2\n}\n\nBuffer.prototype.writeUint16BE =\nBuffer.prototype.writeUInt16BE = function writeUInt16BE (value, offset, noAssert) {\n  value = +value\n  offset = offset >>> 0\n  if (!noAssert) checkInt(this, value, offset, 2, 0xffff, 0)\n  this[offset] = (value >>> 8)\n  this[offset + 1] = (value & 0xff)\n  return offset + 2\n}\n\nBuffer.prototype.writeUint32LE =\nBuffer.prototype.writeUInt32LE = function writeUInt32LE (value, offset, noAssert) {\n  value = +value\n  offset = offset >>> 0\n  if (!noAssert) checkInt(this, value, offset, 4, 0xffffffff, 0)\n  this[offset + 3] = (value >>> 24)\n  this[offset + 2] = (value >>> 16)\n  this[offset + 1] = (value >>> 8)\n  this[offset] = (value & 0xff)\n  return offset + 4\n}\n\nBuffer.prototype.writeUint32BE =\nBuffer.prototype.writeUInt32BE = function writeUInt32BE (value, offset, noAssert) {\n  value = +value\n  offset = offset >>> 0\n  if (!noAssert) checkInt(this, value, offset, 4, 0xffffffff, 0)\n  this[offset] = (value >>> 24)\n  this[offset + 1] = (value >>> 16)\n  this[offset + 2] = (value >>> 8)\n  this[offset + 3] = (value & 0xff)\n  return offset + 4\n}\n\nfunction wrtBigUInt64LE (buf, value, offset, min, max) {\n  checkIntBI(value, min, max, buf, offset, 7)\n\n  let lo = Number(value & BigInt(0xffffffff))\n  buf[offset++] = lo\n  lo = lo >> 8\n  buf[offset++] = lo\n  lo = lo >> 8\n  buf[offset++] = lo\n  lo = lo >> 8\n  buf[offset++] = lo\n  let hi = Number(value >> BigInt(32) & BigInt(0xffffffff))\n  buf[offset++] = hi\n  hi = hi >> 8\n  buf[offset++] = hi\n  hi = hi >> 8\n  buf[offset++] = hi\n  hi = hi >> 8\n  buf[offset++] = hi\n  return offset\n}\n\nfunction wrtBigUInt64BE (buf, value, offset, min, max) {\n  checkIntBI(value, min, max, buf, offset, 7)\n\n  let lo = Number(value & BigInt(0xffffffff))\n  buf[offset + 7] = lo\n  lo = lo >> 8\n  buf[offset + 6] = lo\n  lo = lo >> 8\n  buf[offset + 5] = lo\n  lo = lo >> 8\n  buf[offset + 4] = lo\n  let hi = Number(value >> BigInt(32) & BigInt(0xffffffff))\n  buf[offset + 3] = hi\n  hi = hi >> 8\n  buf[offset + 2] = hi\n  hi = hi >> 8\n  buf[offset + 1] = hi\n  hi = hi >> 8\n  buf[offset] = hi\n  return offset + 8\n}\n\nBuffer.prototype.writeBigUInt64LE = defineBigIntMethod(function writeBigUInt64LE (value, offset = 0) {\n  return wrtBigUInt64LE(this, value, offset, BigInt(0), BigInt('0xffffffffffffffff'))\n})\n\nBuffer.prototype.writeBigUInt64BE = defineBigIntMethod(function writeBigUInt64BE (value, offset = 0) {\n  return wrtBigUInt64BE(this, value, offset, BigInt(0), BigInt('0xffffffffffffffff'))\n})\n\nBuffer.prototype.writeIntLE = function writeIntLE (value, offset, byteLength, noAssert) {\n  value = +value\n  offset = offset >>> 0\n  if (!noAssert) {\n    const limit = Math.pow(2, (8 * byteLength) - 1)\n\n    checkInt(this, value, offset, byteLength, limit - 1, -limit)\n  }\n\n  let i = 0\n  let mul = 1\n  let sub = 0\n  this[offset] = value & 0xFF\n  while (++i < byteLength && (mul *= 0x100)) {\n    if (value < 0 && sub === 0 && this[offset + i - 1] !== 0) {\n      sub = 1\n    }\n    this[offset + i] = ((value / mul) >> 0) - sub & 0xFF\n  }\n\n  return offset + byteLength\n}\n\nBuffer.prototype.writeIntBE = function writeIntBE (value, offset, byteLength, noAssert) {\n  value = +value\n  offset = offset >>> 0\n  if (!noAssert) {\n    const limit = Math.pow(2, (8 * byteLength) - 1)\n\n    checkInt(this, value, offset, byteLength, limit - 1, -limit)\n  }\n\n  let i = byteLength - 1\n  let mul = 1\n  let sub = 0\n  this[offset + i] = value & 0xFF\n  while (--i >= 0 && (mul *= 0x100)) {\n    if (value < 0 && sub === 0 && this[offset + i + 1] !== 0) {\n      sub = 1\n    }\n    this[offset + i] = ((value / mul) >> 0) - sub & 0xFF\n  }\n\n  return offset + byteLength\n}\n\nBuffer.prototype.writeInt8 = function writeInt8 (value, offset, noAssert) {\n  value = +value\n  offset = offset >>> 0\n  if (!noAssert) checkInt(this, value, offset, 1, 0x7f, -0x80)\n  if (value < 0) value = 0xff + value + 1\n  this[offset] = (value & 0xff)\n  return offset + 1\n}\n\nBuffer.prototype.writeInt16LE = function writeInt16LE (value, offset, noAssert) {\n  value = +value\n  offset = offset >>> 0\n  if (!noAssert) checkInt(this, value, offset, 2, 0x7fff, -0x8000)\n  this[offset] = (value & 0xff)\n  this[offset + 1] = (value >>> 8)\n  return offset + 2\n}\n\nBuffer.prototype.writeInt16BE = function writeInt16BE (value, offset, noAssert) {\n  value = +value\n  offset = offset >>> 0\n  if (!noAssert) checkInt(this, value, offset, 2, 0x7fff, -0x8000)\n  this[offset] = (value >>> 8)\n  this[offset + 1] = (value & 0xff)\n  return offset + 2\n}\n\nBuffer.prototype.writeInt32LE = function writeInt32LE (value, offset, noAssert) {\n  value = +value\n  offset = offset >>> 0\n  if (!noAssert) checkInt(this, value, offset, 4, 0x7fffffff, -0x80000000)\n  this[offset] = (value & 0xff)\n  this[offset + 1] = (value >>> 8)\n  this[offset + 2] = (value >>> 16)\n  this[offset + 3] = (value >>> 24)\n  return offset + 4\n}\n\nBuffer.prototype.writeInt32BE = function writeInt32BE (value, offset, noAssert) {\n  value = +value\n  offset = offset >>> 0\n  if (!noAssert) checkInt(this, value, offset, 4, 0x7fffffff, -0x80000000)\n  if (value < 0) value = 0xffffffff + value + 1\n  this[offset] = (value >>> 24)\n  this[offset + 1] = (value >>> 16)\n  this[offset + 2] = (value >>> 8)\n  this[offset + 3] = (value & 0xff)\n  return offset + 4\n}\n\nBuffer.prototype.writeBigInt64LE = defineBigIntMethod(function writeBigInt64LE (value, offset = 0) {\n  return wrtBigUInt64LE(this, value, offset, -BigInt('0x8000000000000000'), BigInt('0x7fffffffffffffff'))\n})\n\nBuffer.prototype.writeBigInt64BE = defineBigIntMethod(function writeBigInt64BE (value, offset = 0) {\n  return wrtBigUInt64BE(this, value, offset, -BigInt('0x8000000000000000'), BigInt('0x7fffffffffffffff'))\n})\n\nfunction checkIEEE754 (buf, value, offset, ext, max, min) {\n  if (offset + ext > buf.length) throw new RangeError('Index out of range')\n  if (offset < 0) throw new RangeError('Index out of range')\n}\n\nfunction writeFloat (buf, value, offset, littleEndian, noAssert) {\n  value = +value\n  offset = offset >>> 0\n  if (!noAssert) {\n    checkIEEE754(buf, value, offset, 4, 3.4028234663852886e+38, -3.4028234663852886e+38)\n  }\n  ieee754.write(buf, value, offset, littleEndian, 23, 4)\n  return offset + 4\n}\n\nBuffer.prototype.writeFloatLE = function writeFloatLE (value, offset, noAssert) {\n  return writeFloat(this, value, offset, true, noAssert)\n}\n\nBuffer.prototype.writeFloatBE = function writeFloatBE (value, offset, noAssert) {\n  return writeFloat(this, value, offset, false, noAssert)\n}\n\nfunction writeDouble (buf, value, offset, littleEndian, noAssert) {\n  value = +value\n  offset = offset >>> 0\n  if (!noAssert) {\n    checkIEEE754(buf, value, offset, 8, 1.7976931348623157E+308, -1.7976931348623157E+308)\n  }\n  ieee754.write(buf, value, offset, littleEndian, 52, 8)\n  return offset + 8\n}\n\nBuffer.prototype.writeDoubleLE = function writeDoubleLE (value, offset, noAssert) {\n  return writeDouble(this, value, offset, true, noAssert)\n}\n\nBuffer.prototype.writeDoubleBE = function writeDoubleBE (value, offset, noAssert) {\n  return writeDouble(this, value, offset, false, noAssert)\n}\n\n// copy(targetBuffer, targetStart=0, sourceStart=0, sourceEnd=buffer.length)\nBuffer.prototype.copy = function copy (target, targetStart, start, end) {\n  if (!Buffer.isBuffer(target)) throw new TypeError('argument should be a Buffer')\n  if (!start) start = 0\n  if (!end && end !== 0) end = this.length\n  if (targetStart >= target.length) targetStart = target.length\n  if (!targetStart) targetStart = 0\n  if (end > 0 && end < start) end = start\n\n  // Copy 0 bytes; we're done\n  if (end === start) return 0\n  if (target.length === 0 || this.length === 0) return 0\n\n  // Fatal error conditions\n  if (targetStart < 0) {\n    throw new RangeError('targetStart out of bounds')\n  }\n  if (start < 0 || start >= this.length) throw new RangeError('Index out of range')\n  if (end < 0) throw new RangeError('sourceEnd out of bounds')\n\n  // Are we oob?\n  if (end > this.length) end = this.length\n  if (target.length - targetStart < end - start) {\n    end = target.length - targetStart + start\n  }\n\n  const len = end - start\n\n  if (this === target && typeof Uint8Array.prototype.copyWithin === 'function') {\n    // Use built-in when available, missing from IE11\n    this.copyWithin(targetStart, start, end)\n  } else {\n    Uint8Array.prototype.set.call(\n      target,\n      this.subarray(start, end),\n      targetStart\n    )\n  }\n\n  return len\n}\n\n// Usage:\n//    buffer.fill(number[, offset[, end]])\n//    buffer.fill(buffer[, offset[, end]])\n//    buffer.fill(string[, offset[, end]][, encoding])\nBuffer.prototype.fill = function fill (val, start, end, encoding) {\n  // Handle string cases:\n  if (typeof val === 'string') {\n    if (typeof start === 'string') {\n      encoding = start\n      start = 0\n      end = this.length\n    } else if (typeof end === 'string') {\n      encoding = end\n      end = this.length\n    }\n    if (encoding !== undefined && typeof encoding !== 'string') {\n      throw new TypeError('encoding must be a string')\n    }\n    if (typeof encoding === 'string' && !Buffer.isEncoding(encoding)) {\n      throw new TypeError('Unknown encoding: ' + encoding)\n    }\n    if (val.length === 1) {\n      const code = val.charCodeAt(0)\n      if ((encoding === 'utf8' && code < 128) ||\n          encoding === 'latin1') {\n        // Fast path: If `val` fits into a single byte, use that numeric value.\n        val = code\n      }\n    }\n  } else if (typeof val === 'number') {\n    val = val & 255\n  } else if (typeof val === 'boolean') {\n    val = Number(val)\n  }\n\n  // Invalid ranges are not set to a default, so can range check early.\n  if (start < 0 || this.length < start || this.length < end) {\n    throw new RangeError('Out of range index')\n  }\n\n  if (end <= start) {\n    return this\n  }\n\n  start = start >>> 0\n  end = end === undefined ? this.length : end >>> 0\n\n  if (!val) val = 0\n\n  let i\n  if (typeof val === 'number') {\n    for (i = start; i < end; ++i) {\n      this[i] = val\n    }\n  } else {\n    const bytes = Buffer.isBuffer(val)\n      ? val\n      : Buffer.from(val, encoding)\n    const len = bytes.length\n    if (len === 0) {\n      throw new TypeError('The value \"' + val +\n        '\" is invalid for argument \"value\"')\n    }\n    for (i = 0; i < end - start; ++i) {\n      this[i + start] = bytes[i % len]\n    }\n  }\n\n  return this\n}\n\n// CUSTOM ERRORS\n// =============\n\n// Simplified versions from Node, changed for Buffer-only usage\nconst errors = {}\nfunction E (sym, getMessage, Base) {\n  errors[sym] = class NodeError extends Base {\n    constructor () {\n      super()\n\n      Object.defineProperty(this, 'message', {\n        value: getMessage.apply(this, arguments),\n        writable: true,\n        configurable: true\n      })\n\n      // Add the error code to the name to include it in the stack trace.\n      this.name = `${this.name} [${sym}]`\n      // Access the stack to generate the error message including the error code\n      // from the name.\n      this.stack // eslint-disable-line no-unused-expressions\n      // Reset the name to the actual name.\n      delete this.name\n    }\n\n    get code () {\n      return sym\n    }\n\n    set code (value) {\n      Object.defineProperty(this, 'code', {\n        configurable: true,\n        enumerable: true,\n        value,\n        writable: true\n      })\n    }\n\n    toString () {\n      return `${this.name} [${sym}]: ${this.message}`\n    }\n  }\n}\n\nE('ERR_BUFFER_OUT_OF_BOUNDS',\n  function (name) {\n    if (name) {\n      return `${name} is outside of buffer bounds`\n    }\n\n    return 'Attempt to access memory outside buffer bounds'\n  }, RangeError)\nE('ERR_INVALID_ARG_TYPE',\n  function (name, actual) {\n    return `The \"${name}\" argument must be of type number. Received type ${typeof actual}`\n  }, TypeError)\nE('ERR_OUT_OF_RANGE',\n  function (str, range, input) {\n    let msg = `The value of \"${str}\" is out of range.`\n    let received = input\n    if (Number.isInteger(input) && Math.abs(input) > 2 ** 32) {\n      received = addNumericalSeparator(String(input))\n    } else if (typeof input === 'bigint') {\n      received = String(input)\n      if (input > BigInt(2) ** BigInt(32) || input < -(BigInt(2) ** BigInt(32))) {\n        received = addNumericalSeparator(received)\n      }\n      received += 'n'\n    }\n    msg += ` It must be ${range}. Received ${received}`\n    return msg\n  }, RangeError)\n\nfunction addNumericalSeparator (val) {\n  let res = ''\n  let i = val.length\n  const start = val[0] === '-' ? 1 : 0\n  for (; i >= start + 4; i -= 3) {\n    res = `_${val.slice(i - 3, i)}${res}`\n  }\n  return `${val.slice(0, i)}${res}`\n}\n\n// CHECK FUNCTIONS\n// ===============\n\nfunction checkBounds (buf, offset, byteLength) {\n  validateNumber(offset, 'offset')\n  if (buf[offset] === undefined || buf[offset + byteLength] === undefined) {\n    boundsError(offset, buf.length - (byteLength + 1))\n  }\n}\n\nfunction checkIntBI (value, min, max, buf, offset, byteLength) {\n  if (value > max || value < min) {\n    const n = typeof min === 'bigint' ? 'n' : ''\n    let range\n    if (byteLength > 3) {\n      if (min === 0 || min === BigInt(0)) {\n        range = `>= 0${n} and < 2${n} ** ${(byteLength + 1) * 8}${n}`\n      } else {\n        range = `>= -(2${n} ** ${(byteLength + 1) * 8 - 1}${n}) and < 2 ** ` +\n                `${(byteLength + 1) * 8 - 1}${n}`\n      }\n    } else {\n      range = `>= ${min}${n} and <= ${max}${n}`\n    }\n    throw new errors.ERR_OUT_OF_RANGE('value', range, value)\n  }\n  checkBounds(buf, offset, byteLength)\n}\n\nfunction validateNumber (value, name) {\n  if (typeof value !== 'number') {\n    throw new errors.ERR_INVALID_ARG_TYPE(name, 'number', value)\n  }\n}\n\nfunction boundsError (value, length, type) {\n  if (Math.floor(value) !== value) {\n    validateNumber(value, type)\n    throw new errors.ERR_OUT_OF_RANGE(type || 'offset', 'an integer', value)\n  }\n\n  if (length < 0) {\n    throw new errors.ERR_BUFFER_OUT_OF_BOUNDS()\n  }\n\n  throw new errors.ERR_OUT_OF_RANGE(type || 'offset',\n                                    `>= ${type ? 1 : 0} and <= ${length}`,\n                                    value)\n}\n\n// HELPER FUNCTIONS\n// ================\n\nconst INVALID_BASE64_RE = /[^+/0-9A-Za-z-_]/g\n\nfunction base64clean (str) {\n  // Node takes equal signs as end of the Base64 encoding\n  str = str.split('=')[0]\n  // Node strips out invalid characters like \\n and \\t from the string, base64-js does not\n  str = str.trim().replace(INVALID_BASE64_RE, '')\n  // Node converts strings with length < 2 to ''\n  if (str.length < 2) return ''\n  // Node allows for non-padded base64 strings (missing trailing ===), base64-js does not\n  while (str.length % 4 !== 0) {\n    str = str + '='\n  }\n  return str\n}\n\nfunction utf8ToBytes (string, units) {\n  units = units || Infinity\n  let codePoint\n  const length = string.length\n  let leadSurrogate = null\n  const bytes = []\n\n  for (let i = 0; i < length; ++i) {\n    codePoint = string.charCodeAt(i)\n\n    // is surrogate component\n    if (codePoint > 0xD7FF && codePoint < 0xE000) {\n      // last char was a lead\n      if (!leadSurrogate) {\n        // no lead yet\n        if (codePoint > 0xDBFF) {\n          // unexpected trail\n          if ((units -= 3) > -1) bytes.push(0xEF, 0xBF, 0xBD)\n          continue\n        } else if (i + 1 === length) {\n          // unpaired lead\n          if ((units -= 3) > -1) bytes.push(0xEF, 0xBF, 0xBD)\n          continue\n        }\n\n        // valid lead\n        leadSurrogate = codePoint\n\n        continue\n      }\n\n      // 2 leads in a row\n      if (codePoint < 0xDC00) {\n        if ((units -= 3) > -1) bytes.push(0xEF, 0xBF, 0xBD)\n        leadSurrogate = codePoint\n        continue\n      }\n\n      // valid surrogate pair\n      codePoint = (leadSurrogate - 0xD800 << 10 | codePoint - 0xDC00) + 0x10000\n    } else if (leadSurrogate) {\n      // valid bmp char, but last char was a lead\n      if ((units -= 3) > -1) bytes.push(0xEF, 0xBF, 0xBD)\n    }\n\n    leadSurrogate = null\n\n    // encode utf8\n    if (codePoint < 0x80) {\n      if ((units -= 1) < 0) break\n      bytes.push(codePoint)\n    } else if (codePoint < 0x800) {\n      if ((units -= 2) < 0) break\n      bytes.push(\n        codePoint >> 0x6 | 0xC0,\n        codePoint & 0x3F | 0x80\n      )\n    } else if (codePoint < 0x10000) {\n      if ((units -= 3) < 0) break\n      bytes.push(\n        codePoint >> 0xC | 0xE0,\n        codePoint >> 0x6 & 0x3F | 0x80,\n        codePoint & 0x3F | 0x80\n      )\n    } else if (codePoint < 0x110000) {\n      if ((units -= 4) < 0) break\n      bytes.push(\n        codePoint >> 0x12 | 0xF0,\n        codePoint >> 0xC & 0x3F | 0x80,\n        codePoint >> 0x6 & 0x3F | 0x80,\n        codePoint & 0x3F | 0x80\n      )\n    } else {\n      throw new Error('Invalid code point')\n    }\n  }\n\n  return bytes\n}\n\nfunction asciiToBytes (str) {\n  const byteArray = []\n  for (let i = 0; i < str.length; ++i) {\n    // Node's code seems to be doing this and not & 0x7F..\n    byteArray.push(str.charCodeAt(i) & 0xFF)\n  }\n  return byteArray\n}\n\nfunction utf16leToBytes (str, units) {\n  let c, hi, lo\n  const byteArray = []\n  for (let i = 0; i < str.length; ++i) {\n    if ((units -= 2) < 0) break\n\n    c = str.charCodeAt(i)\n    hi = c >> 8\n    lo = c % 256\n    byteArray.push(lo)\n    byteArray.push(hi)\n  }\n\n  return byteArray\n}\n\nfunction base64ToBytes (str) {\n  return base64.toByteArray(base64clean(str))\n}\n\nfunction blitBuffer (src, dst, offset, length) {\n  let i\n  for (i = 0; i < length; ++i) {\n    if ((i + offset >= dst.length) || (i >= src.length)) break\n    dst[i + offset] = src[i]\n  }\n  return i\n}\n\n// ArrayBuffer or Uint8Array objects from other contexts (i.e. iframes) do not pass\n// the `instanceof` check but they should be treated as of that type.\n// See: https://github.com/feross/buffer/issues/166\nfunction isInstance (obj, type) {\n  return obj instanceof type ||\n    (obj != null && obj.constructor != null && obj.constructor.name != null &&\n      obj.constructor.name === type.name)\n}\nfunction numberIsNaN (obj) {\n  // For IE11 support\n  return obj !== obj // eslint-disable-line no-self-compare\n}\n\n// Create lookup table for `toString('hex')`\n// See: https://github.com/feross/buffer/issues/219\nconst hexSliceLookupTable = (function () {\n  const alphabet = '0123456789abcdef'\n  const table = new Array(256)\n  for (let i = 0; i < 16; ++i) {\n    const i16 = i * 16\n    for (let j = 0; j < 16; ++j) {\n      table[i16 + j] = alphabet[i] + alphabet[j]\n    }\n  }\n  return table\n})()\n\n// Return not function with Error if BigInt not supported\nfunction defineBigIntMethod (fn) {\n  return typeof BigInt === 'undefined' ? BufferBigIntNotDefined : fn\n}\n\nfunction BufferBigIntNotDefined () {\n  throw new Error('BigInt not supported')\n}\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\n// eslint-disable-next-line @typescript-eslint/triple-slash-reference\n/// <reference path=\"../shims-public.d.ts\" />\n\ntype AbortEventListener = (this: AbortSignalLike, ev?: any) => any;\n\nconst listenersMap = new WeakMap<AbortSignal, AbortEventListener[]>();\nconst abortedMap = new WeakMap<AbortSignal, boolean>();\n\n/**\n * Allows the request to be aborted upon firing of the \"abort\" event.\n * Compatible with the browser built-in AbortSignal and common polyfills.\n */\nexport interface AbortSignalLike {\n  /**\n   * Indicates if the signal has already been aborted.\n   */\n  readonly aborted: boolean;\n  /**\n   * Add new \"abort\" event listener, only support \"abort\" event.\n   */\n  addEventListener(\n    type: \"abort\",\n    listener: (this: AbortSignalLike, ev: any) => any,\n    options?: any\n  ): void;\n  /**\n   * Remove \"abort\" event listener, only support \"abort\" event.\n   */\n  removeEventListener(\n    type: \"abort\",\n    listener: (this: AbortSignalLike, ev: any) => any,\n    options?: any\n  ): void;\n}\n\n/**\n * An aborter instance implements AbortSignal interface, can abort HTTP requests.\n *\n * - Call AbortSignal.none to create a new AbortSignal instance that cannot be cancelled.\n * Use `AbortSignal.none` when you are required to pass a cancellation token but the operation\n * cannot or will not ever be cancelled.\n *\n * @example\n * Abort without timeout\n * ```ts\n * await doAsyncWork(AbortSignal.none);\n * ```\n */\nexport class AbortSignal implements AbortSignalLike {\n  constructor() {\n    listenersMap.set(this, []);\n    abortedMap.set(this, false);\n  }\n\n  /**\n   * Status of whether aborted or not.\n   *\n   * @readonly\n   */\n  public get aborted(): boolean {\n    if (!abortedMap.has(this)) {\n      throw new TypeError(\"Expected `this` to be an instance of AbortSignal.\");\n    }\n\n    return abortedMap.get(this)!;\n  }\n\n  /**\n   * Creates a new AbortSignal instance that will never be aborted.\n   *\n   * @readonly\n   */\n  public static get none(): AbortSignal {\n    return new AbortSignal();\n  }\n\n  /**\n   * onabort event listener.\n   */\n  public onabort: ((ev?: Event) => any) | null = null;\n\n  /**\n   * Added new \"abort\" event listener, only support \"abort\" event.\n   *\n   * @param _type - Only support \"abort\" event\n   * @param listener - The listener to be added\n   */\n  public addEventListener(\n    // tslint:disable-next-line:variable-name\n    _type: \"abort\",\n    listener: (this: AbortSignalLike, ev: any) => any\n  ): void {\n    if (!listenersMap.has(this)) {\n      throw new TypeError(\"Expected `this` to be an instance of AbortSignal.\");\n    }\n\n    const listeners = listenersMap.get(this)!;\n    listeners.push(listener);\n  }\n\n  /**\n   * Remove \"abort\" event listener, only support \"abort\" event.\n   *\n   * @param _type - Only support \"abort\" event\n   * @param listener - The listener to be removed\n   */\n  public removeEventListener(\n    // tslint:disable-next-line:variable-name\n    _type: \"abort\",\n    listener: (this: AbortSignalLike, ev: any) => any\n  ): void {\n    if (!listenersMap.has(this)) {\n      throw new TypeError(\"Expected `this` to be an instance of AbortSignal.\");\n    }\n\n    const listeners = listenersMap.get(this)!;\n\n    const index = listeners.indexOf(listener);\n    if (index > -1) {\n      listeners.splice(index, 1);\n    }\n  }\n\n  /**\n   * Dispatches a synthetic event to the AbortSignal.\n   */\n  dispatchEvent(_event: Event): boolean {\n    throw new Error(\n      \"This is a stub dispatchEvent implementation that should not be used.  It only exists for type-checking purposes.\"\n    );\n  }\n}\n\n/**\n * Helper to trigger an abort event immediately, the onabort and all abort event listeners will be triggered.\n * Will try to trigger abort event for all linked AbortSignal nodes.\n *\n * - If there is a timeout, the timer will be cancelled.\n * - If aborted is true, nothing will happen.\n *\n * @internal\n */\n// eslint-disable-next-line @azure/azure-sdk/ts-use-interface-parameters\nexport function abortSignal(signal: AbortSignal): void {\n  if (signal.aborted) {\n    return;\n  }\n\n  if (signal.onabort) {\n    signal.onabort.call(signal);\n  }\n\n  const listeners = listenersMap.get(signal)!;\n  if (listeners) {\n    // Create a copy of listeners so mutations to the array\n    // (e.g. via removeListener calls) don't affect the listeners\n    // we invoke.\n    listeners.slice().forEach((listener) => {\n      listener.call(signal, { type: \"abort\" });\n    });\n  }\n\n  abortedMap.set(signal, true);\n}\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { AbortSignal, AbortSignalLike, abortSignal } from \"./AbortSignal\";\n\n/**\n * This error is thrown when an asynchronous operation has been aborted.\n * Check for this error by testing the `name` that the name property of the\n * error matches `\"AbortError\"`.\n *\n * @example\n * ```ts\n * const controller = new AbortController();\n * controller.abort();\n * try {\n *   doAsyncWork(controller.signal)\n * } catch (e) {\n *   if (e.name === 'AbortError') {\n *     // handle abort error here.\n *   }\n * }\n * ```\n */\nexport class AbortError extends Error {\n  constructor(message?: string) {\n    super(message);\n    this.name = \"AbortError\";\n  }\n}\n\n/**\n * An AbortController provides an AbortSignal and the associated controls to signal\n * that an asynchronous operation should be aborted.\n *\n * @example\n * Abort an operation when another event fires\n * ```ts\n * const controller = new AbortController();\n * const signal = controller.signal;\n * doAsyncWork(signal);\n * button.addEventListener('click', () => controller.abort());\n * ```\n *\n * @example\n * Share aborter cross multiple operations in 30s\n * ```ts\n * // Upload the same data to 2 different data centers at the same time,\n * // abort another when any of them is finished\n * const controller = AbortController.withTimeout(30 * 1000);\n * doAsyncWork(controller.signal).then(controller.abort);\n * doAsyncWork(controller.signal).then(controller.abort);\n *```\n *\n * @example\n * Cascaded aborting\n * ```ts\n * // All operations can't take more than 30 seconds\n * const aborter = Aborter.timeout(30 * 1000);\n *\n * // Following 2 operations can't take more than 25 seconds\n * await doAsyncWork(aborter.withTimeout(25 * 1000));\n * await doAsyncWork(aborter.withTimeout(25 * 1000));\n * ```\n */\nexport class AbortController {\n  private _signal: AbortSignal;\n\n  /**\n   * @param parentSignals - The AbortSignals that will signal aborted on the AbortSignal associated with this controller.\n   */\n  constructor(parentSignals?: AbortSignalLike[]);\n  /**\n   * @param parentSignals - The AbortSignals that will signal aborted on the AbortSignal associated with this controller.\n   */\n  constructor(...parentSignals: AbortSignalLike[]);\n  // eslint-disable-next-line @typescript-eslint/explicit-module-boundary-types\n  constructor(parentSignals?: any) {\n    this._signal = new AbortSignal();\n\n    if (!parentSignals) {\n      return;\n    }\n    // coerce parentSignals into an array\n    if (!Array.isArray(parentSignals)) {\n      // eslint-disable-next-line prefer-rest-params\n      parentSignals = arguments;\n    }\n    for (const parentSignal of parentSignals) {\n      // if the parent signal has already had abort() called,\n      // then call abort on this signal as well.\n      if (parentSignal.aborted) {\n        this.abort();\n      } else {\n        // when the parent signal aborts, this signal should as well.\n        parentSignal.addEventListener(\"abort\", () => {\n          this.abort();\n        });\n      }\n    }\n  }\n\n  /**\n   * The AbortSignal associated with this controller that will signal aborted\n   * when the abort method is called on this controller.\n   *\n   * @readonly\n   */\n  public get signal(): AbortSignal {\n    return this._signal;\n  }\n\n  /**\n   * Signal that any operations passed this controller's associated abort signal\n   * to cancel any remaining work and throw an `AbortError`.\n   */\n  abort(): void {\n    abortSignal(this._signal);\n  }\n\n  /**\n   * Creates a new AbortSignal instance that will abort after the provided ms.\n   * @param ms - Elapsed time in milliseconds to trigger an abort.\n   */\n  public static timeout(ms: number): AbortSignal {\n    const signal = new AbortSignal();\n    const timer = setTimeout(abortSignal, ms, signal);\n    // Prevent the active Timer from keeping the Node.js event loop active.\n    if (typeof timer.unref === \"function\") {\n      timer.unref();\n    }\n    return signal;\n  }\n}\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\n/**\n * This error is thrown when an asynchronous operation has been aborted.\n * Check for this error by testing the `name` that the name property of the\n * error matches `\"AbortError\"`.\n *\n * @example\n * ```ts\n * const controller = new AbortController();\n * controller.abort();\n * try {\n *   doAsyncWork(controller.signal)\n * } catch (e) {\n *   if (e.name === 'AbortError') {\n *     // handle abort error here.\n *   }\n * }\n * ```\n */\nexport class AbortError extends Error {\n  constructor(message?: string) {\n    super(message);\n    this.name = \"AbortError\";\n  }\n}\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport { AbortError } from \"@azure/abort-controller\";\nimport type { AbortOptions } from \"./aborterUtils.js\";\n\n/**\n * Options for the createAbortablePromise function.\n */\nexport interface CreateAbortablePromiseOptions extends AbortOptions {\n  /** A function to be called if the promise was aborted */\n  cleanupBeforeAbort?: () => void;\n}\n\n/**\n * Creates an abortable promise.\n * @param buildPromise - A function that takes the resolve and reject functions as parameters.\n * @param options - The options for the abortable promise.\n * @returns A promise that can be aborted.\n */\nexport function createAbortablePromise<T>(\n  buildPromise: (\n    resolve: (value: T | PromiseLike<T>) => void,\n    reject: (reason?: any) => void,\n  ) => void,\n  options?: CreateAbortablePromiseOptions,\n): Promise<T> {\n  const { cleanupBeforeAbort, abortSignal, abortErrorMsg } = options ?? {};\n  return new Promise((resolve, reject) => {\n    function rejectOnAbort(): void {\n      reject(new AbortError(abortErrorMsg ?? \"The operation was aborted.\"));\n    }\n    function removeListeners(): void {\n      abortSignal?.removeEventListener(\"abort\", onAbort);\n    }\n    function onAbort(): void {\n      cleanupBeforeAbort?.();\n      removeListeners();\n      rejectOnAbort();\n    }\n    if (abortSignal?.aborted) {\n      return rejectOnAbort();\n    }\n    try {\n      buildPromise(\n        (x) => {\n          removeListeners();\n          resolve(x);\n        },\n        (x) => {\n          removeListeners();\n          reject(x);\n        },\n      );\n    } catch (err) {\n      reject(err);\n    }\n    abortSignal?.addEventListener(\"abort\", onAbort);\n  });\n}\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport type { AbortOptions } from \"./aborterUtils.js\";\nimport { createAbortablePromise } from \"./createAbortablePromise.js\";\nimport { getRandomIntegerInclusive } from \"./random.js\";\n\nconst StandardAbortMessage = \"The delay was aborted.\";\n\n/**\n * Options for support abort functionality for the delay method\n */\nexport interface DelayOptions extends AbortOptions {}\n\n/**\n * A wrapper for setTimeout that resolves a promise after timeInMs milliseconds.\n * @param timeInMs - The number of milliseconds to be delayed.\n * @param options - The options for delay - currently abort options\n * @returns Promise that is resolved after timeInMs\n */\nexport function delay(timeInMs: number, options?: DelayOptions): Promise<void> {\n  let token: ReturnType<typeof setTimeout>;\n  const { abortSignal, abortErrorMsg } = options ?? {};\n  return createAbortablePromise(\n    (resolve) => {\n      token = setTimeout(resolve, timeInMs);\n    },\n    {\n      cleanupBeforeAbort: () => clearTimeout(token),\n      abortSignal,\n      abortErrorMsg: abortErrorMsg ?? StandardAbortMessage,\n    },\n  );\n}\n\n/**\n * Calculates the delay interval for retry attempts using exponential delay with jitter.\n * @param retryAttempt - The current retry attempt number.\n * @param config - The exponential retry configuration.\n * @returns An object containing the calculated retry delay.\n */\nexport function calculateRetryDelay(\n  retryAttempt: number,\n  config: {\n    retryDelayInMs: number;\n    maxRetryDelayInMs: number;\n  },\n): { retryAfterInMs: number } {\n  // Exponentially increase the delay each time\n  const exponentialDelay = config.retryDelayInMs * Math.pow(2, retryAttempt);\n\n  // Don't let the delay exceed the maximum\n  const clampedDelay = Math.min(config.maxRetryDelayInMs, exponentialDelay);\n\n  // Allow the final value to have some \"jitter\" (within 50% of the delay size) so\n  // that retries across multiple clients don't occur simultaneously.\n  const retryAfterInMs = clampedDelay / 2 + getRandomIntegerInclusive(0, clampedDelay / 2);\n\n  return { retryAfterInMs };\n}\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\ndeclare global {\n  // stub these out for the browser\n  function btoa(input: string): string;\n  function atob(input: string): string;\n}\n\n/** The supported character encoding type */\nexport type EncodingType = \"utf-8\" | \"base64\" | \"base64url\" | \"hex\";\n\n/**\n * The helper that transforms bytes with specific character encoding into string\n * @param bytes - the uint8array bytes\n * @param format - the format we use to encode the byte\n * @returns a string of the encoded string\n */\nexport function uint8ArrayToString(bytes: Uint8Array, format: EncodingType): string {\n  switch (format) {\n    case \"utf-8\":\n      return uint8ArrayToUtf8String(bytes);\n    case \"base64\":\n      return uint8ArrayToBase64(bytes);\n    case \"base64url\":\n      return uint8ArrayToBase64Url(bytes);\n    case \"hex\":\n      return uint8ArrayToHexString(bytes);\n  }\n}\n\n/**\n * The helper that transforms string to specific character encoded bytes array.\n * @param value - the string to be converted\n * @param format - the format we use to decode the value\n * @returns a uint8array\n */\nexport function stringToUint8Array(value: string, format: EncodingType): Uint8Array {\n  switch (format) {\n    case \"utf-8\":\n      return utf8StringToUint8Array(value);\n    case \"base64\":\n      return base64ToUint8Array(value);\n    case \"base64url\":\n      return base64UrlToUint8Array(value);\n    case \"hex\":\n      return hexStringToUint8Array(value);\n  }\n}\n\n/**\n * Decodes a Uint8Array into a Base64 string.\n * @internal\n */\nexport function uint8ArrayToBase64(bytes: Uint8Array): string {\n  return btoa([...bytes].map((x) => String.fromCharCode(x)).join(\"\"));\n}\n\n/**\n * Decodes a Uint8Array into a Base64Url string.\n * @internal\n */\nexport function uint8ArrayToBase64Url(bytes: Uint8Array): string {\n  return uint8ArrayToBase64(bytes).replace(/\\+/g, \"-\").replace(/\\//g, \"_\").replace(/=/g, \"\");\n}\n\n/**\n * Decodes a Uint8Array into a javascript string.\n * @internal\n */\nexport function uint8ArrayToUtf8String(bytes: Uint8Array): string {\n  const decoder = new TextDecoder();\n  const dataString = decoder.decode(bytes);\n  return dataString;\n}\n\n/**\n * Decodes a Uint8Array into a hex string\n * @internal\n */\nexport function uint8ArrayToHexString(bytes: Uint8Array): string {\n  return [...bytes].map((x) => x.toString(16).padStart(2, \"0\")).join(\"\");\n}\n\n/**\n * Encodes a JavaScript string into a Uint8Array.\n * @internal\n */\nexport function utf8StringToUint8Array(value: string): Uint8Array {\n  return new TextEncoder().encode(value);\n}\n\n/**\n * Encodes a Base64 string into a Uint8Array.\n * @internal\n */\nexport function base64ToUint8Array(value: string): Uint8Array {\n  return new Uint8Array([...atob(value)].map((x) => x.charCodeAt(0)));\n}\n\n/**\n * Encodes a Base64Url string into a Uint8Array.\n * @internal\n */\nexport function base64UrlToUint8Array(value: string): Uint8Array {\n  const base64String = value.replace(/-/g, \"+\").replace(/_/g, \"/\");\n  return base64ToUint8Array(base64String);\n}\n\nconst hexDigits = new Set(\"0123456789abcdefABCDEF\");\n\n/**\n * Encodes a hex string into a Uint8Array\n * @internal\n */\nexport function hexStringToUint8Array(value: string): Uint8Array {\n  // If value has odd length, the last character will be ignored, consistent with NodeJS Buffer behavior\n  const bytes = new Uint8Array(value.length / 2);\n  for (let i = 0; i < value.length / 2; ++i) {\n    const highNibble = value[2 * i];\n    const lowNibble = value[2 * i + 1];\n    if (!hexDigits.has(highNibble) || !hexDigits.has(lowNibble)) {\n      // Replicate Node Buffer behavior by exiting early when we encounter an invalid byte\n      return bytes.slice(0, i);\n    }\n\n    bytes[i] = parseInt(`${highNibble}${lowNibble}`, 16);\n  }\n\n  return bytes;\n}\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\n/**\n * Generated Universally Unique Identifier\n *\n * @returns RFC4122 v4 UUID.\n */\nexport function generateUUID(): string {\n  let uuid = \"\";\n  for (let i = 0; i < 32; i++) {\n    // Generate a random number between 0 and 15\n    const randomNumber = Math.floor(Math.random() * 16);\n    // Set the UUID version to 4 in the 13th position\n    if (i === 12) {\n      uuid += \"4\";\n    } else if (i === 16) {\n      // Set the UUID variant to \"10\" in the 17th position\n      uuid += (randomNumber & 0x3) | 0x8;\n    } else {\n      // Add a random hexadecimal digit to the UUID string\n      uuid += randomNumber.toString(16);\n    }\n    // Add hyphens to the UUID string at the appropriate positions\n    if (i === 7 || i === 11 || i === 15 || i === 19) {\n      uuid += \"-\";\n    }\n  }\n  return uuid;\n}\n\n/**\n * Generated Universally Unique Identifier\n *\n * @returns RFC4122 v4 UUID.\n */\nexport function randomUUID(): string {\n  return generateUUID();\n}\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport { generateUUID } from \"./uuidUtils.common.js\";\n\ninterface Crypto {\n  randomUUID(): string;\n}\n\ndeclare const globalThis: {\n  crypto: Crypto;\n};\n\n// NOTE: This could be undefined if not used in a secure context\nconst uuidFunction =\n  typeof globalThis?.crypto?.randomUUID === \"function\"\n    ? globalThis.crypto.randomUUID.bind(globalThis.crypto)\n    : generateUUID;\n\n/**\n * Generated Universally Unique Identifier\n *\n * @returns RFC4122 v4 UUID.\n */\nexport function randomUUID(): string {\n  return uuidFunction();\n}\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\ninterface Window {\n  document: unknown;\n}\n\ninterface DedicatedWorkerGlobalScope {\n  constructor: {\n    name: string;\n  };\n\n  importScripts: (...paths: string[]) => void;\n}\n\ninterface Navigator {\n  product: string;\n}\n\ninterface DenoGlobal {\n  version: {\n    deno: string;\n  };\n}\n\ninterface BunGlobal {\n  version: string;\n}\n\n// eslint-disable-next-line @azure/azure-sdk/ts-no-window\ndeclare const window: Window;\ndeclare const self: DedicatedWorkerGlobalScope;\ndeclare const Deno: DenoGlobal;\ndeclare const Bun: BunGlobal;\ndeclare const navigator: Navigator;\n\n/**\n * A constant that indicates whether the environment the code is running is a Web Browser.\n */\n// eslint-disable-next-line @azure/azure-sdk/ts-no-window\nexport const isBrowser = typeof window !== \"undefined\" && typeof window.document !== \"undefined\";\n\n/**\n * A constant that indicates whether the environment the code is running is a Web Worker.\n */\nexport const isWebWorker =\n  typeof self === \"object\" &&\n  typeof self?.importScripts === \"function\" &&\n  (self.constructor?.name === \"DedicatedWorkerGlobalScope\" ||\n    self.constructor?.name === \"ServiceWorkerGlobalScope\" ||\n    self.constructor?.name === \"SharedWorkerGlobalScope\");\n\n/**\n * A constant that indicates whether the environment the code is running is Deno.\n */\nexport const isDeno =\n  typeof Deno !== \"undefined\" &&\n  typeof Deno.version !== \"undefined\" &&\n  typeof Deno.version.deno !== \"undefined\";\n\n/**\n * A constant that indicates whether the environment the code is running is Bun.sh.\n */\nexport const isBun = typeof Bun !== \"undefined\" && typeof Bun.version !== \"undefined\";\n\n/**\n * A constant that indicates whether the environment the code is running is a Node.js compatible environment.\n */\nexport const isNodeLike =\n  typeof globalThis.process !== \"undefined\" &&\n  Boolean(globalThis.process.version) &&\n  Boolean(globalThis.process.versions?.node);\n\n/**\n * A constant that indicates whether the environment the code is running is a Node.js compatible environment.\n * @deprecated Use `isNodeLike` instead.\n */\nexport const isNode = isNodeLike;\n\n/**\n * A constant that indicates whether the environment the code is running is Node.JS.\n */\nexport const isNodeRuntime = isNodeLike && !isBun && !isDeno;\n\n/**\n * A constant that indicates whether the environment the code is running is in React-Native.\n */\n// https://github.com/facebook/react-native/blob/main/packages/react-native/Libraries/Core/setUpNavigator.js\nexport const isReactNative =\n  typeof navigator !== \"undefined\" && navigator?.product === \"ReactNative\";\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { AbortController, AbortSignalLike } from \"@azure/abort-controller\";\nimport { delay } from \"@azure/core-util\";\nimport EventEmitter from \"events\";\nimport { SendMessageError, SendMessageErrorOptions } from \"./errors\";\nimport { logger } from \"./logger\";\nimport {\n  WebPubSubResult,\n  Join<PERSON>roupOptions,\n  LeaveGroupOptions,\n  OnConnectedArgs,\n  OnDisconnectedArgs,\n  OnGroupDataMessageArgs,\n  OnServerDataMessageArgs,\n  OnStoppedArgs,\n  WebPubSubRetryOptions,\n  SendToGroupOptions,\n  SendEventOptions,\n  WebPubSubClientOptions,\n  OnRejoinGroupFailedArgs,\n  StartOptions,\n  GetClientAccessUrlOptions,\n} from \"./models\";\nimport {\n  ConnectedMessage,\n  DisconnectedMessage,\n  GroupDataMessage,\n  ServerDataMessage,\n  WebPubSubDataType,\n  WebPubSubMessage,\n  Join<PERSON>roupMessage,\n  LeaveGroupMessage,\n  SendToGroupMessage,\n  SendEventMessage,\n  AckMessage,\n  SequenceAckMessage,\n} from \"./models/messages\";\nimport { WebPubSubClientProtocol, WebPubSubJsonReliableProtocol } from \"./protocols\";\nimport { WebPubSubClientCredential } from \"./webPubSubClientCredential\";\nimport { WebSocketClientFactory } from \"./websocket/websocketClient\";\nimport { WebSocketClientFactoryLike, WebSocketClientLike } from \"./websocket/websocketClientLike\";\nimport { abortablePromise } from \"./utils/abortablePromise\";\n\nenum WebPubSubClientState {\n  Stopped = \"Stopped\",\n  Disconnected = \"Disconnected\",\n  Connecting = \"Connecting\",\n  Connected = \"Connected\",\n  Recovering = \"Recovering\",\n}\n\n/**\n * Types which can be serialized and sent as JSON.\n */\nexport type JSONTypes = string | number | boolean | object;\n\n/**\n * The WebPubSub client\n */\nexport class WebPubSubClient {\n  private readonly _protocol: WebPubSubClientProtocol;\n  private readonly _credential: WebPubSubClientCredential;\n  private readonly _options: WebPubSubClientOptions;\n  private readonly _groupMap: Map<string, WebPubSubGroup>;\n  private readonly _ackMap: Map<number, AckEntity>;\n  private readonly _sequenceId: SequenceId;\n  private readonly _messageRetryPolicy: RetryPolicy;\n  private readonly _reconnectRetryPolicy: RetryPolicy;\n  private readonly _quickSequenceAckDiff = 300;\n  private readonly _activeTimeoutInMs = 20000;\n\n  private readonly _emitter: EventEmitter = new EventEmitter();\n  private _state: WebPubSubClientState;\n  private _isStopping: boolean = false;\n  private _ackId: number;\n  private _activeKeepaliveTask: AbortableTask | undefined;\n\n  // connection lifetime\n  private _wsClient?: WebSocketClientLike;\n  private _uri?: string;\n  private _lastCloseEvent?: { code: number; reason: string };\n  private _lastDisconnectedMessage?: DisconnectedMessage;\n  private _connectionId?: string;\n  private _reconnectionToken?: string;\n  private _isInitialConnected = false;\n  private _sequenceAckTask?: AbortableTask;\n\n  private nextAckId(): number {\n    this._ackId = this._ackId + 1;\n    return this._ackId;\n  }\n\n  /**\n   * Create an instance of WebPubSubClient\n   * @param clientAccessUrl - The uri to connect\n   * @param options - The client options\n   */\n  constructor(clientAccessUrl: string, options?: WebPubSubClientOptions);\n  /**\n   * Create an instance of WebPubSubClient\n   * @param credential - The credential to use when connecting\n   * @param options - The client options\n   */\n  constructor(credential: WebPubSubClientCredential, options?: WebPubSubClientOptions);\n  constructor(credential: string | WebPubSubClientCredential, options?: WebPubSubClientOptions) {\n    if (typeof credential === \"string\") {\n      this._credential = { getClientAccessUrl: credential } as WebPubSubClientCredential;\n    } else {\n      this._credential = credential;\n    }\n\n    if (options == null) {\n      options = {};\n    }\n    this._buildDefaultOptions(options);\n    this._options = options;\n\n    this._messageRetryPolicy = new RetryPolicy(this._options.messageRetryOptions!);\n    this._reconnectRetryPolicy = new RetryPolicy(this._options.reconnectRetryOptions!);\n\n    this._protocol = this._options.protocol!;\n    this._groupMap = new Map<string, WebPubSubGroup>();\n    this._ackMap = new Map<number, AckEntity>();\n    this._sequenceId = new SequenceId();\n\n    this._state = WebPubSubClientState.Stopped;\n    this._ackId = 0;\n  }\n\n  /**\n   * Start to start to the service.\n   * @param abortSignal - The abort signal\n   */\n  public async start(options?: StartOptions): Promise<void> {\n    if (this._isStopping) {\n      throw new Error(\"Can't start a client during stopping\");\n    }\n\n    if (this._state !== WebPubSubClientState.Stopped) {\n      throw new Error(\"Client can be only started when it's Stopped\");\n    }\n\n    let abortSignal: AbortSignalLike | undefined;\n    if (options) {\n      abortSignal = options.abortSignal;\n    }\n\n    if (!this._activeKeepaliveTask) {\n      this._activeKeepaliveTask = this._getActiveKeepaliveTask();\n    }\n\n    try {\n      await this._startCore(abortSignal);\n    } catch (err) {\n      // this two sentense should be set together. Consider client.stop() is called during _startCore()\n      this._changeState(WebPubSubClientState.Stopped);\n      if (this._activeKeepaliveTask) {\n        this._activeKeepaliveTask.abort();\n        this._activeKeepaliveTask = undefined;\n      }\n      this._isStopping = false;\n      throw err;\n    }\n  }\n\n  private async _startFromRestarting(abortSignal?: AbortSignalLike): Promise<void> {\n    if (this._state !== WebPubSubClientState.Disconnected) {\n      throw new Error(\"Client can be only restarted when it's Disconnected\");\n    }\n\n    try {\n      logger.verbose(\"Staring reconnecting.\");\n      await this._startCore(abortSignal);\n    } catch (err) {\n      this._changeState(WebPubSubClientState.Disconnected);\n      throw err;\n    }\n  }\n\n  private async _startCore(abortSignal?: AbortSignalLike): Promise<void> {\n    this._changeState(WebPubSubClientState.Connecting);\n\n    logger.info(\"Staring a new connection\");\n    // Reset before a pure new connection\n    this._sequenceId.reset();\n    this._isInitialConnected = false;\n    this._lastCloseEvent = undefined;\n    this._lastDisconnectedMessage = undefined;\n    this._connectionId = undefined;\n    this._reconnectionToken = undefined;\n    this._uri = undefined;\n\n    if (typeof this._credential.getClientAccessUrl === \"string\") {\n      this._uri = this._credential.getClientAccessUrl;\n    } else {\n      this._uri = await this._credential.getClientAccessUrl({\n        abortSignal: abortSignal,\n      } as GetClientAccessUrlOptions);\n    }\n\n    if (typeof this._uri !== \"string\") {\n      throw new Error(\n        `The clientAccessUrl must be a string but currently it's ${typeof this._uri}`,\n      );\n    }\n    await this._connectCore(this._uri);\n  }\n\n  /**\n   * Stop the client.\n   */\n  public stop(): void {\n    if (this._state === WebPubSubClientState.Stopped || this._isStopping) {\n      return;\n    }\n\n    // TODO: Maybe we need a better logic for stopping control\n    this._isStopping = true;\n    if (this._wsClient && this._wsClient.isOpen()) {\n      this._wsClient.close();\n    } else {\n      this._isStopping = false;\n    }\n    if (this._activeKeepaliveTask) {\n      this._activeKeepaliveTask.abort();\n      this._activeKeepaliveTask = undefined;\n    }\n  }\n\n  /**\n   * Add handler for connected event\n   * @param event - The event name\n   * @param listener - The handler\n   */\n  public on(event: \"connected\", listener: (e: OnConnectedArgs) => void): void;\n  /**\n   * Add handler for disconnected event\n   * @param event - The event name\n   * @param listener - The handler\n   */\n  public on(event: \"disconnected\", listener: (e: OnDisconnectedArgs) => void): void;\n  /**\n   * Add handler for stopped event\n   * @param event - The event name\n   * @param listener - The handler\n   */\n  public on(event: \"stopped\", listener: (e: OnStoppedArgs) => void): void;\n  /**\n   * Add handler for server messages\n   * @param event - The event name\n   * @param listener - The handler\n   */\n  public on(event: \"server-message\", listener: (e: OnServerDataMessageArgs) => void): void;\n  /**\n   * Add handler for group messags\n   * @param event - The event name\n   * @param listener - The handler\n   */\n  public on(event: \"group-message\", listener: (e: OnGroupDataMessageArgs) => void): void;\n  /**\n   * Add handler for rejoining group failed\n   * @param event - The event name\n   * @param listener - The handler\n   */\n  public on(event: \"rejoin-group-failed\", listener: (e: OnRejoinGroupFailedArgs) => void): void;\n  public on(\n    event:\n      | \"connected\"\n      | \"disconnected\"\n      | \"stopped\"\n      | \"server-message\"\n      | \"group-message\"\n      | \"rejoin-group-failed\",\n    listener: (e: any) => void,\n  ): void {\n    this._emitter.on(event, listener);\n  }\n\n  /**\n   * Remove handler for connected event\n   * @param event - The event name\n   * @param listener - The handler\n   */\n  public off(event: \"connected\", listener: (e: OnConnectedArgs) => void): void;\n  /**\n   * Remove handler for disconnected event\n   * @param event - The event name\n   * @param listener - The handler\n   */\n  public off(event: \"disconnected\", listener: (e: OnDisconnectedArgs) => void): void;\n  /**\n   * Remove handler for stopped event\n   * @param event - The event name\n   * @param listener - The handler\n   */\n  public off(event: \"stopped\", listener: (e: OnStoppedArgs) => void): void;\n  /**\n   * Remove handler for server message\n   * @param event - The event name\n   * @param listener - The handler\n   */\n  public off(event: \"server-message\", listener: (e: OnServerDataMessageArgs) => void): void;\n  /**\n   * Remove handler for group message\n   * @param event - The event name\n   * @param listener - The handler\n   */\n  public off(event: \"group-message\", listener: (e: OnGroupDataMessageArgs) => void): void;\n  /**\n   * Remove handler for rejoining group failed\n   * @param event - The event name\n   * @param listener - The handler\n   */\n  public off(event: \"rejoin-group-failed\", listener: (e: OnRejoinGroupFailedArgs) => void): void;\n  public off(\n    event:\n      | \"connected\"\n      | \"disconnected\"\n      | \"stopped\"\n      | \"server-message\"\n      | \"group-message\"\n      | \"rejoin-group-failed\",\n    listener: (e: any) => void,\n  ): void {\n    this._emitter.removeListener(event, listener);\n  }\n\n  private _emitEvent(event: \"connected\", args: OnConnectedArgs): void;\n  private _emitEvent(event: \"disconnected\", args: OnDisconnectedArgs): void;\n  private _emitEvent(event: \"stopped\", args: OnStoppedArgs): void;\n  private _emitEvent(event: \"server-message\", args: OnServerDataMessageArgs): void;\n  private _emitEvent(event: \"group-message\", args: OnGroupDataMessageArgs): void;\n  private _emitEvent(event: \"rejoin-group-failed\", args: OnRejoinGroupFailedArgs): void;\n  private _emitEvent(\n    event:\n      | \"connected\"\n      | \"disconnected\"\n      | \"stopped\"\n      | \"server-message\"\n      | \"group-message\"\n      | \"rejoin-group-failed\",\n    args: any,\n  ): void {\n    this._emitter.emit(event, args);\n  }\n\n  /**\n   * Send custom event to server.\n   * @param eventName - The event name\n   * @param content - The data content\n   * @param dataType - The data type\n   * @param options - The options\n   * @param abortSignal - The abort signal\n   */\n  public async sendEvent(\n    eventName: string,\n    content: JSONTypes | ArrayBuffer,\n    dataType: WebPubSubDataType,\n    options?: SendEventOptions,\n  ): Promise<WebPubSubResult> {\n    return await this._operationExecuteWithRetry(\n      () => this._sendEventAttempt(eventName, content, dataType, options),\n      options?.abortSignal,\n    );\n  }\n\n  private async _sendEventAttempt(\n    eventName: string,\n    content: JSONTypes | ArrayBuffer,\n    dataType: WebPubSubDataType,\n    options?: SendEventOptions,\n  ): Promise<WebPubSubResult> {\n    const fireAndForget = options?.fireAndForget ?? false;\n    if (!fireAndForget) {\n      return await this._sendMessageWithAckId(\n        (id) => {\n          return {\n            kind: \"sendEvent\",\n            dataType: dataType,\n            data: content,\n            ackId: id,\n            event: eventName,\n          } as SendEventMessage;\n        },\n        options?.ackId,\n        options?.abortSignal,\n      );\n    }\n\n    const message = {\n      kind: \"sendEvent\",\n      dataType: dataType,\n      data: content,\n      event: eventName,\n    } as SendEventMessage;\n\n    await this._sendMessage(message, options?.abortSignal);\n    return { isDuplicated: false };\n  }\n\n  /**\n   * Join the client to group\n   * @param groupName - The group name\n   * @param options - The join group options\n   */\n  public async joinGroup(groupName: string, options?: JoinGroupOptions): Promise<WebPubSubResult> {\n    return await this._operationExecuteWithRetry(\n      () => this._joinGroupAttempt(groupName, options),\n      options?.abortSignal,\n    );\n  }\n\n  private async _joinGroupAttempt(\n    groupName: string,\n    options?: JoinGroupOptions,\n  ): Promise<WebPubSubResult> {\n    const group = this._getOrAddGroup(groupName);\n    const result = await this._joinGroupCore(groupName, options);\n    group.isJoined = true;\n    return result;\n  }\n\n  private async _joinGroupCore(\n    groupName: string,\n    options?: JoinGroupOptions,\n  ): Promise<WebPubSubResult> {\n    return await this._sendMessageWithAckId(\n      (id) => {\n        return {\n          group: groupName,\n          ackId: id,\n          kind: \"joinGroup\",\n        } as JoinGroupMessage;\n      },\n      options?.ackId,\n      options?.abortSignal,\n    );\n  }\n\n  /**\n   * Leave the client from group\n   * @param groupName - The group name\n   * @param ackId - The optional ackId. If not specified, client will generate one.\n   * @param abortSignal - The abort signal\n   */\n  public async leaveGroup(\n    groupName: string,\n    options?: LeaveGroupOptions,\n  ): Promise<WebPubSubResult> {\n    return await this._operationExecuteWithRetry(\n      () => this._leaveGroupAttempt(groupName, options),\n      options?.abortSignal,\n    );\n  }\n\n  private async _leaveGroupAttempt(\n    groupName: string,\n    options?: LeaveGroupOptions,\n  ): Promise<WebPubSubResult> {\n    const group = this._getOrAddGroup(groupName);\n    const result = await this._sendMessageWithAckId(\n      (id) => {\n        return {\n          group: groupName,\n          ackId: id,\n          kind: \"leaveGroup\",\n        } as LeaveGroupMessage;\n      },\n      options?.ackId,\n      options?.abortSignal,\n    );\n    group.isJoined = false;\n    return result;\n  }\n\n  /**\n   * Send message to group.\n   * @param groupName - The group name\n   * @param content - The data content\n   * @param dataType - The data type\n   * @param options - The options\n   * @param abortSignal - The abort signal\n   */\n  public async sendToGroup(\n    groupName: string,\n    content: JSONTypes | ArrayBuffer,\n    dataType: WebPubSubDataType,\n    options?: SendToGroupOptions,\n  ): Promise<WebPubSubResult> {\n    return await this._operationExecuteWithRetry(\n      () => this._sendToGroupAttempt(groupName, content, dataType, options),\n      options?.abortSignal,\n    );\n  }\n\n  private async _sendToGroupAttempt(\n    groupName: string,\n    content: JSONTypes | ArrayBuffer,\n    dataType: WebPubSubDataType,\n    options?: SendToGroupOptions,\n  ): Promise<WebPubSubResult> {\n    const fireAndForget = options?.fireAndForget ?? false;\n    const noEcho = options?.noEcho ?? false;\n    if (!fireAndForget) {\n      return await this._sendMessageWithAckId(\n        (id) => {\n          return {\n            kind: \"sendToGroup\",\n            group: groupName,\n            dataType: dataType,\n            data: content,\n            ackId: id,\n            noEcho: noEcho,\n          } as SendToGroupMessage;\n        },\n        options?.ackId,\n        options?.abortSignal,\n      );\n    }\n\n    const message = {\n      kind: \"sendToGroup\",\n      group: groupName,\n      dataType: dataType,\n      data: content,\n      noEcho: noEcho,\n    } as SendToGroupMessage;\n\n    await this._sendMessage(message, options?.abortSignal);\n    return { isDuplicated: false };\n  }\n\n  private _getWebSocketClientFactory(): WebSocketClientFactoryLike {\n    return new WebSocketClientFactory();\n  }\n\n  private async _trySendSequenceAck(): Promise<void> {\n    if (!this._protocol.isReliableSubProtocol) {\n      return;\n    }\n    const [isUpdated, seqId] = this._sequenceId.tryGetSequenceId();\n    if (isUpdated && seqId) {\n      const message: SequenceAckMessage = {\n        kind: \"sequenceAck\",\n        sequenceId: seqId!,\n      };\n      try {\n        await this._sendMessage(message);\n      } catch {\n        this._sequenceId.tryUpdate(seqId!); // If sending failed, mark it as updated so that it can be sent again.\n      }\n    }\n  }\n\n  private _connectCore(uri: string): Promise<void> {\n    if (this._isStopping) {\n      throw new Error(\"Can't start a client during stopping\");\n    }\n\n    return new Promise<void>((resolve, reject) => {\n      // This part is executed sync\n      const client = (this._wsClient = this._getWebSocketClientFactory().create(\n        uri,\n        this._protocol.name,\n      ));\n      client.onopen(() => {\n        // There's a case that client called stop() before this method. We need to check and close it if it's the case.\n        if (this._isStopping) {\n          try {\n            client.close();\n          } catch {}\n\n          reject(new Error(`The client is stopped`));\n        }\n        logger.verbose(\"WebSocket connection has opened\");\n        this._changeState(WebPubSubClientState.Connected);\n        if (this._protocol.isReliableSubProtocol) {\n          if (this._sequenceAckTask != null) {\n            this._sequenceAckTask.abort();\n          }\n          this._sequenceAckTask = new AbortableTask(async () => {\n            await this._trySendSequenceAck();\n          }, 1000);\n        }\n\n        resolve();\n      });\n\n      client.onerror((e) => {\n        if (this._sequenceAckTask != null) {\n          this._sequenceAckTask.abort();\n        }\n        reject(e);\n      });\n\n      client.onclose((code: number, reason: string) => {\n        if (this._state === WebPubSubClientState.Connected) {\n          logger.verbose(\"WebSocket closed after open\");\n          if (this._sequenceAckTask != null) {\n            this._sequenceAckTask.abort();\n          }\n          logger.info(`WebSocket connection closed. Code: ${code}, Reason: ${reason}`);\n          this._lastCloseEvent = { code: code, reason: reason };\n          this._handleConnectionClose.call(this);\n        } else {\n          logger.verbose(\"WebSocket closed before open\");\n          reject(new Error(`Failed to start WebSocket: ${code}`));\n        }\n      });\n\n      client.onmessage((data: any) => {\n        const handleAckMessage = (message: AckMessage): void => {\n          if (this._ackMap.has(message.ackId)) {\n            const entity = this._ackMap.get(message.ackId)!;\n            this._ackMap.delete(message.ackId);\n            const isDuplicated: boolean =\n              message.error != null && message.error.name === \"Duplicate\";\n            if (message.success || isDuplicated) {\n              entity.resolve({\n                ackId: message.ackId,\n                isDuplicated: isDuplicated,\n              } as WebPubSubResult);\n            } else {\n              entity.reject(\n                new SendMessageError(\"Failed to send message.\", {\n                  ackId: message.ackId,\n                  errorDetail: message.error,\n                } as SendMessageErrorOptions),\n              );\n            }\n          }\n        };\n\n        const handleConnectedMessage = async (message: ConnectedMessage): Promise<void> => {\n          this._connectionId = message.connectionId;\n          this._reconnectionToken = message.reconnectionToken;\n\n          if (!this._isInitialConnected) {\n            this._isInitialConnected = true;\n\n            if (this._options.autoRejoinGroups) {\n              const groupPromises: Promise<void>[] = [];\n              this._groupMap.forEach((g) => {\n                if (g.isJoined) {\n                  groupPromises.push(\n                    (async () => {\n                      try {\n                        await this._joinGroupCore(g.name);\n                      } catch (err) {\n                        this._safeEmitRejoinGroupFailed(g.name, err);\n                      }\n                    })(),\n                  );\n                }\n              });\n\n              try {\n                await Promise.all(groupPromises);\n              } catch {}\n            }\n\n            this._safeEmitConnected(message.connectionId, message.userId);\n          }\n        };\n\n        const handleDisconnectedMessage = (message: DisconnectedMessage): void => {\n          this._lastDisconnectedMessage = message;\n        };\n\n        const handleGroupDataMessage = (message: GroupDataMessage): void => {\n          if (message.sequenceId != null) {\n            const diff = this._sequenceId.tryUpdate(message.sequenceId);\n            if (diff === 0) {\n              // drop duplicated message\n              return;\n            }\n\n            // If the diff is larger than the threshold, we must ack quicker to avoid slow client drop.\n            if (diff > this._quickSequenceAckDiff) {\n              this._trySendSequenceAck();\n            }\n          }\n\n          this._safeEmitGroupMessage(message);\n        };\n\n        const handleServerDataMessage = (message: ServerDataMessage): void => {\n          if (message.sequenceId != null) {\n            const diff = this._sequenceId.tryUpdate(message.sequenceId);\n            if (diff === 0) {\n              // drop duplicated message\n              return;\n            }\n\n            // If the diff is larger than the threshold, we must ack quicker to avoid slow client drop.\n            if (diff > this._quickSequenceAckDiff) {\n              this._trySendSequenceAck();\n            }\n          }\n\n          this._safeEmitServerMessage(message);\n        };\n\n        let messages: WebPubSubMessage[] | WebPubSubMessage | null;\n        try {\n          let convertedData: Buffer | ArrayBuffer | string;\n          if (Array.isArray(data)) {\n            convertedData = Buffer.concat(data);\n          } else {\n            convertedData = data;\n          }\n\n          messages = this._protocol.parseMessages(convertedData);\n          if (messages === null) {\n            // null means the message is not recognized.\n            return;\n          }\n        } catch (err) {\n          logger.warning(\"An error occurred while parsing the message from service\", err);\n          throw err;\n        }\n\n        if (!Array.isArray(messages)) {\n          messages = [messages];\n        }\n\n        messages.forEach((message) => {\n          try {\n            switch (message.kind) {\n              case \"ack\": {\n                handleAckMessage(message as AckMessage);\n                break;\n              }\n              case \"connected\": {\n                handleConnectedMessage(message as ConnectedMessage);\n                break;\n              }\n              case \"disconnected\": {\n                handleDisconnectedMessage(message as DisconnectedMessage);\n                break;\n              }\n              case \"groupData\": {\n                handleGroupDataMessage(message as GroupDataMessage);\n                break;\n              }\n              case \"serverData\": {\n                handleServerDataMessage(message as ServerDataMessage);\n                break;\n              }\n            }\n          } catch (err) {\n            logger.warning(\n              `An error occurred while handling the message with kind: ${message.kind} from service`,\n              err,\n            );\n          }\n        });\n      });\n    });\n  }\n\n  private async _handleConnectionCloseAndNoRecovery(): Promise<void> {\n    this._state = WebPubSubClientState.Disconnected;\n\n    this._safeEmitDisconnected(this._connectionId, this._lastDisconnectedMessage);\n\n    // Auto reconnect or stop\n    if (this._options.autoReconnect) {\n      await this._autoReconnect();\n    } else {\n      await this._handleConnectionStopped();\n    }\n  }\n\n  private async _autoReconnect(): Promise<void> {\n    let isSuccess = false;\n    let attempt = 0;\n    try {\n      while (!this._isStopping) {\n        try {\n          await this._startFromRestarting();\n          isSuccess = true;\n          break;\n        } catch (err) {\n          logger.warning(\"An attempt to reconnect connection failed.\", err);\n\n          attempt++;\n          const delayInMs = this._reconnectRetryPolicy.nextRetryDelayInMs(attempt);\n\n          if (delayInMs == null) {\n            break;\n          }\n\n          try {\n            logger.verbose(`Delay time for reconnect attempt ${attempt}: ${delayInMs}`);\n            await delay(delayInMs);\n          } catch {}\n        }\n      }\n    } finally {\n      if (!isSuccess) {\n        this._handleConnectionStopped();\n      }\n    }\n  }\n\n  private _handleConnectionStopped(): void {\n    this._isStopping = false;\n    this._state = WebPubSubClientState.Stopped;\n    this._safeEmitStopped();\n  }\n\n  private _getActiveKeepaliveTask(): AbortableTask {\n    return new AbortableTask(async () => {\n      this._sequenceId.tryUpdate(0); // force update\n    }, this._activeTimeoutInMs);\n  }\n\n  private async _sendMessage(\n    message: WebPubSubMessage,\n    abortSignal?: AbortSignalLike,\n  ): Promise<void> {\n    if (!this._wsClient || !this._wsClient.isOpen()) {\n      throw new Error(\"The connection is not connected.\");\n    }\n\n    const payload = this._protocol.writeMessage(message);\n    await this._wsClient!.send(payload, abortSignal);\n  }\n\n  private async _sendMessageWithAckId(\n    messageProvider: (ackId: number) => WebPubSubMessage,\n    ackId?: number,\n    abortSignal?: AbortSignalLike,\n  ): Promise<WebPubSubResult> {\n    if (ackId == null) {\n      ackId = this.nextAckId();\n    }\n\n    const message = messageProvider(ackId);\n    if (!this._ackMap.has(ackId)) {\n      this._ackMap.set(ackId, new AckEntity(ackId));\n    }\n    const entity = this._ackMap.get(ackId)!;\n\n    try {\n      await this._sendMessage(message, abortSignal);\n    } catch (error) {\n      this._ackMap.delete(ackId);\n\n      let errorMessage: string = \"\";\n      if (error instanceof Error) {\n        errorMessage = error.message;\n      }\n      throw new SendMessageError(errorMessage, { ackId: ackId });\n    }\n\n    if (abortSignal) {\n      try {\n        return await abortablePromise(entity.promise(), abortSignal);\n      } catch (err) {\n        if (err instanceof Error && err.name === \"AbortError\") {\n          throw new SendMessageError(\"Cancelled by abortSignal\", { ackId: ackId });\n        }\n        throw err;\n      }\n    }\n\n    return await entity.promise();\n  }\n\n  private async _handleConnectionClose(): Promise<void> {\n    // Clean ack cache\n    this._ackMap.forEach((value, key) => {\n      if (this._ackMap.delete(key)) {\n        value.reject(\n          new SendMessageError(\"Connection is disconnected before receive ack from the service\", {\n            ackId: value.ackId,\n          } as SendMessageErrorOptions),\n        );\n      }\n    });\n\n    if (this._isStopping) {\n      logger.warning(\"The client is stopping state. Stop recovery.\");\n      this._handleConnectionCloseAndNoRecovery();\n      return;\n    }\n\n    if (this._lastCloseEvent && this._lastCloseEvent.code === 1008) {\n      logger.warning(\"The websocket close with status code 1008. Stop recovery.\");\n      this._handleConnectionCloseAndNoRecovery();\n      return;\n    }\n\n    if (!this._protocol.isReliableSubProtocol) {\n      logger.warning(\"The protocol is not reliable, recovery is not applicable\");\n      this._handleConnectionCloseAndNoRecovery();\n      return;\n    }\n\n    // Build recovery uri\n    const recoveryUri = this._buildRecoveryUri();\n    if (!recoveryUri) {\n      logger.warning(\"Connection id or reconnection token is not available\");\n      this._handleConnectionCloseAndNoRecovery();\n      return;\n    }\n\n    // Try recover connection\n    let recovered = false;\n    this._state = WebPubSubClientState.Recovering;\n    const abortSignal = AbortController.timeout(30 * 1000);\n    try {\n      while (!abortSignal.aborted || this._isStopping) {\n        try {\n          await this._connectCore.call(this, recoveryUri);\n          recovered = true;\n          return;\n        } catch {\n          await delay(1000);\n        }\n      }\n    } finally {\n      if (!recovered) {\n        logger.warning(\"Recovery attempts failed more then 30 seconds or the client is stopping\");\n        this._handleConnectionCloseAndNoRecovery();\n      }\n    }\n  }\n\n  private _safeEmitConnected(connectionId: string, userId: string): void {\n    this._emitEvent(\"connected\", {\n      connectionId: connectionId,\n      userId: userId,\n    } as OnConnectedArgs);\n  }\n\n  private _safeEmitDisconnected(\n    connectionId: string | undefined,\n    lastDisconnectedMessage: DisconnectedMessage | undefined,\n  ): void {\n    this._emitEvent(\"disconnected\", {\n      connectionId: connectionId,\n      message: lastDisconnectedMessage,\n    } as OnDisconnectedArgs);\n  }\n\n  private _safeEmitGroupMessage(message: GroupDataMessage): void {\n    this._emitEvent(\"group-message\", {\n      message: message,\n    } as OnGroupDataMessageArgs);\n  }\n\n  private _safeEmitServerMessage(message: ServerDataMessage): void {\n    this._emitEvent(\"server-message\", {\n      message: message,\n    } as OnServerDataMessageArgs);\n  }\n\n  private _safeEmitStopped(): void {\n    this._emitEvent(\"stopped\", {});\n  }\n\n  private _safeEmitRejoinGroupFailed(groupName: string, err: unknown): void {\n    this._emitEvent(\"rejoin-group-failed\", {\n      group: groupName,\n      error: err,\n    } as OnRejoinGroupFailedArgs);\n  }\n\n  private _buildDefaultOptions(clientOptions: WebPubSubClientOptions): WebPubSubClientOptions {\n    if (clientOptions.autoReconnect == null) {\n      clientOptions.autoReconnect = true;\n    }\n\n    if (clientOptions.autoRejoinGroups == null) {\n      clientOptions.autoRejoinGroups = true;\n    }\n\n    if (clientOptions.protocol == null) {\n      clientOptions.protocol = WebPubSubJsonReliableProtocol();\n    }\n\n    this._buildMessageRetryOptions(clientOptions);\n    this._buildReconnectRetryOptions(clientOptions);\n\n    return clientOptions;\n  }\n\n  private _buildMessageRetryOptions(clientOptions: WebPubSubClientOptions): void {\n    if (!clientOptions.messageRetryOptions) {\n      clientOptions.messageRetryOptions = {};\n    }\n\n    if (\n      clientOptions.messageRetryOptions.maxRetries == null ||\n      clientOptions.messageRetryOptions.maxRetries < 0\n    ) {\n      clientOptions.messageRetryOptions.maxRetries = 3;\n    }\n\n    if (\n      clientOptions.messageRetryOptions.retryDelayInMs == null ||\n      clientOptions.messageRetryOptions.retryDelayInMs < 0\n    ) {\n      clientOptions.messageRetryOptions.retryDelayInMs = 1000;\n    }\n\n    if (\n      clientOptions.messageRetryOptions.maxRetryDelayInMs == null ||\n      clientOptions.messageRetryOptions.maxRetryDelayInMs < 0\n    ) {\n      clientOptions.messageRetryOptions.maxRetryDelayInMs = 30000;\n    }\n\n    if (clientOptions.messageRetryOptions.mode == null) {\n      clientOptions.messageRetryOptions.mode = \"Fixed\";\n    }\n  }\n\n  private _buildReconnectRetryOptions(clientOptions: WebPubSubClientOptions): void {\n    if (!clientOptions.reconnectRetryOptions) {\n      clientOptions.reconnectRetryOptions = {};\n    }\n\n    if (\n      clientOptions.reconnectRetryOptions.maxRetries == null ||\n      clientOptions.reconnectRetryOptions.maxRetries < 0\n    ) {\n      clientOptions.reconnectRetryOptions.maxRetries = Number.MAX_VALUE;\n    }\n\n    if (\n      clientOptions.reconnectRetryOptions.retryDelayInMs == null ||\n      clientOptions.reconnectRetryOptions.retryDelayInMs < 0\n    ) {\n      clientOptions.reconnectRetryOptions.retryDelayInMs = 1000;\n    }\n\n    if (\n      clientOptions.reconnectRetryOptions.maxRetryDelayInMs == null ||\n      clientOptions.reconnectRetryOptions.maxRetryDelayInMs < 0\n    ) {\n      clientOptions.reconnectRetryOptions.maxRetryDelayInMs = 30000;\n    }\n\n    if (clientOptions.reconnectRetryOptions.mode == null) {\n      clientOptions.reconnectRetryOptions.mode = \"Fixed\";\n    }\n  }\n\n  private _buildRecoveryUri(): string | null {\n    if (this._connectionId && this._reconnectionToken && this._uri) {\n      const url = new URL(this._uri);\n      url.searchParams.append(\"awps_connection_id\", this._connectionId);\n      url.searchParams.append(\"awps_reconnection_token\", this._reconnectionToken);\n      return url.toString();\n    }\n    return null;\n  }\n\n  private _getOrAddGroup(name: string): WebPubSubGroup {\n    if (!this._groupMap.has(name)) {\n      this._groupMap.set(name, new WebPubSubGroup(name));\n    }\n    return this._groupMap.get(name) as WebPubSubGroup;\n  }\n\n  private _changeState(newState: WebPubSubClientState): void {\n    logger.verbose(\n      `The client state transfer from ${this._state.toString()} to ${newState.toString()}`,\n    );\n    this._state = newState;\n  }\n\n  private async _operationExecuteWithRetry<T>(\n    inner: () => Promise<T>,\n    signal?: AbortSignalLike,\n  ): Promise<T> {\n    let retryAttempt = 0;\n\n    while (true) {\n      try {\n        return await inner.call(this);\n      } catch (err) {\n        retryAttempt++;\n        const delayInMs = this._messageRetryPolicy.nextRetryDelayInMs(retryAttempt);\n        if (delayInMs == null) {\n          throw err;\n        }\n\n        await delay(delayInMs);\n\n        if (signal?.aborted) {\n          throw err;\n        }\n      }\n    }\n  }\n}\n\nclass RetryPolicy {\n  private _retryOptions: WebPubSubRetryOptions;\n  private _maxRetriesToGetMaxDelay: number;\n\n  public constructor(retryOptions: WebPubSubRetryOptions) {\n    this._retryOptions = retryOptions;\n    this._maxRetriesToGetMaxDelay = Math.ceil(\n      Math.log2(this._retryOptions.maxRetryDelayInMs!) -\n        Math.log2(this._retryOptions.retryDelayInMs!) +\n        1,\n    );\n  }\n\n  public nextRetryDelayInMs(retryAttempt: number): number | null {\n    if (retryAttempt > this._retryOptions.maxRetries!) {\n      return null;\n    } else {\n      if (this._retryOptions.mode! === \"Fixed\") {\n        return this._retryOptions.retryDelayInMs!;\n      } else {\n        return this._calculateExponentialDelay(retryAttempt);\n      }\n    }\n  }\n\n  private _calculateExponentialDelay(attempt: number): number {\n    if (attempt >= this._maxRetriesToGetMaxDelay) {\n      return this._retryOptions.maxRetryDelayInMs!;\n    } else {\n      return (1 << (attempt - 1)) * this._retryOptions.retryDelayInMs!;\n    }\n  }\n}\n\nclass WebPubSubGroup {\n  public readonly name: string;\n  public isJoined = false;\n\n  constructor(name: string) {\n    this.name = name;\n  }\n}\n\nclass AckEntity {\n  private readonly _promise: Promise<WebPubSubResult>;\n  private _resolve?: (value: WebPubSubResult | PromiseLike<WebPubSubResult>) => void;\n  private _reject?: (reason?: any) => void;\n\n  constructor(ackId: number) {\n    this._promise = new Promise<WebPubSubResult>((resolve, reject) => {\n      this._resolve = resolve;\n      this._reject = reject;\n    });\n    this.ackId = ackId;\n  }\n\n  public ackId;\n\n  public promise(): Promise<WebPubSubResult> {\n    return this._promise;\n  }\n\n  public resolve(value: WebPubSubResult | PromiseLike<WebPubSubResult>): void {\n    this._resolve!(value);\n  }\n\n  public reject(reason?: any): void {\n    this._reject!(reason);\n  }\n}\n\nclass SequenceId {\n  private _sequenceId: number;\n  private _isUpdate: boolean;\n\n  constructor() {\n    this._sequenceId = 0;\n    this._isUpdate = false;\n  }\n\n  public tryUpdate(sequenceId: number): number {\n    this._isUpdate = true;\n    if (sequenceId > this._sequenceId) {\n      const diff = sequenceId - this._sequenceId;\n      this._sequenceId = sequenceId;\n      return diff;\n    }\n    return 0;\n  }\n\n  public tryGetSequenceId(): [boolean, number | null] {\n    if (this._isUpdate) {\n      this._isUpdate = false;\n      return [true, this._sequenceId];\n    }\n\n    return [false, null];\n  }\n\n  public reset(): void {\n    this._sequenceId = 0;\n    this._isUpdate = false;\n  }\n}\n\nclass AbortableTask {\n  private readonly _func: (obj?: any) => Promise<void>;\n  private readonly _abortController: AbortController;\n  private readonly _interval: number;\n  private readonly _obj?: any;\n\n  constructor(func: (obj?: any) => Promise<void>, interval: number, obj?: any) {\n    this._func = func;\n    this._abortController = new AbortController();\n    this._interval = interval;\n    this._obj = obj;\n    this._start();\n  }\n\n  public abort(): void {\n    try {\n      this._abortController.abort();\n    } catch {}\n  }\n\n  private async _start(): Promise<void> {\n    const signal = this._abortController.signal;\n    while (!signal.aborted) {\n      try {\n        await this._func(this._obj);\n      } catch {\n      } finally {\n        await delay(this._interval);\n      }\n    }\n  }\n}\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { AckMessageError } from \"../models/messages\";\n\n/**\n * Error when sending message failed\n */\nexport class SendMessageError extends Error {\n  /**\n   * Error name\n   */\n  public name: string;\n  /**\n   * The ack id of the message\n   */\n  public ackId?: number;\n  /**\n   * The error details from the service\n   */\n  public errorDetail?: AckMessageError;\n  /**\n   * Initialize a SendMessageError\n   * @param message - The error message\n   * @param ackMessage - The ack message\n   */\n  constructor(message: string, options: SendMessageErrorOptions) {\n    super(message);\n    this.name = \"SendMessageError\";\n    this.ackId = options.ackId;\n    this.errorDetail = options.errorDetail;\n  }\n}\n\nexport interface SendMessageErrorOptions {\n  /**\n   * The ack id of the message\n   */\n  ackId?: number;\n  /**\n   * The error details from the service\n   */\n  errorDetail?: AckMessageError;\n}\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nexport function log(...args: any[]): void {\n  if (args.length > 0) {\n    const firstArg = String(args[0]);\n    if (firstArg.includes(\":error\")) {\n      console.error(...args);\n    } else if (firstArg.includes(\":warning\")) {\n      console.warn(...args);\n    } else if (firstArg.includes(\":info\")) {\n      console.info(...args);\n    } else if (firstArg.includes(\":verbose\")) {\n      console.debug(...args);\n    } else {\n      console.debug(...args);\n    }\n  }\n}\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { log } from \"./log.js\";\n\n/**\n * A simple mechanism for enabling logging.\n * Intended to mimic the publicly available `debug` package.\n */\nexport interface Debug {\n  /**\n   * Creates a new logger with the given namespace.\n   */\n  (namespace: string): Debugger;\n  /**\n   * The default log method (defaults to console)\n   */\n  log: (...args: any[]) => void;\n  /**\n   * Enables a particular set of namespaces.\n   * To enable multiple separate them with commas, e.g. \"info,debug\".\n   * Supports wildcards, e.g. \"azure:*\"\n   * Supports skip syntax, e.g. \"azure:*,-azure:storage:*\" will enable\n   * everything under azure except for things under azure:storage.\n   */\n  enable: (namespaces: string) => void;\n  /**\n   * Checks if a particular namespace is enabled.\n   */\n  enabled: (namespace: string) => boolean;\n  /**\n   * Disables all logging, returns what was previously enabled.\n   */\n  disable: () => string;\n}\n\n/**\n * A log function that can be dynamically enabled and redirected.\n */\nexport interface Debugger {\n  /**\n   * Logs the given arguments to the `log` method.\n   */\n  (...args: any[]): void;\n  /**\n   * True if this logger is active and logging.\n   */\n  enabled: boolean;\n  /**\n   * Used to cleanup/remove this logger.\n   */\n  destroy: () => boolean;\n  /**\n   * The current log method. Can be overridden to redirect output.\n   */\n  log: (...args: any[]) => void;\n  /**\n   * The namespace of this logger.\n   */\n  namespace: string;\n  /**\n   * Extends this logger with a child namespace.\n   * Namespaces are separated with a ':' character.\n   */\n  extend: (namespace: string) => Debugger;\n}\n\nconst debugEnvVariable =\n  (typeof process !== \"undefined\" && process.env && process.env.DEBUG) || undefined;\n\nlet enabledString: string | undefined;\nlet enabledNamespaces: RegExp[] = [];\nlet skippedNamespaces: RegExp[] = [];\nconst debuggers: Debugger[] = [];\n\nif (debugEnvVariable) {\n  enable(debugEnvVariable);\n}\n\nconst debugObj: Debug = Object.assign(\n  (namespace: string): Debugger => {\n    return createDebugger(namespace);\n  },\n  {\n    enable,\n    enabled,\n    disable,\n    log,\n  },\n);\n\nfunction enable(namespaces: string): void {\n  enabledString = namespaces;\n  enabledNamespaces = [];\n  skippedNamespaces = [];\n  const wildcard = /\\*/g;\n  const namespaceList = namespaces.split(\",\").map((ns) => ns.trim().replace(wildcard, \".*?\"));\n  for (const ns of namespaceList) {\n    if (ns.startsWith(\"-\")) {\n      skippedNamespaces.push(new RegExp(`^${ns.substr(1)}$`));\n    } else {\n      enabledNamespaces.push(new RegExp(`^${ns}$`));\n    }\n  }\n  for (const instance of debuggers) {\n    instance.enabled = enabled(instance.namespace);\n  }\n}\n\nfunction enabled(namespace: string): boolean {\n  if (namespace.endsWith(\"*\")) {\n    return true;\n  }\n\n  for (const skipped of skippedNamespaces) {\n    if (skipped.test(namespace)) {\n      return false;\n    }\n  }\n  for (const enabledNamespace of enabledNamespaces) {\n    if (enabledNamespace.test(namespace)) {\n      return true;\n    }\n  }\n  return false;\n}\n\nfunction disable(): string {\n  const result = enabledString || \"\";\n  enable(\"\");\n  return result;\n}\n\nfunction createDebugger(namespace: string): Debugger {\n  const newDebugger: Debugger = Object.assign(debug, {\n    enabled: enabled(namespace),\n    destroy,\n    log: debugObj.log,\n    namespace,\n    extend,\n  });\n\n  function debug(...args: any[]): void {\n    if (!newDebugger.enabled) {\n      return;\n    }\n    if (args.length > 0) {\n      args[0] = `${namespace} ${args[0]}`;\n    }\n    newDebugger.log(...args);\n  }\n\n  debuggers.push(newDebugger);\n\n  return newDebugger;\n}\n\nfunction destroy(this: Debugger): boolean {\n  const index = debuggers.indexOf(this);\n  if (index >= 0) {\n    debuggers.splice(index, 1);\n    return true;\n  }\n  return false;\n}\n\nfunction extend(this: Debugger, namespace: string): Debugger {\n  const newDebugger = createDebugger(`${this.namespace}:${namespace}`);\n  newDebugger.log = this.log;\n  return newDebugger;\n}\n\nexport default debugObj;\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport debug, { type Debugger } from \"./debug.js\";\nexport type { Debugger } from \"./debug.js\";\n\nconst registeredLoggers = new Set<AzureDebugger>();\nconst logLevelFromEnv =\n  (typeof process !== \"undefined\" && process.env && process.env.AZURE_LOG_LEVEL) || undefined;\n\nlet azureLogLevel: AzureLogLevel | undefined;\n\n/**\n * The AzureLogger provides a mechanism for overriding where logs are output to.\n * By default, logs are sent to stderr.\n * Override the `log` method to redirect logs to another location.\n */\nexport const AzureLogger: AzureClientLogger = debug(\"azure\");\nAzureLogger.log = (...args) => {\n  debug.log(...args);\n};\n\n/**\n * The log levels supported by the logger.\n * The log levels in order of most verbose to least verbose are:\n * - verbose\n * - info\n * - warning\n * - error\n */\nexport type AzureLogLevel = \"verbose\" | \"info\" | \"warning\" | \"error\";\nconst AZURE_LOG_LEVELS = [\"verbose\", \"info\", \"warning\", \"error\"];\n\ntype AzureDebugger = Debugger & { level: AzureLogLevel };\n\n/**\n * An AzureClientLogger is a function that can log to an appropriate severity level.\n */\nexport type AzureClientLogger = Debugger;\n\nif (logLevelFromEnv) {\n  // avoid calling setLogLevel because we don't want a mis-set environment variable to crash\n  if (isAzureLogLevel(logLevelFromEnv)) {\n    setLogLevel(logLevelFromEnv);\n  } else {\n    console.error(\n      `AZURE_LOG_LEVEL set to unknown log level '${logLevelFromEnv}'; logging is not enabled. Acceptable values: ${AZURE_LOG_LEVELS.join(\n        \", \",\n      )}.`,\n    );\n  }\n}\n\n/**\n * Immediately enables logging at the specified log level. If no level is specified, logging is disabled.\n * @param level - The log level to enable for logging.\n * Options from most verbose to least verbose are:\n * - verbose\n * - info\n * - warning\n * - error\n */\nexport function setLogLevel(level?: AzureLogLevel): void {\n  if (level && !isAzureLogLevel(level)) {\n    throw new Error(\n      `Unknown log level '${level}'. Acceptable values: ${AZURE_LOG_LEVELS.join(\",\")}`,\n    );\n  }\n  azureLogLevel = level;\n\n  const enabledNamespaces = [];\n  for (const logger of registeredLoggers) {\n    if (shouldEnable(logger)) {\n      enabledNamespaces.push(logger.namespace);\n    }\n  }\n\n  debug.enable(enabledNamespaces.join(\",\"));\n}\n\n/**\n * Retrieves the currently specified log level.\n */\nexport function getLogLevel(): AzureLogLevel | undefined {\n  return azureLogLevel;\n}\n\nconst levelMap = {\n  verbose: 400,\n  info: 300,\n  warning: 200,\n  error: 100,\n};\n\n/**\n * Defines the methods available on the SDK-facing logger.\n */\n// eslint-disable-next-line @typescript-eslint/no-redeclare\nexport interface AzureLogger {\n  /**\n   * Used for failures the program is unlikely to recover from,\n   * such as Out of Memory.\n   */\n  error: Debugger;\n  /**\n   * Used when a function fails to perform its intended task.\n   * Usually this means the function will throw an exception.\n   * Not used for self-healing events (e.g. automatic retry)\n   */\n  warning: Debugger;\n  /**\n   * Used when a function operates normally.\n   */\n  info: Debugger;\n  /**\n   * Used for detailed troubleshooting scenarios. This is\n   * intended for use by developers / system administrators\n   * for diagnosing specific failures.\n   */\n  verbose: Debugger;\n}\n\n/**\n * Creates a logger for use by the Azure SDKs that inherits from `AzureLogger`.\n * @param namespace - The name of the SDK package.\n * @hidden\n */\nexport function createClientLogger(namespace: string): AzureLogger {\n  const clientRootLogger: AzureClientLogger = AzureLogger.extend(namespace);\n  patchLogMethod(AzureLogger, clientRootLogger);\n  return {\n    error: createLogger(clientRootLogger, \"error\"),\n    warning: createLogger(clientRootLogger, \"warning\"),\n    info: createLogger(clientRootLogger, \"info\"),\n    verbose: createLogger(clientRootLogger, \"verbose\"),\n  };\n}\n\nfunction patchLogMethod(parent: AzureClientLogger, child: AzureClientLogger | AzureDebugger): void {\n  child.log = (...args) => {\n    parent.log(...args);\n  };\n}\n\nfunction createLogger(parent: AzureClientLogger, level: AzureLogLevel): AzureDebugger {\n  const logger: AzureDebugger = Object.assign(parent.extend(level), {\n    level,\n  });\n\n  patchLogMethod(parent, logger);\n\n  if (shouldEnable(logger)) {\n    const enabledNamespaces = debug.disable();\n    debug.enable(enabledNamespaces + \",\" + logger.namespace);\n  }\n\n  registeredLoggers.add(logger);\n\n  return logger;\n}\n\nfunction shouldEnable(logger: AzureDebugger): boolean {\n  return Boolean(azureLogLevel && levelMap[logger.level] <= levelMap[azureLogLevel]);\n}\n\nfunction isAzureLogLevel(logLevel: string): logLevel is AzureLogLevel {\n  return AZURE_LOG_LEVELS.includes(logLevel as any);\n}\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { createClientLogger } from \"@azure/logger\";\n\n/**\n * The \\@azure\\/logger configuration for this package.\n */\nexport const logger = createClientLogger(\"web-pubsub-client\");\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport {\n  AckMessage,\n  ConnectedMessage,\n  DisconnectedMessage,\n  GroupDataMessage,\n  ServerDataMessage,\n  WebPubSubDataType,\n  WebPubSubMessage,\n} from \"../models/messages\";\nimport { JSONTypes } from \"../webPubSubClient\";\nimport { <PERSON>uffer } from \"buffer\";\n\nexport function parseMessages(input: string): WebPubSubMessage | null {\n  // The interface does allow \"ArrayBuffer\" to be passed in, but this implementation does not. So let's throw a useful error.\n  if (typeof input !== \"string\") {\n    throw new Error(\"Invalid input for JSON hub protocol. Expected a string.\");\n  }\n\n  if (!input) {\n    throw new Error(\"No input\");\n  }\n\n  const parsedMessage = JSON.parse(input);\n  const typedMessage = parsedMessage as { type: string; from: string; event: string };\n  let returnMessage: WebPubSubMessage;\n\n  if (typedMessage.type === \"system\") {\n    if (typedMessage.event === \"connected\") {\n      returnMessage = { ...parsedMessage, kind: \"connected\" } as ConnectedMessage;\n    } else if (typedMessage.event === \"disconnected\") {\n      returnMessage = { ...parsedMessage, kind: \"disconnected\" } as DisconnectedMessage;\n    } else {\n      // Forward compatible\n      return null;\n    }\n  } else if (typedMessage.type === \"message\") {\n    if (typedMessage.from === \"group\") {\n      const data = parsePayload(parsedMessage.data, parsedMessage.dataType as WebPubSubDataType);\n      if (data === null) {\n        return null;\n      }\n      returnMessage = { ...parsedMessage, data: data, kind: \"groupData\" } as GroupDataMessage;\n    } else if (typedMessage.from === \"server\") {\n      const data = parsePayload(parsedMessage.data, parsedMessage.dataType as WebPubSubDataType);\n      if (data === null) {\n        return null;\n      }\n      returnMessage = {\n        ...parsedMessage,\n        data: data,\n        kind: \"serverData\",\n      } as ServerDataMessage;\n    } else {\n      // Forward compatible\n      return null;\n    }\n  } else if (typedMessage.type === \"ack\") {\n    returnMessage = { ...parsedMessage, kind: \"ack\" } as AckMessage;\n  } else {\n    // Forward compatible\n    return null;\n  }\n  return returnMessage;\n}\n\nexport function writeMessage(message: WebPubSubMessage): string {\n  let data: any;\n  switch (message.kind) {\n    case \"joinGroup\": {\n      data = { type: \"joinGroup\", group: message.group, ackId: message.ackId } as JoinGroupData;\n      break;\n    }\n    case \"leaveGroup\": {\n      data = { type: \"leaveGroup\", group: message.group, ackId: message.ackId } as LeaveGroupData;\n      break;\n    }\n    case \"sendEvent\": {\n      data = {\n        type: \"event\",\n        event: message.event,\n        ackId: message.ackId,\n        dataType: message.dataType,\n        data: getPayload(message.data, message.dataType),\n      } as SendEventData;\n      break;\n    }\n    case \"sendToGroup\": {\n      data = {\n        type: \"sendToGroup\",\n        group: message.group,\n        ackId: message.ackId,\n        dataType: message.dataType,\n        data: getPayload(message.data, message.dataType),\n        noEcho: message.noEcho,\n      } as SendToGroupData;\n      break;\n    }\n    case \"sequenceAck\": {\n      data = { type: \"sequenceAck\", sequenceId: message.sequenceId } as SequenceAckData;\n      break;\n    }\n    default: {\n      throw new Error(`Unsupported type: ${message.kind}`);\n    }\n  }\n\n  return JSON.stringify(data);\n}\n\ninterface JoinGroupData {\n  readonly type: \"joinGroup\";\n  group: string;\n  ackId?: number;\n}\n\ninterface LeaveGroupData {\n  readonly type: \"leaveGroup\";\n  group: string;\n  ackId?: number;\n}\n\ninterface SendToGroupData {\n  readonly type: \"sendToGroup\";\n  group: string;\n  ackId?: number;\n  dataType: WebPubSubDataType;\n  data: any;\n  noEcho: boolean;\n}\n\ninterface SendEventData {\n  readonly type: \"event\";\n  ackId?: number;\n  dataType: WebPubSubDataType;\n  data: any;\n  event: string;\n}\n\ninterface SequenceAckData {\n  readonly type: \"sequenceAck\";\n  sequenceId: number;\n}\n\nfunction getPayload(data: JSONTypes | ArrayBuffer, dataType: WebPubSubDataType): any {\n  switch (dataType) {\n    case \"text\": {\n      if (typeof data !== \"string\") {\n        throw new TypeError(\"Message must be a string.\");\n      }\n      return data;\n    }\n    case \"json\": {\n      return data;\n    }\n    case \"binary\":\n    case \"protobuf\": {\n      if (data instanceof ArrayBuffer) {\n        return Buffer.from(data).toString(\"base64\");\n      }\n      throw new TypeError(\"Message must be a ArrayBuffer\");\n    }\n  }\n}\n\nfunction parsePayload(data: any, dataType: string): JSONTypes | ArrayBuffer | null {\n  if (dataType === \"text\") {\n    if (typeof data !== \"string\") {\n      throw new TypeError(\"Message must be a string when dataType is text\");\n    }\n    return data as string;\n  } else if (dataType === \"json\") {\n    return data as JSONTypes;\n  } else if (dataType === \"binary\" || dataType === \"protobuf\") {\n    const buf = Buffer.from(data as string, \"base64\");\n    return buf.buffer.slice(buf.byteOffset, buf.byteOffset + buf.byteLength) as ArrayBuffer;\n  } else {\n    // Forward compatible\n    return null;\n  }\n}\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { WebPubSubClientProtocol } from \".\";\nimport { WebPubSubMessage } from \"../models/messages\";\nimport * as base from \"./jsonProtocolBase\";\n\n/**\n * The \"json.webpubsub.azure.v1\" protocol\n */\nexport class WebPubSubJsonProtocolImpl implements WebPubSubClientProtocol {\n  /**\n   * True if the protocol supports reliable features\n   */\n  public readonly isReliableSubProtocol = false;\n\n  /**\n   * The name of subprotocol. Name will be used in websocket subprotocol\n   */\n  public readonly name = \"json.webpubsub.azure.v1\";\n\n  /**\n   * Creates WebPubSubMessage objects from the specified serialized representation.\n   * @param input - The serialized representation\n   */\n  public parseMessages(input: string): WebPubSubMessage | null {\n    return base.parseMessages(input);\n  }\n\n  /**\n   * Write WebPubSubMessage to string\n   * @param message - The message to be written\n   */\n  public writeMessage(message: WebPubSubMessage): string {\n    return base.writeMessage(message);\n  }\n}\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { WebPubSubClientProtocol } from \".\";\nimport { WebPubSubMessage } from \"../models/messages\";\nimport * as base from \"./jsonProtocolBase\";\n\n/**\n * The \"json.reliable.webpubsub.azure.v1\" protocol\n */\nexport class WebPubSubJsonReliableProtocolImpl implements WebPubSubClientProtocol {\n  /**\n   * True if the protocol supports reliable features\n   */\n  public readonly isReliableSubProtocol = true;\n\n  /**\n   * The name of subprotocol. Name will be used in websocket subprotocol\n   */\n  public readonly name = \"json.reliable.webpubsub.azure.v1\";\n\n  /**\n   * Creates WebPubSubMessage objects from the specified serialized representation.\n   * @param input - The serialized representation\n   */\n  public parseMessages(input: string): WebPubSubMessage | null {\n    return base.parseMessages(input);\n  }\n\n  /**\n   * Write WebPubSubMessage to string\n   * @param message - The message to be written\n   */\n  public writeMessage(message: WebPubSubMessage): string {\n    return base.writeMessage(message);\n  }\n}\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { WebPubSubMessage } from \"../models/messages\";\nimport { WebPubSubJsonProtocolImpl } from \"./webPubSubJsonProtocol\";\nimport { WebPubSubJsonReliableProtocolImpl } from \"./webPubSubJsonReliableProtocol\";\n\n/**\n * The interface to be implemented for a web pubsub subprotocol\n */\nexport interface WebPubSubClientProtocol {\n  /**\n   * The name of subprotocol. Name will be used in websocket subprotocol\n   */\n  readonly name: string;\n\n  /**\n   * True if the protocol supports reliable features\n   */\n  readonly isReliableSubProtocol: boolean;\n\n  /**\n   * Creates WebPubSubMessage objects from the specified serialized representation.\n   * @param input - The serialized representation\n   */\n  parseMessages(input: string | ArrayBuffer | Buffer): WebPubSubMessage[] | WebPubSubMessage | null;\n\n  /**\n   * Write WebPubSubMessage to string or ArrayBuffer\n   * @param message - The message to be written\n   */\n  writeMessage(message: WebPubSubMessage): string | ArrayBuffer;\n}\n\n/**\n * Return the \"json.webpubsub.azure.v1\" protocol\n */\nexport const WebPubSubJsonProtocol = (): WebPubSubClientProtocol => {\n  return new WebPubSubJsonProtocolImpl();\n};\n\n/**\n * Return the \"json.reliable.webpubsub.azure.v1\" protocol\n */\nexport const WebPubSubJsonReliableProtocol = (): WebPubSubClientProtocol => {\n  return new WebPubSubJsonReliableProtocolImpl();\n};\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { AbortSignalLike } from \"@azure/abort-controller\";\nimport { WebSocketClientFactoryLike, WebSocketClientLike } from \"./websocketClientLike\";\n\nexport class WebSocketClient implements WebSocketClientLike {\n  private _socket: WebSocket;\n\n  public constructor(uri: string, protocolName: string) {\n    this._socket = new WebSocket(uri, protocolName);\n    this._socket.binaryType = \"arraybuffer\";\n  }\n\n  onopen(fn: () => void): void {\n    this._socket.onopen = fn;\n  }\n\n  onclose(fn: (code: number, reason: string) => void): void {\n    this._socket.onclose = (ev: CloseEvent) => fn(ev.code, ev.reason);\n  }\n\n  onerror(fn: (error: any) => void): void {\n    this._socket.onerror = (ev: Event) => fn(ev);\n  }\n\n  onmessage(fn: (data: string | Buffer | ArrayBuffer | Buffer[]) => void): void {\n    this._socket.onmessage = (event: MessageEvent) => fn(event.data);\n  }\n\n  close(code?: number, reason?: string): void {\n    this._socket.close(code, reason);\n  }\n\n  // eslint-disable-next-line @typescript-eslint/explicit-module-boundary-types\n  send(data: any, _?: AbortSignalLike): Promise<void> {\n    return new Promise((resolve, reject) => {\n      try {\n        this._socket.send(data);\n        resolve();\n      } catch (err) {\n        reject(err);\n      }\n    });\n  }\n\n  isOpen(): boolean {\n    return this._socket.readyState === WebSocket.OPEN;\n  }\n}\n\nexport class WebSocketClientFactory implements WebSocketClientFactoryLike {\n  public create(uri: string, protocolName: string): WebSocketClient {\n    return new WebSocketClient(uri, protocolName);\n  }\n}\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { AbortError, AbortSignalLike } from \"@azure/abort-controller\";\n\nexport async function abortablePromise<T>(\n  promise: Promise<T>,\n  signal: AbortSignalLike,\n): Promise<T> {\n  if (signal.aborted) {\n    throw new AbortError(\"The operation was aborted.\");\n  }\n\n  let onAbort: () => void;\n  // eslint-disable-next-line promise/param-names\n  const p = new Promise<T>((_, reject) => {\n    onAbort = (): void => {\n      reject(new AbortError(\"The operation was aborted.\"));\n    };\n\n    signal.addEventListener(\"abort\", onAbort);\n  });\n\n  try {\n    return await Promise.race([promise, p]);\n  } finally {\n    signal.removeEventListener(\"abort\", onAbort!);\n  }\n}\n"], "mappings": ";;;;;;AAAA;AAAA;AAAA;AAuBA,QAAI,IAAI,OAAO,YAAY,WAAW,UAAU;AAChD,QAAI,eAAe,KAAK,OAAO,EAAE,UAAU,aACvC,EAAE,QACF,SAASA,cAAa,QAAQ,UAAU,MAAM;AAC9C,aAAO,SAAS,UAAU,MAAM,KAAK,QAAQ,UAAU,IAAI;AAAA,IAC7D;AAEF,QAAI;AACJ,QAAI,KAAK,OAAO,EAAE,YAAY,YAAY;AACxC,uBAAiB,EAAE;AAAA,IACrB,WAAW,OAAO,uBAAuB;AACvC,uBAAiB,SAASC,gBAAe,QAAQ;AAC/C,eAAO,OAAO,oBAAoB,MAAM,EACrC,OAAO,OAAO,sBAAsB,MAAM,CAAC;AAAA,MAChD;AAAA,IACF,OAAO;AACL,uBAAiB,SAASA,gBAAe,QAAQ;AAC/C,eAAO,OAAO,oBAAoB,MAAM;AAAA,MAC1C;AAAA,IACF;AAEA,aAAS,mBAAmB,SAAS;AACnC,UAAI,WAAW,QAAQ,KAAM,SAAQ,KAAK,OAAO;AAAA,IACnD;AAEA,QAAI,cAAc,OAAO,SAAS,SAASC,aAAY,OAAO;AAC5D,aAAO,UAAU;AAAA,IACnB;AAEA,aAASC,gBAAe;AACtB,MAAAA,cAAa,KAAK,KAAK,IAAI;AAAA,IAC7B;AACA,WAAO,UAAUA;AACjB,WAAO,QAAQ,OAAO;AAGtB,IAAAA,cAAa,eAAeA;AAE5B,IAAAA,cAAa,UAAU,UAAU;AACjC,IAAAA,cAAa,UAAU,eAAe;AACtC,IAAAA,cAAa,UAAU,gBAAgB;AAIvC,QAAI,sBAAsB;AAE1B,aAAS,cAAc,UAAU;AAC/B,UAAI,OAAO,aAAa,YAAY;AAClC,cAAM,IAAI,UAAU,qEAAqE,OAAO,QAAQ;AAAA,MAC1G;AAAA,IACF;AAEA,WAAO,eAAeA,eAAc,uBAAuB;AAAA,MACzD,YAAY;AAAA,MACZ,KAAK,WAAW;AACd,eAAO;AAAA,MACT;AAAA,MACA,KAAK,SAAS,KAAK;AACjB,YAAI,OAAO,QAAQ,YAAY,MAAM,KAAK,YAAY,GAAG,GAAG;AAC1D,gBAAM,IAAI,WAAW,oGAAoG,MAAM,GAAG;AAAA,QACpI;AACA,8BAAsB;AAAA,MACxB;AAAA,IACF,CAAC;AAED,IAAAA,cAAa,OAAO,WAAW;AAE7B,UAAI,KAAK,YAAY,UACjB,KAAK,YAAY,OAAO,eAAe,IAAI,EAAE,SAAS;AACxD,aAAK,UAAU,uBAAO,OAAO,IAAI;AACjC,aAAK,eAAe;AAAA,MACtB;AAEA,WAAK,gBAAgB,KAAK,iBAAiB;AAAA,IAC7C;AAIA,IAAAA,cAAa,UAAU,kBAAkB,SAAS,gBAAgB,GAAG;AACnE,UAAI,OAAO,MAAM,YAAY,IAAI,KAAK,YAAY,CAAC,GAAG;AACpD,cAAM,IAAI,WAAW,kFAAkF,IAAI,GAAG;AAAA,MAChH;AACA,WAAK,gBAAgB;AACrB,aAAO;AAAA,IACT;AAEA,aAAS,iBAAiB,MAAM;AAC9B,UAAI,KAAK,kBAAkB;AACzB,eAAOA,cAAa;AACtB,aAAO,KAAK;AAAA,IACd;AAEA,IAAAA,cAAa,UAAU,kBAAkB,SAAS,kBAAkB;AAClE,aAAO,iBAAiB,IAAI;AAAA,IAC9B;AAEA,IAAAA,cAAa,UAAU,OAAO,SAAS,KAAK,MAAM;AAChD,UAAI,OAAO,CAAC;AACZ,eAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,IAAK,MAAK,KAAK,UAAU,CAAC,CAAC;AACjE,UAAI,UAAW,SAAS;AAExB,UAAI,SAAS,KAAK;AAClB,UAAI,WAAW;AACb,kBAAW,WAAW,OAAO,UAAU;AAAA,eAChC,CAAC;AACR,eAAO;AAGT,UAAI,SAAS;AACX,YAAI;AACJ,YAAI,KAAK,SAAS;AAChB,eAAK,KAAK,CAAC;AACb,YAAI,cAAc,OAAO;AAGvB,gBAAM;AAAA,QACR;AAEA,YAAI,MAAM,IAAI,MAAM,sBAAsB,KAAK,OAAO,GAAG,UAAU,MAAM,GAAG;AAC5E,YAAI,UAAU;AACd,cAAM;AAAA,MACR;AAEA,UAAI,UAAU,OAAO,IAAI;AAEzB,UAAI,YAAY;AACd,eAAO;AAET,UAAI,OAAO,YAAY,YAAY;AACjC,qBAAa,SAAS,MAAM,IAAI;AAAA,MAClC,OAAO;AACL,YAAI,MAAM,QAAQ;AAClB,YAAI,YAAY,WAAW,SAAS,GAAG;AACvC,iBAAS,IAAI,GAAG,IAAI,KAAK,EAAE;AACzB,uBAAa,UAAU,CAAC,GAAG,MAAM,IAAI;AAAA,MACzC;AAEA,aAAO;AAAA,IACT;AAEA,aAAS,aAAa,QAAQ,MAAM,UAAU,SAAS;AACrD,UAAI;AACJ,UAAI;AACJ,UAAI;AAEJ,oBAAc,QAAQ;AAEtB,eAAS,OAAO;AAChB,UAAI,WAAW,QAAW;AACxB,iBAAS,OAAO,UAAU,uBAAO,OAAO,IAAI;AAC5C,eAAO,eAAe;AAAA,MACxB,OAAO;AAGL,YAAI,OAAO,gBAAgB,QAAW;AACpC,iBAAO;AAAA,YAAK;AAAA,YAAe;AAAA,YACf,SAAS,WAAW,SAAS,WAAW;AAAA,UAAQ;AAI5D,mBAAS,OAAO;AAAA,QAClB;AACA,mBAAW,OAAO,IAAI;AAAA,MACxB;AAEA,UAAI,aAAa,QAAW;AAE1B,mBAAW,OAAO,IAAI,IAAI;AAC1B,UAAE,OAAO;AAAA,MACX,OAAO;AACL,YAAI,OAAO,aAAa,YAAY;AAElC,qBAAW,OAAO,IAAI,IACpB,UAAU,CAAC,UAAU,QAAQ,IAAI,CAAC,UAAU,QAAQ;AAAA,QAExD,WAAW,SAAS;AAClB,mBAAS,QAAQ,QAAQ;AAAA,QAC3B,OAAO;AACL,mBAAS,KAAK,QAAQ;AAAA,QACxB;AAGA,YAAI,iBAAiB,MAAM;AAC3B,YAAI,IAAI,KAAK,SAAS,SAAS,KAAK,CAAC,SAAS,QAAQ;AACpD,mBAAS,SAAS;AAGlB,cAAI,IAAI,IAAI,MAAM,iDACE,SAAS,SAAS,MAAM,OAAO,IAAI,IAAI,mEAEvB;AACpC,YAAE,OAAO;AACT,YAAE,UAAU;AACZ,YAAE,OAAO;AACT,YAAE,QAAQ,SAAS;AACnB,6BAAmB,CAAC;AAAA,QACtB;AAAA,MACF;AAEA,aAAO;AAAA,IACT;AAEA,IAAAA,cAAa,UAAU,cAAc,SAAS,YAAY,MAAM,UAAU;AACxE,aAAO,aAAa,MAAM,MAAM,UAAU,KAAK;AAAA,IACjD;AAEA,IAAAA,cAAa,UAAU,KAAKA,cAAa,UAAU;AAEnD,IAAAA,cAAa,UAAU,kBACnB,SAAS,gBAAgB,MAAM,UAAU;AACvC,aAAO,aAAa,MAAM,MAAM,UAAU,IAAI;AAAA,IAChD;AAEJ,aAAS,cAAc;AACrB,UAAI,CAAC,KAAK,OAAO;AACf,aAAK,OAAO,eAAe,KAAK,MAAM,KAAK,MAAM;AACjD,aAAK,QAAQ;AACb,YAAI,UAAU,WAAW;AACvB,iBAAO,KAAK,SAAS,KAAK,KAAK,MAAM;AACvC,eAAO,KAAK,SAAS,MAAM,KAAK,QAAQ,SAAS;AAAA,MACnD;AAAA,IACF;AAEA,aAAS,UAAU,QAAQ,MAAM,UAAU;AACzC,UAAI,QAAQ,EAAE,OAAO,OAAO,QAAQ,QAAW,QAAgB,MAAY,SAAmB;AAC9F,UAAI,UAAU,YAAY,KAAK,KAAK;AACpC,cAAQ,WAAW;AACnB,YAAM,SAAS;AACf,aAAO;AAAA,IACT;AAEA,IAAAA,cAAa,UAAU,OAAO,SAASC,MAAK,MAAM,UAAU;AAC1D,oBAAc,QAAQ;AACtB,WAAK,GAAG,MAAM,UAAU,MAAM,MAAM,QAAQ,CAAC;AAC7C,aAAO;AAAA,IACT;AAEA,IAAAD,cAAa,UAAU,sBACnB,SAAS,oBAAoB,MAAM,UAAU;AAC3C,oBAAc,QAAQ;AACtB,WAAK,gBAAgB,MAAM,UAAU,MAAM,MAAM,QAAQ,CAAC;AAC1D,aAAO;AAAA,IACT;AAGJ,IAAAA,cAAa,UAAU,iBACnB,SAAS,eAAe,MAAM,UAAU;AACtC,UAAI,MAAM,QAAQ,UAAU,GAAG;AAE/B,oBAAc,QAAQ;AAEtB,eAAS,KAAK;AACd,UAAI,WAAW;AACb,eAAO;AAET,aAAO,OAAO,IAAI;AAClB,UAAI,SAAS;AACX,eAAO;AAET,UAAI,SAAS,YAAY,KAAK,aAAa,UAAU;AACnD,YAAI,EAAE,KAAK,iBAAiB;AAC1B,eAAK,UAAU,uBAAO,OAAO,IAAI;AAAA,aAC9B;AACH,iBAAO,OAAO,IAAI;AAClB,cAAI,OAAO;AACT,iBAAK,KAAK,kBAAkB,MAAM,KAAK,YAAY,QAAQ;AAAA,QAC/D;AAAA,MACF,WAAW,OAAO,SAAS,YAAY;AACrC,mBAAW;AAEX,aAAK,IAAI,KAAK,SAAS,GAAG,KAAK,GAAG,KAAK;AACrC,cAAI,KAAK,CAAC,MAAM,YAAY,KAAK,CAAC,EAAE,aAAa,UAAU;AACzD,+BAAmB,KAAK,CAAC,EAAE;AAC3B,uBAAW;AACX;AAAA,UACF;AAAA,QACF;AAEA,YAAI,WAAW;AACb,iBAAO;AAET,YAAI,aAAa;AACf,eAAK,MAAM;AAAA,aACR;AACH,oBAAU,MAAM,QAAQ;AAAA,QAC1B;AAEA,YAAI,KAAK,WAAW;AAClB,iBAAO,IAAI,IAAI,KAAK,CAAC;AAEvB,YAAI,OAAO,mBAAmB;AAC5B,eAAK,KAAK,kBAAkB,MAAM,oBAAoB,QAAQ;AAAA,MAClE;AAEA,aAAO;AAAA,IACT;AAEJ,IAAAA,cAAa,UAAU,MAAMA,cAAa,UAAU;AAEpD,IAAAA,cAAa,UAAU,qBACnB,SAAS,mBAAmB,MAAM;AAChC,UAAI,WAAW,QAAQ;AAEvB,eAAS,KAAK;AACd,UAAI,WAAW;AACb,eAAO;AAGT,UAAI,OAAO,mBAAmB,QAAW;AACvC,YAAI,UAAU,WAAW,GAAG;AAC1B,eAAK,UAAU,uBAAO,OAAO,IAAI;AACjC,eAAK,eAAe;AAAA,QACtB,WAAW,OAAO,IAAI,MAAM,QAAW;AACrC,cAAI,EAAE,KAAK,iBAAiB;AAC1B,iBAAK,UAAU,uBAAO,OAAO,IAAI;AAAA;AAEjC,mBAAO,OAAO,IAAI;AAAA,QACtB;AACA,eAAO;AAAA,MACT;AAGA,UAAI,UAAU,WAAW,GAAG;AAC1B,YAAI,OAAO,OAAO,KAAK,MAAM;AAC7B,YAAI;AACJ,aAAK,IAAI,GAAG,IAAI,KAAK,QAAQ,EAAE,GAAG;AAChC,gBAAM,KAAK,CAAC;AACZ,cAAI,QAAQ,iBAAkB;AAC9B,eAAK,mBAAmB,GAAG;AAAA,QAC7B;AACA,aAAK,mBAAmB,gBAAgB;AACxC,aAAK,UAAU,uBAAO,OAAO,IAAI;AACjC,aAAK,eAAe;AACpB,eAAO;AAAA,MACT;AAEA,kBAAY,OAAO,IAAI;AAEvB,UAAI,OAAO,cAAc,YAAY;AACnC,aAAK,eAAe,MAAM,SAAS;AAAA,MACrC,WAAW,cAAc,QAAW;AAElC,aAAK,IAAI,UAAU,SAAS,GAAG,KAAK,GAAG,KAAK;AAC1C,eAAK,eAAe,MAAM,UAAU,CAAC,CAAC;AAAA,QACxC;AAAA,MACF;AAEA,aAAO;AAAA,IACT;AAEJ,aAAS,WAAW,QAAQ,MAAM,QAAQ;AACxC,UAAI,SAAS,OAAO;AAEpB,UAAI,WAAW;AACb,eAAO,CAAC;AAEV,UAAI,aAAa,OAAO,IAAI;AAC5B,UAAI,eAAe;AACjB,eAAO,CAAC;AAEV,UAAI,OAAO,eAAe;AACxB,eAAO,SAAS,CAAC,WAAW,YAAY,UAAU,IAAI,CAAC,UAAU;AAEnE,aAAO,SACL,gBAAgB,UAAU,IAAI,WAAW,YAAY,WAAW,MAAM;AAAA,IAC1E;AAEA,IAAAA,cAAa,UAAU,YAAY,SAAS,UAAU,MAAM;AAC1D,aAAO,WAAW,MAAM,MAAM,IAAI;AAAA,IACpC;AAEA,IAAAA,cAAa,UAAU,eAAe,SAAS,aAAa,MAAM;AAChE,aAAO,WAAW,MAAM,MAAM,KAAK;AAAA,IACrC;AAEA,IAAAA,cAAa,gBAAgB,SAAS,SAAS,MAAM;AACnD,UAAI,OAAO,QAAQ,kBAAkB,YAAY;AAC/C,eAAO,QAAQ,cAAc,IAAI;AAAA,MACnC,OAAO;AACL,eAAO,cAAc,KAAK,SAAS,IAAI;AAAA,MACzC;AAAA,IACF;AAEA,IAAAA,cAAa,UAAU,gBAAgB;AACvC,aAAS,cAAc,MAAM;AAC3B,UAAI,SAAS,KAAK;AAElB,UAAI,WAAW,QAAW;AACxB,YAAI,aAAa,OAAO,IAAI;AAE5B,YAAI,OAAO,eAAe,YAAY;AACpC,iBAAO;AAAA,QACT,WAAW,eAAe,QAAW;AACnC,iBAAO,WAAW;AAAA,QACpB;AAAA,MACF;AAEA,aAAO;AAAA,IACT;AAEA,IAAAA,cAAa,UAAU,aAAa,SAAS,aAAa;AACxD,aAAO,KAAK,eAAe,IAAI,eAAe,KAAK,OAAO,IAAI,CAAC;AAAA,IACjE;AAEA,aAAS,WAAW,KAAK,GAAG;AAC1B,UAAI,OAAO,IAAI,MAAM,CAAC;AACtB,eAAS,IAAI,GAAG,IAAI,GAAG,EAAE;AACvB,aAAK,CAAC,IAAI,IAAI,CAAC;AACjB,aAAO;AAAA,IACT;AAEA,aAAS,UAAU,MAAM,OAAO;AAC9B,aAAO,QAAQ,IAAI,KAAK,QAAQ;AAC9B,aAAK,KAAK,IAAI,KAAK,QAAQ,CAAC;AAC9B,WAAK,IAAI;AAAA,IACX;AAEA,aAAS,gBAAgB,KAAK;AAC5B,UAAI,MAAM,IAAI,MAAM,IAAI,MAAM;AAC9B,eAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,EAAE,GAAG;AACnC,YAAI,CAAC,IAAI,IAAI,CAAC,EAAE,YAAY,IAAI,CAAC;AAAA,MACnC;AACA,aAAO;AAAA,IACT;AAEA,aAAS,KAAK,SAAS,MAAM;AAC3B,aAAO,IAAI,QAAQ,SAAU,SAAS,QAAQ;AAC5C,iBAAS,cAAc,KAAK;AAC1B,kBAAQ,eAAe,MAAM,QAAQ;AACrC,iBAAO,GAAG;AAAA,QACZ;AAEA,iBAAS,WAAW;AAClB,cAAI,OAAO,QAAQ,mBAAmB,YAAY;AAChD,oBAAQ,eAAe,SAAS,aAAa;AAAA,UAC/C;AACA,kBAAQ,CAAC,EAAE,MAAM,KAAK,SAAS,CAAC;AAAA,QAClC;AAAC;AAED,uCAA+B,SAAS,MAAM,UAAU,EAAE,MAAM,KAAK,CAAC;AACtE,YAAI,SAAS,SAAS;AACpB,wCAA8B,SAAS,eAAe,EAAE,MAAM,KAAK,CAAC;AAAA,QACtE;AAAA,MACF,CAAC;AAAA,IACH;AAEA,aAAS,8BAA8B,SAAS,SAAS,OAAO;AAC9D,UAAI,OAAO,QAAQ,OAAO,YAAY;AACpC,uCAA+B,SAAS,SAAS,SAAS,KAAK;AAAA,MACjE;AAAA,IACF;AAEA,aAAS,+BAA+B,SAAS,MAAM,UAAU,OAAO;AACtE,UAAI,OAAO,QAAQ,OAAO,YAAY;AACpC,YAAI,MAAM,MAAM;AACd,kBAAQ,KAAK,MAAM,QAAQ;AAAA,QAC7B,OAAO;AACL,kBAAQ,GAAG,MAAM,QAAQ;AAAA,QAC3B;AAAA,MACF,WAAW,OAAO,QAAQ,qBAAqB,YAAY;AAGzD,gBAAQ,iBAAiB,MAAM,SAAS,aAAa,KAAK;AAGxD,cAAI,MAAM,MAAM;AACd,oBAAQ,oBAAoB,MAAM,YAAY;AAAA,UAChD;AACA,mBAAS,GAAG;AAAA,QACd,CAAC;AAAA,MACH,OAAO;AACL,cAAM,IAAI,UAAU,wEAAwE,OAAO,OAAO;AAAA,MAC5G;AAAA,IACF;AAAA;AAAA;;;AChfA;AAAA;AAAA;AAEA,YAAQ,aAAa;AACrB,YAAQ,cAAc;AACtB,YAAQ,gBAAgB;AAExB,QAAI,SAAS,CAAC;AACd,QAAI,YAAY,CAAC;AACjB,QAAI,MAAM,OAAO,eAAe,cAAc,aAAa;AAE3D,QAAI,OAAO;AACX,SAAS,IAAI,GAAG,MAAM,KAAK,QAAQ,IAAI,KAAK,EAAE,GAAG;AAC/C,aAAO,CAAC,IAAI,KAAK,CAAC;AAClB,gBAAU,KAAK,WAAW,CAAC,CAAC,IAAI;AAAA,IAClC;AAHS;AAAO;AAOhB,cAAU,IAAI,WAAW,CAAC,CAAC,IAAI;AAC/B,cAAU,IAAI,WAAW,CAAC,CAAC,IAAI;AAE/B,aAAS,QAAS,KAAK;AACrB,UAAIE,OAAM,IAAI;AAEd,UAAIA,OAAM,IAAI,GAAG;AACf,cAAM,IAAI,MAAM,gDAAgD;AAAA,MAClE;AAIA,UAAI,WAAW,IAAI,QAAQ,GAAG;AAC9B,UAAI,aAAa,GAAI,YAAWA;AAEhC,UAAI,kBAAkB,aAAaA,OAC/B,IACA,IAAK,WAAW;AAEpB,aAAO,CAAC,UAAU,eAAe;AAAA,IACnC;AAGA,aAAS,WAAY,KAAK;AACxB,UAAI,OAAO,QAAQ,GAAG;AACtB,UAAI,WAAW,KAAK,CAAC;AACrB,UAAI,kBAAkB,KAAK,CAAC;AAC5B,cAAS,WAAW,mBAAmB,IAAI,IAAK;AAAA,IAClD;AAEA,aAAS,YAAa,KAAK,UAAU,iBAAiB;AACpD,cAAS,WAAW,mBAAmB,IAAI,IAAK;AAAA,IAClD;AAEA,aAAS,YAAa,KAAK;AACzB,UAAI;AACJ,UAAI,OAAO,QAAQ,GAAG;AACtB,UAAI,WAAW,KAAK,CAAC;AACrB,UAAI,kBAAkB,KAAK,CAAC;AAE5B,UAAI,MAAM,IAAI,IAAI,YAAY,KAAK,UAAU,eAAe,CAAC;AAE7D,UAAI,UAAU;AAGd,UAAIA,OAAM,kBAAkB,IACxB,WAAW,IACX;AAEJ,UAAIC;AACJ,WAAKA,KAAI,GAAGA,KAAID,MAAKC,MAAK,GAAG;AAC3B,cACG,UAAU,IAAI,WAAWA,EAAC,CAAC,KAAK,KAChC,UAAU,IAAI,WAAWA,KAAI,CAAC,CAAC,KAAK,KACpC,UAAU,IAAI,WAAWA,KAAI,CAAC,CAAC,KAAK,IACrC,UAAU,IAAI,WAAWA,KAAI,CAAC,CAAC;AACjC,YAAI,SAAS,IAAK,OAAO,KAAM;AAC/B,YAAI,SAAS,IAAK,OAAO,IAAK;AAC9B,YAAI,SAAS,IAAI,MAAM;AAAA,MACzB;AAEA,UAAI,oBAAoB,GAAG;AACzB,cACG,UAAU,IAAI,WAAWA,EAAC,CAAC,KAAK,IAChC,UAAU,IAAI,WAAWA,KAAI,CAAC,CAAC,KAAK;AACvC,YAAI,SAAS,IAAI,MAAM;AAAA,MACzB;AAEA,UAAI,oBAAoB,GAAG;AACzB,cACG,UAAU,IAAI,WAAWA,EAAC,CAAC,KAAK,KAChC,UAAU,IAAI,WAAWA,KAAI,CAAC,CAAC,KAAK,IACpC,UAAU,IAAI,WAAWA,KAAI,CAAC,CAAC,KAAK;AACvC,YAAI,SAAS,IAAK,OAAO,IAAK;AAC9B,YAAI,SAAS,IAAI,MAAM;AAAA,MACzB;AAEA,aAAO;AAAA,IACT;AAEA,aAAS,gBAAiB,KAAK;AAC7B,aAAO,OAAO,OAAO,KAAK,EAAI,IAC5B,OAAO,OAAO,KAAK,EAAI,IACvB,OAAO,OAAO,IAAI,EAAI,IACtB,OAAO,MAAM,EAAI;AAAA,IACrB;AAEA,aAAS,YAAa,OAAO,OAAO,KAAK;AACvC,UAAI;AACJ,UAAI,SAAS,CAAC;AACd,eAASA,KAAI,OAAOA,KAAI,KAAKA,MAAK,GAAG;AACnC,eACI,MAAMA,EAAC,KAAK,KAAM,aAClB,MAAMA,KAAI,CAAC,KAAK,IAAK,UACtB,MAAMA,KAAI,CAAC,IAAI;AAClB,eAAO,KAAK,gBAAgB,GAAG,CAAC;AAAA,MAClC;AACA,aAAO,OAAO,KAAK,EAAE;AAAA,IACvB;AAEA,aAAS,cAAe,OAAO;AAC7B,UAAI;AACJ,UAAID,OAAM,MAAM;AAChB,UAAI,aAAaA,OAAM;AACvB,UAAI,QAAQ,CAAC;AACb,UAAI,iBAAiB;AAGrB,eAASC,KAAI,GAAGC,QAAOF,OAAM,YAAYC,KAAIC,OAAMD,MAAK,gBAAgB;AACtE,cAAM,KAAK,YAAY,OAAOA,IAAIA,KAAI,iBAAkBC,QAAOA,QAAQD,KAAI,cAAe,CAAC;AAAA,MAC7F;AAGA,UAAI,eAAe,GAAG;AACpB,cAAM,MAAMD,OAAM,CAAC;AACnB,cAAM;AAAA,UACJ,OAAO,OAAO,CAAC,IACf,OAAQ,OAAO,IAAK,EAAI,IACxB;AAAA,QACF;AAAA,MACF,WAAW,eAAe,GAAG;AAC3B,eAAO,MAAMA,OAAM,CAAC,KAAK,KAAK,MAAMA,OAAM,CAAC;AAC3C,cAAM;AAAA,UACJ,OAAO,OAAO,EAAE,IAChB,OAAQ,OAAO,IAAK,EAAI,IACxB,OAAQ,OAAO,IAAK,EAAI,IACxB;AAAA,QACF;AAAA,MACF;AAEA,aAAO,MAAM,KAAK,EAAE;AAAA,IACtB;AAAA;AAAA;;;ACrJA;AAAA;AACA,YAAQ,OAAO,SAAU,QAAQ,QAAQ,MAAM,MAAM,QAAQ;AAC3D,UAAI,GAAG;AACP,UAAI,OAAQ,SAAS,IAAK,OAAO;AACjC,UAAI,QAAQ,KAAK,QAAQ;AACzB,UAAI,QAAQ,QAAQ;AACpB,UAAI,QAAQ;AACZ,UAAI,IAAI,OAAQ,SAAS,IAAK;AAC9B,UAAI,IAAI,OAAO,KAAK;AACpB,UAAI,IAAI,OAAO,SAAS,CAAC;AAEzB,WAAK;AAEL,UAAI,KAAM,KAAM,CAAC,SAAU;AAC3B,YAAO,CAAC;AACR,eAAS;AACT,aAAO,QAAQ,GAAG,IAAK,IAAI,MAAO,OAAO,SAAS,CAAC,GAAG,KAAK,GAAG,SAAS,GAAG;AAAA,MAAC;AAE3E,UAAI,KAAM,KAAM,CAAC,SAAU;AAC3B,YAAO,CAAC;AACR,eAAS;AACT,aAAO,QAAQ,GAAG,IAAK,IAAI,MAAO,OAAO,SAAS,CAAC,GAAG,KAAK,GAAG,SAAS,GAAG;AAAA,MAAC;AAE3E,UAAI,MAAM,GAAG;AACX,YAAI,IAAI;AAAA,MACV,WAAW,MAAM,MAAM;AACrB,eAAO,IAAI,OAAQ,IAAI,KAAK,KAAK;AAAA,MACnC,OAAO;AACL,YAAI,IAAI,KAAK,IAAI,GAAG,IAAI;AACxB,YAAI,IAAI;AAAA,MACV;AACA,cAAQ,IAAI,KAAK,KAAK,IAAI,KAAK,IAAI,GAAG,IAAI,IAAI;AAAA,IAChD;AAEA,YAAQ,QAAQ,SAAU,QAAQ,OAAO,QAAQ,MAAM,MAAM,QAAQ;AACnE,UAAI,GAAG,GAAG;AACV,UAAI,OAAQ,SAAS,IAAK,OAAO;AACjC,UAAI,QAAQ,KAAK,QAAQ;AACzB,UAAI,QAAQ,QAAQ;AACpB,UAAI,KAAM,SAAS,KAAK,KAAK,IAAI,GAAG,GAAG,IAAI,KAAK,IAAI,GAAG,GAAG,IAAI;AAC9D,UAAI,IAAI,OAAO,IAAK,SAAS;AAC7B,UAAI,IAAI,OAAO,IAAI;AACnB,UAAI,IAAI,QAAQ,KAAM,UAAU,KAAK,IAAI,QAAQ,IAAK,IAAI;AAE1D,cAAQ,KAAK,IAAI,KAAK;AAEtB,UAAI,MAAM,KAAK,KAAK,UAAU,UAAU;AACtC,YAAI,MAAM,KAAK,IAAI,IAAI;AACvB,YAAI;AAAA,MACN,OAAO;AACL,YAAI,KAAK,MAAM,KAAK,IAAI,KAAK,IAAI,KAAK,GAAG;AACzC,YAAI,SAAS,IAAI,KAAK,IAAI,GAAG,CAAC,CAAC,KAAK,GAAG;AACrC;AACA,eAAK;AAAA,QACP;AACA,YAAI,IAAI,SAAS,GAAG;AAClB,mBAAS,KAAK;AAAA,QAChB,OAAO;AACL,mBAAS,KAAK,KAAK,IAAI,GAAG,IAAI,KAAK;AAAA,QACrC;AACA,YAAI,QAAQ,KAAK,GAAG;AAClB;AACA,eAAK;AAAA,QACP;AAEA,YAAI,IAAI,SAAS,MAAM;AACrB,cAAI;AACJ,cAAI;AAAA,QACN,WAAW,IAAI,SAAS,GAAG;AACzB,eAAM,QAAQ,IAAK,KAAK,KAAK,IAAI,GAAG,IAAI;AACxC,cAAI,IAAI;AAAA,QACV,OAAO;AACL,cAAI,QAAQ,KAAK,IAAI,GAAG,QAAQ,CAAC,IAAI,KAAK,IAAI,GAAG,IAAI;AACrD,cAAI;AAAA,QACN;AAAA,MACF;AAEA,aAAO,QAAQ,GAAG,OAAO,SAAS,CAAC,IAAI,IAAI,KAAM,KAAK,GAAG,KAAK,KAAK,QAAQ,GAAG;AAAA,MAAC;AAE/E,UAAK,KAAK,OAAQ;AAClB,cAAQ;AACR,aAAO,OAAO,GAAG,OAAO,SAAS,CAAC,IAAI,IAAI,KAAM,KAAK,GAAG,KAAK,KAAK,QAAQ,GAAG;AAAA,MAAC;AAE9E,aAAO,SAAS,IAAI,CAAC,KAAK,IAAI;AAAA,IAChC;AAAA;AAAA;;;ACpFA;AAAA;AAAA;AAUA,QAAM,SAAS;AACf,QAAM,UAAU;AAChB,QAAM,sBACH,OAAO,WAAW,cAAc,OAAO,OAAO,KAAK,MAAM,aACtD,OAAO,KAAK,EAAE,4BAA4B,IAC1C;AAEN,YAAQ,SAASG;AACjB,YAAQ,aAAa;AACrB,YAAQ,oBAAoB;AAE5B,QAAM,eAAe;AACrB,YAAQ,aAAa;AAgBrB,IAAAA,QAAO,sBAAsB,kBAAkB;AAE/C,QAAI,CAACA,QAAO,uBAAuB,OAAO,YAAY,eAClD,OAAO,QAAQ,UAAU,YAAY;AACvC,cAAQ;AAAA,QACN;AAAA,MAEF;AAAA,IACF;AAEA,aAAS,oBAAqB;AAE5B,UAAI;AACF,cAAM,MAAM,IAAI,WAAW,CAAC;AAC5B,cAAM,QAAQ,EAAE,KAAK,WAAY;AAAE,iBAAO;AAAA,QAAG,EAAE;AAC/C,eAAO,eAAe,OAAO,WAAW,SAAS;AACjD,eAAO,eAAe,KAAK,KAAK;AAChC,eAAO,IAAI,IAAI,MAAM;AAAA,MACvB,SAAS,GAAG;AACV,eAAO;AAAA,MACT;AAAA,IACF;AAEA,WAAO,eAAeA,QAAO,WAAW,UAAU;AAAA,MAChD,YAAY;AAAA,MACZ,KAAK,WAAY;AACf,YAAI,CAACA,QAAO,SAAS,IAAI,EAAG,QAAO;AACnC,eAAO,KAAK;AAAA,MACd;AAAA,IACF,CAAC;AAED,WAAO,eAAeA,QAAO,WAAW,UAAU;AAAA,MAChD,YAAY;AAAA,MACZ,KAAK,WAAY;AACf,YAAI,CAACA,QAAO,SAAS,IAAI,EAAG,QAAO;AACnC,eAAO,KAAK;AAAA,MACd;AAAA,IACF,CAAC;AAED,aAAS,aAAc,QAAQ;AAC7B,UAAI,SAAS,cAAc;AACzB,cAAM,IAAI,WAAW,gBAAgB,SAAS,gCAAgC;AAAA,MAChF;AAEA,YAAM,MAAM,IAAI,WAAW,MAAM;AACjC,aAAO,eAAe,KAAKA,QAAO,SAAS;AAC3C,aAAO;AAAA,IACT;AAYA,aAASA,QAAQ,KAAK,kBAAkB,QAAQ;AAE9C,UAAI,OAAO,QAAQ,UAAU;AAC3B,YAAI,OAAO,qBAAqB,UAAU;AACxC,gBAAM,IAAI;AAAA,YACR;AAAA,UACF;AAAA,QACF;AACA,eAAO,YAAY,GAAG;AAAA,MACxB;AACA,aAAO,KAAK,KAAK,kBAAkB,MAAM;AAAA,IAC3C;AAEA,IAAAA,QAAO,WAAW;AAElB,aAAS,KAAM,OAAO,kBAAkB,QAAQ;AAC9C,UAAI,OAAO,UAAU,UAAU;AAC7B,eAAO,WAAW,OAAO,gBAAgB;AAAA,MAC3C;AAEA,UAAI,YAAY,OAAO,KAAK,GAAG;AAC7B,eAAO,cAAc,KAAK;AAAA,MAC5B;AAEA,UAAI,SAAS,MAAM;AACjB,cAAM,IAAI;AAAA,UACR,oHAC0C,OAAO;AAAA,QACnD;AAAA,MACF;AAEA,UAAI,WAAW,OAAO,WAAW,KAC5B,SAAS,WAAW,MAAM,QAAQ,WAAW,GAAI;AACpD,eAAO,gBAAgB,OAAO,kBAAkB,MAAM;AAAA,MACxD;AAEA,UAAI,OAAO,sBAAsB,gBAC5B,WAAW,OAAO,iBAAiB,KACnC,SAAS,WAAW,MAAM,QAAQ,iBAAiB,IAAK;AAC3D,eAAO,gBAAgB,OAAO,kBAAkB,MAAM;AAAA,MACxD;AAEA,UAAI,OAAO,UAAU,UAAU;AAC7B,cAAM,IAAI;AAAA,UACR;AAAA,QACF;AAAA,MACF;AAEA,YAAM,UAAU,MAAM,WAAW,MAAM,QAAQ;AAC/C,UAAI,WAAW,QAAQ,YAAY,OAAO;AACxC,eAAOA,QAAO,KAAK,SAAS,kBAAkB,MAAM;AAAA,MACtD;AAEA,YAAM,IAAI,WAAW,KAAK;AAC1B,UAAI,EAAG,QAAO;AAEd,UAAI,OAAO,WAAW,eAAe,OAAO,eAAe,QACvD,OAAO,MAAM,OAAO,WAAW,MAAM,YAAY;AACnD,eAAOA,QAAO,KAAK,MAAM,OAAO,WAAW,EAAE,QAAQ,GAAG,kBAAkB,MAAM;AAAA,MAClF;AAEA,YAAM,IAAI;AAAA,QACR,oHAC0C,OAAO;AAAA,MACnD;AAAA,IACF;AAUA,IAAAA,QAAO,OAAO,SAAU,OAAO,kBAAkB,QAAQ;AACvD,aAAO,KAAK,OAAO,kBAAkB,MAAM;AAAA,IAC7C;AAIA,WAAO,eAAeA,QAAO,WAAW,WAAW,SAAS;AAC5D,WAAO,eAAeA,SAAQ,UAAU;AAExC,aAAS,WAAY,MAAM;AACzB,UAAI,OAAO,SAAS,UAAU;AAC5B,cAAM,IAAI,UAAU,wCAAwC;AAAA,MAC9D,WAAW,OAAO,GAAG;AACnB,cAAM,IAAI,WAAW,gBAAgB,OAAO,gCAAgC;AAAA,MAC9E;AAAA,IACF;AAEA,aAAS,MAAO,MAAM,MAAM,UAAU;AACpC,iBAAW,IAAI;AACf,UAAI,QAAQ,GAAG;AACb,eAAO,aAAa,IAAI;AAAA,MAC1B;AACA,UAAI,SAAS,QAAW;AAItB,eAAO,OAAO,aAAa,WACvB,aAAa,IAAI,EAAE,KAAK,MAAM,QAAQ,IACtC,aAAa,IAAI,EAAE,KAAK,IAAI;AAAA,MAClC;AACA,aAAO,aAAa,IAAI;AAAA,IAC1B;AAMA,IAAAA,QAAO,QAAQ,SAAU,MAAM,MAAM,UAAU;AAC7C,aAAO,MAAM,MAAM,MAAM,QAAQ;AAAA,IACnC;AAEA,aAAS,YAAa,MAAM;AAC1B,iBAAW,IAAI;AACf,aAAO,aAAa,OAAO,IAAI,IAAI,QAAQ,IAAI,IAAI,CAAC;AAAA,IACtD;AAKA,IAAAA,QAAO,cAAc,SAAU,MAAM;AACnC,aAAO,YAAY,IAAI;AAAA,IACzB;AAIA,IAAAA,QAAO,kBAAkB,SAAU,MAAM;AACvC,aAAO,YAAY,IAAI;AAAA,IACzB;AAEA,aAAS,WAAY,QAAQ,UAAU;AACrC,UAAI,OAAO,aAAa,YAAY,aAAa,IAAI;AACnD,mBAAW;AAAA,MACb;AAEA,UAAI,CAACA,QAAO,WAAW,QAAQ,GAAG;AAChC,cAAM,IAAI,UAAU,uBAAuB,QAAQ;AAAA,MACrD;AAEA,YAAM,SAAS,WAAW,QAAQ,QAAQ,IAAI;AAC9C,UAAI,MAAM,aAAa,MAAM;AAE7B,YAAM,SAAS,IAAI,MAAM,QAAQ,QAAQ;AAEzC,UAAI,WAAW,QAAQ;AAIrB,cAAM,IAAI,MAAM,GAAG,MAAM;AAAA,MAC3B;AAEA,aAAO;AAAA,IACT;AAEA,aAAS,cAAe,OAAO;AAC7B,YAAM,SAAS,MAAM,SAAS,IAAI,IAAI,QAAQ,MAAM,MAAM,IAAI;AAC9D,YAAM,MAAM,aAAa,MAAM;AAC/B,eAAS,IAAI,GAAG,IAAI,QAAQ,KAAK,GAAG;AAClC,YAAI,CAAC,IAAI,MAAM,CAAC,IAAI;AAAA,MACtB;AACA,aAAO;AAAA,IACT;AAEA,aAAS,cAAe,WAAW;AACjC,UAAI,WAAW,WAAW,UAAU,GAAG;AACrC,cAAM,OAAO,IAAI,WAAW,SAAS;AACrC,eAAO,gBAAgB,KAAK,QAAQ,KAAK,YAAY,KAAK,UAAU;AAAA,MACtE;AACA,aAAO,cAAc,SAAS;AAAA,IAChC;AAEA,aAAS,gBAAiB,OAAO,YAAY,QAAQ;AACnD,UAAI,aAAa,KAAK,MAAM,aAAa,YAAY;AACnD,cAAM,IAAI,WAAW,sCAAsC;AAAA,MAC7D;AAEA,UAAI,MAAM,aAAa,cAAc,UAAU,IAAI;AACjD,cAAM,IAAI,WAAW,sCAAsC;AAAA,MAC7D;AAEA,UAAI;AACJ,UAAI,eAAe,UAAa,WAAW,QAAW;AACpD,cAAM,IAAI,WAAW,KAAK;AAAA,MAC5B,WAAW,WAAW,QAAW;AAC/B,cAAM,IAAI,WAAW,OAAO,UAAU;AAAA,MACxC,OAAO;AACL,cAAM,IAAI,WAAW,OAAO,YAAY,MAAM;AAAA,MAChD;AAGA,aAAO,eAAe,KAAKA,QAAO,SAAS;AAE3C,aAAO;AAAA,IACT;AAEA,aAAS,WAAY,KAAK;AACxB,UAAIA,QAAO,SAAS,GAAG,GAAG;AACxB,cAAM,MAAM,QAAQ,IAAI,MAAM,IAAI;AAClC,cAAM,MAAM,aAAa,GAAG;AAE5B,YAAI,IAAI,WAAW,GAAG;AACpB,iBAAO;AAAA,QACT;AAEA,YAAI,KAAK,KAAK,GAAG,GAAG,GAAG;AACvB,eAAO;AAAA,MACT;AAEA,UAAI,IAAI,WAAW,QAAW;AAC5B,YAAI,OAAO,IAAI,WAAW,YAAY,YAAY,IAAI,MAAM,GAAG;AAC7D,iBAAO,aAAa,CAAC;AAAA,QACvB;AACA,eAAO,cAAc,GAAG;AAAA,MAC1B;AAEA,UAAI,IAAI,SAAS,YAAY,MAAM,QAAQ,IAAI,IAAI,GAAG;AACpD,eAAO,cAAc,IAAI,IAAI;AAAA,MAC/B;AAAA,IACF;AAEA,aAAS,QAAS,QAAQ;AAGxB,UAAI,UAAU,cAAc;AAC1B,cAAM,IAAI,WAAW,4DACa,aAAa,SAAS,EAAE,IAAI,QAAQ;AAAA,MACxE;AACA,aAAO,SAAS;AAAA,IAClB;AAEA,aAAS,WAAY,QAAQ;AAC3B,UAAI,CAAC,UAAU,QAAQ;AACrB,iBAAS;AAAA,MACX;AACA,aAAOA,QAAO,MAAM,CAAC,MAAM;AAAA,IAC7B;AAEA,IAAAA,QAAO,WAAW,SAAS,SAAU,GAAG;AACtC,aAAO,KAAK,QAAQ,EAAE,cAAc,QAClC,MAAMA,QAAO;AAAA,IACjB;AAEA,IAAAA,QAAO,UAAU,SAAS,QAAS,GAAG,GAAG;AACvC,UAAI,WAAW,GAAG,UAAU,EAAG,KAAIA,QAAO,KAAK,GAAG,EAAE,QAAQ,EAAE,UAAU;AACxE,UAAI,WAAW,GAAG,UAAU,EAAG,KAAIA,QAAO,KAAK,GAAG,EAAE,QAAQ,EAAE,UAAU;AACxE,UAAI,CAACA,QAAO,SAAS,CAAC,KAAK,CAACA,QAAO,SAAS,CAAC,GAAG;AAC9C,cAAM,IAAI;AAAA,UACR;AAAA,QACF;AAAA,MACF;AAEA,UAAI,MAAM,EAAG,QAAO;AAEpB,UAAI,IAAI,EAAE;AACV,UAAI,IAAI,EAAE;AAEV,eAAS,IAAI,GAAG,MAAM,KAAK,IAAI,GAAG,CAAC,GAAG,IAAI,KAAK,EAAE,GAAG;AAClD,YAAI,EAAE,CAAC,MAAM,EAAE,CAAC,GAAG;AACjB,cAAI,EAAE,CAAC;AACP,cAAI,EAAE,CAAC;AACP;AAAA,QACF;AAAA,MACF;AAEA,UAAI,IAAI,EAAG,QAAO;AAClB,UAAI,IAAI,EAAG,QAAO;AAClB,aAAO;AAAA,IACT;AAEA,IAAAA,QAAO,aAAa,SAAS,WAAY,UAAU;AACjD,cAAQ,OAAO,QAAQ,EAAE,YAAY,GAAG;AAAA,QACtC,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AACH,iBAAO;AAAA,QACT;AACE,iBAAO;AAAA,MACX;AAAA,IACF;AAEA,IAAAA,QAAO,SAAS,SAAS,OAAQ,MAAM,QAAQ;AAC7C,UAAI,CAAC,MAAM,QAAQ,IAAI,GAAG;AACxB,cAAM,IAAI,UAAU,6CAA6C;AAAA,MACnE;AAEA,UAAI,KAAK,WAAW,GAAG;AACrB,eAAOA,QAAO,MAAM,CAAC;AAAA,MACvB;AAEA,UAAI;AACJ,UAAI,WAAW,QAAW;AACxB,iBAAS;AACT,aAAK,IAAI,GAAG,IAAI,KAAK,QAAQ,EAAE,GAAG;AAChC,oBAAU,KAAK,CAAC,EAAE;AAAA,QACpB;AAAA,MACF;AAEA,YAAM,SAASA,QAAO,YAAY,MAAM;AACxC,UAAI,MAAM;AACV,WAAK,IAAI,GAAG,IAAI,KAAK,QAAQ,EAAE,GAAG;AAChC,YAAI,MAAM,KAAK,CAAC;AAChB,YAAI,WAAW,KAAK,UAAU,GAAG;AAC/B,cAAI,MAAM,IAAI,SAAS,OAAO,QAAQ;AACpC,gBAAI,CAACA,QAAO,SAAS,GAAG,EAAG,OAAMA,QAAO,KAAK,GAAG;AAChD,gBAAI,KAAK,QAAQ,GAAG;AAAA,UACtB,OAAO;AACL,uBAAW,UAAU,IAAI;AAAA,cACvB;AAAA,cACA;AAAA,cACA;AAAA,YACF;AAAA,UACF;AAAA,QACF,WAAW,CAACA,QAAO,SAAS,GAAG,GAAG;AAChC,gBAAM,IAAI,UAAU,6CAA6C;AAAA,QACnE,OAAO;AACL,cAAI,KAAK,QAAQ,GAAG;AAAA,QACtB;AACA,eAAO,IAAI;AAAA,MACb;AACA,aAAO;AAAA,IACT;AAEA,aAAS,WAAY,QAAQ,UAAU;AACrC,UAAIA,QAAO,SAAS,MAAM,GAAG;AAC3B,eAAO,OAAO;AAAA,MAChB;AACA,UAAI,YAAY,OAAO,MAAM,KAAK,WAAW,QAAQ,WAAW,GAAG;AACjE,eAAO,OAAO;AAAA,MAChB;AACA,UAAI,OAAO,WAAW,UAAU;AAC9B,cAAM,IAAI;AAAA,UACR,6FACmB,OAAO;AAAA,QAC5B;AAAA,MACF;AAEA,YAAM,MAAM,OAAO;AACnB,YAAM,YAAa,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM;AAC5D,UAAI,CAAC,aAAa,QAAQ,EAAG,QAAO;AAGpC,UAAI,cAAc;AAClB,iBAAS;AACP,gBAAQ,UAAU;AAAA,UAChB,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AACH,mBAAO;AAAA,UACT,KAAK;AAAA,UACL,KAAK;AACH,mBAAO,YAAY,MAAM,EAAE;AAAA,UAC7B,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AACH,mBAAO,MAAM;AAAA,UACf,KAAK;AACH,mBAAO,QAAQ;AAAA,UACjB,KAAK;AACH,mBAAO,cAAc,MAAM,EAAE;AAAA,UAC/B;AACE,gBAAI,aAAa;AACf,qBAAO,YAAY,KAAK,YAAY,MAAM,EAAE;AAAA,YAC9C;AACA,wBAAY,KAAK,UAAU,YAAY;AACvC,0BAAc;AAAA,QAClB;AAAA,MACF;AAAA,IACF;AACA,IAAAA,QAAO,aAAa;AAEpB,aAAS,aAAc,UAAU,OAAO,KAAK;AAC3C,UAAI,cAAc;AASlB,UAAI,UAAU,UAAa,QAAQ,GAAG;AACpC,gBAAQ;AAAA,MACV;AAGA,UAAI,QAAQ,KAAK,QAAQ;AACvB,eAAO;AAAA,MACT;AAEA,UAAI,QAAQ,UAAa,MAAM,KAAK,QAAQ;AAC1C,cAAM,KAAK;AAAA,MACb;AAEA,UAAI,OAAO,GAAG;AACZ,eAAO;AAAA,MACT;AAGA,eAAS;AACT,iBAAW;AAEX,UAAI,OAAO,OAAO;AAChB,eAAO;AAAA,MACT;AAEA,UAAI,CAAC,SAAU,YAAW;AAE1B,aAAO,MAAM;AACX,gBAAQ,UAAU;AAAA,UAChB,KAAK;AACH,mBAAO,SAAS,MAAM,OAAO,GAAG;AAAA,UAElC,KAAK;AAAA,UACL,KAAK;AACH,mBAAO,UAAU,MAAM,OAAO,GAAG;AAAA,UAEnC,KAAK;AACH,mBAAO,WAAW,MAAM,OAAO,GAAG;AAAA,UAEpC,KAAK;AAAA,UACL,KAAK;AACH,mBAAO,YAAY,MAAM,OAAO,GAAG;AAAA,UAErC,KAAK;AACH,mBAAO,YAAY,MAAM,OAAO,GAAG;AAAA,UAErC,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AACH,mBAAO,aAAa,MAAM,OAAO,GAAG;AAAA,UAEtC;AACE,gBAAI,YAAa,OAAM,IAAI,UAAU,uBAAuB,QAAQ;AACpE,wBAAY,WAAW,IAAI,YAAY;AACvC,0BAAc;AAAA,QAClB;AAAA,MACF;AAAA,IACF;AAQA,IAAAA,QAAO,UAAU,YAAY;AAE7B,aAAS,KAAM,GAAG,GAAG,GAAG;AACtB,YAAM,IAAI,EAAE,CAAC;AACb,QAAE,CAAC,IAAI,EAAE,CAAC;AACV,QAAE,CAAC,IAAI;AAAA,IACT;AAEA,IAAAA,QAAO,UAAU,SAAS,SAAS,SAAU;AAC3C,YAAM,MAAM,KAAK;AACjB,UAAI,MAAM,MAAM,GAAG;AACjB,cAAM,IAAI,WAAW,2CAA2C;AAAA,MAClE;AACA,eAAS,IAAI,GAAG,IAAI,KAAK,KAAK,GAAG;AAC/B,aAAK,MAAM,GAAG,IAAI,CAAC;AAAA,MACrB;AACA,aAAO;AAAA,IACT;AAEA,IAAAA,QAAO,UAAU,SAAS,SAAS,SAAU;AAC3C,YAAM,MAAM,KAAK;AACjB,UAAI,MAAM,MAAM,GAAG;AACjB,cAAM,IAAI,WAAW,2CAA2C;AAAA,MAClE;AACA,eAAS,IAAI,GAAG,IAAI,KAAK,KAAK,GAAG;AAC/B,aAAK,MAAM,GAAG,IAAI,CAAC;AACnB,aAAK,MAAM,IAAI,GAAG,IAAI,CAAC;AAAA,MACzB;AACA,aAAO;AAAA,IACT;AAEA,IAAAA,QAAO,UAAU,SAAS,SAAS,SAAU;AAC3C,YAAM,MAAM,KAAK;AACjB,UAAI,MAAM,MAAM,GAAG;AACjB,cAAM,IAAI,WAAW,2CAA2C;AAAA,MAClE;AACA,eAAS,IAAI,GAAG,IAAI,KAAK,KAAK,GAAG;AAC/B,aAAK,MAAM,GAAG,IAAI,CAAC;AACnB,aAAK,MAAM,IAAI,GAAG,IAAI,CAAC;AACvB,aAAK,MAAM,IAAI,GAAG,IAAI,CAAC;AACvB,aAAK,MAAM,IAAI,GAAG,IAAI,CAAC;AAAA,MACzB;AACA,aAAO;AAAA,IACT;AAEA,IAAAA,QAAO,UAAU,WAAW,SAAS,WAAY;AAC/C,YAAM,SAAS,KAAK;AACpB,UAAI,WAAW,EAAG,QAAO;AACzB,UAAI,UAAU,WAAW,EAAG,QAAO,UAAU,MAAM,GAAG,MAAM;AAC5D,aAAO,aAAa,MAAM,MAAM,SAAS;AAAA,IAC3C;AAEA,IAAAA,QAAO,UAAU,iBAAiBA,QAAO,UAAU;AAEnD,IAAAA,QAAO,UAAU,SAAS,SAAS,OAAQ,GAAG;AAC5C,UAAI,CAACA,QAAO,SAAS,CAAC,EAAG,OAAM,IAAI,UAAU,2BAA2B;AACxE,UAAI,SAAS,EAAG,QAAO;AACvB,aAAOA,QAAO,QAAQ,MAAM,CAAC,MAAM;AAAA,IACrC;AAEA,IAAAA,QAAO,UAAU,UAAU,SAAS,UAAW;AAC7C,UAAI,MAAM;AACV,YAAM,MAAM,QAAQ;AACpB,YAAM,KAAK,SAAS,OAAO,GAAG,GAAG,EAAE,QAAQ,WAAW,KAAK,EAAE,KAAK;AAClE,UAAI,KAAK,SAAS,IAAK,QAAO;AAC9B,aAAO,aAAa,MAAM;AAAA,IAC5B;AACA,QAAI,qBAAqB;AACvB,MAAAA,QAAO,UAAU,mBAAmB,IAAIA,QAAO,UAAU;AAAA,IAC3D;AAEA,IAAAA,QAAO,UAAU,UAAU,SAAS,QAAS,QAAQ,OAAO,KAAK,WAAW,SAAS;AACnF,UAAI,WAAW,QAAQ,UAAU,GAAG;AAClC,iBAASA,QAAO,KAAK,QAAQ,OAAO,QAAQ,OAAO,UAAU;AAAA,MAC/D;AACA,UAAI,CAACA,QAAO,SAAS,MAAM,GAAG;AAC5B,cAAM,IAAI;AAAA,UACR,mFACoB,OAAO;AAAA,QAC7B;AAAA,MACF;AAEA,UAAI,UAAU,QAAW;AACvB,gBAAQ;AAAA,MACV;AACA,UAAI,QAAQ,QAAW;AACrB,cAAM,SAAS,OAAO,SAAS;AAAA,MACjC;AACA,UAAI,cAAc,QAAW;AAC3B,oBAAY;AAAA,MACd;AACA,UAAI,YAAY,QAAW;AACzB,kBAAU,KAAK;AAAA,MACjB;AAEA,UAAI,QAAQ,KAAK,MAAM,OAAO,UAAU,YAAY,KAAK,UAAU,KAAK,QAAQ;AAC9E,cAAM,IAAI,WAAW,oBAAoB;AAAA,MAC3C;AAEA,UAAI,aAAa,WAAW,SAAS,KAAK;AACxC,eAAO;AAAA,MACT;AACA,UAAI,aAAa,SAAS;AACxB,eAAO;AAAA,MACT;AACA,UAAI,SAAS,KAAK;AAChB,eAAO;AAAA,MACT;AAEA,iBAAW;AACX,eAAS;AACT,qBAAe;AACf,mBAAa;AAEb,UAAI,SAAS,OAAQ,QAAO;AAE5B,UAAI,IAAI,UAAU;AAClB,UAAI,IAAI,MAAM;AACd,YAAM,MAAM,KAAK,IAAI,GAAG,CAAC;AAEzB,YAAM,WAAW,KAAK,MAAM,WAAW,OAAO;AAC9C,YAAM,aAAa,OAAO,MAAM,OAAO,GAAG;AAE1C,eAAS,IAAI,GAAG,IAAI,KAAK,EAAE,GAAG;AAC5B,YAAI,SAAS,CAAC,MAAM,WAAW,CAAC,GAAG;AACjC,cAAI,SAAS,CAAC;AACd,cAAI,WAAW,CAAC;AAChB;AAAA,QACF;AAAA,MACF;AAEA,UAAI,IAAI,EAAG,QAAO;AAClB,UAAI,IAAI,EAAG,QAAO;AAClB,aAAO;AAAA,IACT;AAWA,aAAS,qBAAsB,QAAQ,KAAK,YAAY,UAAU,KAAK;AAErE,UAAI,OAAO,WAAW,EAAG,QAAO;AAGhC,UAAI,OAAO,eAAe,UAAU;AAClC,mBAAW;AACX,qBAAa;AAAA,MACf,WAAW,aAAa,YAAY;AAClC,qBAAa;AAAA,MACf,WAAW,aAAa,aAAa;AACnC,qBAAa;AAAA,MACf;AACA,mBAAa,CAAC;AACd,UAAI,YAAY,UAAU,GAAG;AAE3B,qBAAa,MAAM,IAAK,OAAO,SAAS;AAAA,MAC1C;AAGA,UAAI,aAAa,EAAG,cAAa,OAAO,SAAS;AACjD,UAAI,cAAc,OAAO,QAAQ;AAC/B,YAAI,IAAK,QAAO;AAAA,YACX,cAAa,OAAO,SAAS;AAAA,MACpC,WAAW,aAAa,GAAG;AACzB,YAAI,IAAK,cAAa;AAAA,YACjB,QAAO;AAAA,MACd;AAGA,UAAI,OAAO,QAAQ,UAAU;AAC3B,cAAMA,QAAO,KAAK,KAAK,QAAQ;AAAA,MACjC;AAGA,UAAIA,QAAO,SAAS,GAAG,GAAG;AAExB,YAAI,IAAI,WAAW,GAAG;AACpB,iBAAO;AAAA,QACT;AACA,eAAO,aAAa,QAAQ,KAAK,YAAY,UAAU,GAAG;AAAA,MAC5D,WAAW,OAAO,QAAQ,UAAU;AAClC,cAAM,MAAM;AACZ,YAAI,OAAO,WAAW,UAAU,YAAY,YAAY;AACtD,cAAI,KAAK;AACP,mBAAO,WAAW,UAAU,QAAQ,KAAK,QAAQ,KAAK,UAAU;AAAA,UAClE,OAAO;AACL,mBAAO,WAAW,UAAU,YAAY,KAAK,QAAQ,KAAK,UAAU;AAAA,UACtE;AAAA,QACF;AACA,eAAO,aAAa,QAAQ,CAAC,GAAG,GAAG,YAAY,UAAU,GAAG;AAAA,MAC9D;AAEA,YAAM,IAAI,UAAU,sCAAsC;AAAA,IAC5D;AAEA,aAAS,aAAc,KAAK,KAAK,YAAY,UAAU,KAAK;AAC1D,UAAI,YAAY;AAChB,UAAI,YAAY,IAAI;AACpB,UAAI,YAAY,IAAI;AAEpB,UAAI,aAAa,QAAW;AAC1B,mBAAW,OAAO,QAAQ,EAAE,YAAY;AACxC,YAAI,aAAa,UAAU,aAAa,WACpC,aAAa,aAAa,aAAa,YAAY;AACrD,cAAI,IAAI,SAAS,KAAK,IAAI,SAAS,GAAG;AACpC,mBAAO;AAAA,UACT;AACA,sBAAY;AACZ,uBAAa;AACb,uBAAa;AACb,wBAAc;AAAA,QAChB;AAAA,MACF;AAEA,eAAS,KAAM,KAAKC,IAAG;AACrB,YAAI,cAAc,GAAG;AACnB,iBAAO,IAAIA,EAAC;AAAA,QACd,OAAO;AACL,iBAAO,IAAI,aAAaA,KAAI,SAAS;AAAA,QACvC;AAAA,MACF;AAEA,UAAI;AACJ,UAAI,KAAK;AACP,YAAI,aAAa;AACjB,aAAK,IAAI,YAAY,IAAI,WAAW,KAAK;AACvC,cAAI,KAAK,KAAK,CAAC,MAAM,KAAK,KAAK,eAAe,KAAK,IAAI,IAAI,UAAU,GAAG;AACtE,gBAAI,eAAe,GAAI,cAAa;AACpC,gBAAI,IAAI,aAAa,MAAM,UAAW,QAAO,aAAa;AAAA,UAC5D,OAAO;AACL,gBAAI,eAAe,GAAI,MAAK,IAAI;AAChC,yBAAa;AAAA,UACf;AAAA,QACF;AAAA,MACF,OAAO;AACL,YAAI,aAAa,YAAY,UAAW,cAAa,YAAY;AACjE,aAAK,IAAI,YAAY,KAAK,GAAG,KAAK;AAChC,cAAI,QAAQ;AACZ,mBAAS,IAAI,GAAG,IAAI,WAAW,KAAK;AAClC,gBAAI,KAAK,KAAK,IAAI,CAAC,MAAM,KAAK,KAAK,CAAC,GAAG;AACrC,sBAAQ;AACR;AAAA,YACF;AAAA,UACF;AACA,cAAI,MAAO,QAAO;AAAA,QACpB;AAAA,MACF;AAEA,aAAO;AAAA,IACT;AAEA,IAAAD,QAAO,UAAU,WAAW,SAAS,SAAU,KAAK,YAAY,UAAU;AACxE,aAAO,KAAK,QAAQ,KAAK,YAAY,QAAQ,MAAM;AAAA,IACrD;AAEA,IAAAA,QAAO,UAAU,UAAU,SAAS,QAAS,KAAK,YAAY,UAAU;AACtE,aAAO,qBAAqB,MAAM,KAAK,YAAY,UAAU,IAAI;AAAA,IACnE;AAEA,IAAAA,QAAO,UAAU,cAAc,SAAS,YAAa,KAAK,YAAY,UAAU;AAC9E,aAAO,qBAAqB,MAAM,KAAK,YAAY,UAAU,KAAK;AAAA,IACpE;AAEA,aAAS,SAAU,KAAK,QAAQ,QAAQ,QAAQ;AAC9C,eAAS,OAAO,MAAM,KAAK;AAC3B,YAAM,YAAY,IAAI,SAAS;AAC/B,UAAI,CAAC,QAAQ;AACX,iBAAS;AAAA,MACX,OAAO;AACL,iBAAS,OAAO,MAAM;AACtB,YAAI,SAAS,WAAW;AACtB,mBAAS;AAAA,QACX;AAAA,MACF;AAEA,YAAM,SAAS,OAAO;AAEtB,UAAI,SAAS,SAAS,GAAG;AACvB,iBAAS,SAAS;AAAA,MACpB;AACA,UAAI;AACJ,WAAK,IAAI,GAAG,IAAI,QAAQ,EAAE,GAAG;AAC3B,cAAM,SAAS,SAAS,OAAO,OAAO,IAAI,GAAG,CAAC,GAAG,EAAE;AACnD,YAAI,YAAY,MAAM,EAAG,QAAO;AAChC,YAAI,SAAS,CAAC,IAAI;AAAA,MACpB;AACA,aAAO;AAAA,IACT;AAEA,aAAS,UAAW,KAAK,QAAQ,QAAQ,QAAQ;AAC/C,aAAO,WAAW,YAAY,QAAQ,IAAI,SAAS,MAAM,GAAG,KAAK,QAAQ,MAAM;AAAA,IACjF;AAEA,aAAS,WAAY,KAAK,QAAQ,QAAQ,QAAQ;AAChD,aAAO,WAAW,aAAa,MAAM,GAAG,KAAK,QAAQ,MAAM;AAAA,IAC7D;AAEA,aAAS,YAAa,KAAK,QAAQ,QAAQ,QAAQ;AACjD,aAAO,WAAW,cAAc,MAAM,GAAG,KAAK,QAAQ,MAAM;AAAA,IAC9D;AAEA,aAAS,UAAW,KAAK,QAAQ,QAAQ,QAAQ;AAC/C,aAAO,WAAW,eAAe,QAAQ,IAAI,SAAS,MAAM,GAAG,KAAK,QAAQ,MAAM;AAAA,IACpF;AAEA,IAAAA,QAAO,UAAU,QAAQ,SAAS,MAAO,QAAQ,QAAQ,QAAQ,UAAU;AAEzE,UAAI,WAAW,QAAW;AACxB,mBAAW;AACX,iBAAS,KAAK;AACd,iBAAS;AAAA,MAEX,WAAW,WAAW,UAAa,OAAO,WAAW,UAAU;AAC7D,mBAAW;AACX,iBAAS,KAAK;AACd,iBAAS;AAAA,MAEX,WAAW,SAAS,MAAM,GAAG;AAC3B,iBAAS,WAAW;AACpB,YAAI,SAAS,MAAM,GAAG;AACpB,mBAAS,WAAW;AACpB,cAAI,aAAa,OAAW,YAAW;AAAA,QACzC,OAAO;AACL,qBAAW;AACX,mBAAS;AAAA,QACX;AAAA,MACF,OAAO;AACL,cAAM,IAAI;AAAA,UACR;AAAA,QACF;AAAA,MACF;AAEA,YAAM,YAAY,KAAK,SAAS;AAChC,UAAI,WAAW,UAAa,SAAS,UAAW,UAAS;AAEzD,UAAK,OAAO,SAAS,MAAM,SAAS,KAAK,SAAS,MAAO,SAAS,KAAK,QAAQ;AAC7E,cAAM,IAAI,WAAW,wCAAwC;AAAA,MAC/D;AAEA,UAAI,CAAC,SAAU,YAAW;AAE1B,UAAI,cAAc;AAClB,iBAAS;AACP,gBAAQ,UAAU;AAAA,UAChB,KAAK;AACH,mBAAO,SAAS,MAAM,QAAQ,QAAQ,MAAM;AAAA,UAE9C,KAAK;AAAA,UACL,KAAK;AACH,mBAAO,UAAU,MAAM,QAAQ,QAAQ,MAAM;AAAA,UAE/C,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AACH,mBAAO,WAAW,MAAM,QAAQ,QAAQ,MAAM;AAAA,UAEhD,KAAK;AAEH,mBAAO,YAAY,MAAM,QAAQ,QAAQ,MAAM;AAAA,UAEjD,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AACH,mBAAO,UAAU,MAAM,QAAQ,QAAQ,MAAM;AAAA,UAE/C;AACE,gBAAI,YAAa,OAAM,IAAI,UAAU,uBAAuB,QAAQ;AACpE,wBAAY,KAAK,UAAU,YAAY;AACvC,0BAAc;AAAA,QAClB;AAAA,MACF;AAAA,IACF;AAEA,IAAAA,QAAO,UAAU,SAAS,SAAS,SAAU;AAC3C,aAAO;AAAA,QACL,MAAM;AAAA,QACN,MAAM,MAAM,UAAU,MAAM,KAAK,KAAK,QAAQ,MAAM,CAAC;AAAA,MACvD;AAAA,IACF;AAEA,aAAS,YAAa,KAAK,OAAO,KAAK;AACrC,UAAI,UAAU,KAAK,QAAQ,IAAI,QAAQ;AACrC,eAAO,OAAO,cAAc,GAAG;AAAA,MACjC,OAAO;AACL,eAAO,OAAO,cAAc,IAAI,MAAM,OAAO,GAAG,CAAC;AAAA,MACnD;AAAA,IACF;AAEA,aAAS,UAAW,KAAK,OAAO,KAAK;AACnC,YAAM,KAAK,IAAI,IAAI,QAAQ,GAAG;AAC9B,YAAM,MAAM,CAAC;AAEb,UAAI,IAAI;AACR,aAAO,IAAI,KAAK;AACd,cAAM,YAAY,IAAI,CAAC;AACvB,YAAI,YAAY;AAChB,YAAI,mBAAoB,YAAY,MAChC,IACC,YAAY,MACT,IACC,YAAY,MACT,IACA;AAEZ,YAAI,IAAI,oBAAoB,KAAK;AAC/B,cAAI,YAAY,WAAW,YAAY;AAEvC,kBAAQ,kBAAkB;AAAA,YACxB,KAAK;AACH,kBAAI,YAAY,KAAM;AACpB,4BAAY;AAAA,cACd;AACA;AAAA,YACF,KAAK;AACH,2BAAa,IAAI,IAAI,CAAC;AACtB,mBAAK,aAAa,SAAU,KAAM;AAChC,iCAAiB,YAAY,OAAS,IAAO,aAAa;AAC1D,oBAAI,gBAAgB,KAAM;AACxB,8BAAY;AAAA,gBACd;AAAA,cACF;AACA;AAAA,YACF,KAAK;AACH,2BAAa,IAAI,IAAI,CAAC;AACtB,0BAAY,IAAI,IAAI,CAAC;AACrB,mBAAK,aAAa,SAAU,QAAS,YAAY,SAAU,KAAM;AAC/D,iCAAiB,YAAY,OAAQ,MAAO,aAAa,OAAS,IAAO,YAAY;AACrF,oBAAI,gBAAgB,SAAU,gBAAgB,SAAU,gBAAgB,QAAS;AAC/E,8BAAY;AAAA,gBACd;AAAA,cACF;AACA;AAAA,YACF,KAAK;AACH,2BAAa,IAAI,IAAI,CAAC;AACtB,0BAAY,IAAI,IAAI,CAAC;AACrB,2BAAa,IAAI,IAAI,CAAC;AACtB,mBAAK,aAAa,SAAU,QAAS,YAAY,SAAU,QAAS,aAAa,SAAU,KAAM;AAC/F,iCAAiB,YAAY,OAAQ,MAAQ,aAAa,OAAS,MAAO,YAAY,OAAS,IAAO,aAAa;AACnH,oBAAI,gBAAgB,SAAU,gBAAgB,SAAU;AACtD,8BAAY;AAAA,gBACd;AAAA,cACF;AAAA,UACJ;AAAA,QACF;AAEA,YAAI,cAAc,MAAM;AAGtB,sBAAY;AACZ,6BAAmB;AAAA,QACrB,WAAW,YAAY,OAAQ;AAE7B,uBAAa;AACb,cAAI,KAAK,cAAc,KAAK,OAAQ,KAAM;AAC1C,sBAAY,QAAS,YAAY;AAAA,QACnC;AAEA,YAAI,KAAK,SAAS;AAClB,aAAK;AAAA,MACP;AAEA,aAAO,sBAAsB,GAAG;AAAA,IAClC;AAKA,QAAM,uBAAuB;AAE7B,aAAS,sBAAuB,YAAY;AAC1C,YAAM,MAAM,WAAW;AACvB,UAAI,OAAO,sBAAsB;AAC/B,eAAO,OAAO,aAAa,MAAM,QAAQ,UAAU;AAAA,MACrD;AAGA,UAAI,MAAM;AACV,UAAI,IAAI;AACR,aAAO,IAAI,KAAK;AACd,eAAO,OAAO,aAAa;AAAA,UACzB;AAAA,UACA,WAAW,MAAM,GAAG,KAAK,oBAAoB;AAAA,QAC/C;AAAA,MACF;AACA,aAAO;AAAA,IACT;AAEA,aAAS,WAAY,KAAK,OAAO,KAAK;AACpC,UAAI,MAAM;AACV,YAAM,KAAK,IAAI,IAAI,QAAQ,GAAG;AAE9B,eAAS,IAAI,OAAO,IAAI,KAAK,EAAE,GAAG;AAChC,eAAO,OAAO,aAAa,IAAI,CAAC,IAAI,GAAI;AAAA,MAC1C;AACA,aAAO;AAAA,IACT;AAEA,aAAS,YAAa,KAAK,OAAO,KAAK;AACrC,UAAI,MAAM;AACV,YAAM,KAAK,IAAI,IAAI,QAAQ,GAAG;AAE9B,eAAS,IAAI,OAAO,IAAI,KAAK,EAAE,GAAG;AAChC,eAAO,OAAO,aAAa,IAAI,CAAC,CAAC;AAAA,MACnC;AACA,aAAO;AAAA,IACT;AAEA,aAAS,SAAU,KAAK,OAAO,KAAK;AAClC,YAAM,MAAM,IAAI;AAEhB,UAAI,CAAC,SAAS,QAAQ,EAAG,SAAQ;AACjC,UAAI,CAAC,OAAO,MAAM,KAAK,MAAM,IAAK,OAAM;AAExC,UAAI,MAAM;AACV,eAAS,IAAI,OAAO,IAAI,KAAK,EAAE,GAAG;AAChC,eAAO,oBAAoB,IAAI,CAAC,CAAC;AAAA,MACnC;AACA,aAAO;AAAA,IACT;AAEA,aAAS,aAAc,KAAK,OAAO,KAAK;AACtC,YAAM,QAAQ,IAAI,MAAM,OAAO,GAAG;AAClC,UAAI,MAAM;AAEV,eAAS,IAAI,GAAG,IAAI,MAAM,SAAS,GAAG,KAAK,GAAG;AAC5C,eAAO,OAAO,aAAa,MAAM,CAAC,IAAK,MAAM,IAAI,CAAC,IAAI,GAAI;AAAA,MAC5D;AACA,aAAO;AAAA,IACT;AAEA,IAAAA,QAAO,UAAU,QAAQ,SAAS,MAAO,OAAO,KAAK;AACnD,YAAM,MAAM,KAAK;AACjB,cAAQ,CAAC,CAAC;AACV,YAAM,QAAQ,SAAY,MAAM,CAAC,CAAC;AAElC,UAAI,QAAQ,GAAG;AACb,iBAAS;AACT,YAAI,QAAQ,EAAG,SAAQ;AAAA,MACzB,WAAW,QAAQ,KAAK;AACtB,gBAAQ;AAAA,MACV;AAEA,UAAI,MAAM,GAAG;AACX,eAAO;AACP,YAAI,MAAM,EAAG,OAAM;AAAA,MACrB,WAAW,MAAM,KAAK;AACpB,cAAM;AAAA,MACR;AAEA,UAAI,MAAM,MAAO,OAAM;AAEvB,YAAM,SAAS,KAAK,SAAS,OAAO,GAAG;AAEvC,aAAO,eAAe,QAAQA,QAAO,SAAS;AAE9C,aAAO;AAAA,IACT;AAKA,aAAS,YAAa,QAAQ,KAAK,QAAQ;AACzC,UAAK,SAAS,MAAO,KAAK,SAAS,EAAG,OAAM,IAAI,WAAW,oBAAoB;AAC/E,UAAI,SAAS,MAAM,OAAQ,OAAM,IAAI,WAAW,uCAAuC;AAAA,IACzF;AAEA,IAAAA,QAAO,UAAU,aACjBA,QAAO,UAAU,aAAa,SAAS,WAAY,QAAQE,aAAY,UAAU;AAC/E,eAAS,WAAW;AACpB,MAAAA,cAAaA,gBAAe;AAC5B,UAAI,CAAC,SAAU,aAAY,QAAQA,aAAY,KAAK,MAAM;AAE1D,UAAI,MAAM,KAAK,MAAM;AACrB,UAAI,MAAM;AACV,UAAI,IAAI;AACR,aAAO,EAAE,IAAIA,gBAAe,OAAO,MAAQ;AACzC,eAAO,KAAK,SAAS,CAAC,IAAI;AAAA,MAC5B;AAEA,aAAO;AAAA,IACT;AAEA,IAAAF,QAAO,UAAU,aACjBA,QAAO,UAAU,aAAa,SAAS,WAAY,QAAQE,aAAY,UAAU;AAC/E,eAAS,WAAW;AACpB,MAAAA,cAAaA,gBAAe;AAC5B,UAAI,CAAC,UAAU;AACb,oBAAY,QAAQA,aAAY,KAAK,MAAM;AAAA,MAC7C;AAEA,UAAI,MAAM,KAAK,SAAS,EAAEA,WAAU;AACpC,UAAI,MAAM;AACV,aAAOA,cAAa,MAAM,OAAO,MAAQ;AACvC,eAAO,KAAK,SAAS,EAAEA,WAAU,IAAI;AAAA,MACvC;AAEA,aAAO;AAAA,IACT;AAEA,IAAAF,QAAO,UAAU,YACjBA,QAAO,UAAU,YAAY,SAAS,UAAW,QAAQ,UAAU;AACjE,eAAS,WAAW;AACpB,UAAI,CAAC,SAAU,aAAY,QAAQ,GAAG,KAAK,MAAM;AACjD,aAAO,KAAK,MAAM;AAAA,IACpB;AAEA,IAAAA,QAAO,UAAU,eACjBA,QAAO,UAAU,eAAe,SAAS,aAAc,QAAQ,UAAU;AACvE,eAAS,WAAW;AACpB,UAAI,CAAC,SAAU,aAAY,QAAQ,GAAG,KAAK,MAAM;AACjD,aAAO,KAAK,MAAM,IAAK,KAAK,SAAS,CAAC,KAAK;AAAA,IAC7C;AAEA,IAAAA,QAAO,UAAU,eACjBA,QAAO,UAAU,eAAe,SAAS,aAAc,QAAQ,UAAU;AACvE,eAAS,WAAW;AACpB,UAAI,CAAC,SAAU,aAAY,QAAQ,GAAG,KAAK,MAAM;AACjD,aAAQ,KAAK,MAAM,KAAK,IAAK,KAAK,SAAS,CAAC;AAAA,IAC9C;AAEA,IAAAA,QAAO,UAAU,eACjBA,QAAO,UAAU,eAAe,SAAS,aAAc,QAAQ,UAAU;AACvE,eAAS,WAAW;AACpB,UAAI,CAAC,SAAU,aAAY,QAAQ,GAAG,KAAK,MAAM;AAEjD,cAAS,KAAK,MAAM,IACf,KAAK,SAAS,CAAC,KAAK,IACpB,KAAK,SAAS,CAAC,KAAK,MACpB,KAAK,SAAS,CAAC,IAAI;AAAA,IAC1B;AAEA,IAAAA,QAAO,UAAU,eACjBA,QAAO,UAAU,eAAe,SAAS,aAAc,QAAQ,UAAU;AACvE,eAAS,WAAW;AACpB,UAAI,CAAC,SAAU,aAAY,QAAQ,GAAG,KAAK,MAAM;AAEjD,aAAQ,KAAK,MAAM,IAAI,YACnB,KAAK,SAAS,CAAC,KAAK,KACrB,KAAK,SAAS,CAAC,KAAK,IACrB,KAAK,SAAS,CAAC;AAAA,IACnB;AAEA,IAAAA,QAAO,UAAU,kBAAkB,mBAAmB,SAAS,gBAAiB,QAAQ;AACtF,eAAS,WAAW;AACpB,qBAAe,QAAQ,QAAQ;AAC/B,YAAM,QAAQ,KAAK,MAAM;AACzB,YAAM,OAAO,KAAK,SAAS,CAAC;AAC5B,UAAI,UAAU,UAAa,SAAS,QAAW;AAC7C,oBAAY,QAAQ,KAAK,SAAS,CAAC;AAAA,MACrC;AAEA,YAAM,KAAK,QACT,KAAK,EAAE,MAAM,IAAI,KAAK,IACtB,KAAK,EAAE,MAAM,IAAI,KAAK,KACtB,KAAK,EAAE,MAAM,IAAI,KAAK;AAExB,YAAM,KAAK,KAAK,EAAE,MAAM,IACtB,KAAK,EAAE,MAAM,IAAI,KAAK,IACtB,KAAK,EAAE,MAAM,IAAI,KAAK,KACtB,OAAO,KAAK;AAEd,aAAO,OAAO,EAAE,KAAK,OAAO,EAAE,KAAK,OAAO,EAAE;AAAA,IAC9C,CAAC;AAED,IAAAA,QAAO,UAAU,kBAAkB,mBAAmB,SAAS,gBAAiB,QAAQ;AACtF,eAAS,WAAW;AACpB,qBAAe,QAAQ,QAAQ;AAC/B,YAAM,QAAQ,KAAK,MAAM;AACzB,YAAM,OAAO,KAAK,SAAS,CAAC;AAC5B,UAAI,UAAU,UAAa,SAAS,QAAW;AAC7C,oBAAY,QAAQ,KAAK,SAAS,CAAC;AAAA,MACrC;AAEA,YAAM,KAAK,QAAQ,KAAK,KACtB,KAAK,EAAE,MAAM,IAAI,KAAK,KACtB,KAAK,EAAE,MAAM,IAAI,KAAK,IACtB,KAAK,EAAE,MAAM;AAEf,YAAM,KAAK,KAAK,EAAE,MAAM,IAAI,KAAK,KAC/B,KAAK,EAAE,MAAM,IAAI,KAAK,KACtB,KAAK,EAAE,MAAM,IAAI,KAAK,IACtB;AAEF,cAAQ,OAAO,EAAE,KAAK,OAAO,EAAE,KAAK,OAAO,EAAE;AAAA,IAC/C,CAAC;AAED,IAAAA,QAAO,UAAU,YAAY,SAAS,UAAW,QAAQE,aAAY,UAAU;AAC7E,eAAS,WAAW;AACpB,MAAAA,cAAaA,gBAAe;AAC5B,UAAI,CAAC,SAAU,aAAY,QAAQA,aAAY,KAAK,MAAM;AAE1D,UAAI,MAAM,KAAK,MAAM;AACrB,UAAI,MAAM;AACV,UAAI,IAAI;AACR,aAAO,EAAE,IAAIA,gBAAe,OAAO,MAAQ;AACzC,eAAO,KAAK,SAAS,CAAC,IAAI;AAAA,MAC5B;AACA,aAAO;AAEP,UAAI,OAAO,IAAK,QAAO,KAAK,IAAI,GAAG,IAAIA,WAAU;AAEjD,aAAO;AAAA,IACT;AAEA,IAAAF,QAAO,UAAU,YAAY,SAAS,UAAW,QAAQE,aAAY,UAAU;AAC7E,eAAS,WAAW;AACpB,MAAAA,cAAaA,gBAAe;AAC5B,UAAI,CAAC,SAAU,aAAY,QAAQA,aAAY,KAAK,MAAM;AAE1D,UAAI,IAAIA;AACR,UAAI,MAAM;AACV,UAAI,MAAM,KAAK,SAAS,EAAE,CAAC;AAC3B,aAAO,IAAI,MAAM,OAAO,MAAQ;AAC9B,eAAO,KAAK,SAAS,EAAE,CAAC,IAAI;AAAA,MAC9B;AACA,aAAO;AAEP,UAAI,OAAO,IAAK,QAAO,KAAK,IAAI,GAAG,IAAIA,WAAU;AAEjD,aAAO;AAAA,IACT;AAEA,IAAAF,QAAO,UAAU,WAAW,SAAS,SAAU,QAAQ,UAAU;AAC/D,eAAS,WAAW;AACpB,UAAI,CAAC,SAAU,aAAY,QAAQ,GAAG,KAAK,MAAM;AACjD,UAAI,EAAE,KAAK,MAAM,IAAI,KAAO,QAAQ,KAAK,MAAM;AAC/C,cAAS,MAAO,KAAK,MAAM,IAAI,KAAK;AAAA,IACtC;AAEA,IAAAA,QAAO,UAAU,cAAc,SAAS,YAAa,QAAQ,UAAU;AACrE,eAAS,WAAW;AACpB,UAAI,CAAC,SAAU,aAAY,QAAQ,GAAG,KAAK,MAAM;AACjD,YAAM,MAAM,KAAK,MAAM,IAAK,KAAK,SAAS,CAAC,KAAK;AAChD,aAAQ,MAAM,QAAU,MAAM,aAAa;AAAA,IAC7C;AAEA,IAAAA,QAAO,UAAU,cAAc,SAAS,YAAa,QAAQ,UAAU;AACrE,eAAS,WAAW;AACpB,UAAI,CAAC,SAAU,aAAY,QAAQ,GAAG,KAAK,MAAM;AACjD,YAAM,MAAM,KAAK,SAAS,CAAC,IAAK,KAAK,MAAM,KAAK;AAChD,aAAQ,MAAM,QAAU,MAAM,aAAa;AAAA,IAC7C;AAEA,IAAAA,QAAO,UAAU,cAAc,SAAS,YAAa,QAAQ,UAAU;AACrE,eAAS,WAAW;AACpB,UAAI,CAAC,SAAU,aAAY,QAAQ,GAAG,KAAK,MAAM;AAEjD,aAAQ,KAAK,MAAM,IAChB,KAAK,SAAS,CAAC,KAAK,IACpB,KAAK,SAAS,CAAC,KAAK,KACpB,KAAK,SAAS,CAAC,KAAK;AAAA,IACzB;AAEA,IAAAA,QAAO,UAAU,cAAc,SAAS,YAAa,QAAQ,UAAU;AACrE,eAAS,WAAW;AACpB,UAAI,CAAC,SAAU,aAAY,QAAQ,GAAG,KAAK,MAAM;AAEjD,aAAQ,KAAK,MAAM,KAAK,KACrB,KAAK,SAAS,CAAC,KAAK,KACpB,KAAK,SAAS,CAAC,KAAK,IACpB,KAAK,SAAS,CAAC;AAAA,IACpB;AAEA,IAAAA,QAAO,UAAU,iBAAiB,mBAAmB,SAAS,eAAgB,QAAQ;AACpF,eAAS,WAAW;AACpB,qBAAe,QAAQ,QAAQ;AAC/B,YAAM,QAAQ,KAAK,MAAM;AACzB,YAAM,OAAO,KAAK,SAAS,CAAC;AAC5B,UAAI,UAAU,UAAa,SAAS,QAAW;AAC7C,oBAAY,QAAQ,KAAK,SAAS,CAAC;AAAA,MACrC;AAEA,YAAM,MAAM,KAAK,SAAS,CAAC,IACzB,KAAK,SAAS,CAAC,IAAI,KAAK,IACxB,KAAK,SAAS,CAAC,IAAI,KAAK,MACvB,QAAQ;AAEX,cAAQ,OAAO,GAAG,KAAK,OAAO,EAAE,KAC9B,OAAO,QACP,KAAK,EAAE,MAAM,IAAI,KAAK,IACtB,KAAK,EAAE,MAAM,IAAI,KAAK,KACtB,KAAK,EAAE,MAAM,IAAI,KAAK,EAAE;AAAA,IAC5B,CAAC;AAED,IAAAA,QAAO,UAAU,iBAAiB,mBAAmB,SAAS,eAAgB,QAAQ;AACpF,eAAS,WAAW;AACpB,qBAAe,QAAQ,QAAQ;AAC/B,YAAM,QAAQ,KAAK,MAAM;AACzB,YAAM,OAAO,KAAK,SAAS,CAAC;AAC5B,UAAI,UAAU,UAAa,SAAS,QAAW;AAC7C,oBAAY,QAAQ,KAAK,SAAS,CAAC;AAAA,MACrC;AAEA,YAAM,OAAO,SAAS;AAAA,MACpB,KAAK,EAAE,MAAM,IAAI,KAAK,KACtB,KAAK,EAAE,MAAM,IAAI,KAAK,IACtB,KAAK,EAAE,MAAM;AAEf,cAAQ,OAAO,GAAG,KAAK,OAAO,EAAE,KAC9B,OAAO,KAAK,EAAE,MAAM,IAAI,KAAK,KAC7B,KAAK,EAAE,MAAM,IAAI,KAAK,KACtB,KAAK,EAAE,MAAM,IAAI,KAAK,IACtB,IAAI;AAAA,IACR,CAAC;AAED,IAAAA,QAAO,UAAU,cAAc,SAAS,YAAa,QAAQ,UAAU;AACrE,eAAS,WAAW;AACpB,UAAI,CAAC,SAAU,aAAY,QAAQ,GAAG,KAAK,MAAM;AACjD,aAAO,QAAQ,KAAK,MAAM,QAAQ,MAAM,IAAI,CAAC;AAAA,IAC/C;AAEA,IAAAA,QAAO,UAAU,cAAc,SAAS,YAAa,QAAQ,UAAU;AACrE,eAAS,WAAW;AACpB,UAAI,CAAC,SAAU,aAAY,QAAQ,GAAG,KAAK,MAAM;AACjD,aAAO,QAAQ,KAAK,MAAM,QAAQ,OAAO,IAAI,CAAC;AAAA,IAChD;AAEA,IAAAA,QAAO,UAAU,eAAe,SAAS,aAAc,QAAQ,UAAU;AACvE,eAAS,WAAW;AACpB,UAAI,CAAC,SAAU,aAAY,QAAQ,GAAG,KAAK,MAAM;AACjD,aAAO,QAAQ,KAAK,MAAM,QAAQ,MAAM,IAAI,CAAC;AAAA,IAC/C;AAEA,IAAAA,QAAO,UAAU,eAAe,SAAS,aAAc,QAAQ,UAAU;AACvE,eAAS,WAAW;AACpB,UAAI,CAAC,SAAU,aAAY,QAAQ,GAAG,KAAK,MAAM;AACjD,aAAO,QAAQ,KAAK,MAAM,QAAQ,OAAO,IAAI,CAAC;AAAA,IAChD;AAEA,aAAS,SAAU,KAAK,OAAO,QAAQ,KAAK,KAAK,KAAK;AACpD,UAAI,CAACA,QAAO,SAAS,GAAG,EAAG,OAAM,IAAI,UAAU,6CAA6C;AAC5F,UAAI,QAAQ,OAAO,QAAQ,IAAK,OAAM,IAAI,WAAW,mCAAmC;AACxF,UAAI,SAAS,MAAM,IAAI,OAAQ,OAAM,IAAI,WAAW,oBAAoB;AAAA,IAC1E;AAEA,IAAAA,QAAO,UAAU,cACjBA,QAAO,UAAU,cAAc,SAAS,YAAa,OAAO,QAAQE,aAAY,UAAU;AACxF,cAAQ,CAAC;AACT,eAAS,WAAW;AACpB,MAAAA,cAAaA,gBAAe;AAC5B,UAAI,CAAC,UAAU;AACb,cAAM,WAAW,KAAK,IAAI,GAAG,IAAIA,WAAU,IAAI;AAC/C,iBAAS,MAAM,OAAO,QAAQA,aAAY,UAAU,CAAC;AAAA,MACvD;AAEA,UAAI,MAAM;AACV,UAAI,IAAI;AACR,WAAK,MAAM,IAAI,QAAQ;AACvB,aAAO,EAAE,IAAIA,gBAAe,OAAO,MAAQ;AACzC,aAAK,SAAS,CAAC,IAAK,QAAQ,MAAO;AAAA,MACrC;AAEA,aAAO,SAASA;AAAA,IAClB;AAEA,IAAAF,QAAO,UAAU,cACjBA,QAAO,UAAU,cAAc,SAAS,YAAa,OAAO,QAAQE,aAAY,UAAU;AACxF,cAAQ,CAAC;AACT,eAAS,WAAW;AACpB,MAAAA,cAAaA,gBAAe;AAC5B,UAAI,CAAC,UAAU;AACb,cAAM,WAAW,KAAK,IAAI,GAAG,IAAIA,WAAU,IAAI;AAC/C,iBAAS,MAAM,OAAO,QAAQA,aAAY,UAAU,CAAC;AAAA,MACvD;AAEA,UAAI,IAAIA,cAAa;AACrB,UAAI,MAAM;AACV,WAAK,SAAS,CAAC,IAAI,QAAQ;AAC3B,aAAO,EAAE,KAAK,MAAM,OAAO,MAAQ;AACjC,aAAK,SAAS,CAAC,IAAK,QAAQ,MAAO;AAAA,MACrC;AAEA,aAAO,SAASA;AAAA,IAClB;AAEA,IAAAF,QAAO,UAAU,aACjBA,QAAO,UAAU,aAAa,SAAS,WAAY,OAAO,QAAQ,UAAU;AAC1E,cAAQ,CAAC;AACT,eAAS,WAAW;AACpB,UAAI,CAAC,SAAU,UAAS,MAAM,OAAO,QAAQ,GAAG,KAAM,CAAC;AACvD,WAAK,MAAM,IAAK,QAAQ;AACxB,aAAO,SAAS;AAAA,IAClB;AAEA,IAAAA,QAAO,UAAU,gBACjBA,QAAO,UAAU,gBAAgB,SAAS,cAAe,OAAO,QAAQ,UAAU;AAChF,cAAQ,CAAC;AACT,eAAS,WAAW;AACpB,UAAI,CAAC,SAAU,UAAS,MAAM,OAAO,QAAQ,GAAG,OAAQ,CAAC;AACzD,WAAK,MAAM,IAAK,QAAQ;AACxB,WAAK,SAAS,CAAC,IAAK,UAAU;AAC9B,aAAO,SAAS;AAAA,IAClB;AAEA,IAAAA,QAAO,UAAU,gBACjBA,QAAO,UAAU,gBAAgB,SAAS,cAAe,OAAO,QAAQ,UAAU;AAChF,cAAQ,CAAC;AACT,eAAS,WAAW;AACpB,UAAI,CAAC,SAAU,UAAS,MAAM,OAAO,QAAQ,GAAG,OAAQ,CAAC;AACzD,WAAK,MAAM,IAAK,UAAU;AAC1B,WAAK,SAAS,CAAC,IAAK,QAAQ;AAC5B,aAAO,SAAS;AAAA,IAClB;AAEA,IAAAA,QAAO,UAAU,gBACjBA,QAAO,UAAU,gBAAgB,SAAS,cAAe,OAAO,QAAQ,UAAU;AAChF,cAAQ,CAAC;AACT,eAAS,WAAW;AACpB,UAAI,CAAC,SAAU,UAAS,MAAM,OAAO,QAAQ,GAAG,YAAY,CAAC;AAC7D,WAAK,SAAS,CAAC,IAAK,UAAU;AAC9B,WAAK,SAAS,CAAC,IAAK,UAAU;AAC9B,WAAK,SAAS,CAAC,IAAK,UAAU;AAC9B,WAAK,MAAM,IAAK,QAAQ;AACxB,aAAO,SAAS;AAAA,IAClB;AAEA,IAAAA,QAAO,UAAU,gBACjBA,QAAO,UAAU,gBAAgB,SAAS,cAAe,OAAO,QAAQ,UAAU;AAChF,cAAQ,CAAC;AACT,eAAS,WAAW;AACpB,UAAI,CAAC,SAAU,UAAS,MAAM,OAAO,QAAQ,GAAG,YAAY,CAAC;AAC7D,WAAK,MAAM,IAAK,UAAU;AAC1B,WAAK,SAAS,CAAC,IAAK,UAAU;AAC9B,WAAK,SAAS,CAAC,IAAK,UAAU;AAC9B,WAAK,SAAS,CAAC,IAAK,QAAQ;AAC5B,aAAO,SAAS;AAAA,IAClB;AAEA,aAAS,eAAgB,KAAK,OAAO,QAAQ,KAAK,KAAK;AACrD,iBAAW,OAAO,KAAK,KAAK,KAAK,QAAQ,CAAC;AAE1C,UAAI,KAAK,OAAO,QAAQ,OAAO,UAAU,CAAC;AAC1C,UAAI,QAAQ,IAAI;AAChB,WAAK,MAAM;AACX,UAAI,QAAQ,IAAI;AAChB,WAAK,MAAM;AACX,UAAI,QAAQ,IAAI;AAChB,WAAK,MAAM;AACX,UAAI,QAAQ,IAAI;AAChB,UAAI,KAAK,OAAO,SAAS,OAAO,EAAE,IAAI,OAAO,UAAU,CAAC;AACxD,UAAI,QAAQ,IAAI;AAChB,WAAK,MAAM;AACX,UAAI,QAAQ,IAAI;AAChB,WAAK,MAAM;AACX,UAAI,QAAQ,IAAI;AAChB,WAAK,MAAM;AACX,UAAI,QAAQ,IAAI;AAChB,aAAO;AAAA,IACT;AAEA,aAAS,eAAgB,KAAK,OAAO,QAAQ,KAAK,KAAK;AACrD,iBAAW,OAAO,KAAK,KAAK,KAAK,QAAQ,CAAC;AAE1C,UAAI,KAAK,OAAO,QAAQ,OAAO,UAAU,CAAC;AAC1C,UAAI,SAAS,CAAC,IAAI;AAClB,WAAK,MAAM;AACX,UAAI,SAAS,CAAC,IAAI;AAClB,WAAK,MAAM;AACX,UAAI,SAAS,CAAC,IAAI;AAClB,WAAK,MAAM;AACX,UAAI,SAAS,CAAC,IAAI;AAClB,UAAI,KAAK,OAAO,SAAS,OAAO,EAAE,IAAI,OAAO,UAAU,CAAC;AACxD,UAAI,SAAS,CAAC,IAAI;AAClB,WAAK,MAAM;AACX,UAAI,SAAS,CAAC,IAAI;AAClB,WAAK,MAAM;AACX,UAAI,SAAS,CAAC,IAAI;AAClB,WAAK,MAAM;AACX,UAAI,MAAM,IAAI;AACd,aAAO,SAAS;AAAA,IAClB;AAEA,IAAAA,QAAO,UAAU,mBAAmB,mBAAmB,SAAS,iBAAkB,OAAO,SAAS,GAAG;AACnG,aAAO,eAAe,MAAM,OAAO,QAAQ,OAAO,CAAC,GAAG,OAAO,oBAAoB,CAAC;AAAA,IACpF,CAAC;AAED,IAAAA,QAAO,UAAU,mBAAmB,mBAAmB,SAAS,iBAAkB,OAAO,SAAS,GAAG;AACnG,aAAO,eAAe,MAAM,OAAO,QAAQ,OAAO,CAAC,GAAG,OAAO,oBAAoB,CAAC;AAAA,IACpF,CAAC;AAED,IAAAA,QAAO,UAAU,aAAa,SAAS,WAAY,OAAO,QAAQE,aAAY,UAAU;AACtF,cAAQ,CAAC;AACT,eAAS,WAAW;AACpB,UAAI,CAAC,UAAU;AACb,cAAM,QAAQ,KAAK,IAAI,GAAI,IAAIA,cAAc,CAAC;AAE9C,iBAAS,MAAM,OAAO,QAAQA,aAAY,QAAQ,GAAG,CAAC,KAAK;AAAA,MAC7D;AAEA,UAAI,IAAI;AACR,UAAI,MAAM;AACV,UAAI,MAAM;AACV,WAAK,MAAM,IAAI,QAAQ;AACvB,aAAO,EAAE,IAAIA,gBAAe,OAAO,MAAQ;AACzC,YAAI,QAAQ,KAAK,QAAQ,KAAK,KAAK,SAAS,IAAI,CAAC,MAAM,GAAG;AACxD,gBAAM;AAAA,QACR;AACA,aAAK,SAAS,CAAC,KAAM,QAAQ,OAAQ,KAAK,MAAM;AAAA,MAClD;AAEA,aAAO,SAASA;AAAA,IAClB;AAEA,IAAAF,QAAO,UAAU,aAAa,SAAS,WAAY,OAAO,QAAQE,aAAY,UAAU;AACtF,cAAQ,CAAC;AACT,eAAS,WAAW;AACpB,UAAI,CAAC,UAAU;AACb,cAAM,QAAQ,KAAK,IAAI,GAAI,IAAIA,cAAc,CAAC;AAE9C,iBAAS,MAAM,OAAO,QAAQA,aAAY,QAAQ,GAAG,CAAC,KAAK;AAAA,MAC7D;AAEA,UAAI,IAAIA,cAAa;AACrB,UAAI,MAAM;AACV,UAAI,MAAM;AACV,WAAK,SAAS,CAAC,IAAI,QAAQ;AAC3B,aAAO,EAAE,KAAK,MAAM,OAAO,MAAQ;AACjC,YAAI,QAAQ,KAAK,QAAQ,KAAK,KAAK,SAAS,IAAI,CAAC,MAAM,GAAG;AACxD,gBAAM;AAAA,QACR;AACA,aAAK,SAAS,CAAC,KAAM,QAAQ,OAAQ,KAAK,MAAM;AAAA,MAClD;AAEA,aAAO,SAASA;AAAA,IAClB;AAEA,IAAAF,QAAO,UAAU,YAAY,SAAS,UAAW,OAAO,QAAQ,UAAU;AACxE,cAAQ,CAAC;AACT,eAAS,WAAW;AACpB,UAAI,CAAC,SAAU,UAAS,MAAM,OAAO,QAAQ,GAAG,KAAM,IAAK;AAC3D,UAAI,QAAQ,EAAG,SAAQ,MAAO,QAAQ;AACtC,WAAK,MAAM,IAAK,QAAQ;AACxB,aAAO,SAAS;AAAA,IAClB;AAEA,IAAAA,QAAO,UAAU,eAAe,SAAS,aAAc,OAAO,QAAQ,UAAU;AAC9E,cAAQ,CAAC;AACT,eAAS,WAAW;AACpB,UAAI,CAAC,SAAU,UAAS,MAAM,OAAO,QAAQ,GAAG,OAAQ,MAAO;AAC/D,WAAK,MAAM,IAAK,QAAQ;AACxB,WAAK,SAAS,CAAC,IAAK,UAAU;AAC9B,aAAO,SAAS;AAAA,IAClB;AAEA,IAAAA,QAAO,UAAU,eAAe,SAAS,aAAc,OAAO,QAAQ,UAAU;AAC9E,cAAQ,CAAC;AACT,eAAS,WAAW;AACpB,UAAI,CAAC,SAAU,UAAS,MAAM,OAAO,QAAQ,GAAG,OAAQ,MAAO;AAC/D,WAAK,MAAM,IAAK,UAAU;AAC1B,WAAK,SAAS,CAAC,IAAK,QAAQ;AAC5B,aAAO,SAAS;AAAA,IAClB;AAEA,IAAAA,QAAO,UAAU,eAAe,SAAS,aAAc,OAAO,QAAQ,UAAU;AAC9E,cAAQ,CAAC;AACT,eAAS,WAAW;AACpB,UAAI,CAAC,SAAU,UAAS,MAAM,OAAO,QAAQ,GAAG,YAAY,WAAW;AACvE,WAAK,MAAM,IAAK,QAAQ;AACxB,WAAK,SAAS,CAAC,IAAK,UAAU;AAC9B,WAAK,SAAS,CAAC,IAAK,UAAU;AAC9B,WAAK,SAAS,CAAC,IAAK,UAAU;AAC9B,aAAO,SAAS;AAAA,IAClB;AAEA,IAAAA,QAAO,UAAU,eAAe,SAAS,aAAc,OAAO,QAAQ,UAAU;AAC9E,cAAQ,CAAC;AACT,eAAS,WAAW;AACpB,UAAI,CAAC,SAAU,UAAS,MAAM,OAAO,QAAQ,GAAG,YAAY,WAAW;AACvE,UAAI,QAAQ,EAAG,SAAQ,aAAa,QAAQ;AAC5C,WAAK,MAAM,IAAK,UAAU;AAC1B,WAAK,SAAS,CAAC,IAAK,UAAU;AAC9B,WAAK,SAAS,CAAC,IAAK,UAAU;AAC9B,WAAK,SAAS,CAAC,IAAK,QAAQ;AAC5B,aAAO,SAAS;AAAA,IAClB;AAEA,IAAAA,QAAO,UAAU,kBAAkB,mBAAmB,SAAS,gBAAiB,OAAO,SAAS,GAAG;AACjG,aAAO,eAAe,MAAM,OAAO,QAAQ,CAAC,OAAO,oBAAoB,GAAG,OAAO,oBAAoB,CAAC;AAAA,IACxG,CAAC;AAED,IAAAA,QAAO,UAAU,kBAAkB,mBAAmB,SAAS,gBAAiB,OAAO,SAAS,GAAG;AACjG,aAAO,eAAe,MAAM,OAAO,QAAQ,CAAC,OAAO,oBAAoB,GAAG,OAAO,oBAAoB,CAAC;AAAA,IACxG,CAAC;AAED,aAAS,aAAc,KAAK,OAAO,QAAQ,KAAK,KAAK,KAAK;AACxD,UAAI,SAAS,MAAM,IAAI,OAAQ,OAAM,IAAI,WAAW,oBAAoB;AACxE,UAAI,SAAS,EAAG,OAAM,IAAI,WAAW,oBAAoB;AAAA,IAC3D;AAEA,aAAS,WAAY,KAAK,OAAO,QAAQ,cAAc,UAAU;AAC/D,cAAQ,CAAC;AACT,eAAS,WAAW;AACpB,UAAI,CAAC,UAAU;AACb,qBAAa,KAAK,OAAO,QAAQ,GAAG,sBAAwB,qBAAuB;AAAA,MACrF;AACA,cAAQ,MAAM,KAAK,OAAO,QAAQ,cAAc,IAAI,CAAC;AACrD,aAAO,SAAS;AAAA,IAClB;AAEA,IAAAA,QAAO,UAAU,eAAe,SAAS,aAAc,OAAO,QAAQ,UAAU;AAC9E,aAAO,WAAW,MAAM,OAAO,QAAQ,MAAM,QAAQ;AAAA,IACvD;AAEA,IAAAA,QAAO,UAAU,eAAe,SAAS,aAAc,OAAO,QAAQ,UAAU;AAC9E,aAAO,WAAW,MAAM,OAAO,QAAQ,OAAO,QAAQ;AAAA,IACxD;AAEA,aAAS,YAAa,KAAK,OAAO,QAAQ,cAAc,UAAU;AAChE,cAAQ,CAAC;AACT,eAAS,WAAW;AACpB,UAAI,CAAC,UAAU;AACb,qBAAa,KAAK,OAAO,QAAQ,GAAG,uBAAyB,sBAAwB;AAAA,MACvF;AACA,cAAQ,MAAM,KAAK,OAAO,QAAQ,cAAc,IAAI,CAAC;AACrD,aAAO,SAAS;AAAA,IAClB;AAEA,IAAAA,QAAO,UAAU,gBAAgB,SAAS,cAAe,OAAO,QAAQ,UAAU;AAChF,aAAO,YAAY,MAAM,OAAO,QAAQ,MAAM,QAAQ;AAAA,IACxD;AAEA,IAAAA,QAAO,UAAU,gBAAgB,SAAS,cAAe,OAAO,QAAQ,UAAU;AAChF,aAAO,YAAY,MAAM,OAAO,QAAQ,OAAO,QAAQ;AAAA,IACzD;AAGA,IAAAA,QAAO,UAAU,OAAO,SAAS,KAAM,QAAQ,aAAa,OAAO,KAAK;AACtE,UAAI,CAACA,QAAO,SAAS,MAAM,EAAG,OAAM,IAAI,UAAU,6BAA6B;AAC/E,UAAI,CAAC,MAAO,SAAQ;AACpB,UAAI,CAAC,OAAO,QAAQ,EAAG,OAAM,KAAK;AAClC,UAAI,eAAe,OAAO,OAAQ,eAAc,OAAO;AACvD,UAAI,CAAC,YAAa,eAAc;AAChC,UAAI,MAAM,KAAK,MAAM,MAAO,OAAM;AAGlC,UAAI,QAAQ,MAAO,QAAO;AAC1B,UAAI,OAAO,WAAW,KAAK,KAAK,WAAW,EAAG,QAAO;AAGrD,UAAI,cAAc,GAAG;AACnB,cAAM,IAAI,WAAW,2BAA2B;AAAA,MAClD;AACA,UAAI,QAAQ,KAAK,SAAS,KAAK,OAAQ,OAAM,IAAI,WAAW,oBAAoB;AAChF,UAAI,MAAM,EAAG,OAAM,IAAI,WAAW,yBAAyB;AAG3D,UAAI,MAAM,KAAK,OAAQ,OAAM,KAAK;AAClC,UAAI,OAAO,SAAS,cAAc,MAAM,OAAO;AAC7C,cAAM,OAAO,SAAS,cAAc;AAAA,MACtC;AAEA,YAAM,MAAM,MAAM;AAElB,UAAI,SAAS,UAAU,OAAO,WAAW,UAAU,eAAe,YAAY;AAE5E,aAAK,WAAW,aAAa,OAAO,GAAG;AAAA,MACzC,OAAO;AACL,mBAAW,UAAU,IAAI;AAAA,UACvB;AAAA,UACA,KAAK,SAAS,OAAO,GAAG;AAAA,UACxB;AAAA,QACF;AAAA,MACF;AAEA,aAAO;AAAA,IACT;AAMA,IAAAA,QAAO,UAAU,OAAO,SAAS,KAAM,KAAK,OAAO,KAAK,UAAU;AAEhE,UAAI,OAAO,QAAQ,UAAU;AAC3B,YAAI,OAAO,UAAU,UAAU;AAC7B,qBAAW;AACX,kBAAQ;AACR,gBAAM,KAAK;AAAA,QACb,WAAW,OAAO,QAAQ,UAAU;AAClC,qBAAW;AACX,gBAAM,KAAK;AAAA,QACb;AACA,YAAI,aAAa,UAAa,OAAO,aAAa,UAAU;AAC1D,gBAAM,IAAI,UAAU,2BAA2B;AAAA,QACjD;AACA,YAAI,OAAO,aAAa,YAAY,CAACA,QAAO,WAAW,QAAQ,GAAG;AAChE,gBAAM,IAAI,UAAU,uBAAuB,QAAQ;AAAA,QACrD;AACA,YAAI,IAAI,WAAW,GAAG;AACpB,gBAAM,OAAO,IAAI,WAAW,CAAC;AAC7B,cAAK,aAAa,UAAU,OAAO,OAC/B,aAAa,UAAU;AAEzB,kBAAM;AAAA,UACR;AAAA,QACF;AAAA,MACF,WAAW,OAAO,QAAQ,UAAU;AAClC,cAAM,MAAM;AAAA,MACd,WAAW,OAAO,QAAQ,WAAW;AACnC,cAAM,OAAO,GAAG;AAAA,MAClB;AAGA,UAAI,QAAQ,KAAK,KAAK,SAAS,SAAS,KAAK,SAAS,KAAK;AACzD,cAAM,IAAI,WAAW,oBAAoB;AAAA,MAC3C;AAEA,UAAI,OAAO,OAAO;AAChB,eAAO;AAAA,MACT;AAEA,cAAQ,UAAU;AAClB,YAAM,QAAQ,SAAY,KAAK,SAAS,QAAQ;AAEhD,UAAI,CAAC,IAAK,OAAM;AAEhB,UAAI;AACJ,UAAI,OAAO,QAAQ,UAAU;AAC3B,aAAK,IAAI,OAAO,IAAI,KAAK,EAAE,GAAG;AAC5B,eAAK,CAAC,IAAI;AAAA,QACZ;AAAA,MACF,OAAO;AACL,cAAM,QAAQA,QAAO,SAAS,GAAG,IAC7B,MACAA,QAAO,KAAK,KAAK,QAAQ;AAC7B,cAAM,MAAM,MAAM;AAClB,YAAI,QAAQ,GAAG;AACb,gBAAM,IAAI,UAAU,gBAAgB,MAClC,mCAAmC;AAAA,QACvC;AACA,aAAK,IAAI,GAAG,IAAI,MAAM,OAAO,EAAE,GAAG;AAChC,eAAK,IAAI,KAAK,IAAI,MAAM,IAAI,GAAG;AAAA,QACjC;AAAA,MACF;AAEA,aAAO;AAAA,IACT;AAMA,QAAM,SAAS,CAAC;AAChB,aAAS,EAAG,KAAK,YAAY,MAAM;AACjC,aAAO,GAAG,IAAI,MAAM,kBAAkB,KAAK;AAAA,QACzC,cAAe;AACb,gBAAM;AAEN,iBAAO,eAAe,MAAM,WAAW;AAAA,YACrC,OAAO,WAAW,MAAM,MAAM,SAAS;AAAA,YACvC,UAAU;AAAA,YACV,cAAc;AAAA,UAChB,CAAC;AAGD,eAAK,OAAO,GAAG,KAAK,IAAI,KAAK,GAAG;AAGhC,eAAK;AAEL,iBAAO,KAAK;AAAA,QACd;AAAA,QAEA,IAAI,OAAQ;AACV,iBAAO;AAAA,QACT;AAAA,QAEA,IAAI,KAAM,OAAO;AACf,iBAAO,eAAe,MAAM,QAAQ;AAAA,YAClC,cAAc;AAAA,YACd,YAAY;AAAA,YACZ;AAAA,YACA,UAAU;AAAA,UACZ,CAAC;AAAA,QACH;AAAA,QAEA,WAAY;AACV,iBAAO,GAAG,KAAK,IAAI,KAAK,GAAG,MAAM,KAAK,OAAO;AAAA,QAC/C;AAAA,MACF;AAAA,IACF;AAEA;AAAA,MAAE;AAAA,MACA,SAAU,MAAM;AACd,YAAI,MAAM;AACR,iBAAO,GAAG,IAAI;AAAA,QAChB;AAEA,eAAO;AAAA,MACT;AAAA,MAAG;AAAA,IAAU;AACf;AAAA,MAAE;AAAA,MACA,SAAU,MAAM,QAAQ;AACtB,eAAO,QAAQ,IAAI,oDAAoD,OAAO,MAAM;AAAA,MACtF;AAAA,MAAG;AAAA,IAAS;AACd;AAAA,MAAE;AAAA,MACA,SAAU,KAAK,OAAO,OAAO;AAC3B,YAAI,MAAM,iBAAiB,GAAG;AAC9B,YAAI,WAAW;AACf,YAAI,OAAO,UAAU,KAAK,KAAK,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI;AACxD,qBAAW,sBAAsB,OAAO,KAAK,CAAC;AAAA,QAChD,WAAW,OAAO,UAAU,UAAU;AACpC,qBAAW,OAAO,KAAK;AACvB,cAAI,QAAQ,OAAO,CAAC,KAAK,OAAO,EAAE,KAAK,QAAQ,EAAE,OAAO,CAAC,KAAK,OAAO,EAAE,IAAI;AACzE,uBAAW,sBAAsB,QAAQ;AAAA,UAC3C;AACA,sBAAY;AAAA,QACd;AACA,eAAO,eAAe,KAAK,cAAc,QAAQ;AACjD,eAAO;AAAA,MACT;AAAA,MAAG;AAAA,IAAU;AAEf,aAAS,sBAAuB,KAAK;AACnC,UAAI,MAAM;AACV,UAAI,IAAI,IAAI;AACZ,YAAM,QAAQ,IAAI,CAAC,MAAM,MAAM,IAAI;AACnC,aAAO,KAAK,QAAQ,GAAG,KAAK,GAAG;AAC7B,cAAM,IAAI,IAAI,MAAM,IAAI,GAAG,CAAC,CAAC,GAAG,GAAG;AAAA,MACrC;AACA,aAAO,GAAG,IAAI,MAAM,GAAG,CAAC,CAAC,GAAG,GAAG;AAAA,IACjC;AAKA,aAAS,YAAa,KAAK,QAAQE,aAAY;AAC7C,qBAAe,QAAQ,QAAQ;AAC/B,UAAI,IAAI,MAAM,MAAM,UAAa,IAAI,SAASA,WAAU,MAAM,QAAW;AACvE,oBAAY,QAAQ,IAAI,UAAUA,cAAa,EAAE;AAAA,MACnD;AAAA,IACF;AAEA,aAAS,WAAY,OAAO,KAAK,KAAK,KAAK,QAAQA,aAAY;AAC7D,UAAI,QAAQ,OAAO,QAAQ,KAAK;AAC9B,cAAM,IAAI,OAAO,QAAQ,WAAW,MAAM;AAC1C,YAAI;AACJ,YAAIA,cAAa,GAAG;AAClB,cAAI,QAAQ,KAAK,QAAQ,OAAO,CAAC,GAAG;AAClC,oBAAQ,OAAO,CAAC,WAAW,CAAC,QAAQA,cAAa,KAAK,CAAC,GAAG,CAAC;AAAA,UAC7D,OAAO;AACL,oBAAQ,SAAS,CAAC,QAAQA,cAAa,KAAK,IAAI,CAAC,GAAG,CAAC,iBACzCA,cAAa,KAAK,IAAI,CAAC,GAAG,CAAC;AAAA,UACzC;AAAA,QACF,OAAO;AACL,kBAAQ,MAAM,GAAG,GAAG,CAAC,WAAW,GAAG,GAAG,CAAC;AAAA,QACzC;AACA,cAAM,IAAI,OAAO,iBAAiB,SAAS,OAAO,KAAK;AAAA,MACzD;AACA,kBAAY,KAAK,QAAQA,WAAU;AAAA,IACrC;AAEA,aAAS,eAAgB,OAAO,MAAM;AACpC,UAAI,OAAO,UAAU,UAAU;AAC7B,cAAM,IAAI,OAAO,qBAAqB,MAAM,UAAU,KAAK;AAAA,MAC7D;AAAA,IACF;AAEA,aAAS,YAAa,OAAO,QAAQ,MAAM;AACzC,UAAI,KAAK,MAAM,KAAK,MAAM,OAAO;AAC/B,uBAAe,OAAO,IAAI;AAC1B,cAAM,IAAI,OAAO,iBAAiB,QAAQ,UAAU,cAAc,KAAK;AAAA,MACzE;AAEA,UAAI,SAAS,GAAG;AACd,cAAM,IAAI,OAAO,yBAAyB;AAAA,MAC5C;AAEA,YAAM,IAAI,OAAO;AAAA,QAAiB,QAAQ;AAAA,QACR,MAAM,OAAO,IAAI,CAAC,WAAW,MAAM;AAAA,QACnC;AAAA,MAAK;AAAA,IACzC;AAKA,QAAM,oBAAoB;AAE1B,aAAS,YAAa,KAAK;AAEzB,YAAM,IAAI,MAAM,GAAG,EAAE,CAAC;AAEtB,YAAM,IAAI,KAAK,EAAE,QAAQ,mBAAmB,EAAE;AAE9C,UAAI,IAAI,SAAS,EAAG,QAAO;AAE3B,aAAO,IAAI,SAAS,MAAM,GAAG;AAC3B,cAAM,MAAM;AAAA,MACd;AACA,aAAO;AAAA,IACT;AAEA,aAAS,YAAa,QAAQ,OAAO;AACnC,cAAQ,SAAS;AACjB,UAAI;AACJ,YAAM,SAAS,OAAO;AACtB,UAAI,gBAAgB;AACpB,YAAM,QAAQ,CAAC;AAEf,eAAS,IAAI,GAAG,IAAI,QAAQ,EAAE,GAAG;AAC/B,oBAAY,OAAO,WAAW,CAAC;AAG/B,YAAI,YAAY,SAAU,YAAY,OAAQ;AAE5C,cAAI,CAAC,eAAe;AAElB,gBAAI,YAAY,OAAQ;AAEtB,mBAAK,SAAS,KAAK,GAAI,OAAM,KAAK,KAAM,KAAM,GAAI;AAClD;AAAA,YACF,WAAW,IAAI,MAAM,QAAQ;AAE3B,mBAAK,SAAS,KAAK,GAAI,OAAM,KAAK,KAAM,KAAM,GAAI;AAClD;AAAA,YACF;AAGA,4BAAgB;AAEhB;AAAA,UACF;AAGA,cAAI,YAAY,OAAQ;AACtB,iBAAK,SAAS,KAAK,GAAI,OAAM,KAAK,KAAM,KAAM,GAAI;AAClD,4BAAgB;AAChB;AAAA,UACF;AAGA,uBAAa,gBAAgB,SAAU,KAAK,YAAY,SAAU;AAAA,QACpE,WAAW,eAAe;AAExB,eAAK,SAAS,KAAK,GAAI,OAAM,KAAK,KAAM,KAAM,GAAI;AAAA,QACpD;AAEA,wBAAgB;AAGhB,YAAI,YAAY,KAAM;AACpB,eAAK,SAAS,KAAK,EAAG;AACtB,gBAAM,KAAK,SAAS;AAAA,QACtB,WAAW,YAAY,MAAO;AAC5B,eAAK,SAAS,KAAK,EAAG;AACtB,gBAAM;AAAA,YACJ,aAAa,IAAM;AAAA,YACnB,YAAY,KAAO;AAAA,UACrB;AAAA,QACF,WAAW,YAAY,OAAS;AAC9B,eAAK,SAAS,KAAK,EAAG;AACtB,gBAAM;AAAA,YACJ,aAAa,KAAM;AAAA,YACnB,aAAa,IAAM,KAAO;AAAA,YAC1B,YAAY,KAAO;AAAA,UACrB;AAAA,QACF,WAAW,YAAY,SAAU;AAC/B,eAAK,SAAS,KAAK,EAAG;AACtB,gBAAM;AAAA,YACJ,aAAa,KAAO;AAAA,YACpB,aAAa,KAAM,KAAO;AAAA,YAC1B,aAAa,IAAM,KAAO;AAAA,YAC1B,YAAY,KAAO;AAAA,UACrB;AAAA,QACF,OAAO;AACL,gBAAM,IAAI,MAAM,oBAAoB;AAAA,QACtC;AAAA,MACF;AAEA,aAAO;AAAA,IACT;AAEA,aAAS,aAAc,KAAK;AAC1B,YAAM,YAAY,CAAC;AACnB,eAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,EAAE,GAAG;AAEnC,kBAAU,KAAK,IAAI,WAAW,CAAC,IAAI,GAAI;AAAA,MACzC;AACA,aAAO;AAAA,IACT;AAEA,aAAS,eAAgB,KAAK,OAAO;AACnC,UAAI,GAAG,IAAI;AACX,YAAM,YAAY,CAAC;AACnB,eAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,EAAE,GAAG;AACnC,aAAK,SAAS,KAAK,EAAG;AAEtB,YAAI,IAAI,WAAW,CAAC;AACpB,aAAK,KAAK;AACV,aAAK,IAAI;AACT,kBAAU,KAAK,EAAE;AACjB,kBAAU,KAAK,EAAE;AAAA,MACnB;AAEA,aAAO;AAAA,IACT;AAEA,aAAS,cAAe,KAAK;AAC3B,aAAO,OAAO,YAAY,YAAY,GAAG,CAAC;AAAA,IAC5C;AAEA,aAAS,WAAY,KAAK,KAAK,QAAQ,QAAQ;AAC7C,UAAI;AACJ,WAAK,IAAI,GAAG,IAAI,QAAQ,EAAE,GAAG;AAC3B,YAAK,IAAI,UAAU,IAAI,UAAY,KAAK,IAAI,OAAS;AACrD,YAAI,IAAI,MAAM,IAAI,IAAI,CAAC;AAAA,MACzB;AACA,aAAO;AAAA,IACT;AAKA,aAAS,WAAY,KAAK,MAAM;AAC9B,aAAO,eAAe,QACnB,OAAO,QAAQ,IAAI,eAAe,QAAQ,IAAI,YAAY,QAAQ,QACjE,IAAI,YAAY,SAAS,KAAK;AAAA,IACpC;AACA,aAAS,YAAa,KAAK;AAEzB,aAAO,QAAQ;AAAA,IACjB;AAIA,QAAM,sBAAuB,WAAY;AACvC,YAAM,WAAW;AACjB,YAAM,QAAQ,IAAI,MAAM,GAAG;AAC3B,eAAS,IAAI,GAAG,IAAI,IAAI,EAAE,GAAG;AAC3B,cAAM,MAAM,IAAI;AAChB,iBAAS,IAAI,GAAG,IAAI,IAAI,EAAE,GAAG;AAC3B,gBAAM,MAAM,CAAC,IAAI,SAAS,CAAC,IAAI,SAAS,CAAC;AAAA,QAC3C;AAAA,MACF;AACA,aAAO;AAAA,IACT,EAAG;AAGH,aAAS,mBAAoB,IAAI;AAC/B,aAAO,OAAO,WAAW,cAAc,yBAAyB;AAAA,IAClE;AAEA,aAAS,yBAA0B;AACjC,YAAM,IAAI,MAAM,sBAAsB;AAAA,IACxC;AAAA;AAAA;;;ACjjEA,IAAM,eAAe,oBAAI,QAAO;AAChC,IAAM,aAAa,oBAAI,QAAO;AA0CxB,IAAO,cAAP,MAAO,aAAW;EACtB,cAAA;AA8BO,SAAA,UAAwC;AA7B7C,iBAAa,IAAI,MAAM,CAAA,CAAE;AACzB,eAAW,IAAI,MAAM,KAAK;EAC5B;;;;;;EAOA,IAAW,UAAO;AAChB,QAAI,CAAC,WAAW,IAAI,IAAI,GAAG;AACzB,YAAM,IAAI,UAAU,mDAAmD;;AAGzE,WAAO,WAAW,IAAI,IAAI;EAC5B;;;;;;EAOO,WAAW,OAAI;AACpB,WAAO,IAAI,aAAW;EACxB;;;;;;;EAaO,iBAEL,OACA,UAAiD;AAEjD,QAAI,CAAC,aAAa,IAAI,IAAI,GAAG;AAC3B,YAAM,IAAI,UAAU,mDAAmD;;AAGzE,UAAM,YAAY,aAAa,IAAI,IAAI;AACvC,cAAU,KAAK,QAAQ;EACzB;;;;;;;EAQO,oBAEL,OACA,UAAiD;AAEjD,QAAI,CAAC,aAAa,IAAI,IAAI,GAAG;AAC3B,YAAM,IAAI,UAAU,mDAAmD;;AAGzE,UAAM,YAAY,aAAa,IAAI,IAAI;AAEvC,UAAM,QAAQ,UAAU,QAAQ,QAAQ;AACxC,QAAI,QAAQ,IAAI;AACd,gBAAU,OAAO,OAAO,CAAC;;EAE7B;;;;EAKA,cAAc,QAAa;AACzB,UAAM,IAAI,MACR,kHAAkH;EAEtH;;AAaI,SAAU,YAAY,QAAmB;AAC7C,MAAI,OAAO,SAAS;AAClB;;AAGF,MAAI,OAAO,SAAS;AAClB,WAAO,QAAQ,KAAK,MAAM;;AAG5B,QAAM,YAAY,aAAa,IAAI,MAAM;AACzC,MAAI,WAAW;AAIb,cAAU,MAAK,EAAG,QAAQ,CAAC,aAAY;AACrC,eAAS,KAAK,QAAQ,EAAE,MAAM,QAAO,CAAE;IACzC,CAAC;;AAGH,aAAW,IAAI,QAAQ,IAAI;AAC7B;;;AC/IM,IAAO,aAAP,cAA0B,MAAK;EACnC,YAAY,SAAgB;AAC1B,UAAM,OAAO;AACb,SAAK,OAAO;EACd;;AAqCI,IAAOC,mBAAP,MAAsB;;EAY1B,YAAY,eAAmB;AAC7B,SAAK,UAAU,IAAI,YAAW;AAE9B,QAAI,CAAC,eAAe;AAClB;;AAGF,QAAI,CAAC,MAAM,QAAQ,aAAa,GAAG;AAEjC,sBAAgB;;AAElB,eAAW,gBAAgB,eAAe;AAGxC,UAAI,aAAa,SAAS;AACxB,aAAK,MAAK;aACL;AAEL,qBAAa,iBAAiB,SAAS,MAAK;AAC1C,eAAK,MAAK;QACZ,CAAC;;;EAGP;;;;;;;EAQA,IAAW,SAAM;AACf,WAAO,KAAK;EACd;;;;;EAMA,QAAK;AACH,gBAAY,KAAK,OAAO;EAC1B;;;;;EAMO,OAAO,QAAQ,IAAU;AAC9B,UAAM,SAAS,IAAI,YAAW;AAC9B,UAAM,QAAQ,WAAW,aAAa,IAAI,MAAM;AAEhD,QAAI,OAAO,MAAM,UAAU,YAAY;AACrC,YAAM,MAAK;;AAEb,WAAO;EACT;;;;AC9GI,IAAOC,cAAP,cAA0B,MAAK;EACnC,YAAY,SAAgB;AAC1B,UAAM,OAAO;AACb,SAAK,OAAO;EACd;;;;ACLI,SAAU,uBACd,cAIA,SAAuC;AAEvC,QAAM,EAAE,oBAAoB,aAAAC,cAAa,cAAa,IAAK,YAAO,QAAP,YAAO,SAAP,UAAW,CAAA;AACtE,SAAO,IAAI,QAAQ,CAAC,SAAS,WAAU;AACrC,aAAS,gBAAa;AACpB,aAAO,IAAIC,YAAW,kBAAa,QAAb,kBAAa,SAAb,gBAAiB,4BAA4B,CAAC;IACtE;AACA,aAAS,kBAAe;AACtB,MAAAD,iBAAW,QAAXA,iBAAW,SAAA,SAAXA,aAAa,oBAAoB,SAAS,OAAO;IACnD;AACA,aAAS,UAAO;AACd,6BAAkB,QAAlB,uBAAkB,SAAA,SAAlB,mBAAkB;AAClB,sBAAe;AACf,oBAAa;IACf;AACA,QAAIA,iBAAW,QAAXA,iBAAW,SAAA,SAAXA,aAAa,SAAS;AACxB,aAAO,cAAa;IACtB;AACA,QAAI;AACF,mBACE,CAAC,MAAK;AACJ,wBAAe;AACf,gBAAQ,CAAC;MACX,GACA,CAAC,MAAK;AACJ,wBAAe;AACf,eAAO,CAAC;MACV,CAAC;IAEL,SAAS,KAAK;AACZ,aAAO,GAAG;IACZ;AACA,IAAAA,iBAAW,QAAXA,iBAAW,SAAA,SAAXA,aAAa,iBAAiB,SAAS,OAAO;EAChD,CAAC;AACH;;;ACpDA,IAAM,uBAAuB;AAavB,SAAU,MAAM,UAAkB,SAAsB;AAC5D,MAAI;AACJ,QAAM,EAAE,aAAAE,cAAa,cAAa,IAAK,YAAO,QAAP,YAAO,SAAP,UAAW,CAAA;AAClD,SAAO,uBACL,CAAC,YAAW;AACV,YAAQ,WAAW,SAAS,QAAQ;EACtC,GACA;IACE,oBAAoB,MAAM,aAAa,KAAK;IAC5C,aAAAA;IACA,eAAe,kBAAa,QAAb,kBAAa,SAAb,gBAAiB;GACjC;AAEL;;;AC4EA,IAAM,YAAY,IAAI,IAAI,wBAAwB;;;ACrG5C,SAAU,eAAY;AAC1B,MAAI,OAAO;AACX,WAAS,IAAI,GAAG,IAAI,IAAI,KAAK;AAE3B,UAAM,eAAe,KAAK,MAAM,KAAK,OAAM,IAAK,EAAE;AAElD,QAAI,MAAM,IAAI;AACZ,cAAQ;IACV,WAAW,MAAM,IAAI;AAEnB,cAAS,eAAe,IAAO;IACjC,OAAO;AAEL,cAAQ,aAAa,SAAS,EAAE;IAClC;AAEA,QAAI,MAAM,KAAK,MAAM,MAAM,MAAM,MAAM,MAAM,IAAI;AAC/C,cAAQ;IACV;EACF;AACA,SAAO;AACT;;;;ACfA,IAAM,eACJ,SAAO,KAAA,eAAU,QAAV,eAAU,SAAA,SAAV,WAAY,YAAM,QAAA,OAAA,SAAA,SAAA,GAAE,gBAAe,aACtC,WAAW,OAAO,WAAW,KAAK,WAAW,MAAM,IACnD;;;;;;;ACuBC,IAAM,YAAY,OAAO,WAAW,eAAe,OAAO,OAAO,aAAa;AAK9E,IAAM,cACX,OAAO,SAAS,YAChB,QAAO,SAAI,QAAJ,SAAI,SAAA,SAAJ,KAAM,mBAAkB,iBAC9BC,MAAA,KAAK,iBAAW,QAAAA,QAAA,SAAA,SAAAA,IAAE,UAAS,kCAC1B,KAAA,KAAK,iBAAW,QAAA,OAAA,SAAA,SAAA,GAAE,UAAS,gCAC3B,KAAA,KAAK,iBAAW,QAAA,OAAA,SAAA,SAAA,GAAE,UAAS;AAKxB,IAAM,SACX,OAAO,SAAS,eAChB,OAAO,KAAK,YAAY,eACxB,OAAO,KAAK,QAAQ,SAAS;AAKxB,IAAM,QAAQ,OAAO,QAAQ,eAAe,OAAO,IAAI,YAAY;AAKnE,IAAM,aACX,OAAO,WAAW,YAAY,eAC9B,QAAQ,WAAW,QAAQ,OAAO,KAClC,SAAQ,KAAA,WAAW,QAAQ,cAAQ,QAAA,OAAA,SAAA,SAAA,GAAE,IAAI;AAiBpC,IAAM,gBACX,OAAO,cAAc,gBAAe,cAAS,QAAT,cAAS,SAAA,SAAT,UAAW,aAAY;;;ACpF7D,oBAAyB;;;ACGnB,IAAO,mBAAP,cAAgC,MAAK;;;;;;EAkBzC,YAAY,SAAiB,SAAgC;AAC3D,UAAM,OAAO;AACb,SAAK,OAAO;AACZ,SAAK,QAAQ,QAAQ;AACrB,SAAK,cAAc,QAAQ;EAC7B;;;;AC5BI,SAAU,OAAO,MAAW;AAChC,MAAI,KAAK,SAAS,GAAG;AACnB,UAAM,WAAW,OAAO,KAAK,CAAC,CAAC;AAC/B,QAAI,SAAS,SAAS,QAAQ,GAAG;AAC/B,cAAQ,MAAM,GAAG,IAAI;IACvB,WAAW,SAAS,SAAS,UAAU,GAAG;AACxC,cAAQ,KAAK,GAAG,IAAI;IACtB,WAAW,SAAS,SAAS,OAAO,GAAG;AACrC,cAAQ,KAAK,GAAG,IAAI;IACtB,WAAW,SAAS,SAAS,UAAU,GAAG;AACxC,cAAQ,MAAM,GAAG,IAAI;IACvB,OAAO;AACL,cAAQ,MAAM,GAAG,IAAI;IACvB;EACF;AACF;;;ACiDA,IAAM,mBACH,OAAO,YAAY,eAAe,QAAQ,OAAO,QAAQ,IAAI,SAAU;AAE1E,IAAI;AACJ,IAAI,oBAA8B,CAAA;AAClC,IAAI,oBAA8B,CAAA;AAClC,IAAM,YAAwB,CAAA;AAE9B,IAAI,kBAAkB;AACpB,SAAO,gBAAgB;AACzB;AAEA,IAAM,WAAkB,OAAO,OAC7B,CAAC,cAA+B;AAC9B,SAAO,eAAe,SAAS;AACjC,GACA;EACE;EACA;EACA;EACA;CACD;AAGH,SAAS,OAAO,YAAkB;AAChC,kBAAgB;AAChB,sBAAoB,CAAA;AACpB,sBAAoB,CAAA;AACpB,QAAM,WAAW;AACjB,QAAM,gBAAgB,WAAW,MAAM,GAAG,EAAE,IAAI,CAAC,OAAO,GAAG,KAAI,EAAG,QAAQ,UAAU,KAAK,CAAC;AAC1F,aAAW,MAAM,eAAe;AAC9B,QAAI,GAAG,WAAW,GAAG,GAAG;AACtB,wBAAkB,KAAK,IAAI,OAAO,IAAI,GAAG,OAAO,CAAC,CAAC,GAAG,CAAC;IACxD,OAAO;AACL,wBAAkB,KAAK,IAAI,OAAO,IAAI,EAAE,GAAG,CAAC;IAC9C;EACF;AACA,aAAW,YAAY,WAAW;AAChC,aAAS,UAAU,QAAQ,SAAS,SAAS;EAC/C;AACF;AAEA,SAAS,QAAQ,WAAiB;AAChC,MAAI,UAAU,SAAS,GAAG,GAAG;AAC3B,WAAO;EACT;AAEA,aAAW,WAAW,mBAAmB;AACvC,QAAI,QAAQ,KAAK,SAAS,GAAG;AAC3B,aAAO;IACT;EACF;AACA,aAAW,oBAAoB,mBAAmB;AAChD,QAAI,iBAAiB,KAAK,SAAS,GAAG;AACpC,aAAO;IACT;EACF;AACA,SAAO;AACT;AAEA,SAAS,UAAO;AACd,QAAM,SAAS,iBAAiB;AAChC,SAAO,EAAE;AACT,SAAO;AACT;AAEA,SAAS,eAAe,WAAiB;AACvC,QAAM,cAAwB,OAAO,OAAO,OAAO;IACjD,SAAS,QAAQ,SAAS;IAC1B;IACA,KAAK,SAAS;IACd;IACA;GACD;AAED,WAAS,SAAS,MAAW;AAC3B,QAAI,CAAC,YAAY,SAAS;AACxB;IACF;AACA,QAAI,KAAK,SAAS,GAAG;AACnB,WAAK,CAAC,IAAI,GAAG,SAAS,IAAI,KAAK,CAAC,CAAC;IACnC;AACA,gBAAY,IAAI,GAAG,IAAI;EACzB;AAEA,YAAU,KAAK,WAAW;AAE1B,SAAO;AACT;AAEA,SAAS,UAAO;AACd,QAAM,QAAQ,UAAU,QAAQ,IAAI;AACpC,MAAI,SAAS,GAAG;AACd,cAAU,OAAO,OAAO,CAAC;AACzB,WAAO;EACT;AACA,SAAO;AACT;AAEA,SAAS,OAAuB,WAAiB;AAC/C,QAAM,cAAc,eAAe,GAAG,KAAK,SAAS,IAAI,SAAS,EAAE;AACnE,cAAY,MAAM,KAAK;AACvB,SAAO;AACT;AAEA,IAAA,gBAAe;;;ACtKf,IAAM,oBAAoB,oBAAI,IAAG;AACjC,IAAM,kBACH,OAAO,YAAY,eAAe,QAAQ,OAAO,QAAQ,IAAI,mBAAoB;AAEpF,IAAI;AAOG,IAAM,cAAiC,cAAM,OAAO;AAC3D,YAAY,MAAM,IAAI,SAAQ;AAC5B,gBAAM,IAAI,GAAG,IAAI;AACnB;AAWA,IAAM,mBAAmB,CAAC,WAAW,QAAQ,WAAW,OAAO;AAS/D,IAAI,iBAAiB;AAEnB,MAAI,gBAAgB,eAAe,GAAG;AACpC,gBAAY,eAAe;EAC7B,OAAO;AACL,YAAQ,MACN,6CAA6C,eAAe,iDAAiD,iBAAiB,KAC5H,IAAI,CACL,GAAG;EAER;AACF;AAWM,SAAU,YAAY,OAAqB;AAC/C,MAAI,SAAS,CAAC,gBAAgB,KAAK,GAAG;AACpC,UAAM,IAAI,MACR,sBAAsB,KAAK,yBAAyB,iBAAiB,KAAK,GAAG,CAAC,EAAE;EAEpF;AACA,kBAAgB;AAEhB,QAAMC,qBAAoB,CAAA;AAC1B,aAAWC,WAAU,mBAAmB;AACtC,QAAI,aAAaA,OAAM,GAAG;AACxB,MAAAD,mBAAkB,KAAKC,QAAO,SAAS;IACzC;EACF;AAEA,gBAAM,OAAOD,mBAAkB,KAAK,GAAG,CAAC;AAC1C;AASA,IAAM,WAAW;EACf,SAAS;EACT,MAAM;EACN,SAAS;EACT,OAAO;;AAoCH,SAAU,mBAAmB,WAAiB;AAClD,QAAM,mBAAsC,YAAY,OAAO,SAAS;AACxE,iBAAe,aAAa,gBAAgB;AAC5C,SAAO;IACL,OAAO,aAAa,kBAAkB,OAAO;IAC7C,SAAS,aAAa,kBAAkB,SAAS;IACjD,MAAM,aAAa,kBAAkB,MAAM;IAC3C,SAAS,aAAa,kBAAkB,SAAS;;AAErD;AAEA,SAAS,eAAe,QAA2B,OAAwC;AACzF,QAAM,MAAM,IAAI,SAAQ;AACtB,WAAO,IAAI,GAAG,IAAI;EACpB;AACF;AAEA,SAAS,aAAa,QAA2B,OAAoB;AACnE,QAAME,UAAwB,OAAO,OAAO,OAAO,OAAO,KAAK,GAAG;IAChE;GACD;AAED,iBAAe,QAAQA,OAAM;AAE7B,MAAI,aAAaA,OAAM,GAAG;AACxB,UAAMC,qBAAoB,cAAM,QAAO;AACvC,kBAAM,OAAOA,qBAAoB,MAAMD,QAAO,SAAS;EACzD;AAEA,oBAAkB,IAAIA,OAAM;AAE5B,SAAOA;AACT;AAEA,SAAS,aAAaA,SAAqB;AACzC,SAAO,QAAQ,iBAAiB,SAASA,QAAO,KAAK,KAAK,SAAS,aAAa,CAAC;AACnF;AAEA,SAAS,gBAAgB,UAAgB;AACvC,SAAO,iBAAiB,SAAS,QAAe;AAClD;;;AC/JO,IAAM,SAAS,mBAAmB,mBAAmB;;;ACK5D,oBAAuB;AAEjB,SAAU,cAAc,OAAa;AAEzC,MAAI,OAAO,UAAU,UAAU;AAC7B,UAAM,IAAI,MAAM,yDAAyD;EAC3E;AAEA,MAAI,CAAC,OAAO;AACV,UAAM,IAAI,MAAM,UAAU;EAC5B;AAEA,QAAM,gBAAgB,KAAK,MAAM,KAAK;AACtC,QAAM,eAAe;AACrB,MAAI;AAEJ,MAAI,aAAa,SAAS,UAAU;AAClC,QAAI,aAAa,UAAU,aAAa;AACtC,sBAAgB,OAAA,OAAA,OAAA,OAAA,CAAA,GAAK,aAAa,GAAA,EAAE,MAAM,YAAW,CAAA;IACvD,WAAW,aAAa,UAAU,gBAAgB;AAChD,sBAAgB,OAAA,OAAA,OAAA,OAAA,CAAA,GAAK,aAAa,GAAA,EAAE,MAAM,eAAc,CAAA;IAC1D,OAAO;AAEL,aAAO;IACT;EACF,WAAW,aAAa,SAAS,WAAW;AAC1C,QAAI,aAAa,SAAS,SAAS;AACjC,YAAM,OAAO,aAAa,cAAc,MAAM,cAAc,QAA6B;AACzF,UAAI,SAAS,MAAM;AACjB,eAAO;MACT;AACA,sBAAgB,OAAA,OAAA,OAAA,OAAA,CAAA,GAAK,aAAa,GAAA,EAAE,MAAY,MAAM,YAAW,CAAA;IACnE,WAAW,aAAa,SAAS,UAAU;AACzC,YAAM,OAAO,aAAa,cAAc,MAAM,cAAc,QAA6B;AACzF,UAAI,SAAS,MAAM;AACjB,eAAO;MACT;AACA,sBAAgB,OAAA,OAAA,OAAA,OAAA,CAAA,GACX,aAAa,GAAA,EAChB,MACA,MAAM,aAAY,CAAA;IAEtB,OAAO;AAEL,aAAO;IACT;EACF,WAAW,aAAa,SAAS,OAAO;AACtC,oBAAgB,OAAA,OAAA,OAAA,OAAA,CAAA,GAAK,aAAa,GAAA,EAAE,MAAM,MAAK,CAAA;EACjD,OAAO;AAEL,WAAO;EACT;AACA,SAAO;AACT;AAEM,SAAU,aAAa,SAAyB;AACpD,MAAI;AACJ,UAAQ,QAAQ,MAAM;IACpB,KAAK,aAAa;AAChB,aAAO,EAAE,MAAM,aAAa,OAAO,QAAQ,OAAO,OAAO,QAAQ,MAAK;AACtE;IACF;IACA,KAAK,cAAc;AACjB,aAAO,EAAE,MAAM,cAAc,OAAO,QAAQ,OAAO,OAAO,QAAQ,MAAK;AACvE;IACF;IACA,KAAK,aAAa;AAChB,aAAO;QACL,MAAM;QACN,OAAO,QAAQ;QACf,OAAO,QAAQ;QACf,UAAU,QAAQ;QAClB,MAAM,WAAW,QAAQ,MAAM,QAAQ,QAAQ;;AAEjD;IACF;IACA,KAAK,eAAe;AAClB,aAAO;QACL,MAAM;QACN,OAAO,QAAQ;QACf,OAAO,QAAQ;QACf,UAAU,QAAQ;QAClB,MAAM,WAAW,QAAQ,MAAM,QAAQ,QAAQ;QAC/C,QAAQ,QAAQ;;AAElB;IACF;IACA,KAAK,eAAe;AAClB,aAAO,EAAE,MAAM,eAAe,YAAY,QAAQ,WAAU;AAC5D;IACF;IACA,SAAS;AACP,YAAM,IAAI,MAAM,qBAAqB,QAAQ,IAAI,EAAE;IACrD;EACF;AAEA,SAAO,KAAK,UAAU,IAAI;AAC5B;AAoCA,SAAS,WAAW,MAA+B,UAA2B;AAC5E,UAAQ,UAAU;IAChB,KAAK,QAAQ;AACX,UAAI,OAAO,SAAS,UAAU;AAC5B,cAAM,IAAI,UAAU,2BAA2B;MACjD;AACA,aAAO;IACT;IACA,KAAK,QAAQ;AACX,aAAO;IACT;IACA,KAAK;IACL,KAAK,YAAY;AACf,UAAI,gBAAgB,aAAa;AAC/B,eAAO,qBAAO,KAAK,IAAI,EAAE,SAAS,QAAQ;MAC5C;AACA,YAAM,IAAI,UAAU,+BAA+B;IACrD;EACF;AACF;AAEA,SAAS,aAAa,MAAW,UAAgB;AAC/C,MAAI,aAAa,QAAQ;AACvB,QAAI,OAAO,SAAS,UAAU;AAC5B,YAAM,IAAI,UAAU,gDAAgD;IACtE;AACA,WAAO;EACT,WAAW,aAAa,QAAQ;AAC9B,WAAO;EACT,WAAW,aAAa,YAAY,aAAa,YAAY;AAC3D,UAAM,MAAM,qBAAO,KAAK,MAAgB,QAAQ;AAChD,WAAO,IAAI,OAAO,MAAM,IAAI,YAAY,IAAI,aAAa,IAAI,UAAU;EACzE,OAAO;AAEL,WAAO;EACT;AACF;;;AC5KM,IAAO,4BAAP,MAAgC;EAAtC,cAAA;AAIkB,SAAA,wBAAwB;AAKxB,SAAA,OAAO;EAiBzB;;;;;EAXS,cAAc,OAAa;AAChC,WAAY,cAAc,KAAK;EACjC;;;;;EAMO,aAAa,SAAyB;AAC3C,WAAY,aAAa,OAAO;EAClC;;;;ACzBI,IAAO,oCAAP,MAAwC;EAA9C,cAAA;AAIkB,SAAA,wBAAwB;AAKxB,SAAA,OAAO;EAiBzB;;;;;EAXS,cAAc,OAAa;AAChC,WAAY,cAAc,KAAK;EACjC;;;;;EAMO,aAAa,SAAyB;AAC3C,WAAY,aAAa,OAAO;EAClC;;;;ACEK,IAAM,wBAAwB,MAA8B;AACjE,SAAO,IAAI,0BAAyB;AACtC;AAKO,IAAM,gCAAgC,MAA8B;AACzE,SAAO,IAAI,kCAAiC;AAC9C;;;ACxCM,IAAO,kBAAP,MAAsB;EAG1B,YAAmB,KAAa,cAAoB;AAClD,SAAK,UAAU,IAAI,UAAU,KAAK,YAAY;AAC9C,SAAK,QAAQ,aAAa;EAC5B;EAEA,OAAO,IAAc;AACnB,SAAK,QAAQ,SAAS;EACxB;EAEA,QAAQ,IAA0C;AAChD,SAAK,QAAQ,UAAU,CAAC,OAAmB,GAAG,GAAG,MAAM,GAAG,MAAM;EAClE;EAEA,QAAQ,IAAwB;AAC9B,SAAK,QAAQ,UAAU,CAAC,OAAc,GAAG,EAAE;EAC7C;EAEA,UAAU,IAA4D;AACpE,SAAK,QAAQ,YAAY,CAAC,UAAwB,GAAG,MAAM,IAAI;EACjE;EAEA,MAAM,MAAe,QAAe;AAClC,SAAK,QAAQ,MAAM,MAAM,MAAM;EACjC;;EAGA,KAAK,MAAW,GAAmB;AACjC,WAAO,IAAI,QAAQ,CAAC,SAAS,WAAU;AACrC,UAAI;AACF,aAAK,QAAQ,KAAK,IAAI;AACtB,gBAAO;MACT,SAAS,KAAK;AACZ,eAAO,GAAG;MACZ;IACF,CAAC;EACH;EAEA,SAAM;AACJ,WAAO,KAAK,QAAQ,eAAe,UAAU;EAC/C;;AAGI,IAAO,yBAAP,MAA6B;EAC1B,OAAO,KAAa,cAAoB;AAC7C,WAAO,IAAI,gBAAgB,KAAK,YAAY;EAC9C;;;;ACjDF,eAAsB,iBACpB,SACA,QAAuB;AAEvB,MAAI,OAAO,SAAS;AAClB,UAAM,IAAI,WAAW,4BAA4B;EACnD;AAEA,MAAI;AAEJ,QAAM,IAAI,IAAI,QAAW,CAAC,GAAG,WAAU;AACrC,cAAU,MAAW;AACnB,aAAO,IAAI,WAAW,4BAA4B,CAAC;IACrD;AAEA,WAAO,iBAAiB,SAAS,OAAO;EAC1C,CAAC;AAED,MAAI;AACF,WAAO,MAAM,QAAQ,KAAK,CAAC,SAAS,CAAC,CAAC;EACxC;AACE,WAAO,oBAAoB,SAAS,OAAQ;EAC9C;AACF;;;AXiBA,IAAK;CAAL,SAAKE,uBAAoB;AACvB,EAAAA,sBAAA,SAAA,IAAA;AACA,EAAAA,sBAAA,cAAA,IAAA;AACA,EAAAA,sBAAA,YAAA,IAAA;AACA,EAAAA,sBAAA,WAAA,IAAA;AACA,EAAAA,sBAAA,YAAA,IAAA;AACF,GANK,yBAAA,uBAAoB,CAAA,EAAA;AAgBnB,IAAO,kBAAP,MAAsB;EA4BlB,YAAS;AACf,SAAK,SAAS,KAAK,SAAS;AAC5B,WAAO,KAAK;EACd;EAcA,YAAY,YAAgD,SAAgC;AApC3E,SAAA,wBAAwB;AACxB,SAAA,qBAAqB;AAErB,SAAA,WAAyB,IAAI,cAAAC,QAAY;AAElD,SAAA,cAAuB;AAWvB,SAAA,sBAAsB;AAqB5B,QAAI,OAAO,eAAe,UAAU;AAClC,WAAK,cAAc,EAAE,oBAAoB,WAAU;IACrD,OAAO;AACL,WAAK,cAAc;IACrB;AAEA,QAAI,WAAW,MAAM;AACnB,gBAAU,CAAA;IACZ;AACA,SAAK,qBAAqB,OAAO;AACjC,SAAK,WAAW;AAEhB,SAAK,sBAAsB,IAAI,YAAY,KAAK,SAAS,mBAAoB;AAC7E,SAAK,wBAAwB,IAAI,YAAY,KAAK,SAAS,qBAAsB;AAEjF,SAAK,YAAY,KAAK,SAAS;AAC/B,SAAK,YAAY,oBAAI,IAAG;AACxB,SAAK,UAAU,oBAAI,IAAG;AACtB,SAAK,cAAc,IAAI,WAAU;AAEjC,SAAK,SAAS,qBAAqB;AACnC,SAAK,SAAS;EAChB;;;;;EAMO,MAAM,MAAM,SAAsB;AACvC,QAAI,KAAK,aAAa;AACpB,YAAM,IAAI,MAAM,sCAAsC;IACxD;AAEA,QAAI,KAAK,WAAW,qBAAqB,SAAS;AAChD,YAAM,IAAI,MAAM,8CAA8C;IAChE;AAEA,QAAIC;AACJ,QAAI,SAAS;AACX,MAAAA,eAAc,QAAQ;IACxB;AAEA,QAAI,CAAC,KAAK,sBAAsB;AAC9B,WAAK,uBAAuB,KAAK,wBAAuB;IAC1D;AAEA,QAAI;AACF,YAAM,KAAK,WAAWA,YAAW;IACnC,SAAS,KAAK;AAEZ,WAAK,aAAa,qBAAqB,OAAO;AAC9C,UAAI,KAAK,sBAAsB;AAC7B,aAAK,qBAAqB,MAAK;AAC/B,aAAK,uBAAuB;MAC9B;AACA,WAAK,cAAc;AACnB,YAAM;IACR;EACF;EAEQ,MAAM,qBAAqBA,cAA6B;AAC9D,QAAI,KAAK,WAAW,qBAAqB,cAAc;AACrD,YAAM,IAAI,MAAM,qDAAqD;IACvE;AAEA,QAAI;AACF,aAAO,QAAQ,uBAAuB;AACtC,YAAM,KAAK,WAAWA,YAAW;IACnC,SAAS,KAAK;AACZ,WAAK,aAAa,qBAAqB,YAAY;AACnD,YAAM;IACR;EACF;EAEQ,MAAM,WAAWA,cAA6B;AACpD,SAAK,aAAa,qBAAqB,UAAU;AAEjD,WAAO,KAAK,0BAA0B;AAEtC,SAAK,YAAY,MAAK;AACtB,SAAK,sBAAsB;AAC3B,SAAK,kBAAkB;AACvB,SAAK,2BAA2B;AAChC,SAAK,gBAAgB;AACrB,SAAK,qBAAqB;AAC1B,SAAK,OAAO;AAEZ,QAAI,OAAO,KAAK,YAAY,uBAAuB,UAAU;AAC3D,WAAK,OAAO,KAAK,YAAY;IAC/B,OAAO;AACL,WAAK,OAAO,MAAM,KAAK,YAAY,mBAAmB;QACpD,aAAaA;OACe;IAChC;AAEA,QAAI,OAAO,KAAK,SAAS,UAAU;AACjC,YAAM,IAAI,MACR,2DAA2D,OAAO,KAAK,IAAI,EAAE;IAEjF;AACA,UAAM,KAAK,aAAa,KAAK,IAAI;EACnC;;;;EAKO,OAAI;AACT,QAAI,KAAK,WAAW,qBAAqB,WAAW,KAAK,aAAa;AACpE;IACF;AAGA,SAAK,cAAc;AACnB,QAAI,KAAK,aAAa,KAAK,UAAU,OAAM,GAAI;AAC7C,WAAK,UAAU,MAAK;IACtB,OAAO;AACL,WAAK,cAAc;IACrB;AACA,QAAI,KAAK,sBAAsB;AAC7B,WAAK,qBAAqB,MAAK;AAC/B,WAAK,uBAAuB;IAC9B;EACF;EAsCO,GACL,OAOA,UAA0B;AAE1B,SAAK,SAAS,GAAG,OAAO,QAAQ;EAClC;EAsCO,IACL,OAOA,UAA0B;AAE1B,SAAK,SAAS,eAAe,OAAO,QAAQ;EAC9C;EAQQ,WACN,OAOA,MAAS;AAET,SAAK,SAAS,KAAK,OAAO,IAAI;EAChC;;;;;;;;;EAUO,MAAM,UACX,WACA,SACA,UACA,SAA0B;AAE1B,WAAO,MAAM,KAAK,2BAChB,MAAM,KAAK,kBAAkB,WAAW,SAAS,UAAU,OAAO,GAClE,YAAO,QAAP,YAAO,SAAA,SAAP,QAAS,WAAW;EAExB;EAEQ,MAAM,kBACZ,WACA,SACA,UACA,SAA0B;;AAE1B,UAAM,iBAAgBC,MAAA,YAAO,QAAP,YAAO,SAAA,SAAP,QAAS,mBAAa,QAAAA,QAAA,SAAAA,MAAI;AAChD,QAAI,CAAC,eAAe;AAClB,aAAO,MAAM,KAAK,sBAChB,CAAC,OAAM;AACL,eAAO;UACL,MAAM;UACN;UACA,MAAM;UACN,OAAO;UACP,OAAO;;MAEX,GACA,YAAO,QAAP,YAAO,SAAA,SAAP,QAAS,OACT,YAAO,QAAP,YAAO,SAAA,SAAP,QAAS,WAAW;IAExB;AAEA,UAAM,UAAU;MACd,MAAM;MACN;MACA,MAAM;MACN,OAAO;;AAGT,UAAM,KAAK,aAAa,SAAS,YAAO,QAAP,YAAO,SAAA,SAAP,QAAS,WAAW;AACrD,WAAO,EAAE,cAAc,MAAK;EAC9B;;;;;;EAOO,MAAM,UAAU,WAAmB,SAA0B;AAClE,WAAO,MAAM,KAAK,2BAChB,MAAM,KAAK,kBAAkB,WAAW,OAAO,GAC/C,YAAO,QAAP,YAAO,SAAA,SAAP,QAAS,WAAW;EAExB;EAEQ,MAAM,kBACZ,WACA,SAA0B;AAE1B,UAAM,QAAQ,KAAK,eAAe,SAAS;AAC3C,UAAM,SAAS,MAAM,KAAK,eAAe,WAAW,OAAO;AAC3D,UAAM,WAAW;AACjB,WAAO;EACT;EAEQ,MAAM,eACZ,WACA,SAA0B;AAE1B,WAAO,MAAM,KAAK,sBAChB,CAAC,OAAM;AACL,aAAO;QACL,OAAO;QACP,OAAO;QACP,MAAM;;IAEV,GACA,YAAO,QAAP,YAAO,SAAA,SAAP,QAAS,OACT,YAAO,QAAP,YAAO,SAAA,SAAP,QAAS,WAAW;EAExB;;;;;;;EAQO,MAAM,WACX,WACA,SAA2B;AAE3B,WAAO,MAAM,KAAK,2BAChB,MAAM,KAAK,mBAAmB,WAAW,OAAO,GAChD,YAAO,QAAP,YAAO,SAAA,SAAP,QAAS,WAAW;EAExB;EAEQ,MAAM,mBACZ,WACA,SAA2B;AAE3B,UAAM,QAAQ,KAAK,eAAe,SAAS;AAC3C,UAAM,SAAS,MAAM,KAAK,sBACxB,CAAC,OAAM;AACL,aAAO;QACL,OAAO;QACP,OAAO;QACP,MAAM;;IAEV,GACA,YAAO,QAAP,YAAO,SAAA,SAAP,QAAS,OACT,YAAO,QAAP,YAAO,SAAA,SAAP,QAAS,WAAW;AAEtB,UAAM,WAAW;AACjB,WAAO;EACT;;;;;;;;;EAUO,MAAM,YACX,WACA,SACA,UACA,SAA4B;AAE5B,WAAO,MAAM,KAAK,2BAChB,MAAM,KAAK,oBAAoB,WAAW,SAAS,UAAU,OAAO,GACpE,YAAO,QAAP,YAAO,SAAA,SAAP,QAAS,WAAW;EAExB;EAEQ,MAAM,oBACZ,WACA,SACA,UACA,SAA4B;;AAE5B,UAAM,iBAAgBA,MAAA,YAAO,QAAP,YAAO,SAAA,SAAP,QAAS,mBAAa,QAAAA,QAAA,SAAAA,MAAI;AAChD,UAAM,UAASC,MAAA,YAAO,QAAP,YAAO,SAAA,SAAP,QAAS,YAAM,QAAAA,QAAA,SAAAA,MAAI;AAClC,QAAI,CAAC,eAAe;AAClB,aAAO,MAAM,KAAK,sBAChB,CAAC,OAAM;AACL,eAAO;UACL,MAAM;UACN,OAAO;UACP;UACA,MAAM;UACN,OAAO;UACP;;MAEJ,GACA,YAAO,QAAP,YAAO,SAAA,SAAP,QAAS,OACT,YAAO,QAAP,YAAO,SAAA,SAAP,QAAS,WAAW;IAExB;AAEA,UAAM,UAAU;MACd,MAAM;MACN,OAAO;MACP;MACA,MAAM;MACN;;AAGF,UAAM,KAAK,aAAa,SAAS,YAAO,QAAP,YAAO,SAAA,SAAP,QAAS,WAAW;AACrD,WAAO,EAAE,cAAc,MAAK;EAC9B;EAEQ,6BAA0B;AAChC,WAAO,IAAI,uBAAsB;EACnC;EAEQ,MAAM,sBAAmB;AAC/B,QAAI,CAAC,KAAK,UAAU,uBAAuB;AACzC;IACF;AACA,UAAM,CAAC,WAAW,KAAK,IAAI,KAAK,YAAY,iBAAgB;AAC5D,QAAI,aAAa,OAAO;AACtB,YAAM,UAA8B;QAClC,MAAM;QACN,YAAY;;AAEd,UAAI;AACF,cAAM,KAAK,aAAa,OAAO;MACjC,SAAED,KAAM;AACN,aAAK,YAAY,UAAU,KAAM;MACnC;IACF;EACF;EAEQ,aAAa,KAAW;AAC9B,QAAI,KAAK,aAAa;AACpB,YAAM,IAAI,MAAM,sCAAsC;IACxD;AAEA,WAAO,IAAI,QAAc,CAAC,SAAS,WAAU;AAE3C,YAAM,SAAU,KAAK,YAAY,KAAK,2BAA0B,EAAG,OACjE,KACA,KAAK,UAAU,IAAI;AAErB,aAAO,OAAO,MAAK;AAEjB,YAAI,KAAK,aAAa;AACpB,cAAI;AACF,mBAAO,MAAK;UACd,SAAEA,KAAM;UAAC;AAET,iBAAO,IAAI,MAAM,uBAAuB,CAAC;QAC3C;AACA,eAAO,QAAQ,iCAAiC;AAChD,aAAK,aAAa,qBAAqB,SAAS;AAChD,YAAI,KAAK,UAAU,uBAAuB;AACxC,cAAI,KAAK,oBAAoB,MAAM;AACjC,iBAAK,iBAAiB,MAAK;UAC7B;AACA,eAAK,mBAAmB,IAAI,cAAc,YAAW;AACnD,kBAAM,KAAK,oBAAmB;UAChC,GAAG,GAAI;QACT;AAEA,gBAAO;MACT,CAAC;AAED,aAAO,QAAQ,CAAC,MAAK;AACnB,YAAI,KAAK,oBAAoB,MAAM;AACjC,eAAK,iBAAiB,MAAK;QAC7B;AACA,eAAO,CAAC;MACV,CAAC;AAED,aAAO,QAAQ,CAAC,MAAc,WAAkB;AAC9C,YAAI,KAAK,WAAW,qBAAqB,WAAW;AAClD,iBAAO,QAAQ,6BAA6B;AAC5C,cAAI,KAAK,oBAAoB,MAAM;AACjC,iBAAK,iBAAiB,MAAK;UAC7B;AACA,iBAAO,KAAK,sCAAsC,IAAI,aAAa,MAAM,EAAE;AAC3E,eAAK,kBAAkB,EAAE,MAAY,OAAc;AACnD,eAAK,uBAAuB,KAAK,IAAI;QACvC,OAAO;AACL,iBAAO,QAAQ,8BAA8B;AAC7C,iBAAO,IAAI,MAAM,8BAA8B,IAAI,EAAE,CAAC;QACxD;MACF,CAAC;AAED,aAAO,UAAU,CAAC,SAAa;AAC7B,cAAM,mBAAmB,CAAC,YAA6B;AACrD,cAAI,KAAK,QAAQ,IAAI,QAAQ,KAAK,GAAG;AACnC,kBAAM,SAAS,KAAK,QAAQ,IAAI,QAAQ,KAAK;AAC7C,iBAAK,QAAQ,OAAO,QAAQ,KAAK;AACjC,kBAAM,eACJ,QAAQ,SAAS,QAAQ,QAAQ,MAAM,SAAS;AAClD,gBAAI,QAAQ,WAAW,cAAc;AACnC,qBAAO,QAAQ;gBACb,OAAO,QAAQ;gBACf;eACkB;YACtB,OAAO;AACL,qBAAO,OACL,IAAI,iBAAiB,2BAA2B;gBAC9C,OAAO,QAAQ;gBACf,aAAa,QAAQ;eACK,CAAC;YAEjC;UACF;QACF;AAEA,cAAM,yBAAyB,OAAO,YAA4C;AAChF,eAAK,gBAAgB,QAAQ;AAC7B,eAAK,qBAAqB,QAAQ;AAElC,cAAI,CAAC,KAAK,qBAAqB;AAC7B,iBAAK,sBAAsB;AAE3B,gBAAI,KAAK,SAAS,kBAAkB;AAClC,oBAAM,gBAAiC,CAAA;AACvC,mBAAK,UAAU,QAAQ,CAAC,MAAK;AAC3B,oBAAI,EAAE,UAAU;AACd,gCAAc,MACX,YAAW;AACV,wBAAI;AACF,4BAAM,KAAK,eAAe,EAAE,IAAI;oBAClC,SAAS,KAAK;AACZ,2BAAK,2BAA2B,EAAE,MAAM,GAAG;oBAC7C;kBACF,GAAE,CAAE;gBAER;cACF,CAAC;AAED,kBAAI;AACF,sBAAM,QAAQ,IAAI,aAAa;cACjC,SAAEA,KAAM;cAAC;YACX;AAEA,iBAAK,mBAAmB,QAAQ,cAAc,QAAQ,MAAM;UAC9D;QACF;AAEA,cAAM,4BAA4B,CAAC,YAAsC;AACvE,eAAK,2BAA2B;QAClC;AAEA,cAAM,yBAAyB,CAAC,YAAmC;AACjE,cAAI,QAAQ,cAAc,MAAM;AAC9B,kBAAM,OAAO,KAAK,YAAY,UAAU,QAAQ,UAAU;AAC1D,gBAAI,SAAS,GAAG;AAEd;YACF;AAGA,gBAAI,OAAO,KAAK,uBAAuB;AACrC,mBAAK,oBAAmB;YAC1B;UACF;AAEA,eAAK,sBAAsB,OAAO;QACpC;AAEA,cAAM,0BAA0B,CAAC,YAAoC;AACnE,cAAI,QAAQ,cAAc,MAAM;AAC9B,kBAAM,OAAO,KAAK,YAAY,UAAU,QAAQ,UAAU;AAC1D,gBAAI,SAAS,GAAG;AAEd;YACF;AAGA,gBAAI,OAAO,KAAK,uBAAuB;AACrC,mBAAK,oBAAmB;YAC1B;UACF;AAEA,eAAK,uBAAuB,OAAO;QACrC;AAEA,YAAI;AACJ,YAAI;AACF,cAAI;AACJ,cAAI,MAAM,QAAQ,IAAI,GAAG;AACvB,4BAAgB,OAAO,OAAO,IAAI;UACpC,OAAO;AACL,4BAAgB;UAClB;AAEA,qBAAW,KAAK,UAAU,cAAc,aAAa;AACrD,cAAI,aAAa,MAAM;AAErB;UACF;QACF,SAAS,KAAK;AACZ,iBAAO,QAAQ,4DAA4D,GAAG;AAC9E,gBAAM;QACR;AAEA,YAAI,CAAC,MAAM,QAAQ,QAAQ,GAAG;AAC5B,qBAAW,CAAC,QAAQ;QACtB;AAEA,iBAAS,QAAQ,CAAC,YAAW;AAC3B,cAAI;AACF,oBAAQ,QAAQ,MAAM;cACpB,KAAK,OAAO;AACV,iCAAiB,OAAqB;AACtC;cACF;cACA,KAAK,aAAa;AAChB,uCAAuB,OAA2B;AAClD;cACF;cACA,KAAK,gBAAgB;AACnB,0CAA0B,OAA8B;AACxD;cACF;cACA,KAAK,aAAa;AAChB,uCAAuB,OAA2B;AAClD;cACF;cACA,KAAK,cAAc;AACjB,wCAAwB,OAA4B;AACpD;cACF;YACF;UACF,SAAS,KAAK;AACZ,mBAAO,QACL,2DAA2D,QAAQ,IAAI,iBACvE,GAAG;UAEP;QACF,CAAC;MACH,CAAC;IACH,CAAC;EACH;EAEQ,MAAM,sCAAmC;AAC/C,SAAK,SAAS,qBAAqB;AAEnC,SAAK,sBAAsB,KAAK,eAAe,KAAK,wBAAwB;AAG5E,QAAI,KAAK,SAAS,eAAe;AAC/B,YAAM,KAAK,eAAc;IAC3B,OAAO;AACL,YAAM,KAAK,yBAAwB;IACrC;EACF;EAEQ,MAAM,iBAAc;AAC1B,QAAI,YAAY;AAChB,QAAI,UAAU;AACd,QAAI;AACF,aAAO,CAAC,KAAK,aAAa;AACxB,YAAI;AACF,gBAAM,KAAK,qBAAoB;AAC/B,sBAAY;AACZ;QACF,SAAS,KAAK;AACZ,iBAAO,QAAQ,8CAA8C,GAAG;AAEhE;AACA,gBAAM,YAAY,KAAK,sBAAsB,mBAAmB,OAAO;AAEvE,cAAI,aAAa,MAAM;AACrB;UACF;AAEA,cAAI;AACF,mBAAO,QAAQ,oCAAoC,OAAO,KAAK,SAAS,EAAE;AAC1E,kBAAM,MAAM,SAAS;UACvB,SAAEA,KAAM;UAAC;QACX;MACF;IACF;AACE,UAAI,CAAC,WAAW;AACd,aAAK,yBAAwB;MAC/B;IACF;EACF;EAEQ,2BAAwB;AAC9B,SAAK,cAAc;AACnB,SAAK,SAAS,qBAAqB;AACnC,SAAK,iBAAgB;EACvB;EAEQ,0BAAuB;AAC7B,WAAO,IAAI,cAAc,YAAW;AAClC,WAAK,YAAY,UAAU,CAAC;IAC9B,GAAG,KAAK,kBAAkB;EAC5B;EAEQ,MAAM,aACZ,SACAD,cAA6B;AAE7B,QAAI,CAAC,KAAK,aAAa,CAAC,KAAK,UAAU,OAAM,GAAI;AAC/C,YAAM,IAAI,MAAM,kCAAkC;IACpD;AAEA,UAAM,UAAU,KAAK,UAAU,aAAa,OAAO;AACnD,UAAM,KAAK,UAAW,KAAK,SAASA,YAAW;EACjD;EAEQ,MAAM,sBACZ,iBACA,OACAA,cAA6B;AAE7B,QAAI,SAAS,MAAM;AACjB,cAAQ,KAAK,UAAS;IACxB;AAEA,UAAM,UAAU,gBAAgB,KAAK;AACrC,QAAI,CAAC,KAAK,QAAQ,IAAI,KAAK,GAAG;AAC5B,WAAK,QAAQ,IAAI,OAAO,IAAI,UAAU,KAAK,CAAC;IAC9C;AACA,UAAM,SAAS,KAAK,QAAQ,IAAI,KAAK;AAErC,QAAI;AACF,YAAM,KAAK,aAAa,SAASA,YAAW;IAC9C,SAAS,OAAO;AACd,WAAK,QAAQ,OAAO,KAAK;AAEzB,UAAI,eAAuB;AAC3B,UAAI,iBAAiB,OAAO;AAC1B,uBAAe,MAAM;MACvB;AACA,YAAM,IAAI,iBAAiB,cAAc,EAAE,MAAY,CAAE;IAC3D;AAEA,QAAIA,cAAa;AACf,UAAI;AACF,eAAO,MAAM,iBAAiB,OAAO,QAAO,GAAIA,YAAW;MAC7D,SAAS,KAAK;AACZ,YAAI,eAAe,SAAS,IAAI,SAAS,cAAc;AACrD,gBAAM,IAAI,iBAAiB,4BAA4B,EAAE,MAAY,CAAE;QACzE;AACA,cAAM;MACR;IACF;AAEA,WAAO,MAAM,OAAO,QAAO;EAC7B;EAEQ,MAAM,yBAAsB;AAElC,SAAK,QAAQ,QAAQ,CAAC,OAAO,QAAO;AAClC,UAAI,KAAK,QAAQ,OAAO,GAAG,GAAG;AAC5B,cAAM,OACJ,IAAI,iBAAiB,kEAAkE;UACrF,OAAO,MAAM;SACa,CAAC;MAEjC;IACF,CAAC;AAED,QAAI,KAAK,aAAa;AACpB,aAAO,QAAQ,8CAA8C;AAC7D,WAAK,oCAAmC;AACxC;IACF;AAEA,QAAI,KAAK,mBAAmB,KAAK,gBAAgB,SAAS,MAAM;AAC9D,aAAO,QAAQ,2DAA2D;AAC1E,WAAK,oCAAmC;AACxC;IACF;AAEA,QAAI,CAAC,KAAK,UAAU,uBAAuB;AACzC,aAAO,QAAQ,0DAA0D;AACzE,WAAK,oCAAmC;AACxC;IACF;AAGA,UAAM,cAAc,KAAK,kBAAiB;AAC1C,QAAI,CAAC,aAAa;AAChB,aAAO,QAAQ,sDAAsD;AACrE,WAAK,oCAAmC;AACxC;IACF;AAGA,QAAI,YAAY;AAChB,SAAK,SAAS,qBAAqB;AACnC,UAAMA,eAAcG,iBAAgB,QAAQ,KAAK,GAAI;AACrD,QAAI;AACF,aAAO,CAACH,aAAY,WAAW,KAAK,aAAa;AAC/C,YAAI;AACF,gBAAM,KAAK,aAAa,KAAK,MAAM,WAAW;AAC9C,sBAAY;AACZ;QACF,SAAEC,KAAM;AACN,gBAAM,MAAM,GAAI;QAClB;MACF;IACF;AACE,UAAI,CAAC,WAAW;AACd,eAAO,QAAQ,yEAAyE;AACxF,aAAK,oCAAmC;MAC1C;IACF;EACF;EAEQ,mBAAmB,cAAsB,QAAc;AAC7D,SAAK,WAAW,aAAa;MAC3B;MACA;KACkB;EACtB;EAEQ,sBACN,cACA,yBAAwD;AAExD,SAAK,WAAW,gBAAgB;MAC9B;MACA,SAAS;KACY;EACzB;EAEQ,sBAAsB,SAAyB;AACrD,SAAK,WAAW,iBAAiB;MAC/B;KACyB;EAC7B;EAEQ,uBAAuB,SAA0B;AACvD,SAAK,WAAW,kBAAkB;MAChC;KAC0B;EAC9B;EAEQ,mBAAgB;AACtB,SAAK,WAAW,WAAW,CAAA,CAAE;EAC/B;EAEQ,2BAA2B,WAAmB,KAAY;AAChE,SAAK,WAAW,uBAAuB;MACrC,OAAO;MACP,OAAO;KACmB;EAC9B;EAEQ,qBAAqB,eAAqC;AAChE,QAAI,cAAc,iBAAiB,MAAM;AACvC,oBAAc,gBAAgB;IAChC;AAEA,QAAI,cAAc,oBAAoB,MAAM;AAC1C,oBAAc,mBAAmB;IACnC;AAEA,QAAI,cAAc,YAAY,MAAM;AAClC,oBAAc,WAAW,8BAA6B;IACxD;AAEA,SAAK,0BAA0B,aAAa;AAC5C,SAAK,4BAA4B,aAAa;AAE9C,WAAO;EACT;EAEQ,0BAA0B,eAAqC;AACrE,QAAI,CAAC,cAAc,qBAAqB;AACtC,oBAAc,sBAAsB,CAAA;IACtC;AAEA,QACE,cAAc,oBAAoB,cAAc,QAChD,cAAc,oBAAoB,aAAa,GAC/C;AACA,oBAAc,oBAAoB,aAAa;IACjD;AAEA,QACE,cAAc,oBAAoB,kBAAkB,QACpD,cAAc,oBAAoB,iBAAiB,GACnD;AACA,oBAAc,oBAAoB,iBAAiB;IACrD;AAEA,QACE,cAAc,oBAAoB,qBAAqB,QACvD,cAAc,oBAAoB,oBAAoB,GACtD;AACA,oBAAc,oBAAoB,oBAAoB;IACxD;AAEA,QAAI,cAAc,oBAAoB,QAAQ,MAAM;AAClD,oBAAc,oBAAoB,OAAO;IAC3C;EACF;EAEQ,4BAA4B,eAAqC;AACvE,QAAI,CAAC,cAAc,uBAAuB;AACxC,oBAAc,wBAAwB,CAAA;IACxC;AAEA,QACE,cAAc,sBAAsB,cAAc,QAClD,cAAc,sBAAsB,aAAa,GACjD;AACA,oBAAc,sBAAsB,aAAa,OAAO;IAC1D;AAEA,QACE,cAAc,sBAAsB,kBAAkB,QACtD,cAAc,sBAAsB,iBAAiB,GACrD;AACA,oBAAc,sBAAsB,iBAAiB;IACvD;AAEA,QACE,cAAc,sBAAsB,qBAAqB,QACzD,cAAc,sBAAsB,oBAAoB,GACxD;AACA,oBAAc,sBAAsB,oBAAoB;IAC1D;AAEA,QAAI,cAAc,sBAAsB,QAAQ,MAAM;AACpD,oBAAc,sBAAsB,OAAO;IAC7C;EACF;EAEQ,oBAAiB;AACvB,QAAI,KAAK,iBAAiB,KAAK,sBAAsB,KAAK,MAAM;AAC9D,YAAM,MAAM,IAAI,IAAI,KAAK,IAAI;AAC7B,UAAI,aAAa,OAAO,sBAAsB,KAAK,aAAa;AAChE,UAAI,aAAa,OAAO,2BAA2B,KAAK,kBAAkB;AAC1E,aAAO,IAAI,SAAQ;IACrB;AACA,WAAO;EACT;EAEQ,eAAe,MAAY;AACjC,QAAI,CAAC,KAAK,UAAU,IAAI,IAAI,GAAG;AAC7B,WAAK,UAAU,IAAI,MAAM,IAAI,eAAe,IAAI,CAAC;IACnD;AACA,WAAO,KAAK,UAAU,IAAI,IAAI;EAChC;EAEQ,aAAa,UAA8B;AACjD,WAAO,QACL,kCAAkC,KAAK,OAAO,SAAQ,CAAE,OAAO,SAAS,SAAQ,CAAE,EAAE;AAEtF,SAAK,SAAS;EAChB;EAEQ,MAAM,2BACZ,OACA,QAAwB;AAExB,QAAI,eAAe;AAEnB,WAAO,MAAM;AACX,UAAI;AACF,eAAO,MAAM,MAAM,KAAK,IAAI;MAC9B,SAAS,KAAK;AACZ;AACA,cAAM,YAAY,KAAK,oBAAoB,mBAAmB,YAAY;AAC1E,YAAI,aAAa,MAAM;AACrB,gBAAM;QACR;AAEA,cAAM,MAAM,SAAS;AAErB,YAAI,WAAM,QAAN,WAAM,SAAA,SAAN,OAAQ,SAAS;AACnB,gBAAM;QACR;MACF;IACF;EACF;;AAGF,IAAM,cAAN,MAAiB;EAIf,YAAmB,cAAmC;AACpD,SAAK,gBAAgB;AACrB,SAAK,2BAA2B,KAAK,KACnC,KAAK,KAAK,KAAK,cAAc,iBAAkB,IAC7C,KAAK,KAAK,KAAK,cAAc,cAAe,IAC5C,CAAC;EAEP;EAEO,mBAAmB,cAAoB;AAC5C,QAAI,eAAe,KAAK,cAAc,YAAa;AACjD,aAAO;IACT,OAAO;AACL,UAAI,KAAK,cAAc,SAAU,SAAS;AACxC,eAAO,KAAK,cAAc;MAC5B,OAAO;AACL,eAAO,KAAK,2BAA2B,YAAY;MACrD;IACF;EACF;EAEQ,2BAA2B,SAAe;AAChD,QAAI,WAAW,KAAK,0BAA0B;AAC5C,aAAO,KAAK,cAAc;IAC5B,OAAO;AACL,cAAQ,KAAM,UAAU,KAAM,KAAK,cAAc;IACnD;EACF;;AAGF,IAAM,iBAAN,MAAoB;EAIlB,YAAY,MAAY;AAFjB,SAAA,WAAW;AAGhB,SAAK,OAAO;EACd;;AAGF,IAAM,YAAN,MAAe;EAKb,YAAY,OAAa;AACvB,SAAK,WAAW,IAAI,QAAyB,CAAC,SAAS,WAAU;AAC/D,WAAK,WAAW;AAChB,WAAK,UAAU;IACjB,CAAC;AACD,SAAK,QAAQ;EACf;EAIO,UAAO;AACZ,WAAO,KAAK;EACd;EAEO,QAAQ,OAAqD;AAClE,SAAK,SAAU,KAAK;EACtB;EAEO,OAAO,QAAY;AACxB,SAAK,QAAS,MAAM;EACtB;;AAGF,IAAM,aAAN,MAAgB;EAId,cAAA;AACE,SAAK,cAAc;AACnB,SAAK,YAAY;EACnB;EAEO,UAAU,YAAkB;AACjC,SAAK,YAAY;AACjB,QAAI,aAAa,KAAK,aAAa;AACjC,YAAM,OAAO,aAAa,KAAK;AAC/B,WAAK,cAAc;AACnB,aAAO;IACT;AACA,WAAO;EACT;EAEO,mBAAgB;AACrB,QAAI,KAAK,WAAW;AAClB,WAAK,YAAY;AACjB,aAAO,CAAC,MAAM,KAAK,WAAW;IAChC;AAEA,WAAO,CAAC,OAAO,IAAI;EACrB;EAEO,QAAK;AACV,SAAK,cAAc;AACnB,SAAK,YAAY;EACnB;;AAGF,IAAM,gBAAN,MAAmB;EAMjB,YAAY,MAAoC,UAAkB,KAAS;AACzE,SAAK,QAAQ;AACb,SAAK,mBAAmB,IAAIE,iBAAe;AAC3C,SAAK,YAAY;AACjB,SAAK,OAAO;AACZ,SAAK,OAAM;EACb;EAEO,QAAK;AACV,QAAI;AACF,WAAK,iBAAiB,MAAK;IAC7B,SAAEF,KAAM;IAAC;EACX;EAEQ,MAAM,SAAM;AAClB,UAAM,SAAS,KAAK,iBAAiB;AACrC,WAAO,CAAC,OAAO,SAAS;AACtB,UAAI;AACF,cAAM,KAAK,MAAM,KAAK,IAAI;MAC5B,SAAEA,KAAM;MACR;AACE,cAAM,MAAM,KAAK,SAAS;MAC5B;IACF;EACF;;", "names": ["ReflectApply", "ReflectOwnKeys", "NumberIsNaN", "EventEmitter", "once", "len", "i", "len2", "<PERSON><PERSON><PERSON>", "i", "byteLength", "AbortController", "AbortError", "abortSignal", "AbortError", "abortSignal", "_a", "enabledNamespaces", "logger", "logger", "enabledNamespaces", "WebPubSubClientState", "EventEmitter", "abortSignal", "_a", "_b", "AbortController"]}