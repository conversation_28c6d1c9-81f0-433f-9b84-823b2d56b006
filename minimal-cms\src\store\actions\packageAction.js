import { createAsyncThunk } from "@reduxjs/toolkit";

import { savePackage, getSavedPackages } from "../../services";

export const savePackageAction = createAsyncThunk("settings/savePackage", async params => {
  const response = await savePackage(params);
  return response.data;
});

export const getSavedPackagesAction = createAsyncThunk("settings/getSavedPackages", async params => {
  const response = await getSavedPackages(params);
  return response.data.data;
});