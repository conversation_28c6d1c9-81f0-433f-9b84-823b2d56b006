export type INumberParam = number | undefined | null

export class NumberFormatter {
  constructor(public locale: string = 'en-gb'){}

  private _volumeFormatter: Intl.NumberFormat | undefined;
  get volumeFormatter() {
    if(!this._volumeFormatter) {
      this._volumeFormatter = new Intl.NumberFormat(this.locale, { notation: 'compact', compactDisplay: 'short', maximumFractionDigits: 2})
    }

    return this._volumeFormatter
  }

  private _decimalFormatter: Intl.NumberFormat | undefined;
  get decimalFormatter() {
    if(!this._decimalFormatter) {
      this._decimalFormatter = new Intl.NumberFormat(this.locale)
    }

    return this._decimalFormatter
  }

  private _percentFormatter: Intl.NumberFormat | undefined;
  get percentFormatter() {
    if(!this._percentFormatter) {
      this._percentFormatter = new Intl.NumberFormat(this.locale, { 
        style: "percent",
        minimumFractionDigits: 2, // Ensure 2 decimal places
        maximumFractionDigits: 2, // Restrict to 2 decimal places
      })
    }

    return this._percentFormatter
  }

  volume(num: INumberParam) {
    if(num == null) return ''
    if(Number.isNaN(num)) return "NaN"
    return this.volumeFormatter.format(num)
  }

  decimal(num: INumberParam) {
    if(num == null) return ''
    if(Number.isNaN(num)) return "NaN"
    return this.decimalFormatter.format(num)
  }

  percent(num: INumberParam) {
    if(num == null) return ''
    if(Number.isNaN(num)) return "NaN"
    return this.percentFormatter.format(num)
  }
}

export const NumberFormatterFactory = {
  _cache: new Map<string, NumberFormatter>(),

  formatter(locale: string) {
    if(this._cache.has(locale)) this._cache.get(locale) as NumberFormatter;

    const instance = new NumberFormatter(locale);
    this._cache.set(locale, instance)
    return instance;
  }
}


