@use "../common/mixins" as *;

.select-settings {
    margin-left: auto;
    

    &__pc,
    &__mobile {
        display: flex;
        align-items: center;
        gap: 8px;
    }

    &__mobile {
        display: none;
    }

    @include respond-to(sm) {
        width: 100%;
        margin-left: unset;

        &__pc {
            display: none;
        }
        &__mobile {
            display: flex;
            max-width: 100%;
            width: 100%;
            overflow-x: scroll;
            white-space: nowrap;
            -webkit-overflow-scrolling: touch; /* Enable smooth scrolling on iOS */
            
            /* Hide scrollbar for Chrome, Safari and Opera */
            &::-webkit-scrollbar {
                display: none;
            }
            
            /* Hide scrollbar for IE, Edge and Firefox */
            -ms-overflow-style: none;  /* IE and Edge */
            scrollbar-width: none;  /* Firefox */
        }
    }
}

.mobile-settings-buttons {
    display: flex;
    align-items: center;
    gap: 8px;
    width: 100%;
    
    > * {
        flex: 0 0 auto; /* Prevent items from shrinking */
    }
}

.mobile-button {
    display: flex;
    align-items: center;
    gap: 4px;
}

.setting-drawer {
    &__section {
        margin-bottom: 20px;
        
        &:last-child {
            margin-bottom: 0;
        }
    }
    
    &__section-title {
        font-size: 16px;
        font-weight: 500;
        margin-bottom: 10px;
        padding-bottom: 8px;
        border-bottom: 1px solid #eee;
    }
}

.mobile-drawer {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 9999;
    visibility: hidden;
    isolation: isolate;

    &__overlay {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: rgba(0, 0, 0, 0.5);
        opacity: 0;
        transition: opacity 0.3s ease-in-out;
    }

    &__content {
        position: relative;
        background-color: white;
        border-radius: 16px 16px 0 0;
        padding: 16px;
        transform: translateY(100%);
        transition: transform 0.3s ease-in-out;
        max-height: 80vh;
        overflow-y: auto;
        box-shadow: 0 -4px 10px rgba(0, 0, 0, 0.1);
    }

    &__header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 16px;
        padding-bottom: 8px;
        border-bottom: 1px solid #eee;

        h3 {
            margin: 0;
            font-size: 18px;
            font-weight: 500;
        }
    }

    &__close {
        background: none;
        border: none;
        font-size: 24px;
        cursor: pointer;
        width: 32px;
        height: 32px;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 0;
    }

    &__options {
        list-style: none;
        padding: 0;
        margin: 0;
    }

    &__option {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 12px 8px;
        border-bottom: 1px solid #eee;
        cursor: pointer;
        transition: background-color 0.2s;

        &:last-child {
            border-bottom: none;
        }

        &:hover {
            background-color: #f5f5f5;
        }

        &--selected {
            font-weight: 500;
        }
    }

    &__option-label {
        flex: 1;
    }

    &__option-checkbox {
        width: 22px;
        height: 22px;
        border: 2px solid #ccc;
        border-radius: 4px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    &__checkmark {
        color: #0066cc;
        font-weight: bold;
    }

    &--open {
        visibility: visible;

        .mobile-drawer__overlay {
            opacity: 1;
        }

        .mobile-drawer__content {
            transform: translateY(0);
        }
    }
}