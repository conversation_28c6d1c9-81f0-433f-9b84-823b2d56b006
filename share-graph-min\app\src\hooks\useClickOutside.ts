import { useEffect, useRef } from "react";

/**
 * Custom hook to detect clicks outside a specified element.
 *
 * @param onClickOutside - Callback function to execute when a click occurs outside the element.
 * @returns A ref to attach to the element you want to monitor.
 */
function useClickOutside<T extends HTMLElement>(onClickOutside: () => void) {
    const ref = useRef<T | null>(null);

    useEffect(() => {
        function handleClickOutside(event: MouseEvent) {
            if (ref.current && !ref.current.contains(event.target as Node)) {
                onClickOutside();
            }
        }

        document.addEventListener("mousedown", handleClickOutside);

        return () => {
            document.removeEventListener("mousedown", handleClickOutside);
        };
    }, [onClickOutside]);

    return ref;
}

export default useClickOutside;