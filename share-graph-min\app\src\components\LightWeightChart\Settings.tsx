import { useEffect } from 'react';
import { useAppStore } from '../../stores/useAppStore';
import { useChartContext } from './context';
import { getChartRangeData } from '../../utils/common';
import {
  CHART_SETTING_KEYS,
  CHART_TYPE_KEYS,
} from '../../constants/chartConstant';
import { IndicatorFactory } from "@sharegraph-mini/advance-charts";
import { PriceScaleMode } from 'lightweight-charts';

const Settings = () => {
  const general = useAppStore((state) => state.appSettings.general);
  const { getChart,  getDataFeed } = useChartContext();
  const chartRange = useAppStore((state) => state.chartRange);

  const indicators = useAppStore((state) => state.indicators);
  const indicatorsOverlays = useAppStore((state) => state.overlays);
  const market = useAppStore(state => state.marketInfo?.marketManger)

  const chartType = useAppStore((state) => state.chartType);
  const chartSettings = useAppStore((state) => state.chartSettings);
  const volumeSettings = chartSettings.volume;
  const chartPreferences = chartSettings['chart-preferences'];
  const priceScaleMode = chartSettings['y-axis-preferences'];
  const lastPriceLine = chartSettings['show-last-close-line'];

  
  useEffect(() => {
    if(!market) return;
    const dataFeed = getDataFeed()
    if (!dataFeed) return;
    const { from, to, interval } = getChartRangeData(chartRange, market);

    dataFeed.setRange({ from, to, interval });
  }, [getDataFeed, chartRange, market]);

  useEffect(() => {
    const chart = getChart()
    if (!chart) return;
    switch (chartType) {
      case CHART_TYPE_KEYS.LINE.key:
        return chart.setChartType('line');
      case CHART_TYPE_KEYS.CANDLESTICK.key:
        return chart.setChartType('candle');
      case CHART_TYPE_KEYS.MOUNTAIN.key:
        return chart.setChartType('mountain');
      case CHART_TYPE_KEYS.BASELINE.key:
        return chart.setChartType('baseline');
      case CHART_TYPE_KEYS.BASE_MOUNTAIN.key:
        return chart.setChartType('base-mountain');
      case CHART_TYPE_KEYS.BAR_OHLC.key:
        return chart.setChartType('bar');
      default:
        return chart.setChartType('line');
    }
  }, [chartType, getChart]);

  useEffect(() => {
    const chart = getChart()
    if (!chart) return;
    const allIndicators = indicators.concat(indicatorsOverlays);

    for (const indicator of chart.listIndicators()) {
      if (allIndicators.includes(indicator)) continue;
      chart.removeIndicator(indicator);
    }

    for (const indicator of allIndicators) {
      if (chart.hasIndicator(indicator)) continue;
      if (!IndicatorFactory.indicatorRegistered(indicator)) {
        console.warn(`Indicator ${indicator} is not registered`);
        continue;
      }
      chart.addIndicator(indicator);
    }
  }, [indicators, indicatorsOverlays, getChart]);

  useEffect(() => {
    const chart = getChart()
    if (!chart) return;
    const isShow = volumeSettings.includes(
      CHART_SETTING_KEYS.VOLUME.SHOW_HIDE.key
    );
    const volumeType = volumeSettings.includes(
      CHART_SETTING_KEYS.VOLUME.UNDERLAY.key
    )
      ? 'volume_overlay'
      : 'volume';

    const isSingleColor = !volumeSettings.includes(
      CHART_SETTING_KEYS.VOLUME.COLOR_VOLUME_BAR.key
    );
    chart.hiddenVolume();
    
    if (isShow) {
      chart.showVolume(
        volumeType,
        isSingleColor
          ? { upColor: general.primaryColor, downColor: general.primaryColor }
          : { upColor: general.upColor, downColor: general.downColor }
      );
    }
  }, [volumeSettings, getChart, general.primaryColor, general.upColor, general.downColor]);

  useEffect(() => {
    const chart = getChart()
    if (!chart) return;
    if (
      chartPreferences.includes(
        CHART_SETTING_KEYS.CHART_PREFERENCES.HIGH_LOW_VALUE.key
      )
    ) {
      chart.applyOptions({ highLowLineVisible: true });
    } else {
      chart.applyOptions({ highLowLineVisible: false });
    }

    switch (priceScaleMode[0]) {
      case CHART_SETTING_KEYS.Y_AXIS_PREFERENCES.PERCENTAGE_VIEW.key:
        chart.applyOptions({ priceScaleMode: PriceScaleMode.Percentage });
        break;
      case CHART_SETTING_KEYS.Y_AXIS_PREFERENCES.LINEAR.key:
        chart.applyOptions({ priceScaleMode: PriceScaleMode.Normal });
        break;
      case CHART_SETTING_KEYS.Y_AXIS_PREFERENCES.LOG_SCALE.key:
        chart.applyOptions({ priceScaleMode: PriceScaleMode.Logarithmic });
        break;
      default:
        chart.applyOptions({ priceScaleMode: PriceScaleMode.Normal });
        break;
    }

    if (lastPriceLine.includes(CHART_SETTING_KEYS.SHOW_LAST_CLOSE_LINE.key)) {
      chart.applyOptions({ priceLineVisible: true });
    } else {
      chart.applyOptions({ priceLineVisible: false });
    }
  }, [chartPreferences, priceScaleMode, lastPriceLine, getChart]);

  return null;
};

export default Settings;
