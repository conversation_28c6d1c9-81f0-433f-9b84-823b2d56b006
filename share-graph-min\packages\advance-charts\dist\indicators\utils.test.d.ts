import { IChartApi } from 'lightweight-charts';
import { ChartIndicator, ChartIndicatorOptions } from './abstract-indicator';

export interface IChartIndicatorConstructor<IOptions extends ChartIndicatorOptions, IIndicatorData extends readonly number[], T extends ChartIndicator<IOptions, IIndicatorData>> {
    new (chart: IChartApi, options?: Partial<IOptions>, paneIndex?: number): T;
}
/**
 * @example
 * ```ts
 * const dataSample = [
 *  { time: '2023-01-01', open: 100, high: 105, low: 98, close: 103, volume: 1000 },
 *  { time: '2023-01-02', open: 103, high: 108, low: 101, close: 106, volume: 1200 },
 * ]
 *
 * const explectSmaCalculation = [101.5, 104.5]
 *
 * const indicator = getIndicatorInstance(SMAIndicator, { period: 14 });
 * indicator.setData(dataSample)
 *
 * // type result will look like Array<{time, value: number[]//list of values}>
 * const result = indicator.getData()
 * expect(result.map(r => r.value[0])).toEqual(dataSample);
 * ```
 *
 * @param IndicatorClass
 * @param options
 * @returns
 */
export declare function getIndicatorInstance<IOptions extends ChartIndicatorOptions, IIndicatorData extends readonly number[], T extends ChartIndicator<IOptions, IIndicatorData>>(IndicatorClass: IChartIndicatorConstructor<IOptions, IIndicatorData, T>, options?: Partial<IOptions>): T;
