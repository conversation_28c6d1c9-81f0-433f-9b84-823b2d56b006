import { OHLCVSimple } from '../interface';

export interface ISymbol extends Omit<OHLCVSimple, 'time'> {
  time: number
  isNew: boolean;
}

export type ISymbolData = Omit<ISymbol, 'isNew'>;

export interface IIndicatorBar<D extends readonly number[]> {
  time: number, 
  value?: D
}

export interface IVar {
  /**
   * Gets the historical value at the specified depth.
   * 
   * Depth represents how many periods back to look:
   * - depth=0: current/most recent value
   * - depth=1: previous value
   * - depth=N: value from N periods ago
   * 
   * @param depth - Number of periods to look back (0-based)
   * @returns The value at requested depth or NaN if insufficient history
   */
  get(depth: number): number;
  
  /**
   * Sets the current value (depth=0) and shifts historical values.
   * 
   * When set, the value becomes the new current value (depth=0),
   * and all previous values shift deeper (depth increases by 1).
   * 
   * @param num - The new current value to set
   */
  set(num: number): void;

  /**
   * Gets all historical values in depth order (newest first).
   * 
   * The array represents the complete history buffer where:
   * - index 0: current value (depth=0)
   * - index 1: previous value (depth=1)
   * - index N: value from N periods ago (depth=N)
   * 
   * @returns Array of historical values sorted by depth (shallow to deep)
   */
  getAll(): number[];

  /**
   * Checks if sufficient historical depth is available for calculations.
   * 
   * A variable is calculable when it has collected enough historical
   * values to satisfy its minimum depth requirement.
   * 
   * @returns True if minimum required depth is available
   */
  calculable(): boolean;
  
  /**
   * Prepares the variable for a new data point.
   * 
   * Called when processing new market data. Resets internal state
   * and updates the variable based on the new symbol information.
   * 
   * @param d - The new symbol data point
   */
  prepare(d: ISymbol): void;

  valueOf(): number;
}

export interface IContext {
  new_var(value: number, depth: number): IVar;
  prepare(d: ISymbol): void;
  symbol: ISymbol;
}

export class Context implements IContext {
  _varIndex = 0;
  _vars: IVar[] = [];
  symbol: ISymbol = {
    time: NaN,
    close: NaN,
    high: NaN,
    low: NaN,
    open: NaN,
    volume: NaN,
    isNew: true,
  };
  new_var(value: number, depth: number): IVar {
    if (this._varIndex >= this._vars.length) {
      this._vars.push(new Var(depth));
    }

    const instance = this._vars[this._varIndex++];
    instance.set(value);
    return instance;
  }

  prepare(d: ISymbol): void {
    this._varIndex = 0;
    this.symbol = d;
    this._vars.forEach((item) => item.prepare(d));
  }
}

export class Var implements IVar {
  _his: Array<number> | null = null;
  _hisPosition = 0;
  _minDepth = 0;
  origin: number = NaN;
  modified = false;

  constructor(depth: number) {
    this._his = Array(depth).fill(NaN)
    this._hisPosition = depth - 1;
    this._minDepth = depth
  }

  public valueOf(): number {
    return this.get(0);
  }

  public get(depth: number): number {
    if (this._his) {
      const index = this._hisPosition - depth;
      return this._his[index];
    }
    this._minDepth = Math.max(this._minDepth, depth);
    return NaN;
  }

  public getAll() {
    return Array.from(this._his || []).reverse()
  }

  public calculable() {
    if(!this._his) return false;
    return !this._his.some(item => isNaN(item))
  }

  public set(num: number): void {
    if (!this._his) return;
    this._his[this._hisPosition] = num;
    this.modified = true;
  }

  public prepare(d: ISymbol): void {
    if (d.isNew) {
      this.origin = this.get(0);
      if (this.modified || !this._his) {
        this.addHist();
      }
    } else {
      this.set(this.origin);
    }
  }

  private addHist() {
    if (!this._his) {
      if (!this._minDepth) throw new Error('error');
      this._his = Array(this._minDepth + 1).fill(NaN);
    }

    this._hisPosition = Math.min(this._hisPosition + 1, this._his.length);
    if (this._hisPosition === this._his.length) {
      this._hisPosition = this._his.length - 1;
      this._his.shift();
      this._his.push(NaN);
    }

    this._his[this._hisPosition] = this.origin;
  }
}

/**
 * Execution context for calculating technical indicators.
 * 
 * Key points:
 * - Must process data points in chronological order (time ascending)
 * - Maintains calculation context between points
 * - Supports two operations:
 *   1. Full recalculation (recalc) - processes entire dataset
 *   2. Update (update) - processes only the last/new point
 * 
 * Important: 
 * - Cannot update random points - will break calculation context
 * - Only supports appending new points or updating the last point
 */
export class ExecutionContext<T extends readonly number[]> {
  data: IIndicatorBar<T>[] = [];
  _context = new Context();
  _isCalc = false;
  constructor(protected formula: (c: Context) => IIndicatorBar<T>['value'] | undefined) {
    this.init();
  }

  private init() {
    // Initialize context and formula variables by calling formula once with empty data
    // This ensures all Var instances are properly set up before calculations begin
    this._context = new Context();
    this.formula(this._context);
    this.data = [];
  }

  private calcLasPoint(symbol: ISymbolData) {
    const lastCalc = this.data[this.data.length - 1]
    const isNew = lastCalc?.time !== symbol.time;
    this._context.prepare({ ...symbol, isNew });
    const result = {time: symbol.time, value: this.formula(this._context)};

    if(isNew) {
      this.data.push(result)
    } else {
      this.data[this.data.length - 1] = result
    }
  }

  recalc(data: ISymbolData[]) {
    this.init();
    for (const item of data) {
      // treat any new item as last point
      this.calcLasPoint(item);
    }
    this._isCalc = true;
  }

  update(data: ISymbolData) {
    this.calcLasPoint(data);
  }
}
