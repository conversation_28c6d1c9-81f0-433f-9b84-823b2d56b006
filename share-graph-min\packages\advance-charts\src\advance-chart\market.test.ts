import { describe, it, expect, beforeEach } from "vitest";
import { Market } from "./market";

describe("Market", () => {
  let market: Market;

  // Helper to create test market (reuse pattern)
  const createMarket = (overrides = {}) => new Market({
    name: "Test Market",
    timeZone: "America/New_York", 
    open: "09:00",
    close: "16:30",
    ...overrides
  });

  // Helper for consistent test dates
  const testDates = {
    duringHours: new Date("2025-01-06T14:00:00Z"), // 09:00 AM NY
    afterHours: new Date("2025-01-06T01:00:00Z"),  // 08:00 PM prev day NY
    baseDate: new Date("2025-01-06")
  };

  beforeEach(() => {
    market = createMarket();
  });

  describe("isOpen", () => {
    it("should return true during market hours", () => {
      expect(market.isOpen(testDates.duringHours)).toBe(true);
    });

    it("should return false outside market hours", () => {
      expect(market.isOpen(testDates.afterHours)).toBe(false);
    });
  });

  describe("market time operations", () => {
    it("should return current time in market timezone", () => {
      const marketTime = market.marketZoneNow();
      
      // Test behavior: returns valid dayjs object that can be used for time operations
      expect(marketTime.isValid()).toBe(true);
      expect(typeof marketTime.format).toBe('function');
    });

    it("should get market open and close times for a date", () => {
      const openTime = market.getOpen(testDates.baseDate);
      const closeTime = market.getClose(testDates.baseDate);
      
      expect(openTime.format("HH:mm")).toBe("09:00");
      expect(closeTime.format("HH:mm")).toBe("16:30");
    });
  });

  describe("next/previous time calculations", () => {
    it("should find next open time", () => {
      const nextOpen = market.getNextOpen(testDates.afterHours);
      
      // Test core behavior: next open is after the given time
      expect(nextOpen.isAfter(testDates.afterHours)).toBe(true);
      expect(nextOpen.format("HH:mm")).toBe("09:00");
    });

    it("should find next close time", () => {
      const nextClose = market.getNextClose(testDates.duringHours);
      
      // Test core behavior: next close is after the given time  
      expect(nextClose.isAfter(testDates.duringHours)).toBe(true);
      expect(nextClose.format("HH:mm")).toBe("16:30");
    });

    it("should find previous open time", () => {
      const prevOpen = market.getPrevOpen(testDates.afterHours);
      
      // Test core behavior: previous open is before the given time
      expect(prevOpen.isBefore(testDates.afterHours)).toBe(true);
      expect(prevOpen.format("HH:mm")).toBe("09:00");
    });

    it("should find previous close time", () => {
      const prevClose = market.getPrevClose(testDates.duringHours);
      
      // Test core behavior: previous close is before the given time
      expect(prevClose.isBefore(testDates.duringHours)).toBe(true);
      expect(prevClose.format("HH:mm")).toBe("16:30");
    });
  });
});
