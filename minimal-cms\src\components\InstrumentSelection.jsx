import { useCallback, useEffect, useMemo, useState } from "react";
import { Input, Modal, Select, Table } from "antd";
import fuzzysort from "fuzzysort";
import { useDispatch, useSelector } from "react-redux";
import { useInputStyle } from "../hooks/useInputStyle";
import { getInstrumentByCompanyCodeAction } from "../store/actions";
import { instrumentByCompanyCodeSelector } from "../store/selector/settingSelector";
import { Loading3QuartersOutlined } from "@ant-design/icons";
const columns = [
  {
    title: "Name",
    dataIndex: "name",
  },
  {
    title: "Symbol",
    dataIndex: "symbol",
  },
  {
    title: "Real time Data",
    dataIndex: "allowRealTime",
  },

  {
    title: "Market name",
    dataIndex: "marketName",
  },
  {
    title: "ISIN",
    dataIndex: "isin",
  },
];

const ModalInstrumentInfos = ({
  open,
  onOk,
  onCancel,
  value: initValue,
  onChange,
}) => {
  const [{ instruments, loading }] = useInstrumentsSetting();
  const [search, searchSet] = useState("");
  const [value, valueSet] = useState(initValue);
  const filteredInstruments = useMemo(() => {
    if (!instruments || instruments.length === 0) return instruments;
    if (!search || search.length < 2) return instruments;

    return fuzzysort
      .go(
        search,
        instruments.map((item) => ({
          ...item,
          keySearch: `${item.id} ${item.name} ${item.marketName} ${item.isin} ${item.symbol}`,
        })),
        { key: ["keySearch"] }
      )
      .map((item) => item.obj);
  }, [search, instruments]);

  useEffect(() => {
    if (open) {
      valueSet(initValue);
    }
  }, [initValue, open]);

  const dataSource = useMemo(
    () =>
      filteredInstruments.map((item) => ({
        ...item,
        key: item.id,
        allowRealTime: item.allowRealTime ? "Yes" : "No",
      })),
    [filteredInstruments]
  );

  const rowSelection = {
    selectedRowKeys: value,
    onSelect: (record, selected, selectedRows) => {
      valueSet(selectedRows.map((item) => item.id));
    },
    onSelectAll: (selected, selectedRows) => {
      valueSet(selectedRows.map((item) => item.id));
    },
  };
  const handleOk = () => {
    onChange(value);
    onOk();
  };
  return (
    <Modal
      centered
      width={1200}
      title="Instrument information"
      open={open}
      onOk={handleOk}
      onCancel={onCancel}
      loading={loading}
    >
      <div className="flex mb-2">
        <Input
          className="ml-auto"
          placeholder="Search..."
          allowClear
          value={search}
          style={{ maxWidth: 300 }}
          onChange={({ target: { value } }) => searchSet(value)}
        />
      </div>
      <Table
        rowSelection={{ type: "checkbox", ...rowSelection }}
        columns={columns}
        dataSource={dataSource}
      />
    </Modal>
  );
};

const InstrumentSelection = ({ value, id, onChange }) => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const { inputStyle } = useInputStyle();

  const companyCode = useSelector(
    (state) =>
      state.forms.availableTools.draft?.company?.value
  );

  const handleOk = () => setIsModalOpen(false);
  const handleCancel = () => setIsModalOpen(false);

  const [
    { instruments, companyCode: searchedCompanyCode, loading },
    { getInstrumentSetting },
  ] = useInstrumentsSetting();

  const handleDropdownVisibleChange = async (open) => {
    if (open) {
      setIsModalOpen(true);
    }
  };

  useEffect(() => {
    if (!companyCode) return;
    if (searchedCompanyCode !== companyCode)
      getInstrumentSetting({ companyCode });
  }, [companyCode]);

  return (
    <>
      <Select
        mode="multiple"
        style={{
          ...inputStyle,
          width: "310px",
        }}
        disabled={!companyCode}
        showSearch={false}
        placeholder="Select instruments"
        options={instruments.map(({ id, symbol }) => ({
          label: symbol,
          value: id,
        }))}
        dropdownStyle={{ display: "none" }}
        onDropdownVisibleChange={handleDropdownVisibleChange}
        id={id}
        value={value}
        onChange={onChange}
        suffixIcon={loading ? <Loading3QuartersOutlined spin /> : null}
      />
      <ModalInstrumentInfos
        open={isModalOpen}
        onOk={handleOk}
        onCancel={handleCancel}
        onChange={onChange}
        value={value}
      />
    </>
  );
};

// eslint-disable-next-line react-refresh/only-export-components
export const useInstrumentsSetting = () => {
  const { instruments, companyCode, loading, error } = useSelector(
    instrumentByCompanyCodeSelector
  );
  const dispatch = useDispatch();
  const getInstrumentSetting = useCallback(
    (params) => dispatch(getInstrumentByCompanyCodeAction(params)),
    [dispatch]
  );

  return [
    { instruments, companyCode, loading, error },
    { getInstrumentSetting },
  ];
};

export default InstrumentSelection;
