import dayjs from "dayjs";
import { client } from "../graphql/client";
import { DIVIDEND_EVENT_PAGING_QUERY } from "../graphql/queries/dividendQuery";
import { EARNING_EVENTS_PAGING_QUERY } from "../graphql/queries/earningEventQuery";

export const getDividendEvents = async ({
    insId,
    fromDate,
    toDate,
    cursor
}: {
    insId: number;
    fromDate: Date;
    toDate: Date;
    cursor: string;
}) => {
    const result = await client.query(DIVIDEND_EVENT_PAGING_QUERY, {
        id: insId,
        fromDate: dayjs(fromDate).toISOString(),
        toDate: dayjs(toDate).toISOString(),
        cursor
    })
    return result
}

export const getEarningEvents = async ({
    companyCode,
    fromDate,
    toDate,
    cursor
}: {
    companyCode: string;
    fromDate: Date;
    toDate: Date;
    cursor: string;
}) => {

    const result = await client.query(EARNING_EVENTS_PAGING_QUERY, {
        companyCode,
        fromDate: dayjs(fromDate).toISOString(),
        toDate: dayjs(toDate).toISOString(),
        cursor
    })
    return result
}