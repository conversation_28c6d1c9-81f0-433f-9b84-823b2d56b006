import React, { useState } from 'react';
import ChartTypeIcon from '../../icons/chartTypes/ChartTypeIcon';
import Button from '../Button';
import { i18n } from '@euroland/libs';
import ChartTypeDrawer from '../Drawer/ChartTypeDrawer';

const MobileChartType = () => {
  const [isChartTypeDrawerOpen, setIsChartTypeDrawerOpen] = useState(false);

  const toggleChartTypeDrawer = () => {
    setIsChartTypeDrawerOpen(!isChartTypeDrawerOpen);
  };
  return (
    <>
      <Button onClick={toggleChartTypeDrawer}>
        <ChartTypeIcon /> <span>{i18n.translate('chartType')}</span>
      </Button>
      <ChartTypeDrawer
        isOpen={isChartTypeDrawerOpen}
        onClose={toggleChartTypeDrawer}
      />
    </>
  );
};

export default React.memo(MobileChartType);
