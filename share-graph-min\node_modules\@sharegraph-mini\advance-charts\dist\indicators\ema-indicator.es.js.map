{"version": 3, "file": "ema-indicator.es.js", "sources": ["../../src/indicators/ema-indicator.ts"], "sourcesContent": ["import { ISeriesApi, Nominal, SeriesType, SingleValueData, Time, WhitespaceData} from \"lightweight-charts\";\r\nimport { Context, IIndicatorBar } from \"../helpers/execution-indicator\";\r\nimport { SeriesPrimitiveBase } from \"../custom-primitive/primitive-base\";\r\nimport { LineData, LinePrimitivePaneView } from \"../custom-primitive/pane-view/line\";\r\nimport { ChartIndicator, ChartIndicatorOptions } from \"./abstract-indicator\";\r\n\r\nexport interface EMAIndicatorOptions extends ChartIndicatorOptions {\r\n    color: string,\r\n    period: number\r\n}\r\n\r\nexport const defaultOptions: EMAIndicatorOptions = {\r\n    color: \"#d26400\",\r\n    period: 9,\r\n    overlay: true\r\n}\r\n\r\nexport class EMAPrimitive extends SeriesPrimitiveBase<\r\nSingleValueData | WhitespaceData\r\n> {\r\n    linePrimitive: LinePrimitivePaneView;\r\n    constructor(protected source: EMAIndicator) {\r\n        super();\r\n        this.linePrimitive = new LinePrimitivePaneView({\r\n            lineColor: this.source.options.color,\r\n        });\r\n        this._paneViews = [this.linePrimitive];\r\n    }\r\n\r\n    update(indicatorBars: IIndicatorBar<EMAData>[]) {\r\n        const lineData: LineData[] = []\r\n        for(const bar of indicatorBars) {\r\n            const value = bar.value\r\n            if(value) lineData.push({time: bar.time as Time, price: value[0]})\r\n        }\r\n\r\n        this.linePrimitive.update(lineData);\r\n    }\r\n}\r\n\r\nexport type EMAData = readonly [Nominal<number, 'EMA'>]\r\n\r\nexport default class EMAIndicator extends ChartIndicator<EMAIndicatorOptions, EMAData> {\r\n    emaPrimitive = new EMAPrimitive(this)\r\n\r\n    getDefaultOptions(): EMAIndicatorOptions {\r\n        return defaultOptions\r\n    }\r\n\r\n    _mainSeriesChanged(series: ISeriesApi<SeriesType>): void {\r\n        series.attachPrimitive(this.emaPrimitive)\r\n    }\r\n\r\n    remove(): void {\r\n        super.remove();\r\n        this.mainSeries?.detachPrimitive(this.emaPrimitive)\r\n    }\r\n\r\n    applyIndicatorData(): void {\r\n        this.emaPrimitive.update(\r\n            this._executionContext.data\r\n        )\r\n    }\r\n\r\n    formula(c: Context) {\r\n        \r\n        const period = this.options.period\r\n        const close = c.symbol.close\r\n\r\n        const alpha = 2 / (period + 1);\r\n\r\n        const closeSeries = c.new_var(close, period);\r\n        const emaSeries = c.new_var(NaN, 2);\r\n\r\n        if(!closeSeries.calculable()) return;\r\n        const previusEMA = emaSeries.get(1)\r\n\r\n        let ema: number;\r\n\r\n        if(isNaN(previusEMA)) {\r\n            const closes = closeSeries.getAll();\r\n            ema = closes.reduce((sum, val) => sum + val, 0) / period;\r\n            emaSeries.set(ema);\r\n\r\n            return [ema as Nominal<number, 'EMA'>] as EMAData\r\n        } else {\r\n            ema = alpha * close + (1 - alpha) * previusEMA;\r\n        }\r\n\r\n        emaSeries.set(ema);\r\n\r\n        return [ema as Nominal<number, 'EMA'>] as EMAData\r\n    }\r\n\r\n}"], "names": ["defaultOptions", "EMAPrimitive", "SeriesPrimitiveBase", "source", "__publicField", "LinePrimitivePaneView", "indicatorBars", "lineData", "bar", "value", "EMAIndicator", "ChartIndicator", "series", "_a", "c", "period", "close", "alpha", "closeSeries", "emaSeries", "previusEMA", "ema", "sum", "val"], "mappings": ";;;;;;AAWO,MAAMA,IAAsC;AAAA,EAC/C,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,SAAS;AACb;AAEO,MAAMC,UAAqBC,EAEhC;AAAA,EAEE,YAAsBC,GAAsB;AAClC,UAAA;AAFV,IAAAC,EAAA;AACsB,SAAA,SAAAD,GAEb,KAAA,gBAAgB,IAAIE,EAAsB;AAAA,MAC3C,WAAW,KAAK,OAAO,QAAQ;AAAA,IAAA,CAClC,GACI,KAAA,aAAa,CAAC,KAAK,aAAa;AAAA,EAAA;AAAA,EAGzC,OAAOC,GAAyC;AAC5C,UAAMC,IAAuB,CAAC;AAC9B,eAAUC,KAAOF,GAAe;AAC5B,YAAMG,IAAQD,EAAI;AACf,MAAAC,KAAgBF,EAAA,KAAK,EAAC,MAAMC,EAAI,MAAc,OAAOC,EAAM,CAAC,EAAA,CAAE;AAAA,IAAA;AAGhE,SAAA,cAAc,OAAOF,CAAQ;AAAA,EAAA;AAE1C;AAIA,MAAqBG,UAAqBC,EAA6C;AAAA,EAAvF;AAAA;AACI,IAAAP,EAAA,sBAAe,IAAIH,EAAa,IAAI;AAAA;AAAA,EAEpC,oBAAyC;AAC9B,WAAAD;AAAA,EAAA;AAAA,EAGX,mBAAmBY,GAAsC;AAC9C,IAAAA,EAAA,gBAAgB,KAAK,YAAY;AAAA,EAAA;AAAA,EAG5C,SAAe;;AACX,UAAM,OAAO,IACRC,IAAA,KAAA,eAAA,QAAAA,EAAY,gBAAgB,KAAK;AAAA,EAAY;AAAA,EAGtD,qBAA2B;AACvB,SAAK,aAAa;AAAA,MACd,KAAK,kBAAkB;AAAA,IAC3B;AAAA,EAAA;AAAA,EAGJ,QAAQC,GAAY;AAEV,UAAAC,IAAS,KAAK,QAAQ,QACtBC,IAAQF,EAAE,OAAO,OAEjBG,IAAQ,KAAKF,IAAS,IAEtBG,IAAcJ,EAAE,QAAQE,GAAOD,CAAM,GACrCI,IAAYL,EAAE,QAAQ,KAAK,CAAC;AAE/B,QAAA,CAACI,EAAY,aAAc;AACxB,UAAAE,IAAaD,EAAU,IAAI,CAAC;AAE9B,QAAAE;AAED,WAAA,MAAMD,CAAU,KAETC,IADSH,EAAY,OAAO,EACrB,OAAO,CAACI,GAAKC,MAAQD,IAAMC,GAAK,CAAC,IAAIR,GAClDI,EAAU,IAAIE,CAAG,GAEV,CAACA,CAA6B,MAE/BA,IAAAJ,IAAQD,KAAS,IAAIC,KAASG,GAGxCD,EAAU,IAAIE,CAAG,GAEV,CAACA,CAA6B;AAAA,EAAA;AAG7C;"}