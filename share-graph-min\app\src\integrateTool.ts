const documentReady = () => {
  return new Promise((resolve) => {
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', () => {
        resolve(true);
      }); 
    } else {
      resolve(true);
    }
  });
}

export async function integrateTool() {
  await documentReady();
  const euroland = (window as unknown as { euroland?: { integrate: (options: { toolName: string }) => void } }).euroland;
  if (!euroland) return;
  if (!('integrate' in euroland)) return;
  euroland.integrate({
    toolName: import.meta.env.VITE_TOOL_NAME,
  });
}