import type { CodegenConfig } from '@graphql-codegen/cli'
 
const config: CodegenConfig = {
  schema: 'https://dev.vn.euroland.com/tools/apigateway/graphql',
  documents: ['src/graphql/queries/*.ts'],
  ignoreNoDocuments: true,
  generates: {
    './src/_gql/': {
      preset: 'client',
      config: {
        scalars: {
          Byte: 'number',
          Date: 'string',
          DateTime: 'string',
          Decimal: 'number',
          Long: 'number',
          Short: 'number'
        }
      }
    },
  },
}
 
export default config