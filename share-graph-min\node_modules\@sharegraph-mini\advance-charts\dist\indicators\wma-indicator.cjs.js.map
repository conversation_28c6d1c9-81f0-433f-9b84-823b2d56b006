{"version": 3, "file": "wma-indicator.cjs.js", "sources": ["../../src/indicators/wma-indicator.ts"], "sourcesContent": ["import { ISeriesApi, Nominal, SeriesType, SingleValueData, Time, WhitespaceData} from \"lightweight-charts\";\r\nimport { Context, IIndicatorBar } from \"../helpers/execution-indicator\";\r\nimport { SeriesPrimitiveBase } from \"../custom-primitive/primitive-base\";\r\nimport { LineData, LinePrimitivePaneView } from \"../custom-primitive/pane-view/line\";\r\nimport { ChartIndicator, ChartIndicatorOptions } from \"./abstract-indicator\";\r\n\r\nexport interface WMAIndicatorOptions extends ChartIndicatorOptions {\r\n    color: string,\r\n    period: number,\r\n    overlay: boolean\r\n}\r\n\r\nexport const defaultOptions: WMAIndicatorOptions = {\r\n    color: \"#03fc03\",\r\n    period: 9,\r\n    overlay: true\r\n}\r\n\r\nexport class WMAPrimitive extends SeriesPrimitiveBase<\r\nSingleValueData | WhitespaceData\r\n> {\r\n    linePrimitive: LinePrimitivePaneView;\r\n    constructor(protected source: WMAIndicator) {\r\n        super();\r\n        this.linePrimitive = new LinePrimitivePaneView({\r\n            lineColor: this.source.options.color,\r\n        });\r\n        this._paneViews = [this.linePrimitive];\r\n    }\r\n\r\n    update(indicatorBars: IIndicatorBar<WMAData>[]) {\r\n        const lineData: LineData[] = []\r\n        for(const bar of indicatorBars) {\r\n            const value = bar.value\r\n            if(value) lineData.push({time: bar.time as Time, price: value[0]})\r\n        }\r\n\r\n        this.linePrimitive.update(lineData);\r\n    }\r\n}\r\n\r\nexport type WMAData = readonly [Nominal<number, 'WMA'>]\r\n\r\nexport default class WMAIndicator extends ChartIndicator<WMAIndicatorOptions, WMAData> {\r\n    wmaPrimitive = new WMAPrimitive(this)\r\n\r\n    getDefaultOptions(): WMAIndicatorOptions {\r\n        return defaultOptions\r\n    }\r\n\r\n    _mainSeriesChanged(series: ISeriesApi<SeriesType>): void {\r\n        series.attachPrimitive(this.wmaPrimitive)\r\n    }\r\n\r\n    remove(): void {\r\n        super.remove();\r\n        this.mainSeries?.detachPrimitive(this.wmaPrimitive)\r\n    }\r\n\r\n    applyIndicatorData(): void {\r\n        this.wmaPrimitive.update(\r\n            this._executionContext.data\r\n        )\r\n    }\r\n\r\n    formula(c: Context) {\r\n        const prices = c.new_var(c.symbol.close, this.options.period);\r\n        if (!prices.calculable()) return;\r\n      \r\n        const values = prices.getAll();\r\n        const period = this.options.period;\r\n      \r\n        if (values.length < period) return;\r\n      \r\n        const weights = Array.from({ length: period }, (_, i) => period - i);\r\n        const weightSum = weights.reduce((a, b) => a + b, 0);\r\n      \r\n        const recent = values.slice(-period);\r\n        const weightedSum = recent.reduce((sum, val, i) => sum + val * weights[i], 0);\r\n      \r\n        const wma = weightedSum / weightSum;\r\n      \r\n        return [wma as Nominal<number, 'WMA'>] as WMAData;\r\n    }\r\n\r\n}"], "names": ["defaultOptions", "WMAPrimitive", "SeriesPrimitiveBase", "source", "__publicField", "LinePrimitivePaneView", "indicatorBars", "lineData", "bar", "value", "WMAIndicator", "ChartIndicator", "series", "_a", "c", "prices", "values", "period", "weights", "_", "i", "weightSum", "b", "sum", "val"], "mappings": "6aAYaA,EAAsC,CAC/C,MAAO,UACP,OAAQ,EACR,QAAS,EACb,EAEO,MAAMC,UAAqBC,EAAAA,mBAEhC,CAEE,YAAsBC,EAAsB,CAClC,MAAA,EAFVC,EAAA,sBACsB,KAAA,OAAAD,EAEb,KAAA,cAAgB,IAAIE,wBAAsB,CAC3C,UAAW,KAAK,OAAO,QAAQ,KAAA,CAClC,EACI,KAAA,WAAa,CAAC,KAAK,aAAa,CAAA,CAGzC,OAAOC,EAAyC,CAC5C,MAAMC,EAAuB,CAAC,EAC9B,UAAUC,KAAOF,EAAe,CAC5B,MAAMG,EAAQD,EAAI,MACfC,GAAgBF,EAAA,KAAK,CAAC,KAAMC,EAAI,KAAc,MAAOC,EAAM,CAAC,CAAA,CAAE,CAAA,CAGhE,KAAA,cAAc,OAAOF,CAAQ,CAAA,CAE1C,CAIA,MAAqBG,UAAqBC,EAAAA,cAA6C,CAAvF,kCACIP,EAAA,oBAAe,IAAIH,EAAa,IAAI,GAEpC,mBAAyC,CAC9B,OAAAD,CAAA,CAGX,mBAAmBY,EAAsC,CAC9CA,EAAA,gBAAgB,KAAK,YAAY,CAAA,CAG5C,QAAe,OACX,MAAM,OAAO,GACRC,EAAA,KAAA,aAAA,MAAAA,EAAY,gBAAgB,KAAK,aAAY,CAGtD,oBAA2B,CACvB,KAAK,aAAa,OACd,KAAK,kBAAkB,IAC3B,CAAA,CAGJ,QAAQC,EAAY,CACV,MAAAC,EAASD,EAAE,QAAQA,EAAE,OAAO,MAAO,KAAK,QAAQ,MAAM,EACxD,GAAA,CAACC,EAAO,aAAc,OAEpB,MAAAC,EAASD,EAAO,OAAO,EACvBE,EAAS,KAAK,QAAQ,OAExB,GAAAD,EAAO,OAASC,EAAQ,OAEtB,MAAAC,EAAU,MAAM,KAAK,CAAE,OAAQD,CAAO,EAAG,CAACE,EAAGC,IAAMH,EAASG,CAAC,EAC7DC,EAAYH,EAAQ,OAAO,CAAC,EAAGI,IAAM,EAAIA,EAAG,CAAC,EAOnD,MAAO,CALQN,EAAO,MAAM,CAACC,CAAM,EACR,OAAO,CAACM,EAAKC,EAAKJ,IAAMG,EAAMC,EAAMN,EAAQE,CAAC,EAAG,CAAC,EAElDC,CAEW,CAAA,CAG7C"}