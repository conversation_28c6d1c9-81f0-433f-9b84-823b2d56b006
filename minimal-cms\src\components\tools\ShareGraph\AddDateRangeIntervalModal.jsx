import { But<PERSON>, Flex, Form, Modal, Select } from "antd";
import React, { useEffect } from "react";
import { useInputStyle } from "../../../hooks/useInputStyle";
import { getArrayFieldValue } from "../../../utils";
import DEFAULT_SETTING from "../../../configs/defaultSetting";
import {
  RANGE_CHART_INFO,
  TIME_INTERVALS,
} from "../../../constants/chartConstant";
import { getIntervalLabel } from "../../../constants/common";
import { i18n } from "@euroland/libs";
import { sortedIntervals } from "../../../utils/common";

const INITIAL_VALUES = {
  period: "1D",
  intervals: ["1m", "5m", "10m", "15m", "30m", "1h"],
};

export default function AddDateRangeIntervalModal({
  isModalOpen,
  onOk = () => {},
  onCancel = () => {},
}) {
  const [form] = Form.useForm();
  const { inputStyle } = useInputStyle();

  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      const newValues = structuredClone(values);
      
      onOk({
        period: newValues.period,
        intervals: sortedIntervals(newValues.intervals),
      });
    } catch (errorInfo) {
      console.log("Failed: minimal sharegraph", errorInfo);
    }
  };

  const dateRangesAndIntervalOptions = Object.values(RANGE_CHART_INFO).map(
    (item) => ({
      value: item.period,
      label: i18n.translate(item.period),
    })
  );
  const selectedPeriod = Form.useWatch("period", form);

  const periodOptions = (RANGE_CHART_INFO[selectedPeriod]?.intervals || []).map(
    (item) => {
      const intervalOption = TIME_INTERVALS[item];
      const intervalValue = intervalOption.value;
      return {
        value: intervalValue,
        label: getIntervalLabel(
          intervalOption.label,
          intervalOption.interval.times
        ),
      };
    }
  );
  useEffect(() => {
    const intervals = RANGE_CHART_INFO[selectedPeriod]?.intervals;
    if (!intervals) return;
    form.setFieldsValue({
      intervals: RANGE_CHART_INFO[selectedPeriod]?.intervals,
    });
  }, [selectedPeriod]);

  return (
    <Modal
      okText="Add"
      title="Add Range"
      open={isModalOpen}
      onOk={onOk}
      onCancel={onCancel}
      footer={null}
    >
      <Form
        layout={"vertical"}
        form={form}
        initialValues={INITIAL_VALUES}
        style={{ width: "100%" }}
      >
        <Flex gap={10} className="flex-wrap">
          <Form.Item style={{ minWidth: 120 }} label="Range" name={["period"]}>
            <Select options={dateRangesAndIntervalOptions} />
          </Form.Item>
          <Form.Item
            className="flex-1"
            label="Intervals"
            name={["intervals"]}
            required
            rules={[{ required: true }]}
          >
            <Select mode="multiple" options={periodOptions} />
          </Form.Item>
        </Flex>
      </Form>
      <Flex className="justify-end gap-2">
        <Button onClick={onCancel}>Cancel</Button>
        <Button type="primary" onClick={handleSubmit}>
          Add
        </Button>
      </Flex>
    </Modal>
  );
}
