import { expect, it, describe, vi } from 'vitest';
import { DataFeed } from './data-feed';
import { Logical, Time } from 'lightweight-charts';
import { AdvanceChart } from './advance-chart';
import { Period } from './i-advance-chart';
import { timeToDayjs } from '../helpers/utils';

describe('DataFeed', () => {
  // Helper to create mock chart (reuse pattern)
  const createMockChart = (overrides = {}) => ({
    chartApi: {
      timeScale: () => ({
        subscribeVisibleLogicalRangeChange: vi.fn(),
        unsubscribeVisibleLogicalRangeChange: vi.fn(),
        getVisibleLogicalRange: vi.fn(),
        timeToIndex: vi.fn().mockReturnValue(0),
        fitContent: vi.fn(),
      }),
    },
    chartLoading: {
      show: vi.fn(),
      hidden: vi.fn(),
    },
    setData: vi.fn(),
    update: vi.fn(),
    lastPoint: vi.fn().mockReturnValue([]),
    fitContent: vi.fn(),
    fitRange: vi.fn(),
    ...overrides
  });

  // Helper to create mock data fetcher
  const createMockDataFetch = (overrides = {}) => ({
    fetchInitialData: vi.fn().mockResolvedValue([]),
    fetchPaginationData: vi.fn().mockResolvedValue([]),
    fetchUpdateData: vi.fn(),
    ...overrides
  });

  // Test data helpers
  const sampleData = [
    { time: '2023-01-01', open: 100, high: 110, low: 90, close: 105, volume: 1000 },
    { time: '2023-01-02', open: 105, high: 115, low: 95, close: 110, volume: 1200 },
  ];

  const sampleRange = {
    from: new Date('2023-01-01'),
    to: new Date('2023-01-02'),
    interval: { period: Period.day, times: 1 },
  };

  describe('forward method', () => {
    it('should advance time correctly for different periods', () => {
      const mockChart = createMockChart();
      const mockDataFetch = createMockDataFetch();
      const dataFeed = new DataFeed(mockChart as unknown as AdvanceChart, mockDataFetch);
      
      const baseTime = 1672531200 as Time; // 2023-01-01 00:00:00 UTC
      const baseDayjs = timeToDayjs(baseTime);
      
      // Test minute advancement
      dataFeed.interval = { period: Period.minute, times: 1 };
      const minuteResult = dataFeed.forward(baseTime, 30);
      expect(minuteResult.unix()).toBe(baseDayjs.add(30, 'minute').unix());
      
      // Test hour advancement  
      dataFeed.interval = { period: Period.hour, times: 1 };
      const hourResult = dataFeed.forward(baseTime, 2);
      expect(hourResult.unix()).toBe(baseDayjs.add(2, 'hour').unix());
      
      // Test day advancement
      dataFeed.interval = { period: Period.day, times: 1 };
      const dayResult = dataFeed.forward(baseTime, 5);
      expect(dayResult.unix()).toBe(baseDayjs.add(5, 'day').unix());

      // Test week advancement
      dataFeed.interval = { period: Period.week, times: 1 };
      const weekResult = dataFeed.forward(baseTime, 3);
      expect(weekResult.unix()).toBe(baseDayjs.add(3, 'week').unix());

      // Test month advancement
      dataFeed.interval = { period: Period.month, times: 1 };
      const monthResult = dataFeed.forward(baseTime, 2);
      expect(monthResult.unix()).toBe(baseDayjs.add(2, 'month').unix());
    });

    it('should handle negative steps correctly', () => {
      const mockChart = createMockChart();
      const mockDataFetch = createMockDataFetch();
      const dataFeed = new DataFeed(mockChart as unknown as AdvanceChart, mockDataFetch);
      
      const baseTime = 1672531200 as Time; // 2023-01-01 00:00:00 UTC
      const baseDayjs = timeToDayjs(baseTime);
      dataFeed.interval = { period: Period.day, times: 1 };
      
      const result = dataFeed.forward(baseTime, -3);
      expect(result.unix()).toBe(baseDayjs.add(-3, 'day').unix());
    });

    it('should round fractional steps correctly', () => {
      const mockChart = createMockChart();
      const mockDataFetch = createMockDataFetch();
      const dataFeed = new DataFeed(mockChart as unknown as AdvanceChart, mockDataFetch);
      
      const baseTime = 1672531200 as Time;
      const baseDayjs = timeToDayjs(baseTime);
      dataFeed.interval = { period: Period.day, times: 1 };
      
      // 2.7 should round to 3
      const result = dataFeed.forward(baseTime, 2.7);
      expect(result.unix()).toBe(baseDayjs.add(3, 'day').unix());
    });
  });

  describe('dataChanged event', () => {
    it('should trigger when data is set', () => {
      const mockChart = createMockChart();
      const mockDataFetch = createMockDataFetch();
      const dataFeed = new DataFeed(mockChart as unknown as AdvanceChart, mockDataFetch);
      
      const callback = vi.fn();
      dataFeed.dataChanged().subscribe(callback);
      
      // Setting new data should trigger the event
      dataFeed.data = sampleData;
      
      expect(callback).toHaveBeenCalled();
    });

    it('should trigger when processNewData adds new data', () => {
      const mockChart = createMockChart();
      const mockDataFetch = createMockDataFetch();
      const dataFeed = new DataFeed(mockChart as unknown as AdvanceChart, mockDataFetch);
      
      // Set initial data
      dataFeed.data = sampleData;
      
      const callback = vi.fn();
      dataFeed.dataChanged().subscribe(callback);
      
      // Process new data that extends the existing data
      const newData = [
        ...sampleData,
        { time: '2023-01-03', open: 110, high: 120, low: 105, close: 115, volume: 1500 }
      ];
      
      dataFeed.processNewData(newData);
      
      expect(callback).toHaveBeenCalled();
      expect(mockChart.setData).toHaveBeenCalled();
    });

    it('should not trigger when setting same data reference', () => {
      const mockChart = createMockChart();
      const mockDataFetch = createMockDataFetch();
      const dataFeed = new DataFeed(mockChart as unknown as AdvanceChart, mockDataFetch);
      
      dataFeed.data = sampleData;
      
      const callback = vi.fn();
      dataFeed.dataChanged().subscribe(callback);
      
      // Setting the same data reference should still trigger (current behavior)
      dataFeed.data = sampleData;
      
      expect(callback).toHaveBeenCalled();
    });
  });

  describe('initial data loading', () => {
    it('should fetch and set initial data', async () => {
      const mockChart = createMockChart();
      const mockDataFetch = createMockDataFetch({
        fetchInitialData: vi.fn().mockResolvedValue(sampleData)
      });

      const dataFeed = new DataFeed(mockChart as unknown as AdvanceChart, mockDataFetch);

      await dataFeed.setRange(sampleRange);

      // Test core behavior: fetches data and sets it on chart
      expect(mockDataFetch.fetchInitialData).toHaveBeenCalled();
      expect(mockChart.setData).toHaveBeenCalled();
      expect(dataFeed.initialData).toBe(true);
    });
  });

  describe('pagination', () => {
    it('should load more data when scrolling to start', async () => {
      const mockChart = createMockChart();
      const mockDataFetch = createMockDataFetch({
        fetchPaginationData: vi.fn().mockResolvedValue(sampleData)
      });

      const dataFeed = new DataFeed(mockChart as unknown as AdvanceChart, mockDataFetch);
      dataFeed.initialData = true;
      dataFeed.data = sampleData;

      await dataFeed.onVisibleLogicalRangeChange({
        from: 20 as Logical,
        to: 50 as Logical,
      });

      // Test core behavior: triggers pagination fetch
      expect(mockDataFetch.fetchPaginationData).toHaveBeenCalled();
    });

    it('should handle end of data gracefully', async () => {
      const mockChart = createMockChart();
      const mockDataFetch = createMockDataFetch({
        fetchPaginationData: vi.fn().mockResolvedValue([]) // Empty response
      });

      const dataFeed = new DataFeed(mockChart as unknown as AdvanceChart, mockDataFetch);
      dataFeed.initialData = true;
      dataFeed.data = sampleData;

      await dataFeed.onVisibleLogicalRangeChange({
        from: 20 as Logical,
        to: 100 as Logical,
      });

      // Test core behavior: marks end of data and doesn't update chart
      expect(dataFeed.endOfData).toBe(true);
      expect(mockChart.setData).not.toHaveBeenCalled();
    });
  });

  describe('trade data handling', () => {
    it('should update existing point when trade is in same interval', () => {
      const lastPoint = {
        time: 1672531200 as Time, // 2023-01-01 00:00:00 UTC
        open: 100,
        high: 110,
        low: 95,
        close: 105,
        volume: 1000,
      };

      const mockChart = createMockChart({
        lastPoint: vi.fn().mockReturnValue([lastPoint])
      });
      const mockDataFetch = createMockDataFetch();

      const dataFeed = new DataFeed(mockChart as unknown as AdvanceChart, mockDataFetch);
      dataFeed.interval = { period: Period.day, times: 1 };

      const trade = {
        time: 1672574400 as Time, // Same day
        price: 112,
        volume: 500,
      };

      dataFeed.trade(trade);

      // Test core behavior: aggregates with existing point
      expect(mockChart.update).toHaveBeenCalledWith(
        expect.objectContaining({
          time: trade.time,
          close: trade.price,
          high: 112, // Max of existing high (110) and trade price (112)
          volume: 1500, // Aggregated volume
        }),
        true // Updates existing point
      );
    });

    it('should create new point when trade is in different interval', () => {
      const lastPoint = {
        time: 1672531200 as Time, // 2023-01-01
        open: 100,
        high: 110,
        low: 95,
        close: 105,
        volume: 1000,
      };

      const mockChart = createMockChart({
        lastPoint: vi.fn().mockReturnValue([lastPoint])
      });
      const mockDataFetch = createMockDataFetch();

      const dataFeed = new DataFeed(mockChart as unknown as AdvanceChart, mockDataFetch);
      dataFeed.interval = { period: Period.day, times: 1 };

      const trade = {
        time: 1672617600 as Time, // Next day
        price: 115,
        volume: 200,
      };

      dataFeed.trade(trade);

      // Test core behavior: creates new point
      expect(mockChart.update).toHaveBeenCalledWith(
        expect.objectContaining({
          time: trade.time,
          open: trade.price,
          high: trade.price,
          low: trade.price,
          close: trade.price,
          volume: trade.volume,
        })
        // No second parameter means creates new point
      );
    });
  });
});
