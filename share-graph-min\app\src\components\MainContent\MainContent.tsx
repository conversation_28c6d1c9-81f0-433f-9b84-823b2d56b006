import { useAppStore } from "../../stores/useAppStore"
import {LightWeight<PERSON>hart} from "../LightWeightChart"
import Range<PERSON>hart from "../RangeChart"
import Ticker from "../Ticker"

const MainContent = () => {
  const selectedInstrumentId =  useAppStore(state => state.selectedInstrumentId)
  const footerText =  useAppStore(state => state.appSettings.footerText)
  
  return (
    <div className="main-content">
      <Ticker />
      <LightWeightChart key={selectedInstrumentId} />
      <div className="footer__container">
        <RangeChart />
        <div className="footer__text" 
          dangerouslySetInnerHTML={{ __html: footerText }}
        />
      </div>
      
    </div>
  )
}

export default MainContent