import MainLegend from './MainLegend';
import { useAppStore } from '../../stores/useAppStore';
import SelectSettings from '../SelectSettings/SelectSettings';

const TopSettingChart = () => {
  const settingPosition = useAppStore(
    (state) => state.appSettings.settingPosition
  );
  return (
    <div className="top-setting-chart">
      <MainLegend />
      {settingPosition === 'top' && <SelectSettings />}
    </div>
  );
};

export default TopSettingChart;
