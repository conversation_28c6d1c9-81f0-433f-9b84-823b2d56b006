import { describe, it, expect, vi } from 'vitest';
import { Delegate } from './delegate';

describe('Delegate', () => {
  // Helper to create delegate and callback (reuse pattern)
  const createTestDelegate = () => {
    const delegate = new Delegate<number, string, boolean>();
    const callback = vi.fn();
    return { delegate, callback };
  };

  describe('subscription and firing', () => {
    it('should fire subscribed callbacks with correct parameters', () => {
      const { delegate, callback } = createTestDelegate();

      delegate.subscribe(callback);
      delegate.fire(1, 'test', true);

      // Test core behavior: callbacks receive correct parameters
      expect(callback).toHaveBeenCalledWith(1, 'test', true);
      expect(callback).toHaveBeenCalledTimes(1);
    });

    it('should not fire unsubscribed callbacks', () => {
      const { delegate, callback } = createTestDelegate();

      delegate.subscribe(callback);
      delegate.unsubscribe(callback);
      delegate.fire(1, 'test', true);

      // Test core behavior: unsubscribed callbacks don't fire
      expect(callback).not.toHaveBeenCalled();
    });

    it('should handle single-shot listeners', () => {
      const { delegate, callback } = createTestDelegate();

      delegate.subscribe(callback, undefined, true); // Single-shot
      delegate.fire(1, 'test', true);
      delegate.fire(2, 'another', false);

      // Test core behavior: single-shot fires only once
      expect(callback).toHaveBeenCalledTimes(1);
      expect(callback).toHaveBeenCalledWith(1, 'test', true);
    });
  });

  describe('parameter tracking', () => {
    it('should track last fired parameters', () => {
      const { delegate } = createTestDelegate();

      delegate.fire(42, 'hello', false);
      const params = delegate.lastParams();

      // Test core behavior: tracks last parameters
      expect(params).toEqual([42, 'hello', false]);
    });
  });

  describe('listener management', () => {
    it('should unsubscribe listeners by object reference', () => {
      const { delegate } = createTestDelegate();
      const callback1 = vi.fn();
      const callback2 = vi.fn();
      const obj = {};

      delegate.subscribe(callback1, obj);
      delegate.subscribe(callback2); // No object reference

      delegate.unsubscribeAll(obj);
      delegate.fire(1, 'test', true);

      // Test core behavior: only unsubscribes listeners linked to object
      expect(callback1).not.toHaveBeenCalled();
      expect(callback2).toHaveBeenCalled();
    });

    it('should check listener presence', () => {
      const { delegate, callback } = createTestDelegate();

      // Test core behavior: tracks listener presence
      expect(delegate.hasListener()).toBe(false);

      delegate.subscribe(callback);
      expect(delegate.hasListener()).toBe(true);

      delegate.unsubscribe(callback);
      expect(delegate.hasListener()).toBe(false);
    });

    it('should destroy all listeners', () => {
      const { delegate, callback } = createTestDelegate();

      delegate.subscribe(callback);
      delegate.destroy();
      delegate.fire(1, 'test', true);

      // Test core behavior: destroy removes all listeners
      expect(delegate.hasListener()).toBe(false);
      expect(callback).not.toHaveBeenCalled();
    });
  });
});
