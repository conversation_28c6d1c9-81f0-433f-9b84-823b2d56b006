import { CopyToClipboard } from "react-copy-to-clipboard";
import { CopyOutlined, ExportOutlined } from "@ant-design/icons";
import { Button, Input, message, Space, Tooltip } from "antd";
import { useEffect, useImperativeHandle, useRef, useState } from "react";

import { stringifyParamsToURL } from "../../utils";
import { useAppStore } from "../../hooks/useAppStore";
import pickBy from "lodash.pickby";
const ToolIFrame = ({
  handleRef,
  name,
  handleIframePreviewsRef,
  tool,
  companyCode,
  languageCode,
  version,
}) => {
  const ref = useRef(null);
  // handleIframePreviewsRef.current[name] = handleRef;
  const store = useAppStore();
  const [url, setURL] = useState("");
  const [messageApi, contextHolder] = message.useMessage();

  const getDateRangesAndIntervalStr = (dateRangesAndInterval = []) => {
    return dateRangesAndInterval
      .map(({ period, intervals }) => `${period}|${intervals.join(",")}`)
      .join(";");
  };

  const getUnControllerUIParams = (controllerUI = {}) => {
    return Object.keys(controllerUI)
      .filter((key) => !controllerUI[key])
      .join(",");
  };

  const getNewURL = () => {
    const formState = store.getState().forms;
    const availableTools =
      formState.availableTools.draft ?? formState.availableTools.initState;
    const generalSettings =
      formState.generalSettings.draft ?? formState.generalSettings.initState;
    const shareGraph =
      formState.tools.shareGraph.draft ?? formState.tools.shareGraph.initState;
    const newURL = stringifyParamsToURL(
      pickBy({
        companyCode: availableTools.company?.value,
        instrumentIds: generalSettings.instrumentIds.join(","),
        realtimeIds: generalSettings.instrumentIds.join(","),
        defaultSelectedInstrumentId:
          generalSettings.defaultSelectedInstrumentId,
        // width: generalSettings.size.width,
        // height: generalSettings.size.height,
        // locale: generalSettings.general.locale,
        fontFamily: generalSettings.general.fontFamily,
        fontSize: generalSettings.general.fontSize,
        fontColor: generalSettings.general.fontColor,
        upColor: generalSettings.general.upColor,
        downColor: generalSettings.general.downColor,
        primaryColor: generalSettings.general.primaryColor,
        gridColor: shareGraph.chartConfiguration.gridColor,
        axesFontsize: shareGraph.chartConfiguration.axesFontsize,
        axesColor: shareGraph.chartConfiguration.axesColor,
        chartType: shareGraph.chartConfiguration.chartType,
        "chart-preferences": shareGraph.chartConfiguration["chart-preferences"],
        "y-axis-preferences":
          shareGraph.chartConfiguration["y-axis-preferences"],
        volume: shareGraph.chartConfiguration.volume,
        // tooltip: shareGraph.chartConfiguration.tooltip,
        "show-last-close-line":
          shareGraph.chartConfiguration["show-last-close-line"],
        // "table-view": shareGraph.chartConfiguration["table-view"],
        // valueTracking: shareGraph.valueTracking,
        // dataFields: shareGraph.tickerSettings.dataFields,
        // template: shareGraph.tickerSettings.template,
        refreshTickerTime:  shareGraph.tickerSettings.refreshTickerTime,
        defaultRange: `${shareGraph.defaultRange.period},${shareGraph.defaultRange.interval}`,
        dateRangesAndInterval: getDateRangesAndIntervalStr(
          shareGraph.dateRangesAndInterval
        ),
        customRange: shareGraph.customRange && null,
        unControllerUI: getUnControllerUIParams(shareGraph.controllerUI),
        events: shareGraph.events,
      }, value => !!value || typeof value === 'boolean')
    );
    return newURL;
  };

  useEffect(() => {
    setURL(getNewURL());
  }, []);

  useImperativeHandle(
    handleRef,
    () => ({
      onReload: (formState) => {
        // ref.current.src = getNewURL();
        setURL(getNewURL());
      },
    }),
    []
  );

  const handleCopyLink = () => {
    messageApi.open({
      type: "success",
      content: "Copied link!",
    });
  };

  const handleOpenLink = (openUrl) => {
    window.open(openUrl, "_blank");
  };

  return (
    <div className="flex flex-col h-full gap-2">
      <div className=" flex-1">
        <iframe
          ref={ref}
          width="100%"
          height="100%"
          className="preview-iframe-tool"
          src={url}
        />
      </div>
      <Space.Compact
        style={{
          width: "100%",
        }}
      >
        <Input value={url} readOnly />
        <Tooltip title="Copy">
          <CopyToClipboard text={url} onCopy={() => handleCopyLink()}>
            <Button type="primary" icon={<CopyOutlined />}></Button>
          </CopyToClipboard>
        </Tooltip>
        <Tooltip title="Open link">
          <Button
            type="primary"
            icon={<ExportOutlined style={{ fontSize: 17 }} />}
            onClick={() => handleOpenLink(url)}
          ></Button>
        </Tooltip>
      </Space.Compact>
      {contextHolder}
    </div>
  );
};

export default ToolIFrame;
