{"name": "minimal-cms", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@ant-design/icons": "^5.5.2", "@euroland/libs": "^2.1.0", "@reduxjs/toolkit": "^2.5.0", "antd": "^5.23.0", "axios": "^1.7.9", "fuzzysort": "^3.1.0", "lodash.debounce": "^4.0.8", "lodash.pickby": "^4.6.0", "query-string": "^9.1.1", "react": "^18.3.1", "react-copy-to-clipboard": "^5.1.0", "react-dom": "^18.3.1", "react-redux": "^9.2.0", "sass": "^1.83.1", "tailwindcss": "^3.4.17"}, "devDependencies": {"@eslint/js": "^9.17.0", "@types/react": "^18.3.18", "@types/react-dom": "^18.3.5", "@vitejs/plugin-react": "^4.3.4", "autoprefixer": "^10.4.20", "eslint": "^9.17.0", "eslint-plugin-react": "^7.37.2", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.16", "globals": "^15.14.0", "vite": "^6.0.5", "vite-plugin-mkcert": "^1.17.6"}}