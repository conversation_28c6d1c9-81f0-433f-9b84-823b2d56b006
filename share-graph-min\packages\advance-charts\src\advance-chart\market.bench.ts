import { describe, bench, beforeEach } from 'vitest'
import { Market } from './market' // Adjust import path as needed
import dayjs from 'dayjs'

describe('Market Performance Tests', () => {
  const marketOptions = {
    name: 'Test Market',
    timeZone: 'America/New_York',
    open: '09:30',
    close: '16:00'
  }

  let market: Market

  beforeEach(() => {
    market = new Market(marketOptions)
  })

  // Helper function to generate dates
  const generateDates = (count: number): Date[] => {
    const dates: Date[] = []
    const baseDate = dayjs().startOf('day')
    
    for (let i = 0; i < count; i++) {
      dates.push(baseDate.add(i, 'hour').toDate())
    }
    return dates
  }

  // Test cache performance with sequential reads
  bench('Sequential cache performance - isOpen', () => {
    const date = new Date()
    for (let i = 0; i < 1000; i++) {
      market.isOpen(date)
    }
  }, { iterations: 100 })

  // Test cache performance with different dates
  bench('Different dates cache performance - isOpen', () => {
    const dates = generateDates(1000)
    for (const date of dates) {
      market.isOpen(date)
    }
  }, { iterations: 100 })

  // Test cache size limit behavior
  bench('Cache size limit behavior', () => {
    const dates = generateDates(2000) // Generate more dates than cache limit
    for (const date of dates) {
      market.isOpen(date)
      market.getOpen(date)
      market.getClose(date)
    }
  }, { iterations: 50 })

  // Test performance of all market operations in sequence
  bench('All operations sequence', () => {
    const date = new Date()
    market.isOpen(date)
    market.getOpen(date)
    market.getClose(date)
    market.getNextOpen(date)
    market.getNextClose(date)
  }, { iterations: 1000 })

  // Test parallel operations
  bench('Parallel operations with different dates', async () => {
    const dates = generateDates(100)
    await Promise.all(dates.map(date => {
      return new Promise((resolve) => {
        market.isOpen(date)
        market.getOpen(date)
        market.getClose(date)
        resolve(true)
      })
    }))
  }, { iterations: 100 })

  // Test day cache performance
  bench('Day cache performance', () => {
    const dates = generateDates(24) // Test with 24 hours in a day
    for (const date of dates) {
      market.isOpen(date)
    }
  }, { iterations: 1000 })

  // Test cache invalidation performance
  bench('Cache invalidation performance', () => {
    const dates = generateDates(1500) // More than cache limit
    for (const date of dates) {
      market.isOpen(date)
    }
  }, { iterations: 50 })

  // Test timezone conversion performance
  bench('Timezone conversion performance', () => {
    const date = new Date()
    for (let i = 0; i < 1000; i++) {
      market.marketZoneNow()
      market.getOpen(date)
    }
  }, { iterations: 100 })

  // Test performance under load with mixed operations
  bench('Mixed operations under load', () => {
    const dates = generateDates(100)
    dates.forEach(date => {
      market.isOpen(date)
      if (market.isOpen(date)) {
        market.getNextClose(date)
      } else {
        market.getNextOpen(date)
      }
    })
  }, { iterations: 100 })
})