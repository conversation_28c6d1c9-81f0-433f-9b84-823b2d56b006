.option-list {
    margin-bottom: 15px;
    
    &__title {
        margin-bottom: 10px;
    }

    &__menu {
        display: flex;
        // justify-content: space-between;
        flex-wrap: wrap;
        padding: 2px;
        border-radius: 8px;
        border: 1px solid #434651;
        width: fit-content;
        gap: 5px;

        // &__item {
        //     width: calc(100% /3);
        // }
    }

    &__tab {
        background: none;
        border: none;
        padding: 8px;
        border-radius: 4px;
        cursor: pointer;
        transition: background-color 0.3s ease;

        &:hover {
            background-color: var(--hover-color);
        }

        &.active {
            background-color: var(--active-color);
            color: #fff;
            font-weight: bold;
        }

        &.disabled {
            cursor: not-allowed;
            opacity: 0.5;
        }
    }
}