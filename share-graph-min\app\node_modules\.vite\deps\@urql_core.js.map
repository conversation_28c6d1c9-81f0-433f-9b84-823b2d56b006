{"version": 3, "sources": ["../../../../node_modules/@0no-co/graphql.web/src/kind.js", "../../../../node_modules/@0no-co/graphql.web/src/error.ts", "../../../../node_modules/@0no-co/graphql.web/src/parser.ts", "../../../../node_modules/@0no-co/graphql.web/src/visitor.ts", "../../../../node_modules/@0no-co/graphql.web/src/printer.ts", "../../../../node_modules/@0no-co/graphql.web/src/values.ts", "../../../../node_modules/@0no-co/graphql.web/src/helpers.ts", "../../../../node_modules/wonka/dist/wonka.mjs", "../../../../node_modules/@urql/core/src/utils/error.ts", "../../../../node_modules/@urql/core/src/utils/hash.ts", "../../../../node_modules/@urql/core/src/utils/variables.ts", "../../../../node_modules/@urql/core/src/utils/request.ts", "../../../../node_modules/@urql/core/src/utils/result.ts", "../../../../node_modules/@urql/core/src/internal/fetchOptions.ts", "../../../../node_modules/@urql/core/src/internal/fetchSource.ts", "../../../../node_modules/@urql/core/src/utils/collectTypenames.ts", "../../../../node_modules/@urql/core/src/utils/formatDocument.ts", "../../../../node_modules/@urql/core/src/utils/streamUtils.ts", "../../../../node_modules/@urql/core/src/utils/operation.ts", "../../../../node_modules/@urql/core/src/utils/index.ts", "../../../../node_modules/@urql/core/src/gql.ts", "../../../../node_modules/@urql/core/src/exchanges/cache.ts", "../../../../node_modules/@urql/core/src/exchanges/ssr.ts", "../../../../node_modules/@urql/core/src/exchanges/subscription.ts", "../../../../node_modules/@urql/core/src/exchanges/debug.ts", "../../../../node_modules/@urql/core/src/exchanges/fetch.ts", "../../../../node_modules/@urql/core/src/exchanges/compose.ts", "../../../../node_modules/@urql/core/src/exchanges/map.ts", "../../../../node_modules/@urql/core/src/exchanges/fallback.ts", "../../../../node_modules/@urql/core/src/client.ts"], "sourcesContent": ["export const Kind = {\n  NAME: 'Name',\n  DOCUMENT: 'Document',\n  OPERATION_DEFINITION: 'OperationDefinition',\n  VARIABLE_DEFINITION: 'VariableDefinition',\n  SELECTION_SET: 'SelectionSet',\n  FIELD: 'Field',\n  ARGUMENT: 'Argument',\n  FRAGMENT_SPREAD: 'FragmentSpread',\n  INLINE_FRAGMENT: 'InlineFragment',\n  FRAGMENT_DEFINITION: 'FragmentDefinition',\n  VARIABLE: 'Variable',\n  INT: 'IntValue',\n  FLOAT: 'FloatValue',\n  STRING: 'StringValue',\n  BOOLEAN: 'BooleanValue',\n  NULL: 'NullValue',\n  ENUM: 'EnumValue',\n  LIST: 'ListValue',\n  OBJECT: 'ObjectValue',\n  OBJECT_FIELD: 'ObjectField',\n  DIRECTIVE: 'Directive',\n  NAMED_TYPE: 'NamedType',\n  LIST_TYPE: 'ListType',\n  NON_NULL_TYPE: 'NonNullType',\n\n  /*\n  SCHEMA_DEFINITION: 'SchemaDefinition',\n  OPERATION_TYPE_DEFINITION: 'OperationTypeDefinition',\n  SCALAR_TYPE_DEFINITION: 'ScalarTypeDefinition',\n  OBJECT_TYPE_DEFINITION: 'ObjectTypeDefinition',\n  FIELD_DEFINITION: 'FieldDefinition',\n  INPUT_VALUE_DEFINITION: 'InputValueDefinition',\n  INTERFACE_TYPE_DEFINITION: 'InterfaceTypeDefinition',\n  UNION_TYPE_DEFINITION: 'UnionTypeDefinition',\n  ENUM_TYPE_DEFINITION: 'EnumTypeDefinition',\n  ENUM_VALUE_DEFINITION: 'EnumValueDefinition',\n  INPUT_OBJECT_TYPE_DEFINITION: 'InputObjectTypeDefinition',\n  DIRECTIVE_DEFINITION: 'DirectiveDefinition',\n  SCHEMA_EXTENSION: 'SchemaExtension',\n  SCALAR_TYPE_EXTENSION: 'ScalarTypeExtension',\n  OBJECT_TYPE_EXTENSION: 'ObjectTypeExtension',\n  INTERFACE_TYPE_EXTENSION: 'InterfaceTypeExtension',\n  UNION_TYPE_EXTENSION: 'UnionTypeExtension',\n  ENUM_TYPE_EXTENSION: 'EnumTypeExtension',\n  INPUT_OBJECT_TYPE_EXTENSION: 'InputObjectTypeExtension',\n  */\n};\n\nexport const OperationTypeNode = {\n  QUERY: 'query',\n  MUTATION: 'mutation',\n  SUBSCRIPTION: 'subscription',\n};\n", "import type { Maybe, Extensions, Source } from './types';\nimport type { ASTNode } from './ast';\n\nexport class GraphQLError extends Error {\n  readonly locations: ReadonlyArray<any> | undefined;\n  readonly path: ReadonlyArray<string | number> | undefined;\n  readonly nodes: ReadonlyArray<any> | undefined;\n  readonly source: Source | undefined;\n  readonly positions: ReadonlyArray<number> | undefined;\n  readonly originalError: Error | undefined;\n  readonly extensions: Extensions;\n\n  constructor(\n    message: string,\n    nodes?: ReadonlyArray<ASTNode> | ASTNode | null,\n    source?: Maybe<Source>,\n    positions?: Maybe<ReadonlyArray<number>>,\n    path?: Maybe<ReadonlyArray<string | number>>,\n    originalError?: Maybe<Error>,\n    extensions?: Maybe<Extensions>\n  ) {\n    super(message);\n\n    this.name = 'GraphQLError';\n    this.message = message;\n\n    if (path) this.path = path;\n    if (nodes) this.nodes = (Array.isArray(nodes) ? nodes : [nodes]) as ASTNode[];\n    if (source) this.source = source;\n    if (positions) this.positions = positions;\n    if (originalError) this.originalError = originalError;\n\n    let _extensions = extensions;\n    if (!_extensions && originalError) {\n      const originalExtensions = (originalError as any).extensions;\n      if (originalExtensions && typeof originalExtensions === 'object') {\n        _extensions = originalExtensions;\n      }\n    }\n\n    this.extensions = _extensions || {};\n  }\n\n  toJSON(): any {\n    return { ...this, message: this.message };\n  }\n\n  toString(): string {\n    return this.message;\n  }\n\n  get [Symbol.toStringTag](): string {\n    return 'GraphQLError';\n  }\n}\n", "/**\n * This is a spec-compliant implementation of a GraphQL query language parser,\n * up-to-date with the October 2021 Edition. Unlike the reference implementation\n * in graphql.js it will only parse the query language, but not the schema\n * language.\n */\nimport type { Kind, OperationTypeNode } from './kind';\nimport { GraphQLError } from './error';\nimport type { Location, Source } from './types';\nimport type * as ast from './ast';\n\nlet input: string;\nlet idx: number;\n\nfunction error(kind: string) {\n  return new GraphQLError(`Syntax Error: Unexpected token at ${idx} in ${kind}`);\n}\n\nfunction advance(pattern: RegExp) {\n  pattern.lastIndex = idx;\n  if (pattern.test(input)) {\n    const match = input.slice(idx, (idx = pattern.lastIndex));\n    return match;\n  }\n}\n\nconst leadingRe = / +(?=[^\\s])/y;\nfunction blockString(string: string) {\n  const lines = string.split('\\n');\n  let out = '';\n  let commonIndent = 0;\n  let firstNonEmptyLine = 0;\n  let lastNonEmptyLine = lines.length - 1;\n  for (let i = 0; i < lines.length; i++) {\n    leadingRe.lastIndex = 0;\n    if (leadingRe.test(lines[i])) {\n      if (i && (!commonIndent || leadingRe.lastIndex < commonIndent))\n        commonIndent = leadingRe.lastIndex;\n      firstNonEmptyLine = firstNonEmptyLine || i;\n      lastNonEmptyLine = i;\n    }\n  }\n  for (let i = firstNonEmptyLine; i <= lastNonEmptyLine; i++) {\n    if (i !== firstNonEmptyLine) out += '\\n';\n    out += lines[i].slice(commonIndent).replace(/\\\\\"\"\"/g, '\"\"\"');\n  }\n  return out;\n}\n\n// Note: This is equivalent to: /(?:[\\s,]*|#[^\\n\\r]*)*/y\nfunction ignored() {\n  for (\n    let char = input.charCodeAt(idx++) | 0;\n    char === 9 /*'\\t'*/ ||\n    char === 10 /*'\\n'*/ ||\n    char === 13 /*'\\r'*/ ||\n    char === 32 /*' '*/ ||\n    char === 35 /*'#'*/ ||\n    char === 44 /*','*/ ||\n    char === 65279 /*'\\ufeff'*/;\n    char = input.charCodeAt(idx++) | 0\n  ) {\n    if (char === 35 /*'#'*/) while ((char = input.charCodeAt(idx++)) !== 10 && char !== 13);\n  }\n  idx--;\n}\n\nconst nameRe = /[_A-Za-z]\\w*/y;\n\n// NOTE: This should be compressed by our build step\n// This merges all possible value parsing into one regular expression\nconst valueRe = new RegExp(\n  '(?:' +\n    // `null`, `true`, and `false` literals (BooleanValue & NullValue)\n    '(null|true|false)|' +\n    // Variables starting with `$` then having a name (VariableNode)\n    '\\\\$(' +\n    nameRe.source +\n    ')|' +\n    // Numbers, starting with int then optionally following with a float part (IntValue and FloatValue)\n    '(-?\\\\d+)((?:\\\\.\\\\d+)?[eE][+-]?\\\\d+|\\\\.\\\\d+)?|' +\n    // Block strings starting with `\"\"\"` until the next unescaped `\"\"\"` (StringValue)\n    '(\"\"\"(?:\"\"\"|(?:[\\\\s\\\\S]*?[^\\\\\\\\])\"\"\"))|' +\n    // Strings starting with `\"` must be on one line (StringValue)\n    '(\"(?:\"|[^\\\\r\\\\n]*?[^\\\\\\\\]\"))|' + // string\n    // Enums are simply names except for our literals (EnumValue)\n    '(' +\n    nameRe.source +\n    '))',\n  'y'\n);\n\n// NOTE: Each of the groups above end up in the RegExpExecArray at the specified indices (starting with 1)\nconst enum ValueGroup {\n  Const = 1,\n  Var,\n  Int,\n  Float,\n  BlockString,\n  String,\n  Enum,\n}\n\ntype ValueExec = RegExpExecArray & {\n  [Prop in ValueGroup]: string | undefined;\n};\n\nconst complexStringRe = /\\\\/;\n\nfunction value(constant: true): ast.ConstValueNode;\nfunction value(constant: boolean): ast.ValueNode;\n\nfunction value(constant: boolean): ast.ValueNode {\n  let match: string | undefined;\n  let exec: ValueExec | null;\n  valueRe.lastIndex = idx;\n  if (input.charCodeAt(idx) === 91 /*'['*/) {\n    // Lists are checked ahead of time with `[` chars\n    idx++;\n    ignored();\n    const values: ast.ValueNode[] = [];\n    while (input.charCodeAt(idx) !== 93 /*']'*/) values.push(value(constant));\n    idx++;\n    ignored();\n    return {\n      kind: 'ListValue' as Kind.LIST,\n      values,\n    };\n  } else if (input.charCodeAt(idx) === 123 /*'{'*/) {\n    // Objects are checked ahead of time with `{` chars\n    idx++;\n    ignored();\n    const fields: ast.ObjectFieldNode[] = [];\n    while (input.charCodeAt(idx) !== 125 /*'}'*/) {\n      if ((match = advance(nameRe)) == null) throw error('ObjectField');\n      ignored();\n      if (input.charCodeAt(idx++) !== 58 /*':'*/) throw error('ObjectField');\n      ignored();\n      fields.push({\n        kind: 'ObjectField' as Kind.OBJECT_FIELD,\n        name: { kind: 'Name' as Kind.NAME, value: match },\n        value: value(constant),\n      });\n    }\n    idx++;\n    ignored();\n    return {\n      kind: 'ObjectValue' as Kind.OBJECT,\n      fields,\n    };\n  } else if ((exec = valueRe.exec(input) as ValueExec) != null) {\n    // Starting from here, the merged `valueRe` is used\n    idx = valueRe.lastIndex;\n    ignored();\n    if ((match = exec[ValueGroup.Const]) != null) {\n      return match === 'null'\n        ? { kind: 'NullValue' as Kind.NULL }\n        : {\n            kind: 'BooleanValue' as Kind.BOOLEAN,\n            value: match === 'true',\n          };\n    } else if ((match = exec[ValueGroup.Var]) != null) {\n      if (constant) {\n        throw error('Variable');\n      } else {\n        return {\n          kind: 'Variable' as Kind.VARIABLE,\n          name: {\n            kind: 'Name' as Kind.NAME,\n            value: match,\n          },\n        };\n      }\n    } else if ((match = exec[ValueGroup.Int]) != null) {\n      let floatPart: string | undefined;\n      if ((floatPart = exec[ValueGroup.Float]) != null) {\n        return {\n          kind: 'FloatValue' as Kind.FLOAT,\n          value: match + floatPart,\n        };\n      } else {\n        return {\n          kind: 'IntValue' as Kind.INT,\n          value: match,\n        };\n      }\n    } else if ((match = exec[ValueGroup.BlockString]) != null) {\n      return {\n        kind: 'StringValue' as Kind.STRING,\n        value: blockString(match.slice(3, -3)),\n        block: true,\n      };\n    } else if ((match = exec[ValueGroup.String]) != null) {\n      return {\n        kind: 'StringValue' as Kind.STRING,\n        // When strings don't contain escape codes, a simple slice will be enough, otherwise\n        // `JSON.parse` matches GraphQL's string parsing perfectly\n        value: complexStringRe.test(match) ? (JSON.parse(match) as string) : match.slice(1, -1),\n        block: false,\n      };\n    } else if ((match = exec[ValueGroup.Enum]) != null) {\n      return {\n        kind: 'EnumValue' as Kind.ENUM,\n        value: match,\n      };\n    }\n  }\n\n  throw error('Value');\n}\n\nfunction arguments_(constant: boolean): ast.ArgumentNode[] | undefined {\n  if (input.charCodeAt(idx) === 40 /*'('*/) {\n    const args: ast.ArgumentNode[] = [];\n    idx++;\n    ignored();\n    let _name: string | undefined;\n    do {\n      if ((_name = advance(nameRe)) == null) throw error('Argument');\n      ignored();\n      if (input.charCodeAt(idx++) !== 58 /*':'*/) throw error('Argument');\n      ignored();\n      args.push({\n        kind: 'Argument' as Kind.ARGUMENT,\n        name: { kind: 'Name' as Kind.NAME, value: _name },\n        value: value(constant),\n      });\n    } while (input.charCodeAt(idx) !== 41 /*')'*/);\n    idx++;\n    ignored();\n    return args;\n  }\n}\n\nfunction directives(constant: true): ast.ConstDirectiveNode[] | undefined;\nfunction directives(constant: boolean): ast.DirectiveNode[] | undefined;\n\nfunction directives(constant: boolean): ast.DirectiveNode[] | undefined {\n  if (input.charCodeAt(idx) === 64 /*'@'*/) {\n    const directives: ast.DirectiveNode[] = [];\n    let _name: string | undefined;\n    do {\n      idx++;\n      if ((_name = advance(nameRe)) == null) throw error('Directive');\n      ignored();\n      directives.push({\n        kind: 'Directive' as Kind.DIRECTIVE,\n        name: { kind: 'Name' as Kind.NAME, value: _name },\n        arguments: arguments_(constant),\n      });\n    } while (input.charCodeAt(idx) === 64 /*'@'*/);\n    return directives;\n  }\n}\n\nfunction type(): ast.TypeNode {\n  let match: string | undefined;\n  let lists = 0;\n  while (input.charCodeAt(idx) === 91 /*'['*/) {\n    lists++;\n    idx++;\n    ignored();\n  }\n  if ((match = advance(nameRe)) == null) throw error('NamedType');\n  ignored();\n  let type: ast.TypeNode = {\n    kind: 'NamedType' as Kind.NAMED_TYPE,\n    name: { kind: 'Name' as Kind.NAME, value: match },\n  };\n  do {\n    if (input.charCodeAt(idx) === 33 /*'!'*/) {\n      idx++;\n      ignored();\n      type = {\n        kind: 'NonNullType' as Kind.NON_NULL_TYPE,\n        type: type as ast.NamedTypeNode | ast.ListTypeNode,\n      } satisfies ast.NonNullTypeNode;\n    }\n    if (lists) {\n      if (input.charCodeAt(idx++) !== 93 /*']'*/) throw error('NamedType');\n      ignored();\n      type = {\n        kind: 'ListType' as Kind.LIST_TYPE,\n        type: type as ast.NamedTypeNode | ast.ListTypeNode,\n      } satisfies ast.ListTypeNode;\n    }\n  } while (lists--);\n  return type;\n}\n\n// NOTE: This should be compressed by our build step\n// This merges the two possible selection parsing branches into one regular expression\nconst selectionRe = new RegExp(\n  '(?:' +\n    // fragment spreads (FragmentSpread or InlineFragment nodes)\n    '(\\\\.{3})|' +\n    // field aliases or names (FieldNode)\n    '(' +\n    nameRe.source +\n    '))',\n  'y'\n);\n\n// NOTE: Each of the groups above end up in the RegExpExecArray at the indices 1&2\nconst enum SelectionGroup {\n  Spread = 1,\n  Name,\n}\n\ntype SelectionExec = RegExpExecArray & {\n  [Prop in SelectionGroup]: string | undefined;\n};\n\nfunction selectionSet(): ast.SelectionSetNode {\n  const selections: ast.SelectionNode[] = [];\n  let match: string | undefined;\n  let exec: SelectionExec | null;\n  do {\n    selectionRe.lastIndex = idx;\n    if ((exec = selectionRe.exec(input) as SelectionExec) != null) {\n      idx = selectionRe.lastIndex;\n      if (exec[SelectionGroup.Spread] != null) {\n        ignored();\n        let match = advance(nameRe);\n        if (match != null && match !== 'on') {\n          // A simple `...Name` spread with optional directives\n          ignored();\n          selections.push({\n            kind: 'FragmentSpread' as Kind.FRAGMENT_SPREAD,\n            name: { kind: 'Name' as Kind.NAME, value: match },\n            directives: directives(false),\n          });\n        } else {\n          ignored();\n          if (match === 'on') {\n            // An inline `... on Name` spread; if this doesn't match, the type condition has been omitted\n            if ((match = advance(nameRe)) == null) throw error('NamedType');\n            ignored();\n          }\n          const _directives = directives(false);\n          if (input.charCodeAt(idx++) !== 123 /*'{'*/) throw error('InlineFragment');\n          ignored();\n          selections.push({\n            kind: 'InlineFragment' as Kind.INLINE_FRAGMENT,\n            typeCondition: match\n              ? {\n                  kind: 'NamedType' as Kind.NAMED_TYPE,\n                  name: { kind: 'Name' as Kind.NAME, value: match },\n                }\n              : undefined,\n            directives: _directives,\n            selectionSet: selectionSet(),\n          });\n        }\n      } else if ((match = exec[SelectionGroup.Name]) != null) {\n        let _alias: string | undefined;\n        ignored();\n        // Parse the optional alias, by reassigning and then getting the name\n        if (input.charCodeAt(idx) === 58 /*':'*/) {\n          idx++;\n          ignored();\n          _alias = match;\n          if ((match = advance(nameRe)) == null) throw error('Field');\n          ignored();\n        }\n        const _arguments = arguments_(false);\n        ignored();\n        const _directives = directives(false);\n        let _selectionSet: ast.SelectionSetNode | undefined;\n        if (input.charCodeAt(idx) === 123 /*'{'*/) {\n          idx++;\n          ignored();\n          _selectionSet = selectionSet();\n        }\n        selections.push({\n          kind: 'Field' as Kind.FIELD,\n          alias: _alias ? { kind: 'Name' as Kind.NAME, value: _alias } : undefined,\n          name: { kind: 'Name' as Kind.NAME, value: match },\n          arguments: _arguments,\n          directives: _directives,\n          selectionSet: _selectionSet,\n        });\n      }\n    } else {\n      throw error('SelectionSet');\n    }\n  } while (input.charCodeAt(idx) !== 125 /*'}'*/);\n  idx++;\n  ignored();\n  return {\n    kind: 'SelectionSet' as Kind.SELECTION_SET,\n    selections,\n  };\n}\n\nfunction variableDefinitions(): ast.VariableDefinitionNode[] | undefined {\n  ignored();\n  if (input.charCodeAt(idx) === 40 /*'('*/) {\n    const vars: ast.VariableDefinitionNode[] = [];\n    idx++;\n    ignored();\n    let _name: string | undefined;\n    do {\n      if (input.charCodeAt(idx++) !== 36 /*'$'*/) throw error('Variable');\n      if ((_name = advance(nameRe)) == null) throw error('Variable');\n      ignored();\n      if (input.charCodeAt(idx++) !== 58 /*':'*/) throw error('VariableDefinition');\n      ignored();\n      const _type = type();\n      let _defaultValue: ast.ConstValueNode | undefined;\n      if (input.charCodeAt(idx) === 61 /*'='*/) {\n        idx++;\n        ignored();\n        _defaultValue = value(true);\n      }\n      ignored();\n      vars.push({\n        kind: 'VariableDefinition' as Kind.VARIABLE_DEFINITION,\n        variable: {\n          kind: 'Variable' as Kind.VARIABLE,\n          name: { kind: 'Name' as Kind.NAME, value: _name },\n        },\n        type: _type,\n        defaultValue: _defaultValue,\n        directives: directives(true),\n      });\n    } while (input.charCodeAt(idx) !== 41 /*')'*/);\n    idx++;\n    ignored();\n    return vars;\n  }\n}\n\nfunction fragmentDefinition(): ast.FragmentDefinitionNode {\n  let _name: string | undefined;\n  let _condition: string | undefined;\n  if ((_name = advance(nameRe)) == null) throw error('FragmentDefinition');\n  ignored();\n  if (advance(nameRe) !== 'on') throw error('FragmentDefinition');\n  ignored();\n  if ((_condition = advance(nameRe)) == null) throw error('FragmentDefinition');\n  ignored();\n  const _directives = directives(false);\n  if (input.charCodeAt(idx++) !== 123 /*'{'*/) throw error('FragmentDefinition');\n  ignored();\n  return {\n    kind: 'FragmentDefinition' as Kind.FRAGMENT_DEFINITION,\n    name: { kind: 'Name' as Kind.NAME, value: _name },\n    typeCondition: {\n      kind: 'NamedType' as Kind.NAMED_TYPE,\n      name: { kind: 'Name' as Kind.NAME, value: _condition },\n    },\n    directives: _directives,\n    selectionSet: selectionSet(),\n  };\n}\n\nconst definitionRe = /(?:query|mutation|subscription|fragment)/y;\n\nfunction operationDefinition(\n  operation: OperationTypeNode | undefined\n): ast.OperationDefinitionNode | undefined {\n  let _name: string | undefined;\n  let _variableDefinitions: ast.VariableDefinitionNode[] | undefined;\n  let _directives: ast.DirectiveNode[] | undefined;\n  if (operation) {\n    ignored();\n    _name = advance(nameRe);\n    _variableDefinitions = variableDefinitions();\n    _directives = directives(false);\n  }\n  if (input.charCodeAt(idx) === 123 /*'{'*/) {\n    idx++;\n    ignored();\n    return {\n      kind: 'OperationDefinition' as Kind.OPERATION_DEFINITION,\n      operation: operation || ('query' as OperationTypeNode.QUERY),\n      name: _name ? { kind: 'Name' as Kind.NAME, value: _name } : undefined,\n      variableDefinitions: _variableDefinitions,\n      directives: _directives,\n      selectionSet: selectionSet(),\n    };\n  }\n}\n\nfunction document(input: string, noLoc: boolean): ast.DocumentNode {\n  let match: string | undefined;\n  let definition: ast.OperationDefinitionNode | undefined;\n  ignored();\n  const definitions: ast.ExecutableDefinitionNode[] = [];\n  do {\n    if ((match = advance(definitionRe)) === 'fragment') {\n      ignored();\n      definitions.push(fragmentDefinition());\n    } else if ((definition = operationDefinition(match as OperationTypeNode)) != null) {\n      definitions.push(definition);\n    } else {\n      throw error('Document');\n    }\n  } while (idx < input.length);\n\n  if (!noLoc) {\n    let loc: Location | undefined;\n    return {\n      kind: 'Document' as Kind.DOCUMENT,\n      definitions,\n      /* v8 ignore start */\n      set loc(_loc: Location) {\n        loc = _loc;\n      },\n      /* v8 ignore stop */\n      // @ts-ignore\n      get loc() {\n        if (!loc) {\n          loc = {\n            start: 0,\n            end: input.length,\n            startToken: undefined,\n            endToken: undefined,\n            source: {\n              body: input,\n              name: 'graphql.web',\n              locationOffset: { line: 1, column: 1 },\n            },\n          };\n        }\n        return loc;\n      },\n    };\n  }\n\n  return {\n    kind: 'Document' as Kind.DOCUMENT,\n    definitions,\n  };\n}\n\ntype ParseOptions = {\n  [option: string]: any;\n};\n\nexport function parse(\n  string: string | Source,\n  options?: ParseOptions | undefined\n): ast.DocumentNode {\n  input = typeof string.body === 'string' ? string.body : string;\n  idx = 0;\n  return document(input, options && options.noLocation);\n}\n\nexport function parseValue(\n  string: string | Source,\n  _options?: ParseOptions | undefined\n): ast.ValueNode {\n  input = typeof string.body === 'string' ? string.body : string;\n  idx = 0;\n  ignored();\n  return value(false);\n}\n\nexport function parseType(\n  string: string | Source,\n  _options?: ParseOptions | undefined\n): ast.TypeNode {\n  input = typeof string.body === 'string' ? string.body : string;\n  idx = 0;\n  return type();\n}\n", "import type { ASTNode } from './ast';\n\nexport const BREAK = {};\n\nexport function visit<N extends ASTNode>(root: N, visitor: ASTVisitor): N;\nexport function visit<R>(root: ASTNode, visitor: ASTReducer<R>): R;\n\nexport function visit(node: ASTNode, visitor: ASTVisitor | ASTReducer<any>) {\n  const ancestors: Array<ASTNode | ReadonlyArray<ASTNode>> = [];\n  const path: Array<string | number> = [];\n\n  function traverse(\n    node: ASTNode,\n    key?: string | number | undefined,\n    parent?: ASTNode | ReadonlyArray<ASTNode> | undefined\n  ) {\n    let hasEdited = false;\n\n    const enter =\n      (visitor[node.kind] && visitor[node.kind].enter) ||\n      visitor[node.kind] ||\n      (visitor as EnterLeaveVisitor<ASTNode>).enter;\n    const resultEnter = enter && enter.call(visitor, node, key, parent, path, ancestors);\n    if (resultEnter === false) {\n      return node;\n    } else if (resultEnter === null) {\n      return null;\n    } else if (resultEnter === BREAK) {\n      throw BREAK;\n    } else if (resultEnter && typeof resultEnter.kind === 'string') {\n      hasEdited = resultEnter !== node;\n      node = resultEnter;\n    }\n\n    if (parent) ancestors.push(parent);\n\n    let result: any;\n    const copy = { ...node };\n    for (const nodeKey in node) {\n      path.push(nodeKey);\n      let value = node[nodeKey];\n      if (Array.isArray(value)) {\n        const newValue: any[] = [];\n        for (let index = 0; index < value.length; index++) {\n          if (value[index] != null && typeof value[index].kind === 'string') {\n            ancestors.push(node);\n            path.push(index);\n            result = traverse(value[index], index, value);\n            path.pop();\n            ancestors.pop();\n            if (result == null) {\n              hasEdited = true;\n            } else {\n              hasEdited = hasEdited || result !== value[index];\n              newValue.push(result);\n            }\n          }\n        }\n        value = newValue;\n      } else if (value != null && typeof value.kind === 'string') {\n        result = traverse(value, nodeKey, node);\n        if (result !== undefined) {\n          hasEdited = hasEdited || value !== result;\n          value = result;\n        }\n      }\n\n      path.pop();\n      if (hasEdited) copy[nodeKey] = value;\n    }\n\n    if (parent) ancestors.pop();\n    const leave =\n      (visitor[node.kind] && visitor[node.kind].leave) ||\n      (visitor as EnterLeaveVisitor<ASTNode>).leave;\n    const resultLeave = leave && leave.call(visitor, node, key, parent, path, ancestors);\n    if (resultLeave === BREAK) {\n      throw BREAK;\n    } else if (resultLeave !== undefined) {\n      return resultLeave;\n    } else if (resultEnter !== undefined) {\n      return hasEdited ? copy : resultEnter;\n    } else {\n      return hasEdited ? copy : node;\n    }\n  }\n\n  try {\n    const result = traverse(node);\n    return result !== undefined && result !== false ? result : node;\n  } catch (error) {\n    if (error !== BREAK) throw error;\n    return node;\n  }\n}\n\nexport type ASTVisitor = EnterLeaveVisitor<ASTNode> | KindVisitor;\n\ntype KindVisitor = {\n  readonly [NodeT in ASTNode as NodeT['kind']]?: ASTVisitFn<NodeT> | EnterLeaveVisitor<NodeT>;\n};\n\ninterface EnterLeaveVisitor<TVisitedNode extends ASTNode> {\n  readonly enter?: ASTVisitFn<TVisitedNode> | undefined;\n  readonly leave?: ASTVisitFn<TVisitedNode> | undefined;\n}\n\nexport type ASTVisitFn<Node extends ASTNode> = (\n  node: Node,\n  key: string | number | undefined,\n  parent: ASTNode | ReadonlyArray<ASTNode> | undefined,\n  path: ReadonlyArray<string | number>,\n  ancestors: ReadonlyArray<ASTNode | ReadonlyArray<ASTNode>>\n) => any;\n\nexport type ASTReducer<R> = {\n  readonly [NodeT in ASTNode as NodeT['kind']]?: {\n    readonly enter?: ASTVisitFn<NodeT>;\n    readonly leave: ASTReducerFn<NodeT, R>;\n  };\n};\n\ntype ASTReducerFn<TReducedNode extends ASTNode, R> = (\n  node: { [K in keyof TReducedNode]: ReducedField<TReducedNode[K], R> },\n  key: string | number | undefined,\n  parent: ASTNode | ReadonlyArray<ASTNode> | undefined,\n  path: ReadonlyArray<string | number>,\n  ancestors: ReadonlyArray<ASTNode | ReadonlyArray<ASTNode>>\n) => R;\n\ntype ReducedField<T, R> = T extends null | undefined\n  ? T\n  : T extends ReadonlyArray<any>\n    ? ReadonlyArray<R>\n    : R;\n", "import type {\n  ASTNode,\n  NameNode,\n  DocumentNode,\n  VariableNode,\n  SelectionSetNode,\n  FieldNode,\n  ArgumentNode,\n  FragmentSpreadNode,\n  InlineFragmentNode,\n  VariableDefinitionNode,\n  OperationDefinitionNode,\n  FragmentDefinitionNode,\n  IntValueNode,\n  FloatValueNode,\n  StringValueNode,\n  BooleanValueNode,\n  NullValueNode,\n  EnumValueNode,\n  ListValueNode,\n  ObjectValueNode,\n  ObjectFieldNode,\n  DirectiveNode,\n  NamedTypeNode,\n  ListTypeNode,\n  NonNullTypeNode,\n} from './ast';\n\nfunction mapJoin<T>(value: readonly T[], joiner: string, mapper: (value: T) => string): string {\n  let out = '';\n  for (let index = 0; index < value.length; index++) {\n    if (index) out += joiner;\n    out += mapper(value[index]);\n  }\n  return out;\n}\n\nfunction printString(string: string): string {\n  return JSON.stringify(string);\n}\n\nfunction printBlockString(string: string): string {\n  return '\"\"\"\\n' + string.replace(/\"\"\"/g, '\\\\\"\"\"') + '\\n\"\"\"';\n}\n\nconst MAX_LINE_LENGTH = 80;\n\nlet LF = '\\n';\n\nconst nodes = {\n  OperationDefinition(node: OperationDefinitionNode): string {\n    let out: string = node.operation;\n    if (node.name) out += ' ' + node.name.value;\n    if (node.variableDefinitions && node.variableDefinitions.length) {\n      if (!node.name) out += ' ';\n      out += '(' + mapJoin(node.variableDefinitions, ', ', nodes.VariableDefinition) + ')';\n    }\n    if (node.directives && node.directives.length)\n      out += ' ' + mapJoin(node.directives, ' ', nodes.Directive);\n    return out !== 'query'\n      ? out + ' ' + nodes.SelectionSet(node.selectionSet)\n      : nodes.SelectionSet(node.selectionSet);\n  },\n  VariableDefinition(node: VariableDefinitionNode): string {\n    let out = nodes.Variable!(node.variable) + ': ' + _print(node.type);\n    if (node.defaultValue) out += ' = ' + _print(node.defaultValue);\n    if (node.directives && node.directives.length)\n      out += ' ' + mapJoin(node.directives, ' ', nodes.Directive);\n    return out;\n  },\n  Field(node: FieldNode): string {\n    let out = node.alias ? node.alias.value + ': ' + node.name.value : node.name.value;\n    if (node.arguments && node.arguments.length) {\n      const args = mapJoin(node.arguments, ', ', nodes.Argument);\n      if (out.length + args.length + 2 > MAX_LINE_LENGTH) {\n        out +=\n          '(' +\n          (LF += '  ') +\n          mapJoin(node.arguments, LF, nodes.Argument) +\n          (LF = LF.slice(0, -2)) +\n          ')';\n      } else {\n        out += '(' + args + ')';\n      }\n    }\n    if (node.directives && node.directives.length)\n      out += ' ' + mapJoin(node.directives, ' ', nodes.Directive);\n    if (node.selectionSet && node.selectionSet.selections.length) {\n      out += ' ' + nodes.SelectionSet(node.selectionSet);\n    }\n    return out;\n  },\n  StringValue(node: StringValueNode): string {\n    if (node.block) {\n      return printBlockString(node.value).replace(/\\n/g, LF);\n    } else {\n      return printString(node.value);\n    }\n  },\n  BooleanValue(node: BooleanValueNode): string {\n    return '' + node.value;\n  },\n  NullValue(_node: NullValueNode): string {\n    return 'null';\n  },\n  IntValue(node: IntValueNode): string {\n    return node.value;\n  },\n  FloatValue(node: FloatValueNode): string {\n    return node.value;\n  },\n  EnumValue(node: EnumValueNode): string {\n    return node.value;\n  },\n  Name(node: NameNode): string {\n    return node.value;\n  },\n  Variable(node: VariableNode): string {\n    return '$' + node.name.value;\n  },\n  ListValue(node: ListValueNode): string {\n    return '[' + mapJoin(node.values, ', ', _print) + ']';\n  },\n  ObjectValue(node: ObjectValueNode): string {\n    return '{' + mapJoin(node.fields, ', ', nodes.ObjectField) + '}';\n  },\n  ObjectField(node: ObjectFieldNode): string {\n    return node.name.value + ': ' + _print(node.value);\n  },\n  Document(node: DocumentNode): string {\n    if (!node.definitions || !node.definitions.length) return '';\n    return mapJoin(node.definitions, '\\n\\n', _print);\n  },\n  SelectionSet(node: SelectionSetNode): string {\n    return '{' + (LF += '  ') + mapJoin(node.selections, LF, _print) + (LF = LF.slice(0, -2)) + '}';\n  },\n  Argument(node: ArgumentNode): string {\n    return node.name.value + ': ' + _print(node.value);\n  },\n  FragmentSpread(node: FragmentSpreadNode): string {\n    let out = '...' + node.name.value;\n    if (node.directives && node.directives.length)\n      out += ' ' + mapJoin(node.directives, ' ', nodes.Directive);\n    return out;\n  },\n  InlineFragment(node: InlineFragmentNode): string {\n    let out = '...';\n    if (node.typeCondition) out += ' on ' + node.typeCondition.name.value;\n    if (node.directives && node.directives.length)\n      out += ' ' + mapJoin(node.directives, ' ', nodes.Directive);\n    out += ' ' + nodes.SelectionSet(node.selectionSet);\n    return out;\n  },\n  FragmentDefinition(node: FragmentDefinitionNode): string {\n    let out = 'fragment ' + node.name.value;\n    out += ' on ' + node.typeCondition.name.value;\n    if (node.directives && node.directives.length)\n      out += ' ' + mapJoin(node.directives, ' ', nodes.Directive);\n    return out + ' ' + nodes.SelectionSet(node.selectionSet);\n  },\n  Directive(node: DirectiveNode): string {\n    let out = '@' + node.name.value;\n    if (node.arguments && node.arguments.length)\n      out += '(' + mapJoin(node.arguments, ', ', nodes.Argument) + ')';\n    return out;\n  },\n  NamedType(node: NamedTypeNode): string {\n    return node.name.value;\n  },\n  ListType(node: ListTypeNode): string {\n    return '[' + _print(node.type) + ']';\n  },\n  NonNullType(node: NonNullTypeNode): string {\n    return _print(node.type) + '!';\n  },\n} as const;\n\nconst _print = (node: ASTNode): string => nodes[node.kind](node);\n\nfunction print(node: ASTNode): string {\n  LF = '\\n';\n  return nodes[node.kind] ? nodes[node.kind](node) : '';\n}\n\nexport { print, printString, printBlockString };\n", "import type { TypeNode, ValueNode } from './ast';\nimport type { Maybe } from './types';\n\nexport function valueFromASTUntyped(\n  node: ValueNode,\n  variables?: Maybe<Record<string, any>>\n): unknown {\n  switch (node.kind) {\n    case 'NullValue':\n      return null;\n    case 'IntValue':\n      return parseInt(node.value, 10);\n    case 'FloatValue':\n      return parseFloat(node.value);\n    case 'StringValue':\n    case 'EnumValue':\n    case 'BooleanValue':\n      return node.value;\n    case 'ListValue': {\n      const values: unknown[] = [];\n      for (let i = 0, l = node.values.length; i < l; i++)\n        values.push(valueFromASTUntyped(node.values[i], variables));\n      return values;\n    }\n    case 'ObjectValue': {\n      const obj = Object.create(null);\n      for (let i = 0, l = node.fields.length; i < l; i++) {\n        const field = node.fields[i];\n        obj[field.name.value] = valueFromASTUntyped(field.value, variables);\n      }\n      return obj;\n    }\n    case 'Variable':\n      return variables && variables[node.name.value];\n  }\n}\n\nexport function valueFromTypeNode(\n  node: ValueNode,\n  type: TypeNode,\n  variables?: Maybe<Record<string, any>>\n): unknown {\n  if (node.kind === 'Variable') {\n    const variableName = node.name.value;\n    return variables ? valueFromTypeNode(variables[variableName], type, variables) : undefined;\n  } else if (type.kind === 'NonNullType') {\n    return node.kind !== 'NullValue' ? valueFromTypeNode(node, type, variables) : undefined;\n  } else if (node.kind === 'NullValue') {\n    return null;\n  } else if (type.kind === 'ListType') {\n    if (node.kind === 'ListValue') {\n      const values: unknown[] = [];\n      for (let i = 0, l = node.values.length; i < l; i++) {\n        const value = node.values[i];\n        const coerced = valueFromTypeNode(value, type.type, variables);\n        if (coerced === undefined) {\n          return undefined;\n        } else {\n          values.push(coerced);\n        }\n      }\n      return values;\n    }\n  } else if (type.kind === 'NamedType') {\n    switch (type.name.value) {\n      case 'Int':\n      case 'Float':\n      case 'String':\n      case 'Bool':\n        return type.name.value + 'Value' === node.kind\n          ? valueFromASTUntyped(node, variables)\n          : undefined;\n      default:\n        return valueFromASTUntyped(node, variables);\n    }\n  }\n}\n", "import type { Location, Source as _Source } from './types';\nimport type { ASTNode, SelectionNode } from './ast';\n\nexport function isSelectionNode(node: ASTNode): node is SelectionNode {\n  return node.kind === 'Field' || node.kind === 'FragmentSpread' || node.kind === 'InlineFragment';\n}\n\nexport function Source(body: string, name?: string, locationOffset?: Location): _Source {\n  return {\n    body,\n    name,\n    locationOffset: locationOffset || { line: 1, column: 1 },\n  };\n}\n", "var teardownPlaceholder = () => {};\n\nvar e = teardownPlaceholder;\n\nfunction start(e) {\n  return {\n    tag: 0,\n    0: e\n  };\n}\n\nfunction push(e) {\n  return {\n    tag: 1,\n    0: e\n  };\n}\n\nvar asyncIteratorSymbol = () => \"function\" == typeof Symbol && Symbol.asyncIterator || \"@@asyncIterator\";\n\nvar observableSymbol = () => \"function\" == typeof Symbol && Symbol.observable || \"@@observable\";\n\nvar identity = e => e;\n\nfunction buffer(r) {\n  return t => i => {\n    var a = [];\n    var f = e;\n    var n = e;\n    var s = !1;\n    var l = !1;\n    t((e => {\n      if (l) {} else if (0 === e) {\n        l = !0;\n        n(1);\n        if (a.length) {\n          i(push(a));\n        }\n        i(0);\n      } else if (0 === e.tag) {\n        f = e[0];\n        r((e => {\n          if (l) {} else if (0 === e) {\n            l = !0;\n            f(1);\n            if (a.length) {\n              i(push(a));\n            }\n            i(0);\n          } else if (0 === e.tag) {\n            n = e[0];\n          } else if (a.length) {\n            var r = push(a);\n            a = [];\n            i(r);\n          }\n        }));\n      } else {\n        a.push(e[0]);\n        if (!s) {\n          s = !0;\n          f(0);\n          n(0);\n        } else {\n          s = !1;\n        }\n      }\n    }));\n    i(start((e => {\n      if (1 === e && !l) {\n        l = !0;\n        f(1);\n        n(1);\n      } else if (!l && !s) {\n        s = !0;\n        f(0);\n        n(0);\n      }\n    })));\n  };\n}\n\nfunction concatMap(r) {\n  return t => i => {\n    var a = [];\n    var f = e;\n    var n = e;\n    var s = !1;\n    var l = !1;\n    var u = !1;\n    var o = !1;\n    function applyInnerSource(e) {\n      u = !0;\n      e((e => {\n        if (0 === e) {\n          if (u) {\n            u = !1;\n            if (a.length) {\n              applyInnerSource(r(a.shift()));\n            } else if (o) {\n              i(0);\n            } else if (!s) {\n              s = !0;\n              f(0);\n            }\n          }\n        } else if (0 === e.tag) {\n          l = !1;\n          (n = e[0])(0);\n        } else if (u) {\n          i(e);\n          if (l) {\n            l = !1;\n          } else {\n            n(0);\n          }\n        }\n      }));\n    }\n    t((e => {\n      if (o) {} else if (0 === e) {\n        o = !0;\n        if (!u && !a.length) {\n          i(0);\n        }\n      } else if (0 === e.tag) {\n        f = e[0];\n      } else {\n        s = !1;\n        if (u) {\n          a.push(e[0]);\n        } else {\n          applyInnerSource(r(e[0]));\n        }\n      }\n    }));\n    i(start((e => {\n      if (1 === e) {\n        if (!o) {\n          o = !0;\n          f(1);\n        }\n        if (u) {\n          u = !1;\n          n(1);\n        }\n      } else {\n        if (!o && !s) {\n          s = !0;\n          f(0);\n        }\n        if (u && !l) {\n          l = !0;\n          n(0);\n        }\n      }\n    })));\n  };\n}\n\nfunction concatAll(e) {\n  return concatMap(identity)(e);\n}\n\nfunction concat(e) {\n  return concatAll(r(e));\n}\n\nfunction filter(r) {\n  return t => i => {\n    var a = e;\n    t((e => {\n      if (0 === e) {\n        i(0);\n      } else if (0 === e.tag) {\n        a = e[0];\n        i(e);\n      } else if (!r(e[0])) {\n        a(0);\n      } else {\n        i(e);\n      }\n    }));\n  };\n}\n\nfunction map(e) {\n  return r => t => r((r => {\n    if (0 === r || 0 === r.tag) {\n      t(r);\n    } else {\n      t(push(e(r[0])));\n    }\n  }));\n}\n\nfunction mergeMap(r) {\n  return t => i => {\n    var a = [];\n    var f = e;\n    var n = !1;\n    var s = !1;\n    t((t => {\n      if (s) {} else if (0 === t) {\n        s = !0;\n        if (!a.length) {\n          i(0);\n        }\n      } else if (0 === t.tag) {\n        f = t[0];\n      } else {\n        n = !1;\n        !function applyInnerSource(r) {\n          var t = e;\n          r((e => {\n            if (0 === e) {\n              if (a.length) {\n                var r = a.indexOf(t);\n                if (r > -1) {\n                  (a = a.slice()).splice(r, 1);\n                }\n                if (!a.length) {\n                  if (s) {\n                    i(0);\n                  } else if (!n) {\n                    n = !0;\n                    f(0);\n                  }\n                }\n              }\n            } else if (0 === e.tag) {\n              a.push(t = e[0]);\n              t(0);\n            } else if (a.length) {\n              i(e);\n              t(0);\n            }\n          }));\n        }(r(t[0]));\n        if (!n) {\n          n = !0;\n          f(0);\n        }\n      }\n    }));\n    i(start((e => {\n      if (1 === e) {\n        if (!s) {\n          s = !0;\n          f(1);\n        }\n        for (var r = 0, t = a, i = a.length; r < i; r++) {\n          t[r](1);\n        }\n        a.length = 0;\n      } else {\n        if (!s && !n) {\n          n = !0;\n          f(0);\n        } else {\n          n = !1;\n        }\n        for (var l = 0, u = a, o = a.length; l < o; l++) {\n          u[l](0);\n        }\n      }\n    })));\n  };\n}\n\nfunction mergeAll(e) {\n  return mergeMap(identity)(e);\n}\n\nfunction merge(e) {\n  return mergeAll(r(e));\n}\n\nfunction onEnd(e) {\n  return r => t => {\n    var i = !1;\n    r((r => {\n      if (i) {} else if (0 === r) {\n        i = !0;\n        t(0);\n        e();\n      } else if (0 === r.tag) {\n        var a = r[0];\n        t(start((r => {\n          if (1 === r) {\n            i = !0;\n            a(1);\n            e();\n          } else {\n            a(r);\n          }\n        })));\n      } else {\n        t(r);\n      }\n    }));\n  };\n}\n\nfunction onPush(e) {\n  return r => t => {\n    var i = !1;\n    r((r => {\n      if (i) {} else if (0 === r) {\n        i = !0;\n        t(0);\n      } else if (0 === r.tag) {\n        var a = r[0];\n        t(start((e => {\n          if (1 === e) {\n            i = !0;\n          }\n          a(e);\n        })));\n      } else {\n        e(r[0]);\n        t(r);\n      }\n    }));\n  };\n}\n\nfunction onStart(e) {\n  return r => t => r((r => {\n    if (0 === r) {\n      t(0);\n    } else if (0 === r.tag) {\n      t(r);\n      e();\n    } else {\n      t(r);\n    }\n  }));\n}\n\nfunction sample(r) {\n  return t => i => {\n    var a = e;\n    var f = e;\n    var n;\n    var s = !1;\n    var l = !1;\n    t((e => {\n      if (l) {} else if (0 === e) {\n        l = !0;\n        f(1);\n        i(0);\n      } else if (0 === e.tag) {\n        a = e[0];\n      } else {\n        n = e[0];\n        if (!s) {\n          s = !0;\n          f(0);\n          a(0);\n        } else {\n          s = !1;\n        }\n      }\n    }));\n    r((e => {\n      if (l) {} else if (0 === e) {\n        l = !0;\n        a(1);\n        i(0);\n      } else if (0 === e.tag) {\n        f = e[0];\n      } else if (void 0 !== n) {\n        var r = push(n);\n        n = void 0;\n        i(r);\n      }\n    }));\n    i(start((e => {\n      if (1 === e && !l) {\n        l = !0;\n        a(1);\n        f(1);\n      } else if (!l && !s) {\n        s = !0;\n        a(0);\n        f(0);\n      }\n    })));\n  };\n}\n\nfunction scan(e, r) {\n  return t => i => {\n    var a = r;\n    t((r => {\n      if (0 === r) {\n        i(0);\n      } else if (0 === r.tag) {\n        i(r);\n      } else {\n        i(push(a = e(a, r[0])));\n      }\n    }));\n  };\n}\n\nfunction share(r) {\n  var t = [];\n  var i = e;\n  var a = !1;\n  return e => {\n    t.push(e);\n    if (1 === t.length) {\n      r((e => {\n        if (0 === e) {\n          for (var r = 0, f = t, n = t.length; r < n; r++) {\n            f[r](0);\n          }\n          t.length = 0;\n        } else if (0 === e.tag) {\n          i = e[0];\n        } else {\n          a = !1;\n          for (var s = 0, l = t, u = t.length; s < u; s++) {\n            l[s](e);\n          }\n        }\n      }));\n    }\n    e(start((r => {\n      if (1 === r) {\n        var f = t.indexOf(e);\n        if (f > -1) {\n          (t = t.slice()).splice(f, 1);\n        }\n        if (!t.length) {\n          i(1);\n        }\n      } else if (!a) {\n        a = !0;\n        i(0);\n      }\n    })));\n  };\n}\n\nfunction skip(r) {\n  return t => i => {\n    var a = e;\n    var f = r;\n    t((e => {\n      if (0 === e) {\n        i(0);\n      } else if (0 === e.tag) {\n        a = e[0];\n        i(e);\n      } else if (f-- > 0) {\n        a(0);\n      } else {\n        i(e);\n      }\n    }));\n  };\n}\n\nfunction skipUntil(r) {\n  return t => i => {\n    var a = e;\n    var f = e;\n    var n = !0;\n    var s = !1;\n    var l = !1;\n    t((e => {\n      if (l) {} else if (0 === e) {\n        l = !0;\n        if (n) {\n          f(1);\n        }\n        i(0);\n      } else if (0 === e.tag) {\n        a = e[0];\n        r((e => {\n          if (0 === e) {\n            if (n) {\n              l = !0;\n              a(1);\n            }\n          } else if (0 === e.tag) {\n            (f = e[0])(0);\n          } else {\n            n = !1;\n            f(1);\n          }\n        }));\n      } else if (!n) {\n        s = !1;\n        i(e);\n      } else if (!s) {\n        s = !0;\n        a(0);\n        f(0);\n      } else {\n        s = !1;\n      }\n    }));\n    i(start((e => {\n      if (1 === e && !l) {\n        l = !0;\n        a(1);\n        if (n) {\n          f(1);\n        }\n      } else if (!l && !s) {\n        s = !0;\n        if (n) {\n          f(0);\n        }\n        a(0);\n      }\n    })));\n  };\n}\n\nfunction skipWhile(r) {\n  return t => i => {\n    var a = e;\n    var f = !0;\n    t((e => {\n      if (0 === e) {\n        i(0);\n      } else if (0 === e.tag) {\n        a = e[0];\n        i(e);\n      } else if (f) {\n        if (r(e[0])) {\n          a(0);\n        } else {\n          f = !1;\n          i(e);\n        }\n      } else {\n        i(e);\n      }\n    }));\n  };\n}\n\nfunction switchMap(r) {\n  return t => i => {\n    var a = e;\n    var f = e;\n    var n = !1;\n    var s = !1;\n    var l = !1;\n    var u = !1;\n    t((t => {\n      if (u) {} else if (0 === t) {\n        u = !0;\n        if (!l) {\n          i(0);\n        }\n      } else if (0 === t.tag) {\n        a = t[0];\n      } else {\n        if (l) {\n          f(1);\n          f = e;\n        }\n        if (!n) {\n          n = !0;\n          a(0);\n        } else {\n          n = !1;\n        }\n        !function applyInnerSource(e) {\n          l = !0;\n          e((e => {\n            if (!l) {} else if (0 === e) {\n              l = !1;\n              if (u) {\n                i(0);\n              } else if (!n) {\n                n = !0;\n                a(0);\n              }\n            } else if (0 === e.tag) {\n              s = !1;\n              (f = e[0])(0);\n            } else {\n              i(e);\n              if (!s) {\n                f(0);\n              } else {\n                s = !1;\n              }\n            }\n          }));\n        }(r(t[0]));\n      }\n    }));\n    i(start((e => {\n      if (1 === e) {\n        if (!u) {\n          u = !0;\n          a(1);\n        }\n        if (l) {\n          l = !1;\n          f(1);\n        }\n      } else {\n        if (!u && !n) {\n          n = !0;\n          a(0);\n        }\n        if (l && !s) {\n          s = !0;\n          f(0);\n        }\n      }\n    })));\n  };\n}\n\nfunction switchAll(e) {\n  return switchMap(identity)(e);\n}\n\nfunction take(r) {\n  return t => i => {\n    var a = e;\n    var f = !1;\n    var n = 0;\n    t((e => {\n      if (f) {} else if (0 === e) {\n        f = !0;\n        i(0);\n      } else if (0 === e.tag) {\n        if (r <= 0) {\n          f = !0;\n          i(0);\n          e[0](1);\n        } else {\n          a = e[0];\n        }\n      } else if (n++ < r) {\n        i(e);\n        if (!f && n >= r) {\n          f = !0;\n          i(0);\n          a(1);\n        }\n      } else {\n        i(e);\n      }\n    }));\n    i(start((e => {\n      if (1 === e && !f) {\n        f = !0;\n        a(1);\n      } else if (0 === e && !f && n < r) {\n        a(0);\n      }\n    })));\n  };\n}\n\nfunction takeLast(t) {\n  return i => a => {\n    var f = [];\n    var n = e;\n    i((e => {\n      if (0 === e) {\n        r(f)(a);\n      } else if (0 === e.tag) {\n        if (t <= 0) {\n          e[0](1);\n          r(f)(a);\n        } else {\n          (n = e[0])(0);\n        }\n      } else {\n        if (f.length >= t && t) {\n          f.shift();\n        }\n        f.push(e[0]);\n        n(0);\n      }\n    }));\n  };\n}\n\nfunction takeUntil(r) {\n  return t => i => {\n    var a = e;\n    var f = e;\n    var n = !1;\n    t((e => {\n      if (n) {} else if (0 === e) {\n        n = !0;\n        f(1);\n        i(0);\n      } else if (0 === e.tag) {\n        a = e[0];\n        r((e => {\n          if (0 === e) {} else if (0 === e.tag) {\n            (f = e[0])(0);\n          } else {\n            n = !0;\n            f(1);\n            a(1);\n            i(0);\n          }\n        }));\n      } else {\n        i(e);\n      }\n    }));\n    i(start((e => {\n      if (1 === e && !n) {\n        n = !0;\n        a(1);\n        f(1);\n      } else if (!n) {\n        a(0);\n      }\n    })));\n  };\n}\n\nfunction takeWhile(r, t) {\n  return i => a => {\n    var f = e;\n    var n = !1;\n    i((e => {\n      if (n) {} else if (0 === e) {\n        n = !0;\n        a(0);\n      } else if (0 === e.tag) {\n        f = e[0];\n        a(e);\n      } else if (!r(e[0])) {\n        n = !0;\n        if (t) {\n          a(e);\n        }\n        a(0);\n        f(1);\n      } else {\n        a(e);\n      }\n    }));\n  };\n}\n\nfunction debounce(e) {\n  return r => t => {\n    var i;\n    var a = !1;\n    var f = !1;\n    r((r => {\n      if (f) {} else if (0 === r) {\n        f = !0;\n        if (i) {\n          a = !0;\n        } else {\n          t(0);\n        }\n      } else if (0 === r.tag) {\n        var n = r[0];\n        t(start((e => {\n          if (1 === e && !f) {\n            f = !0;\n            a = !1;\n            if (i) {\n              clearTimeout(i);\n            }\n            n(1);\n          } else if (!f) {\n            n(0);\n          }\n        })));\n      } else {\n        if (i) {\n          clearTimeout(i);\n        }\n        i = setTimeout((() => {\n          i = void 0;\n          t(r);\n          if (a) {\n            t(0);\n          }\n        }), e(r[0]));\n      }\n    }));\n  };\n}\n\nfunction delay(e) {\n  return r => t => {\n    var i = 0;\n    r((r => {\n      if (0 !== r && 0 === r.tag) {\n        t(r);\n      } else {\n        i++;\n        setTimeout((() => {\n          if (i) {\n            i--;\n            t(r);\n          }\n        }), e);\n      }\n    }));\n  };\n}\n\nfunction throttle(e) {\n  return r => t => {\n    var i = !1;\n    var a;\n    r((r => {\n      if (0 === r) {\n        if (a) {\n          clearTimeout(a);\n        }\n        t(0);\n      } else if (0 === r.tag) {\n        var f = r[0];\n        t(start((e => {\n          if (1 === e) {\n            if (a) {\n              clearTimeout(a);\n            }\n            f(1);\n          } else {\n            f(0);\n          }\n        })));\n      } else if (!i) {\n        i = !0;\n        if (a) {\n          clearTimeout(a);\n        }\n        a = setTimeout((() => {\n          a = void 0;\n          i = !1;\n        }), e(r[0]));\n        t(r);\n      }\n    }));\n  };\n}\n\nfunction lazy(e) {\n  return r => e()(r);\n}\n\nfunction fromAsyncIterable(e) {\n  return r => {\n    var t = e[asyncIteratorSymbol()] && e[asyncIteratorSymbol()]() || e;\n    var i = !1;\n    var a = !1;\n    var f = !1;\n    var n;\n    r(start((async e => {\n      if (1 === e) {\n        i = !0;\n        if (t.return) {\n          t.return();\n        }\n      } else if (a) {\n        f = !0;\n      } else {\n        for (f = a = !0; f && !i; ) {\n          if ((n = await t.next()).done) {\n            i = !0;\n            if (t.return) {\n              await t.return();\n            }\n            r(0);\n          } else {\n            try {\n              f = !1;\n              r(push(n.value));\n            } catch (e) {\n              if (t.throw) {\n                if (i = !!(await t.throw(e)).done) {\n                  r(0);\n                }\n              } else {\n                throw e;\n              }\n            }\n          }\n        }\n        a = !1;\n      }\n    })));\n  };\n}\n\nfunction fromIterable(e) {\n  if (e[Symbol.asyncIterator]) {\n    return fromAsyncIterable(e);\n  }\n  return r => {\n    var t = e[Symbol.iterator]();\n    var i = !1;\n    var a = !1;\n    var f = !1;\n    var n;\n    r(start((e => {\n      if (1 === e) {\n        i = !0;\n        if (t.return) {\n          t.return();\n        }\n      } else if (a) {\n        f = !0;\n      } else {\n        for (f = a = !0; f && !i; ) {\n          if ((n = t.next()).done) {\n            i = !0;\n            if (t.return) {\n              t.return();\n            }\n            r(0);\n          } else {\n            try {\n              f = !1;\n              r(push(n.value));\n            } catch (e) {\n              if (t.throw) {\n                if (i = !!t.throw(e).done) {\n                  r(0);\n                }\n              } else {\n                throw e;\n              }\n            }\n          }\n        }\n        a = !1;\n      }\n    })));\n  };\n}\n\nvar r = fromIterable;\n\nfunction fromValue(e) {\n  return r => {\n    var t = !1;\n    r(start((i => {\n      if (1 === i) {\n        t = !0;\n      } else if (!t) {\n        t = !0;\n        r(push(e));\n        r(0);\n      }\n    })));\n  };\n}\n\nfunction make(e) {\n  return r => {\n    var t = !1;\n    var i = e({\n      next(e) {\n        if (!t) {\n          r(push(e));\n        }\n      },\n      complete() {\n        if (!t) {\n          t = !0;\n          r(0);\n        }\n      }\n    });\n    r(start((e => {\n      if (1 === e && !t) {\n        t = !0;\n        i();\n      }\n    })));\n  };\n}\n\nfunction makeSubject() {\n  var e;\n  var r;\n  return {\n    source: share(make((t => {\n      e = t.next;\n      r = t.complete;\n      return teardownPlaceholder;\n    }))),\n    next(r) {\n      if (e) {\n        e(r);\n      }\n    },\n    complete() {\n      if (r) {\n        r();\n      }\n    }\n  };\n}\n\nvar empty = e => {\n  var r = !1;\n  e(start((t => {\n    if (1 === t) {\n      r = !0;\n    } else if (!r) {\n      r = !0;\n      e(0);\n    }\n  })));\n};\n\nvar never = r => {\n  r(start(e));\n};\n\nfunction interval(e) {\n  return make((r => {\n    var t = 0;\n    var i = setInterval((() => r.next(t++)), e);\n    return () => clearInterval(i);\n  }));\n}\n\nfunction fromDomEvent(e, r) {\n  return make((t => {\n    e.addEventListener(r, t.next);\n    return () => e.removeEventListener(r, t.next);\n  }));\n}\n\nfunction fromPromise(e) {\n  return make((r => {\n    e.then((e => {\n      Promise.resolve(e).then((() => {\n        r.next(e);\n        r.complete();\n      }));\n    }));\n    return teardownPlaceholder;\n  }));\n}\n\nfunction subscribe(r) {\n  return t => {\n    var i = e;\n    var a = !1;\n    t((e => {\n      if (0 === e) {\n        a = !0;\n      } else if (0 === e.tag) {\n        (i = e[0])(0);\n      } else if (!a) {\n        r(e[0]);\n        i(0);\n      }\n    }));\n    return {\n      unsubscribe() {\n        if (!a) {\n          a = !0;\n          i(1);\n        }\n      }\n    };\n  };\n}\n\nfunction forEach(e) {\n  return r => {\n    subscribe(e)(r);\n  };\n}\n\nfunction publish(e) {\n  subscribe((e => {}))(e);\n}\n\nvar t = {\n  done: !0\n};\n\nvar toAsyncIterable = r => {\n  var i = [];\n  var a = !1;\n  var f = !1;\n  var n = !1;\n  var s = e;\n  var l;\n  return {\n    async next() {\n      if (!f) {\n        f = !0;\n        r((e => {\n          if (a) {} else if (0 === e) {\n            if (l) {\n              l = l(t);\n            }\n            a = !0;\n          } else if (0 === e.tag) {\n            n = !0;\n            (s = e[0])(0);\n          } else {\n            n = !1;\n            if (l) {\n              l = l({\n                value: e[0],\n                done: !1\n              });\n            } else {\n              i.push(e[0]);\n            }\n          }\n        }));\n      }\n      if (a && !i.length) {\n        return t;\n      } else if (!a && !n && i.length <= 1) {\n        n = !0;\n        s(0);\n      }\n      return i.length ? {\n        value: i.shift(),\n        done: !1\n      } : new Promise((e => l = e));\n    },\n    async return() {\n      if (!a) {\n        l = s(1);\n      }\n      a = !0;\n      return t;\n    },\n    [asyncIteratorSymbol()]() {\n      return this;\n    }\n  };\n};\n\nfunction toArray(r) {\n  var t = [];\n  var i = e;\n  var a = !1;\n  r((e => {\n    if (0 === e) {\n      a = !0;\n    } else if (0 === e.tag) {\n      (i = e[0])(0);\n    } else {\n      t.push(e[0]);\n      i(0);\n    }\n  }));\n  if (!a) {\n    i(1);\n  }\n  return t;\n}\n\nfunction toPromise(r) {\n  return new Promise((t => {\n    var i = e;\n    var a;\n    r((e => {\n      if (0 === e) {\n        Promise.resolve(a).then(t);\n      } else if (0 === e.tag) {\n        (i = e[0])(0);\n      } else {\n        a = e[0];\n        i(0);\n      }\n    }));\n  }));\n}\n\nfunction zip(r) {\n  var t = Object.keys(r).length;\n  return i => {\n    var a = new Set;\n    var f = Array.isArray(r) ? new Array(t).fill(e) : {};\n    var n = Array.isArray(r) ? new Array(t) : {};\n    var s = !1;\n    var l = !1;\n    var u = !1;\n    var o = 0;\n    var loop = function(v) {\n      r[v]((c => {\n        if (0 === c) {\n          if (o >= t - 1) {\n            u = !0;\n            i(0);\n          } else {\n            o++;\n          }\n        } else if (0 === c.tag) {\n          f[v] = c[0];\n        } else if (!u) {\n          n[v] = c[0];\n          a.add(v);\n          if (!s && a.size < t) {\n            if (!l) {\n              for (var h in r) {\n                if (!a.has(h)) {\n                  (f[h] || e)(0);\n                }\n              }\n            } else {\n              l = !1;\n            }\n          } else {\n            s = !0;\n            l = !1;\n            i(push(Array.isArray(n) ? n.slice() : {\n              ...n\n            }));\n          }\n        }\n      }));\n    };\n    for (var v in r) {\n      loop(v);\n    }\n    i(start((e => {\n      if (u) {} else if (1 === e) {\n        u = !0;\n        for (var r in f) {\n          f[r](1);\n        }\n      } else if (!l) {\n        l = !0;\n        for (var t in f) {\n          f[t](0);\n        }\n      }\n    })));\n  };\n}\n\nfunction combine(...e) {\n  return zip(e);\n}\n\nfunction fromObservable(e) {\n  return r => {\n    var t = (e[observableSymbol()] ? e[observableSymbol()]() : e).subscribe({\n      next(e) {\n        r(push(e));\n      },\n      complete() {\n        r(0);\n      },\n      error(e) {\n        throw e;\n      }\n    });\n    r(start((e => {\n      if (1 === e) {\n        t.unsubscribe();\n      }\n    })));\n  };\n}\n\nfunction toObservable(r) {\n  return {\n    subscribe(t, i, a) {\n      var f = \"object\" == typeof t ? t : {\n        next: t,\n        error: i,\n        complete: a\n      };\n      var n = e;\n      var s = !1;\n      r((e => {\n        if (s) {} else if (0 === e) {\n          s = !0;\n          if (f.complete) {\n            f.complete();\n          }\n        } else if (0 === e.tag) {\n          (n = e[0])(0);\n        } else {\n          f.next(e[0]);\n          n(0);\n        }\n      }));\n      var l = {\n        closed: !1,\n        unsubscribe() {\n          l.closed = !0;\n          s = !0;\n          n(1);\n        }\n      };\n      return l;\n    },\n    [observableSymbol()]() {\n      return this;\n    }\n  };\n}\n\nfunction fromCallbag(e) {\n  return r => {\n    e(0, ((e, t) => {\n      if (0 === e) {\n        r(start((e => {\n          t(e + 1);\n        })));\n      } else if (1 === e) {\n        r(push(t));\n      } else {\n        r(0);\n      }\n    }));\n  };\n}\n\nfunction toCallbag(e) {\n  return (r, t) => {\n    if (0 === r) {\n      e((e => {\n        if (0 === e) {\n          t(2);\n        } else if (0 === e.tag) {\n          t(0, (r => {\n            if (r < 3) {\n              e[0](r - 1);\n            }\n          }));\n        } else {\n          t(1, e[0]);\n        }\n      }));\n    }\n  };\n}\n\nvar pipe = (...e) => {\n  var r = e[0];\n  for (var t = 1, i = e.length; t < i; t++) {\n    r = e[t](r);\n  }\n  return r;\n};\n\nexport { buffer, combine, concat, concatAll, concatMap, debounce, delay, empty, filter, mergeAll as flatten, forEach, r as fromArray, fromAsyncIterable, fromCallbag, fromDomEvent, fromIterable, fromObservable, fromPromise, fromValue, interval, lazy, make, makeSubject, map, merge, mergeAll, mergeMap, never, onEnd, onPush, onStart, pipe, publish, sample, scan, share, skip, skipUntil, skipWhile, subscribe, switchAll, switchMap, take, takeLast, takeUntil, takeWhile, onPush as tap, throttle, toArray, toAsyncIterable, toCallbag, toObservable, toPromise, zip };\n//# sourceMappingURL=wonka.mjs.map\n", "import { GraphQLError } from '@0no-co/graphql.web';\nimport type { ErrorLike } from '../types';\n\nconst generateErrorMessage = (\n  networkErr?: Error,\n  graphQlErrs?: GraphQLError[]\n) => {\n  let error = '';\n  if (networkErr) return `[Network] ${networkErr.message}`;\n  if (graphQlErrs) {\n    for (let i = 0, l = graphQlErrs.length; i < l; i++) {\n      if (error) error += '\\n';\n      error += `[GraphQL] ${graphQlErrs[i].message}`;\n    }\n  }\n  return error;\n};\n\nconst rehydrateGraphQlError = (error: any): GraphQLError => {\n  if (\n    error &&\n    typeof error.message === 'string' &&\n    (error.extensions || error.name === 'GraphQLError')\n  ) {\n    return error;\n  } else if (typeof error === 'object' && typeof error.message === 'string') {\n    return new GraphQLError(\n      error.message,\n      error.nodes,\n      error.source,\n      error.positions,\n      error.path,\n      error,\n      error.extensions || {}\n    );\n  } else {\n    return new GraphQLError(error as any);\n  }\n};\n\n/** An abstracted `Error` that provides either a `networkError` or `graphQLErrors`.\n *\n * @remarks\n * During a GraphQL request, either the request can fail entirely, causing a network error,\n * or the GraphQL execution or fields can fail, which will cause an {@link ExecutionResult}\n * to contain an array of GraphQL errors.\n *\n * The `CombinedError` abstracts and normalizes both failure cases. When {@link OperationResult.error}\n * is set to this error, the `CombinedError` abstracts all errors, making it easier to handle only\n * a subset of error cases.\n *\n * @see {@link https://urql.dev/goto/docs/basics/errors} for more information on handling\n * GraphQL errors and the `CombinedError`.\n */\nexport class CombinedError extends Error {\n  public name: string;\n  public message: string;\n\n  /** A list of GraphQL errors rehydrated from a {@link ExecutionResult}.\n   *\n   * @remarks\n   * If an {@link ExecutionResult} received from the API contains a list of errors,\n   * the `CombinedError` will rehydrate them, normalize them to\n   * {@link GraphQLError | GraphQLErrors} and list them here.\n   * An empty list indicates that no GraphQL error has been sent by the API.\n   */\n  public graphQLErrors: GraphQLError[];\n\n  /** Set to an error, if a GraphQL request has failed outright.\n   *\n   * @remarks\n   * A GraphQL over HTTP request may fail and not reach the API. Any error that\n   * prevents a GraphQl request outright, will be considered a “network error” and\n   * set here.\n   */\n  public networkError?: Error;\n\n  /** Set to the {@link Response} object a fetch exchange received.\n   *\n   * @remarks\n   * If a built-in fetch {@link Exchange} is used in `urql`, this may\n   * be set to the {@link Response} object of the Fetch API response.\n   * However, since `urql` doesn’t assume that all users will use HTTP\n   * as the only or exclusive transport for GraphQL this property is\n   * neither typed nor guaranteed and may be re-used for other purposes\n   * by non-fetch exchanges.\n   *\n   * Hint: It can be useful to use `response.status` here, however, if\n   * you plan on relying on this being a {@link Response} in your app,\n   * which it is by default, then make sure you add some extra checks\n   * before blindly assuming so!\n   */\n  public response?: any;\n\n  constructor(input: {\n    networkError?: Error;\n    graphQLErrors?: Array<string | ErrorLike>;\n    response?: any;\n  }) {\n    const normalizedGraphQLErrors = (input.graphQLErrors || []).map(\n      rehydrateGraphQlError\n    );\n    const message = generateErrorMessage(\n      input.networkError,\n      normalizedGraphQLErrors\n    );\n\n    super(message);\n\n    this.name = 'CombinedError';\n    this.message = message;\n    this.graphQLErrors = normalizedGraphQLErrors;\n    this.networkError = input.networkError;\n    this.response = input.response;\n  }\n\n  toString(): string {\n    return this.message;\n  }\n}\n", "/** A hash value as computed by {@link phash}.\n *\n * @remarks\n * Typically `HashValue`s are used as hashes and keys of GraphQL documents,\n * variables, and combined, for GraphQL requests.\n */\nexport type HashValue = number & {\n  /** Marker to indicate that a `HashValue` may not be created by a user.\n   *\n   * @remarks\n   * `HashValue`s are created by {@link phash} and are marked as such to not mix them\n   * up with other numbers and prevent them from being created or used outside of this\n   * hashing function.\n   *\n   * @internal\n   */\n  readonly _opaque: unique symbol;\n};\n\n/** Computes a djb2 hash of the given string.\n *\n * @param x - the string to be hashed\n * @param seed - optionally a prior hash for progressive hashing\n * @returns a hash value, i.e. a number\n *\n * @remark\n * This is the hashing function used throughout `urql`, primarily to compute\n * {@link Operation.key}.\n *\n * @see {@link http://www.cse.yorku.ca/~oz/hash.html#djb2} for a further description of djb2.\n */\nexport const phash = (x: string, seed?: HashValue): HashValue => {\n  let h = (seed || 5381) | 0;\n  for (let i = 0, l = x.length | 0; i < l; i++)\n    h = (h << 5) + h + x.charCodeAt(i);\n  return h as HashValue;\n};\n", "export type FileMap = Map<string, File | Blob>;\n\nconst seen: Set<any> = new Set();\nconst cache: WeakMap<any, any> = new WeakMap();\n\nconst stringify = (x: any, includeFiles: boolean): string => {\n  if (x === null || seen.has(x)) {\n    return 'null';\n  } else if (typeof x !== 'object') {\n    return JSON.stringify(x) || '';\n  } else if (x.toJSON) {\n    return stringify(x.toJSON(), includeFiles);\n  } else if (Array.isArray(x)) {\n    let out = '[';\n    for (let i = 0, l = x.length; i < l; i++) {\n      if (out.length > 1) out += ',';\n      out += stringify(x[i], includeFiles) || 'null';\n    }\n    out += ']';\n    return out;\n  } else if (\n    !includeFiles &&\n    ((FileConstructor !== NoopConstructor && x instanceof FileConstructor) ||\n      (BlobConstructor !== NoopConstructor && x instanceof BlobConstructor))\n  ) {\n    return 'null';\n  }\n\n  const keys = Object.keys(x).sort();\n  if (\n    !keys.length &&\n    x.constructor &&\n    Object.getPrototypeOf(x).constructor !== Object.prototype.constructor\n  ) {\n    const key = cache.get(x) || Math.random().toString(36).slice(2);\n    cache.set(x, key);\n    return stringify({ __key: key }, includeFiles);\n  }\n\n  seen.add(x);\n  let out = '{';\n  for (let i = 0, l = keys.length; i < l; i++) {\n    const value = stringify(x[keys[i]], includeFiles);\n    if (value) {\n      if (out.length > 1) out += ',';\n      out += stringify(keys[i], includeFiles) + ':' + value;\n    }\n  }\n\n  seen.delete(x);\n  out += '}';\n  return out;\n};\n\nconst extract = (map: FileMap, path: string, x: any): void => {\n  if (x == null || typeof x !== 'object' || x.toJSON || seen.has(x)) {\n    /*noop*/\n  } else if (Array.isArray(x)) {\n    for (let i = 0, l = x.length; i < l; i++)\n      extract(map, `${path}.${i}`, x[i]);\n  } else if (x instanceof FileConstructor || x instanceof BlobConstructor) {\n    map.set(path, x as File | Blob);\n  } else {\n    seen.add(x);\n    for (const key in x) extract(map, `${path}.${key}`, x[key]);\n  }\n};\n\n/** A stable stringifier for GraphQL variables objects.\n *\n * @param x - any JSON-like data.\n * @return A JSON string.\n *\n * @remarks\n * This utility creates a stable JSON string from any passed data,\n * and protects itself from throwing.\n *\n * The JSON string is stable insofar as objects’ keys are sorted,\n * and instances of non-plain objects are replaced with random keys\n * replacing their values, which remain stable for the objects’\n * instance.\n */\nexport const stringifyVariables = (x: any, includeFiles?: boolean): string => {\n  seen.clear();\n  return stringify(x, includeFiles || false);\n};\n\nclass NoopConstructor {}\nconst FileConstructor = typeof File !== 'undefined' ? File : NoopConstructor;\nconst BlobConstructor = typeof Blob !== 'undefined' ? Blob : NoopConstructor;\n\nexport const extractFiles = (x: any): FileMap => {\n  const map: FileMap = new Map();\n  if (\n    FileConstructor !== NoopConstructor ||\n    BlobConstructor !== NoopConstructor\n  ) {\n    seen.clear();\n    extract(map, 'variables', x);\n  }\n  return map;\n};\n", "import { Kind, parse, print } from '@0no-co/graphql.web';\nimport type { DocumentNode, DefinitionNode } from './graphql';\nimport type { HashValue } from './hash';\nimport { phash } from './hash';\nimport { stringifyVariables } from './variables';\n\nimport type {\n  DocumentInput,\n  TypedDocumentNode,\n  AnyVariables,\n  GraphQLRequest,\n  RequestExtensions,\n} from '../types';\n\ntype PersistedDocumentNode = TypedDocumentNode & {\n  documentId?: string;\n};\n\n/** A `DocumentNode` annotated with its hashed key.\n * @internal\n */\nexport type KeyedDocumentNode = TypedDocumentNode & {\n  __key: HashValue;\n};\n\nconst SOURCE_NAME = 'gql';\nconst GRAPHQL_STRING_RE = /(\"{3}[\\s\\S]*\"{3}|\"(?:\\\\.|[^\"])*\")/g;\nconst REPLACE_CHAR_RE = /(?:#[^\\n\\r]+)?(?:[\\r\\n]+|$)/g;\n\nconst replaceOutsideStrings = (str: string, idx: number): string =>\n  idx % 2 === 0 ? str.replace(REPLACE_CHAR_RE, '\\n') : str;\n\n/** Sanitizes a GraphQL document string by replacing comments and redundant newlines in it. */\nconst sanitizeDocument = (node: string): string =>\n  node.split(GRAPHQL_STRING_RE).map(replaceOutsideStrings).join('').trim();\n\nconst prints: Map<DocumentNode | DefinitionNode, string> = new Map<\n  DocumentNode | DefinitionNode,\n  string\n>();\nconst docs: Map<HashValue, KeyedDocumentNode> = new Map<\n  HashValue,\n  KeyedDocumentNode\n>();\n\n/** A cached printing function for GraphQL documents.\n *\n * @param node - A string of a document or a {@link DocumentNode}\n * @returns A normalized printed string of the passed GraphQL document.\n *\n * @remarks\n * This function accepts a GraphQL query string or {@link DocumentNode},\n * then prints and sanitizes it. The sanitizer takes care of removing\n * comments, which otherwise alter the key of the document although the\n * document is otherwise equivalent to another.\n *\n * When a {@link DocumentNode} is passed to this function, it caches its\n * output by modifying the `loc.source.body` property on the GraphQL node.\n */\nexport const stringifyDocument = (\n  node: string | DefinitionNode | DocumentNode\n): string => {\n  let printed: string;\n  if (typeof node === 'string') {\n    printed = sanitizeDocument(node);\n  } else if (node.loc && docs.get((node as KeyedDocumentNode).__key) === node) {\n    printed = node.loc.source.body;\n  } else {\n    printed = prints.get(node) || sanitizeDocument(print(node));\n    prints.set(node, printed);\n  }\n\n  if (typeof node !== 'string' && !node.loc) {\n    (node as any).loc = {\n      start: 0,\n      end: printed.length,\n      source: {\n        body: printed,\n        name: SOURCE_NAME,\n        locationOffset: { line: 1, column: 1 },\n      },\n    };\n  }\n\n  return printed;\n};\n\n/** Computes the hash for a document's string using {@link stringifyDocument}'s output.\n *\n * @param node - A string of a document or a {@link DocumentNode}\n * @returns A {@link HashValue}\n *\n * @privateRemarks\n * This function adds the operation name of the document to the hash, since sometimes\n * a merged document with multiple operations may be used. Although `urql` requires a\n * `DocumentNode` to only contain a single operation, when the cached `loc.source.body`\n * of a `DocumentNode` is used, this string may still contain multiple operations and\n * the resulting hash should account for only one at a time.\n */\nconst hashDocument = (\n  node: string | DefinitionNode | DocumentNode\n): HashValue => {\n  let key: HashValue;\n  if ((node as PersistedDocumentNode).documentId) {\n    key = phash((node as PersistedDocumentNode).documentId!);\n  } else {\n    key = phash(stringifyDocument(node));\n    // Add the operation name to the produced hash\n    if ((node as DocumentNode).definitions) {\n      const operationName = getOperationName(node as DocumentNode);\n      if (operationName) key = phash(`\\n# ${operationName}`, key);\n    }\n  }\n  return key;\n};\n\n/** Returns a canonical version of the passed `DocumentNode` with an added hash key.\n *\n * @param node - A string of a document or a {@link DocumentNode}\n * @returns A {@link KeyedDocumentNode}\n *\n * @remarks\n * `urql` will always avoid unnecessary work, no matter whether a user passes `DocumentNode`s\n * or strings of GraphQL documents to its APIs.\n *\n * This function will return a canonical version of a {@link KeyedDocumentNode} no matter\n * which kind of input is passed, avoiding parsing or hashing of passed data as needed.\n */\nexport const keyDocument = (node: string | DocumentNode): KeyedDocumentNode => {\n  let key: HashValue;\n  let query: DocumentNode;\n  if (typeof node === 'string') {\n    key = hashDocument(node);\n    query = docs.get(key) || parse(node, { noLocation: true });\n  } else {\n    key = (node as KeyedDocumentNode).__key || hashDocument(node);\n    query = docs.get(key) || node;\n  }\n\n  // Add location information if it's missing\n  if (!query.loc) stringifyDocument(query);\n\n  (query as KeyedDocumentNode).__key = key;\n  docs.set(key, query as KeyedDocumentNode);\n  return query as KeyedDocumentNode;\n};\n\n/** Creates a `GraphQLRequest` from the passed parameters.\n *\n * @param q - A string of a document or a {@link DocumentNode}\n * @param variables - A variables object for the defined GraphQL operation.\n * @returns A {@link GraphQLRequest}\n *\n * @remarks\n * `createRequest` creates a {@link GraphQLRequest} from the passed parameters,\n * while replacing the document as needed with a canonical version of itself,\n * to avoid parsing, printing, or hashing the same input multiple times.\n *\n * If no variables are passed, canonically it'll default to an empty object,\n * which is removed from the resulting hash key.\n */\nexport const createRequest = <\n  Data = any,\n  Variables extends AnyVariables = AnyVariables,\n>(\n  _query: DocumentInput<Data, Variables>,\n  _variables: Variables,\n  extensions?: RequestExtensions | undefined\n): GraphQLRequest<Data, Variables> => {\n  const variables = _variables || ({} as Variables);\n  const query = keyDocument(_query);\n  const printedVars = stringifyVariables(variables, true);\n  let key = query.__key;\n  if (printedVars !== '{}') key = phash(printedVars, key);\n  return { key, query, variables, extensions };\n};\n\n/** Returns the name of the `DocumentNode`'s operation, if any.\n * @param query - A {@link DocumentNode}\n * @returns the operation's name contained within the document, or `undefined`\n */\nexport const getOperationName = (query: DocumentNode): string | undefined => {\n  for (let i = 0, l = query.definitions.length; i < l; i++) {\n    const node = query.definitions[i];\n    if (node.kind === Kind.OPERATION_DEFINITION) {\n      return node.name ? node.name.value : undefined;\n    }\n  }\n};\n\n/** Returns the type of the `DocumentNode`'s operation, if any.\n * @param query - A {@link DocumentNode}\n * @returns the operation's type contained within the document, or `undefined`\n */\nexport const getOperationType = (query: DocumentNode): string | undefined => {\n  for (let i = 0, l = query.definitions.length; i < l; i++) {\n    const node = query.definitions[i];\n    if (node.kind === Kind.OPERATION_DEFINITION) {\n      return node.operation;\n    }\n  }\n};\n", "import type {\n  ExecutionResult,\n  Operation,\n  OperationResult,\n  IncrementalPayload,\n} from '../types';\nimport { CombinedError } from './error';\n\n/** Converts the `ExecutionResult` received for a given `Operation` to an `OperationResult`.\n *\n * @param operation - The {@link Operation} for which the API’s result is for.\n * @param result - The GraphQL API’s {@link ExecutionResult}.\n * @param response - Optionally, a raw object representing the API’s result (Typically a {@link Response}).\n * @returns An {@link OperationResult}.\n *\n * @remarks\n * This utility can be used to create {@link OperationResult | OperationResults} in the shape\n * that `urql` expects and defines, and should be used rather than creating the results manually.\n *\n * @throws\n * If no data, or errors are contained within the result, or the result is instead an incremental\n * response containing a `path` property, a “No Content” error is thrown.\n *\n * @see {@link ExecutionResult} for the type definition of GraphQL API results.\n */\nexport const makeResult = (\n  operation: Operation,\n  result: ExecutionResult,\n  response?: any\n): OperationResult => {\n  if (\n    !('data' in result) &&\n    (!('errors' in result) || !Array.isArray(result.errors))\n  ) {\n    throw new Error('No Content');\n  }\n\n  const defaultHasNext = operation.kind === 'subscription';\n  return {\n    operation,\n    data: result.data,\n    error: Array.isArray(result.errors)\n      ? new CombinedError({\n          graphQLErrors: result.errors,\n          response,\n        })\n      : undefined,\n    extensions: result.extensions ? { ...result.extensions } : undefined,\n    hasNext: result.hasNext == null ? defaultHasNext : result.hasNext,\n    stale: false,\n  };\n};\n\nconst deepMerge = (target: any, source: any): any => {\n  if (typeof target === 'object' && target != null) {\n    if (Array.isArray(target)) {\n      target = [...target];\n      for (let i = 0, l = source.length; i < l; i++)\n        target[i] = deepMerge(target[i], source[i]);\n\n      return target;\n    }\n    if (!target.constructor || target.constructor === Object) {\n      target = { ...target };\n      for (const key in source)\n        target[key] = deepMerge(target[key], source[key]);\n      return target;\n    }\n  }\n  return source;\n};\n\n/** Merges an incrementally delivered `ExecutionResult` into a previous `OperationResult`.\n *\n * @param prevResult - The {@link OperationResult} that preceded this result.\n * @param path - The GraphQL API’s {@link ExecutionResult} that should be patching the `prevResult`.\n * @param response - Optionally, a raw object representing the API’s result (Typically a {@link Response}).\n * @returns A new {@link OperationResult} patched with the incremental result.\n *\n * @remarks\n * This utility should be used to merge subsequent {@link ExecutionResult | ExecutionResults} of\n * incremental responses into a prior {@link OperationResult}.\n *\n * When directives like `@defer`, `@stream`, and `@live` are used, GraphQL may deliver new\n * results that modify previous results. In these cases, it'll set a `path` property to modify\n * the result it sent last. This utility is built to handle these cases and merge these payloads\n * into existing {@link OperationResult | OperationResults}.\n *\n * @see {@link ExecutionResult} for the type definition of GraphQL API results.\n */\nexport const mergeResultPatch = (\n  prevResult: OperationResult,\n  nextResult: ExecutionResult,\n  response?: any,\n  pending?: ExecutionResult['pending']\n): OperationResult => {\n  let errors = prevResult.error ? prevResult.error.graphQLErrors : [];\n  let hasExtensions =\n    !!prevResult.extensions || !!(nextResult.payload || nextResult).extensions;\n  const extensions = {\n    ...prevResult.extensions,\n    ...(nextResult.payload || nextResult).extensions,\n  };\n\n  let incremental = nextResult.incremental;\n\n  // NOTE: We handle the old version of the incremental delivery payloads as well\n  if ('path' in nextResult) {\n    incremental = [nextResult as IncrementalPayload];\n  }\n\n  const withData = { data: prevResult.data };\n  if (incremental) {\n    for (let i = 0, l = incremental.length; i < l; i++) {\n      const patch = incremental[i];\n      if (Array.isArray(patch.errors)) {\n        errors.push(...(patch.errors as any));\n      }\n\n      if (patch.extensions) {\n        Object.assign(extensions, patch.extensions);\n        hasExtensions = true;\n      }\n\n      let prop: string | number = 'data';\n      let part: Record<string, any> | Array<any> = withData;\n      let path: readonly (string | number)[] = [];\n      if (patch.path) {\n        path = patch.path;\n      } else if (pending) {\n        const res = pending.find(pendingRes => pendingRes.id === patch.id);\n        if (patch.subPath) {\n          path = [...res!.path, ...patch.subPath];\n        } else {\n          path = res!.path;\n        }\n      }\n\n      for (let i = 0, l = path.length; i < l; prop = path[i++]) {\n        part = part[prop] = Array.isArray(part[prop])\n          ? [...part[prop]]\n          : { ...part[prop] };\n      }\n\n      if (patch.items) {\n        const startIndex = +prop >= 0 ? (prop as number) : 0;\n        for (let i = 0, l = patch.items.length; i < l; i++)\n          part[startIndex + i] = deepMerge(\n            part[startIndex + i],\n            patch.items[i]\n          );\n      } else if (patch.data !== undefined) {\n        part[prop] = deepMerge(part[prop], patch.data);\n      }\n    }\n  } else {\n    withData.data = (nextResult.payload || nextResult).data || prevResult.data;\n    errors =\n      (nextResult.errors as any[]) ||\n      (nextResult.payload && nextResult.payload.errors) ||\n      errors;\n  }\n\n  return {\n    operation: prevResult.operation,\n    data: withData.data,\n    error: errors.length\n      ? new CombinedError({ graphQLErrors: errors, response })\n      : undefined,\n    extensions: hasExtensions ? extensions : undefined,\n    hasNext:\n      nextResult.hasNext != null ? nextResult.hasNext : prevResult.hasNext,\n    stale: false,\n  };\n};\n\n/** Creates an `OperationResult` containing a network error for requests that encountered unexpected errors.\n *\n * @param operation - The {@link Operation} for which the API’s result is for.\n * @param error - The network-like error that prevented an API result from being delivered.\n * @param response - Optionally, a raw object representing the API’s result (Typically a {@link Response}).\n * @returns An {@link OperationResult} containing only a {@link CombinedError}.\n *\n * @remarks\n * This utility can be used to create {@link OperationResult | OperationResults} in the shape\n * that `urql` expects and defines, and should be used rather than creating the results manually.\n * This function should be used for when the {@link CombinedError.networkError} property is\n * populated and no GraphQL execution actually occurred.\n */\nexport const makeErrorResult = (\n  operation: Operation,\n  error: Error,\n  response?: any\n): OperationResult => ({\n  operation,\n  data: undefined,\n  error: new CombinedError({\n    networkError: error,\n    response,\n  }),\n  extensions: undefined,\n  hasNext: false,\n  stale: false,\n});\n", "import {\n  stringifyDocument,\n  getOperationName,\n  stringifyVariables,\n  extractFiles,\n} from '../utils';\n\nimport type { AnyVariables, GraphQLRequest, Operation } from '../types';\n\n/** Abstract definition of the JSON data sent during GraphQL HTTP POST requests. */\nexport interface FetchBody {\n  query?: string;\n  documentId?: string;\n  operationName: string | undefined;\n  variables: undefined | Record<string, any>;\n  extensions: undefined | Record<string, any>;\n}\n\n/** Creates a GraphQL over HTTP compliant JSON request body.\n * @param request - An object containing a `query` document and `variables`.\n * @returns A {@link FetchBody}\n * @see {@link https://github.com/graphql/graphql-over-http} for the GraphQL over HTTP spec.\n */\nexport function makeFetchBody<\n  Data = any,\n  Variables extends AnyVariables = AnyVariables,\n>(request: Omit<GraphQLRequest<Data, Variables>, 'key'>): FetchBody {\n  const body: FetchBody = {\n    query: undefined,\n    documentId: undefined,\n    operationName: getOperationName(request.query),\n    variables: request.variables || undefined,\n    extensions: request.extensions,\n  };\n\n  if (\n    'documentId' in request.query &&\n    request.query.documentId &&\n    // NOTE: We have to check that the document will definitely be sent\n    // as a persisted document to avoid breaking changes\n    (!request.query.definitions || !request.query.definitions.length)\n  ) {\n    body.documentId = request.query.documentId;\n  } else if (\n    !request.extensions ||\n    !request.extensions.persistedQuery ||\n    !!request.extensions.persistedQuery.miss\n  ) {\n    body.query = stringifyDocument(request.query);\n  }\n\n  return body;\n}\n\n/** Creates a URL that will be called for a GraphQL HTTP request.\n *\n * @param operation - An {@link Operation} for which to make the request.\n * @param body - A {@link FetchBody} which may be replaced with a URL.\n *\n * @remarks\n * Creates the URL that’ll be called as part of a GraphQL HTTP request.\n * Built-in fetch exchanges support sending GET requests, even for\n * non-persisted full requests, which this function supports by being\n * able to serialize GraphQL requests into the URL.\n */\nexport const makeFetchURL = (\n  operation: Operation,\n  body?: FetchBody\n): string => {\n  const useGETMethod =\n    operation.kind === 'query' && operation.context.preferGetMethod;\n  if (!useGETMethod || !body) return operation.context.url;\n\n  const urlParts = splitOutSearchParams(operation.context.url);\n  for (const key in body) {\n    const value = body[key];\n    if (value) {\n      urlParts[1].set(\n        key,\n        typeof value === 'object' ? stringifyVariables(value) : value\n      );\n    }\n  }\n  const finalUrl = urlParts.join('?');\n  if (finalUrl.length > 2047 && useGETMethod !== 'force') {\n    operation.context.preferGetMethod = false;\n    return operation.context.url;\n  }\n\n  return finalUrl;\n};\n\nconst splitOutSearchParams = (\n  url: string\n): readonly [string, URLSearchParams] => {\n  const start = url.indexOf('?');\n  return start > -1\n    ? [url.slice(0, start), new URLSearchParams(url.slice(start + 1))]\n    : [url, new URLSearchParams()];\n};\n\n/** Serializes a {@link FetchBody} into a {@link RequestInit.body} format. */\nconst serializeBody = (\n  operation: Operation,\n  body?: FetchBody\n): FormData | string | undefined => {\n  const omitBody =\n    operation.kind === 'query' && !!operation.context.preferGetMethod;\n  if (body && !omitBody) {\n    const json = stringifyVariables(body);\n    const files = extractFiles(body.variables);\n    if (files.size) {\n      const form = new FormData();\n      form.append('operations', json);\n      form.append(\n        'map',\n        stringifyVariables({\n          ...[...files.keys()].map(value => [value]),\n        })\n      );\n      let index = 0;\n      for (const file of files.values()) form.append(`${index++}`, file);\n      return form;\n    }\n    return json;\n  }\n};\n\nconst isHeaders = (headers: HeadersInit): headers is Headers =>\n  'has' in headers && !Object.keys(headers).length;\n\n/** Creates a `RequestInit` object for a given `Operation`.\n *\n * @param operation - An {@link Operation} for which to make the request.\n * @param body - A {@link FetchBody} which is added to the options, if the request isn’t a GET request.\n *\n * @remarks\n * Creates the fetch options {@link RequestInit} object that’ll be passed to the Fetch API\n * as part of a GraphQL over HTTP request. It automatically sets a default `Content-Type`\n * header.\n *\n * @see {@link https://github.com/graphql/graphql-over-http} for the GraphQL over HTTP spec.\n * @see {@link https://developer.mozilla.org/en-US/docs/Web/API/Fetch_API} for the Fetch API spec.\n */\nexport const makeFetchOptions = (\n  operation: Operation,\n  body?: FetchBody\n): RequestInit => {\n  const headers: HeadersInit = {\n    accept:\n      operation.kind === 'subscription'\n        ? 'text/event-stream, multipart/mixed'\n        : 'application/graphql-response+json, application/graphql+json, application/json, text/event-stream, multipart/mixed',\n  };\n  const extraOptions =\n    (typeof operation.context.fetchOptions === 'function'\n      ? operation.context.fetchOptions()\n      : operation.context.fetchOptions) || {};\n  if (extraOptions.headers) {\n    if (isHeaders(extraOptions.headers)) {\n      extraOptions.headers.forEach((value, key) => {\n        headers[key] = value;\n      });\n    } else if (Array.isArray(extraOptions.headers)) {\n      (extraOptions.headers as Array<[string, string]>).forEach(\n        (value, key) => {\n          if (Array.isArray(value)) {\n            if (headers[value[0]]) {\n              headers[value[0]] = `${headers[value[0]]},${value[1]}`;\n            } else {\n              headers[value[0]] = value[1];\n            }\n          } else {\n            headers[key] = value;\n          }\n        }\n      );\n    } else {\n      for (const key in extraOptions.headers) {\n        headers[key.toLowerCase()] = extraOptions.headers[key];\n      }\n    }\n  }\n\n  const serializedBody = serializeBody(operation, body);\n  if (typeof serializedBody === 'string' && !headers['content-type'])\n    headers['content-type'] = 'application/json';\n  return {\n    ...extraOptions,\n    method: serializedBody ? 'POST' : 'GET',\n    body: serializedBody,\n    headers,\n  };\n};\n", "/* Summary: This file handles the HTTP transport via GraphQL over HTTP\n * See: https://graphql.github.io/graphql-over-http/draft/\n *\n * `@urql/core`, by default, implements several RFC'd protocol extensions\n * on top of this. As such, this implementation supports:\n * - [Incremental Delivery](https://github.com/graphql/graphql-over-http/blob/main/rfcs/IncrementalDelivery.md)\n * - [GraphQL over SSE](https://github.com/graphql/graphql-over-http/blob/main/rfcs/GraphQLOverSSE.md)\n *\n * This also supports the \"Defer Stream\" payload format.\n * See: https://github.com/graphql/graphql-wg/blob/main/rfcs/DeferStream.md\n * Implementation for this is located in `../utils/result.ts` in `mergeResultPatch`\n *\n * And; this also supports the GraphQL Multipart spec for file uploads.\n * See: https://github.com/jaydenseric/graphql-multipart-request-spec\n * Implementation for this is located in `../utils/variables.ts` in `extractFiles`,\n * and `./fetchOptions.ts` in `serializeBody`.\n *\n * And; this also supports GET requests (and hence; automatic persisted queries)\n * via the `@urql/exchange-persisted` package.\n *\n * This implementation DOES NOT support Batching.\n * See: https://github.com/graphql/graphql-over-http/blob/main/rfcs/Batching.md\n * Which is deemed out-of-scope, as it's sufficiently unnecessary given\n * modern handling of HTTP requests being in parallel.\n *\n * The implementation in this file needs to make certain accommodations for:\n * - The Web Fetch API\n * - Non-browser or polyfill Fetch APIs\n * - Node.js-like Fetch implementations (see `toString` below)\n *\n * GraphQL over SSE has a reference implementation, which supports non-HTTP/2\n * modes and is a faithful implementation of the spec.\n * See: https://github.com/enisdenjo/graphql-sse\n *\n * GraphQL Inremental Delivery (aka “GraphQL Multipart Responses”) has a\n * reference implementation, which a prior implementation of this file heavily\n * leaned on (See prior attribution comments)\n * See: https://github.com/maraisr/meros\n *\n * This file merges support for all three GraphQL over HTTP response formats\n * via async generators and Wonka’s `fromAsyncIterable`. As part of this, `streamBody`\n * and `split` are the common, cross-compatible base implementations.\n */\n\nimport type { Source } from 'wonka';\nimport { fromAsyncIterable, onEnd, filter, pipe } from 'wonka';\nimport type { Operation, OperationResult, ExecutionResult } from '../types';\nimport { makeResult, makeErrorResult, mergeResultPatch } from '../utils';\n\nconst decoder = typeof TextDecoder !== 'undefined' ? new TextDecoder() : null;\nconst boundaryHeaderRe = /boundary=\"?([^=\";]+)\"?/i;\nconst eventStreamRe = /data: ?([^\\n]+)/;\n\ntype ChunkData = Buffer | Uint8Array;\n\n// NOTE: We're avoiding referencing the `Buffer` global here to prevent\n// auto-polyfilling in Webpack\nconst toString = (input: Buffer | ArrayBuffer): string =>\n  input.constructor.name === 'Buffer'\n    ? (input as Buffer).toString()\n    : decoder!.decode(input as ArrayBuffer);\n\nasync function* streamBody(response: Response): AsyncIterableIterator<string> {\n  if (response.body![Symbol.asyncIterator]) {\n    for await (const chunk of response.body! as any)\n      yield toString(chunk as ChunkData);\n  } else {\n    const reader = response.body!.getReader();\n    let result: ReadableStreamReadResult<ChunkData>;\n    try {\n      while (!(result = await reader.read()).done) yield toString(result.value);\n    } finally {\n      reader.cancel();\n    }\n  }\n}\n\nasync function* split(\n  chunks: AsyncIterableIterator<string>,\n  boundary: string\n): AsyncIterableIterator<string> {\n  let buffer = '';\n  let boundaryIndex: number;\n  for await (const chunk of chunks) {\n    buffer += chunk;\n    while ((boundaryIndex = buffer.indexOf(boundary)) > -1) {\n      yield buffer.slice(0, boundaryIndex);\n      buffer = buffer.slice(boundaryIndex + boundary.length);\n    }\n  }\n}\n\nasync function* parseJSON(\n  response: Response\n): AsyncIterableIterator<ExecutionResult> {\n  yield JSON.parse(await response.text());\n}\n\nasync function* parseEventStream(\n  response: Response\n): AsyncIterableIterator<ExecutionResult> {\n  let payload: any;\n  for await (const chunk of split(streamBody(response), '\\n\\n')) {\n    const match = chunk.match(eventStreamRe);\n    if (match) {\n      const chunk = match[1];\n      try {\n        yield (payload = JSON.parse(chunk));\n      } catch (error) {\n        if (!payload) throw error;\n      }\n      if (payload && payload.hasNext === false) break;\n    }\n  }\n  if (payload && payload.hasNext !== false) {\n    yield { hasNext: false };\n  }\n}\n\nasync function* parseMultipartMixed(\n  contentType: string,\n  response: Response\n): AsyncIterableIterator<ExecutionResult> {\n  const boundaryHeader = contentType.match(boundaryHeaderRe);\n  const boundary = '--' + (boundaryHeader ? boundaryHeader[1] : '-');\n  let isPreamble = true;\n  let payload: any;\n  for await (let chunk of split(streamBody(response), '\\r\\n' + boundary)) {\n    if (isPreamble) {\n      isPreamble = false;\n      const preambleIndex = chunk.indexOf(boundary);\n      if (preambleIndex > -1) {\n        chunk = chunk.slice(preambleIndex + boundary.length);\n      } else {\n        continue;\n      }\n    }\n    try {\n      yield (payload = JSON.parse(chunk.slice(chunk.indexOf('\\r\\n\\r\\n') + 4)));\n    } catch (error) {\n      if (!payload) throw error;\n    }\n    if (payload && payload.hasNext === false) break;\n  }\n  if (payload && payload.hasNext !== false) {\n    yield { hasNext: false };\n  }\n}\n\nasync function* parseMaybeJSON(\n  response: Response\n): AsyncIterableIterator<ExecutionResult> {\n  const text = await response.text();\n  try {\n    const result = JSON.parse(text);\n    if (process.env.NODE_ENV !== 'production') {\n      console.warn(\n        `Found response with content-type \"text/plain\" but it had a valid \"application/json\" response.`\n      );\n    }\n    yield result;\n  } catch (e) {\n    throw new Error(text);\n  }\n}\n\nasync function* fetchOperation(\n  operation: Operation,\n  url: string,\n  fetchOptions: RequestInit\n) {\n  let networkMode = true;\n  let result: OperationResult | null = null;\n  let response: Response | undefined;\n\n  try {\n    // Delay for a tick to give the Client a chance to cancel the request\n    // if a teardown comes in immediately\n    yield await Promise.resolve();\n\n    response = await (operation.context.fetch || fetch)(url, fetchOptions);\n    const contentType = response.headers.get('Content-Type') || '';\n\n    let results: AsyncIterable<ExecutionResult>;\n    if (/multipart\\/mixed/i.test(contentType)) {\n      results = parseMultipartMixed(contentType, response);\n    } else if (/text\\/event-stream/i.test(contentType)) {\n      results = parseEventStream(response);\n    } else if (!/text\\//i.test(contentType)) {\n      results = parseJSON(response);\n    } else {\n      results = parseMaybeJSON(response);\n    }\n\n    let pending: ExecutionResult['pending'];\n    for await (const payload of results) {\n      if (payload.pending && !result) {\n        pending = payload.pending;\n      } else if (payload.pending) {\n        pending = [...pending!, ...payload.pending];\n      }\n      result = result\n        ? mergeResultPatch(result, payload, response, pending)\n        : makeResult(operation, payload, response);\n      networkMode = false;\n      yield result;\n      networkMode = true;\n    }\n\n    if (!result) {\n      yield (result = makeResult(operation, {}, response));\n    }\n  } catch (error: any) {\n    if (!networkMode) {\n      throw error;\n    }\n\n    yield makeErrorResult(\n      operation,\n      response &&\n        (response.status < 200 || response.status >= 300) &&\n        response.statusText\n        ? new Error(response.statusText)\n        : error,\n      response\n    );\n  }\n}\n\n/** Makes a GraphQL HTTP request to a given API by wrapping around the Fetch API.\n *\n * @param operation - The {@link Operation} that should be sent via GraphQL over HTTP.\n * @param url - The endpoint URL for the GraphQL HTTP API.\n * @param fetchOptions - The {@link RequestInit} fetch options for the request.\n * @returns A Wonka {@link Source} of {@link OperationResult | OperationResults}.\n *\n * @remarks\n * This utility defines how all built-in fetch exchanges make GraphQL HTTP requests,\n * supporting multipart incremental responses, cancellation and other smaller\n * implementation details.\n *\n * If you’re implementing a modified fetch exchange for a GraphQL over HTTP API\n * it’s recommended you use this utility.\n *\n * Hint: This function does not use the passed `operation` to create or modify the\n * `fetchOptions` and instead expects that the options have already been created\n * using {@link makeFetchOptions} and modified as needed.\n *\n * @throws\n * If the `fetch` polyfill or globally available `fetch` function doesn’t support\n * streamed multipart responses while trying to handle a `multipart/mixed` GraphQL response,\n * the source will throw “Streaming requests unsupported”.\n * This shouldn’t happen in modern browsers and Node.js.\n *\n * @see {@link https://developer.mozilla.org/en-US/docs/Web/API/Fetch_API} for the Fetch API spec.\n */\nexport function makeFetchSource(\n  operation: Operation,\n  url: string,\n  fetchOptions: RequestInit\n): Source<OperationResult> {\n  let abortController: AbortController | void;\n  if (typeof AbortController !== 'undefined') {\n    fetchOptions.signal = (abortController = new AbortController()).signal;\n  }\n  return pipe(\n    fromAsyncIterable(fetchOperation(operation, url, fetchOptions)),\n    filter((result): result is OperationResult => !!result),\n    onEnd(() => {\n      if (abortController) abortController.abort();\n    })\n  );\n}\n", "interface EntityLike {\n  [key: string]: EntityLike | EntityLike[] | any;\n  __typename: string | null | void;\n}\n\nconst collectTypes = (obj: EntityLike | EntityLike[], types: Set<string>) => {\n  if (Array.isArray(obj)) {\n    for (let i = 0, l = obj.length; i < l; i++) {\n      collectTypes(obj[i], types);\n    }\n  } else if (typeof obj === 'object' && obj !== null) {\n    for (const key in obj) {\n      if (key === '__typename' && typeof obj[key] === 'string') {\n        types.add(obj[key] as string);\n      } else {\n        collectTypes(obj[key], types);\n      }\n    }\n  }\n\n  return types;\n};\n\n/** Finds and returns a list of `__typename` fields found in response data.\n *\n * @privateRemarks\n * This is used by `@urql/core`’s document `cacheExchange` to find typenames\n * in a given GraphQL response’s data.\n */\nexport const collectTypenames = (response: object): string[] => [\n  ...collectTypes(response as EntityLike, new Set()),\n];\n", "import type {\n  FieldNode,\n  SelectionNode,\n  DefinitionNode,\n  DirectiveNode,\n} from '@0no-co/graphql.web';\nimport { Kind } from '@0no-co/graphql.web';\nimport type { KeyedDocumentNode } from './request';\nimport { keyDocument } from './request';\nimport type { FormattedNode, TypedDocumentNode } from '../types';\n\nconst formatNode = <\n  T extends SelectionNode | DefinitionNode | TypedDocumentNode<any, any>,\n>(\n  node: T\n): FormattedNode<T> => {\n  if ('definitions' in node) {\n    const definitions: FormattedNode<DefinitionNode>[] = [];\n    for (let i = 0, l = node.definitions.length; i < l; i++) {\n      const newDefinition = formatNode(node.definitions[i]);\n      definitions.push(newDefinition);\n    }\n\n    return { ...node, definitions } as FormattedNode<T>;\n  }\n\n  if ('directives' in node && node.directives && node.directives.length) {\n    const directives: DirectiveNode[] = [];\n    const _directives = {};\n    for (let i = 0, l = node.directives.length; i < l; i++) {\n      const directive = node.directives[i];\n      let name = directive.name.value;\n      if (name[0] !== '_') {\n        directives.push(directive);\n      } else {\n        name = name.slice(1);\n      }\n      _directives[name] = directive;\n    }\n    node = { ...node, directives, _directives };\n  }\n\n  if ('selectionSet' in node) {\n    const selections: FormattedNode<SelectionNode>[] = [];\n    let hasTypename = node.kind === Kind.OPERATION_DEFINITION;\n    if (node.selectionSet) {\n      for (let i = 0, l = node.selectionSet.selections.length; i < l; i++) {\n        const selection = node.selectionSet.selections[i];\n        hasTypename =\n          hasTypename ||\n          (selection.kind === Kind.FIELD &&\n            selection.name.value === '__typename' &&\n            !selection.alias);\n        const newSelection = formatNode(selection);\n        selections.push(newSelection);\n      }\n\n      if (!hasTypename) {\n        selections.push({\n          kind: Kind.FIELD,\n          name: {\n            kind: Kind.NAME,\n            value: '__typename',\n          },\n          _generated: true,\n        } as FormattedNode<FieldNode>);\n      }\n\n      return {\n        ...node,\n        selectionSet: { ...node.selectionSet, selections },\n      } as FormattedNode<T>;\n    }\n  }\n\n  return node as FormattedNode<T>;\n};\n\nconst formattedDocs: Map<number, KeyedDocumentNode> = new Map<\n  number,\n  KeyedDocumentNode\n>();\n\n/** Formats a GraphQL document to add `__typename` fields and process client-side directives.\n *\n * @param node - a {@link DocumentNode}.\n * @returns a {@link FormattedDocument}\n *\n * @remarks\n * Cache {@link Exchange | Exchanges} will require typename introspection to\n * recognize types in a GraphQL response. To retrieve these typenames,\n * this function is used to add the `__typename` fields to non-root\n * selection sets of a GraphQL document.\n *\n * Additionally, this utility will process directives, filter out client-side\n * directives starting with an `_` underscore, and place a `_directives` dictionary\n * on selection nodes.\n *\n * This utility also preserves the internally computed key of the\n * document as created by {@link createRequest} to avoid any\n * formatting from being duplicated.\n *\n * @see {@link https://spec.graphql.org/October2021/#sec-Type-Name-Introspection} for more information\n * on typename introspection via the `__typename` field.\n */\nexport const formatDocument = <T extends TypedDocumentNode<any, any>>(\n  node: T\n): FormattedNode<T> => {\n  const query = keyDocument(node);\n\n  let result = formattedDocs.get(query.__key);\n  if (!result) {\n    formattedDocs.set(\n      query.__key,\n      (result = formatNode(query) as KeyedDocumentNode)\n    );\n    // Ensure that the hash of the resulting document won't suddenly change\n    // we are marking __key as non-enumerable so when external exchanges use visit\n    // to manipulate a document we won't restore the previous query due to the __key\n    // property.\n    Object.defineProperty(result, '__key', {\n      value: query.__key,\n      enumerable: false,\n    });\n  }\n\n  return result as FormattedNode<T>;\n};\n", "import type { Sink, Source } from 'wonka';\nimport { subscribe, take, filter, toPromise, pipe } from 'wonka';\nimport type { OperationResult, OperationResultSource } from '../types';\n\n/** Patches a `toPromise` method onto the `Source` passed to it.\n * @param source$ - the Wonka {@link Source} to patch.\n * @returns The passed `source$` with a patched `toPromise` method as a {@link PromisifiedSource}.\n * @internal\n */\nexport function withPromise<T extends OperationResult>(\n  _source$: Source<T>\n): OperationResultSource<T> {\n  const source$ = ((sink: Sink<T>) =>\n    _source$(sink)) as OperationResultSource<T>;\n  source$.toPromise = () =>\n    pipe(\n      source$,\n      filter(result => !result.stale && !result.hasNext),\n      take(1),\n      toPromise\n    );\n  source$.then = (onResolve, onReject) =>\n    source$.toPromise().then(onResolve, onReject);\n  source$.subscribe = onResult => subscribe(onResult)(source$);\n  return source$;\n}\n", "import type {\n  AnyVariables,\n  GraphQLRequest,\n  Operation,\n  OperationContext,\n  OperationType,\n} from '../types';\n\n/** Creates a {@link Operation} from the given parameters.\n *\n * @param kind - The {@link OperationType} of GraphQL operation, i.e. `query`, `mutation`, or `subscription`.\n * @param request - The {@link GraphQLRequest} or {@link Operation} used as a template for the new `Operation`.\n * @param context - The {@link OperationContext} `context` data for the `Operation`.\n * @returns A new {@link Operation}.\n *\n * @remarks\n * This method is both used to create new {@link Operation | Operations} as well as copy and modify existing\n * operations. While it’s not required to use this function to copy an `Operation`, it is recommended, in case\n * additional dynamic logic is added to them in the future.\n *\n * Hint: When an {@link Operation} is passed to the `request` argument, the `context` argument does not have to be\n * a complete {@link OperationContext} and will instead be combined with passed {@link Operation.context}.\n *\n * @example\n * An example of copying an existing `Operation` to modify its `context`:\n *\n * ```ts\n * makeOperation(\n *   operation.kind,\n *   operation,\n *   { requestPolicy: 'cache-first' },\n * );\n * ```\n */\nfunction makeOperation<\n  Data = any,\n  Variables extends AnyVariables = AnyVariables,\n>(\n  kind: OperationType,\n  request: GraphQLRequest<Data, Variables>,\n  context: OperationContext\n): Operation<Data, Variables>;\n\nfunction makeOperation<\n  Data = any,\n  Variables extends AnyVariables = AnyVariables,\n>(\n  kind: OperationType,\n  request: Operation<Data, Variables>,\n  context?: Partial<OperationContext>\n): Operation<Data, Variables>;\n\nfunction makeOperation(kind, request, context) {\n  return {\n    ...request,\n    kind,\n    context: request.context\n      ? {\n          ...request.context,\n          ...context,\n        }\n      : context || request.context,\n  };\n}\n\nexport { makeOperation };\n\n/** Adds additional metadata to an `Operation`'s `context.meta` property while copying it.\n * @see {@link OperationDebugMeta} for more information on the {@link OperationContext.meta} property.\n */\nexport const addMetadata = (\n  operation: Operation,\n  meta: OperationContext['meta']\n) => {\n  return makeOperation(operation.kind, operation, {\n    meta: {\n      ...operation.context.meta,\n      ...meta,\n    },\n  });\n};\n", "export * from './error';\nexport * from './request';\nexport * from './result';\nexport * from './variables';\nexport * from './collectTypenames';\nexport * from './formatDocument';\nexport * from './streamUtils';\nexport * from './operation';\n\nexport const noop = () => {\n  /* noop */\n};\n", "/* eslint-disable prefer-rest-params */\nimport { Kind } from '@0no-co/graphql.web';\nimport type { DocumentNode, DefinitionNode } from './utils/graphql';\nimport type { AnyVariables, TypedDocumentNode } from './types';\nimport { keyDocument, stringifyDocument } from './utils';\n\n/** A GraphQL parse function, which may be called as a tagged template literal, returning a parsed {@link DocumentNode}.\n *\n * @remarks\n * The `gql` tag or function is used to parse a GraphQL query document into a {@link DocumentNode}.\n *\n * When used as a tagged template, `gql` will automatically merge fragment definitions into the resulting\n * document and deduplicate them.\n *\n * It enforces that all fragments have a unique name. When fragments with different definitions share a name,\n * it will log a warning in development.\n *\n * Hint: It’s recommended to use this `gql` function over other GraphQL parse functions, since it puts the parsed\n * results directly into `@urql/core`’s internal caches and prevents further unnecessary work.\n *\n * @example\n * ```ts\n * const AuthorFragment = gql`\n *   fragment AuthorDisplayComponent on Author {\n *     id\n *     name\n *   }\n * `;\n *\n * const BookFragment = gql`\n *   fragment ListBookComponent on Book {\n *     id\n *     title\n *     author {\n *       ...AuthorDisplayComponent\n *     }\n *   }\n *\n *   ${AuthorFragment}\n * `;\n *\n * const BookQuery = gql`\n *   query Book($id: ID!) {\n *     book(id: $id) {\n *       ...BookFragment\n *     }\n *   }\n *\n *   ${BookFragment}\n * `;\n * ```\n */\nfunction gql<Data = any, Variables extends AnyVariables = AnyVariables>(\n  strings: TemplateStringsArray,\n  ...interpolations: Array<TypedDocumentNode | DocumentNode | string>\n): TypedDocumentNode<Data, Variables>;\n\nfunction gql<Data = any, Variables extends AnyVariables = AnyVariables>(\n  string: string\n): TypedDocumentNode<Data, Variables>;\n\nfunction gql(parts: string | TemplateStringsArray /* arguments */) {\n  const fragmentNames = new Map<string, string>();\n  const definitions: DefinitionNode[] = [];\n  const source: DocumentNode[] = [];\n\n  // Apply the entire tagged template body's definitions\n  let body: string = Array.isArray(parts) ? parts[0] : parts || '';\n  for (let i = 1; i < arguments.length; i++) {\n    const value = arguments[i];\n    if (value && value.definitions) {\n      source.push(value);\n    } else {\n      body += value;\n    }\n\n    body += arguments[0][i];\n  }\n\n  source.unshift(keyDocument(body));\n  for (let i = 0; i < source.length; i++) {\n    for (let j = 0; j < source[i].definitions.length; j++) {\n      const definition = source[i].definitions[j];\n      if (definition.kind === Kind.FRAGMENT_DEFINITION) {\n        const name = definition.name.value;\n        const value = stringifyDocument(definition);\n        // Fragments will be deduplicated according to this Map\n        if (!fragmentNames.has(name)) {\n          fragmentNames.set(name, value);\n          definitions.push(definition);\n        } else if (\n          process.env.NODE_ENV !== 'production' &&\n          fragmentNames.get(name) !== value\n        ) {\n          // Fragments with the same names is expected to have the same contents\n          console.warn(\n            '[WARNING: Duplicate Fragment] A fragment with name `' +\n              name +\n              '` already exists in this document.\\n' +\n              'While fragment names may not be unique across your source, each name must be unique per document.'\n          );\n        }\n      } else {\n        definitions.push(definition);\n      }\n    }\n  }\n\n  return keyDocument({\n    kind: Kind.DOCUMENT,\n    definitions,\n  });\n}\n\nexport { gql };\n", "/* eslint-disable @typescript-eslint/no-use-before-define */\nimport { filter, map, merge, pipe, tap } from 'wonka';\n\nimport type { Client } from '../client';\nimport type { Exchange, Operation, OperationResult } from '../types';\n\nimport {\n  makeOperation,\n  addMetadata,\n  collectTypenames,\n  formatDocument,\n  makeResult,\n} from '../utils';\n\ntype ResultCache = Map<number, OperationResult>;\ntype OperationCache = Map<string, Set<number>>;\n\nconst shouldSkip = ({ kind }: Operation) =>\n  kind !== 'mutation' && kind !== 'query';\n\n/** Adds unique typenames to query (for invalidating cache entries) */\nexport const mapTypeNames = (operation: Operation): Operation => {\n  const query = formatDocument(operation.query);\n  if (query !== operation.query) {\n    const formattedOperation = makeOperation(operation.kind, operation);\n    formattedOperation.query = query;\n    return formattedOperation;\n  } else {\n    return operation;\n  }\n};\n\n/** Default document cache exchange.\n *\n * @remarks\n * The default document cache in `urql` avoids sending the same GraphQL request\n * multiple times by caching it using the {@link Operation.key}. It will invalidate\n * query results automatically whenever it sees a mutation responses with matching\n * `__typename`s in their responses.\n *\n * The document cache will get the introspected `__typename` fields by modifying\n * your GraphQL operation documents using the {@link formatDocument} utility.\n *\n * This automatic invalidation strategy can fail if your query or mutation don’t\n * contain matching typenames, for instance, because the query contained an\n * empty list.\n * You can manually add hints for this exchange by specifying a list of\n * {@link OperationContext.additionalTypenames} for queries and mutations that\n * should invalidate one another.\n *\n * @see {@link https://urql.dev/goto/docs/basics/document-caching} for more information on this cache.\n */\nexport const cacheExchange: Exchange = ({ forward, client, dispatchDebug }) => {\n  const resultCache: ResultCache = new Map();\n  const operationCache: OperationCache = new Map();\n\n  const isOperationCached = (operation: Operation) =>\n    operation.kind === 'query' &&\n    operation.context.requestPolicy !== 'network-only' &&\n    (operation.context.requestPolicy === 'cache-only' ||\n      resultCache.has(operation.key));\n\n  return ops$ => {\n    const cachedOps$ = pipe(\n      ops$,\n      filter(op => !shouldSkip(op) && isOperationCached(op)),\n      map(operation => {\n        const cachedResult = resultCache.get(operation.key);\n\n        dispatchDebug({\n          operation,\n          ...(cachedResult\n            ? {\n                type: 'cacheHit',\n                message: 'The result was successfully retried from the cache',\n              }\n            : {\n                type: 'cacheMiss',\n                message: 'The result could not be retrieved from the cache',\n              }),\n        });\n\n        let result: OperationResult =\n          cachedResult ||\n          makeResult(operation, {\n            data: null,\n          });\n\n        result = {\n          ...result,\n          operation: addMetadata(operation, {\n            cacheOutcome: cachedResult ? 'hit' : 'miss',\n          }),\n        };\n\n        if (operation.context.requestPolicy === 'cache-and-network') {\n          result.stale = true;\n          reexecuteOperation(client, operation);\n        }\n\n        return result;\n      })\n    );\n\n    const forwardedOps$ = pipe(\n      merge([\n        pipe(\n          ops$,\n          filter(op => !shouldSkip(op) && !isOperationCached(op)),\n          map(mapTypeNames)\n        ),\n        pipe(\n          ops$,\n          filter(op => shouldSkip(op))\n        ),\n      ]),\n      map(op => addMetadata(op, { cacheOutcome: 'miss' })),\n      filter(\n        op => op.kind !== 'query' || op.context.requestPolicy !== 'cache-only'\n      ),\n      forward,\n      tap(response => {\n        let { operation } = response;\n        if (!operation) return;\n\n        let typenames = operation.context.additionalTypenames || [];\n        // NOTE: For now, we only respect `additionalTypenames` from subscriptions to\n        // avoid unexpected breaking changes\n        // We'd expect live queries or other update mechanisms to be more suitable rather\n        // than using subscriptions as “signals” to reexecute queries. However, if they’re\n        // just used as signals, it’s intuitive to hook them up using `additionalTypenames`\n        if (response.operation.kind !== 'subscription') {\n          typenames = collectTypenames(response.data).concat(typenames);\n        }\n\n        // Invalidates the cache given a mutation's response\n        if (\n          response.operation.kind === 'mutation' ||\n          response.operation.kind === 'subscription'\n        ) {\n          const pendingOperations = new Set<number>();\n\n          dispatchDebug({\n            type: 'cacheInvalidation',\n            message: `The following typenames have been invalidated: ${typenames}`,\n            operation,\n            data: { typenames, response },\n          });\n\n          for (let i = 0; i < typenames.length; i++) {\n            const typeName = typenames[i];\n            let operations = operationCache.get(typeName);\n            if (!operations)\n              operationCache.set(typeName, (operations = new Set()));\n            for (const key of operations.values()) pendingOperations.add(key);\n            operations.clear();\n          }\n\n          for (const key of pendingOperations.values()) {\n            if (resultCache.has(key)) {\n              operation = (resultCache.get(key) as OperationResult).operation;\n              resultCache.delete(key);\n              reexecuteOperation(client, operation);\n            }\n          }\n        } else if (operation.kind === 'query' && response.data) {\n          resultCache.set(operation.key, response);\n          for (let i = 0; i < typenames.length; i++) {\n            const typeName = typenames[i];\n            let operations = operationCache.get(typeName);\n            if (!operations)\n              operationCache.set(typeName, (operations = new Set()));\n            operations.add(operation.key);\n          }\n        }\n      })\n    );\n\n    return merge([cachedOps$, forwardedOps$]);\n  };\n};\n\n/** Reexecutes an `Operation` with the `network-only` request policy.\n * @internal\n */\nexport const reexecuteOperation = (client: Client, operation: Operation) => {\n  return client.reexecuteOperation(\n    makeOperation(operation.kind, operation, {\n      requestPolicy: 'network-only',\n    })\n  );\n};\n", "import type { GraphQLError } from '../utils/graphql';\nimport { pipe, filter, merge, map, tap } from 'wonka';\nimport type { Exchange, OperationResult, Operation } from '../types';\nimport { addMetadata, CombinedError } from '../utils';\nimport { reexecuteOperation, mapTypeNames } from './cache';\n\n/** A serialized version of an {@link OperationResult}.\n *\n * @remarks\n * All properties are serialized separately as JSON strings, except for the\n * {@link CombinedError} to speed up JS parsing speed, even if a result doesn’t\n * end up being used.\n *\n * @internal\n */\nexport interface SerializedResult {\n  hasNext?: boolean;\n  /** JSON-serialized version of {@link OperationResult.data}. */\n  data?: string | undefined; // JSON string of data\n  /** JSON-serialized version of {@link OperationResult.extensions}. */\n  extensions?: string | undefined;\n  /** JSON version of {@link CombinedError}. */\n  error?: {\n    graphQLErrors: Array<Partial<GraphQLError> | string>;\n    networkError?: string;\n  };\n}\n\n/** A dictionary of {@link Operation.key} keys to serializable {@link SerializedResult} objects.\n *\n * @remarks\n * It’s not recommended to modify the serialized data manually, however, multiple payloads of\n * this dictionary may safely be merged and combined.\n */\nexport interface SSRData {\n  [key: string]: SerializedResult;\n}\n\n/** Options for the `ssrExchange` allowing it to either operate on the server- or client-side. */\nexport interface SSRExchangeParams {\n  /** Indicates to the {@link SSRExchange} whether it's currently in server-side or client-side mode.\n   *\n   * @remarks\n   * Depending on this option, the {@link SSRExchange} will either capture or replay results.\n   * When `true`, it’s in client-side mode and results will be serialized. When `false`, it’ll\n   * use its deserialized data and replay results from it.\n   */\n  isClient?: boolean;\n  /** May be used on the client-side to pass the {@link SSRExchange} serialized data from the server-side.\n   *\n   * @remarks\n   * Alternatively, {@link SSRExchange.restoreData} may be called to imperatively add serialized data to\n   * the exchange.\n   *\n   * Hint: This method also works on the server-side to add to the initial serialized data, which enables\n   * you to combine multiple {@link SSRExchange} results, as needed.\n   */\n  initialState?: SSRData;\n  /** Forces a new API request to be sent in the background after replaying the deserialized result.\n   *\n   * @remarks\n   * Similarly to the `cache-and-network` {@link RequestPolicy}, this option tells the {@link SSRExchange}\n   * to send a new API request for the {@link Operation} after replaying a serialized result.\n   *\n   * Hint: This is useful when you're caching SSR results and need the client-side to update itself after\n   * rendering the initial serialized SSR results.\n   */\n  staleWhileRevalidate?: boolean;\n  /** Forces {@link OperationResult.extensions} to be serialized alongside the rest of a result.\n   *\n   * @remarks\n   * Entries in the `extension` object of a GraphQL result are often non-standard metdata, and many\n   * APIs use it for data that changes between every request. As such, the {@link SSRExchange} will\n   * not serialize this data by default, unless this flag is set.\n   */\n  includeExtensions?: boolean;\n}\n\n/** An `SSRExchange` either in server-side mode, serializing results, or client-side mode, deserializing and replaying results..\n *\n * @remarks\n * This same {@link Exchange} is used in your code both for the client-side and server-side as it’s “universal”\n * and can be put into either client-side or server-side mode using the {@link SSRExchangeParams.isClient} flag.\n *\n * In server-side mode, the `ssrExchange` will “record” results it sees from your API and provide them for you\n * to send to the client-side using the {@link SSRExchange.extractData} method.\n *\n * In client-side mode, the `ssrExchange` will use these serialized results, rehydrated either using\n * {@link SSRExchange.restoreData} or {@link SSRexchangeParams.initialState}, to replay results the\n * server-side has seen and sent before.\n *\n * Each serialized result will only be replayed once, as it’s assumed that your cache exchange will have the\n * results cached afterwards.\n */\nexport interface SSRExchange extends Exchange {\n  /** Client-side method to add serialized results to the {@link SSRExchange}.\n   * @param data - {@link SSRData},\n   */\n  restoreData(data: SSRData): void;\n  /** Server-side method to get all serialized results the {@link SSRExchange} has captured.\n   * @returns an {@link SSRData} dictionary.\n   */\n  extractData(): SSRData;\n}\n\n/** Serialize an OperationResult to plain JSON */\nconst serializeResult = (\n  result: OperationResult,\n  includeExtensions: boolean\n): SerializedResult => {\n  const serialized: SerializedResult = {\n    hasNext: result.hasNext,\n  };\n\n  if (result.data !== undefined) {\n    serialized.data = JSON.stringify(result.data);\n  }\n\n  if (includeExtensions && result.extensions !== undefined) {\n    serialized.extensions = JSON.stringify(result.extensions);\n  }\n\n  if (result.error) {\n    serialized.error = {\n      graphQLErrors: result.error.graphQLErrors.map(error => {\n        if (!error.path && !error.extensions) return error.message;\n\n        return {\n          message: error.message,\n          path: error.path,\n          extensions: error.extensions,\n        };\n      }),\n    };\n\n    if (result.error.networkError) {\n      serialized.error.networkError = '' + result.error.networkError;\n    }\n  }\n\n  return serialized;\n};\n\n/** Deserialize plain JSON to an OperationResult\n * @internal\n */\nconst deserializeResult = (\n  operation: Operation,\n  result: SerializedResult,\n  includeExtensions: boolean\n): OperationResult => ({\n  operation,\n  data: result.data ? JSON.parse(result.data) : undefined,\n  extensions:\n    includeExtensions && result.extensions\n      ? JSON.parse(result.extensions)\n      : undefined,\n  error: result.error\n    ? new CombinedError({\n        networkError: result.error.networkError\n          ? new Error(result.error.networkError)\n          : undefined,\n        graphQLErrors: result.error.graphQLErrors,\n      })\n    : undefined,\n  stale: false,\n  hasNext: !!result.hasNext,\n});\n\nconst revalidated = new Set<number>();\n\n/** Creates a server-side rendering `Exchange` that either captures responses on the server-side or replays them on the client-side.\n *\n * @param params - An {@link SSRExchangeParams} configuration object.\n * @returns the created {@link SSRExchange}\n *\n * @remarks\n * When dealing with server-side rendering, we essentially have two {@link Client | Clients} making requests,\n * the server-side client, and the client-side one. The `ssrExchange` helps implementing a tiny cache on both\n * sides that:\n *\n * - captures results on the server-side which it can serialize,\n * - replays results on the client-side that it deserialized from the server-side.\n *\n * Hint: The `ssrExchange` is basically an exchange that acts like a replacement for any fetch exchange\n * temporarily. As such, you should place it after your cache exchange but in front of any fetch exchange.\n */\nexport const ssrExchange = (params: SSRExchangeParams = {}): SSRExchange => {\n  const staleWhileRevalidate = !!params.staleWhileRevalidate;\n  const includeExtensions = !!params.includeExtensions;\n  const data: Record<string, SerializedResult | null> = {};\n\n  // On the client-side, we delete results from the cache as they're resolved\n  // this is delayed so that concurrent queries don't delete each other's data\n  const invalidateQueue: number[] = [];\n  const invalidate = (result: OperationResult) => {\n    invalidateQueue.push(result.operation.key);\n    if (invalidateQueue.length === 1) {\n      Promise.resolve().then(() => {\n        let key: number | void;\n        while ((key = invalidateQueue.shift())) {\n          data[key] = null;\n        }\n      });\n    }\n  };\n\n  // The SSR Exchange is a temporary cache that can populate results into data for suspense\n  // On the client it can be used to retrieve these temporary results from a rehydrated cache\n  const ssr: SSRExchange =\n    ({ client, forward }) =>\n    ops$ => {\n      // params.isClient tells us whether we're on the client-side\n      // By default we assume that we're on the client if suspense-mode is disabled\n      const isClient =\n        params && typeof params.isClient === 'boolean'\n          ? !!params.isClient\n          : !client.suspense;\n\n      let forwardedOps$ = pipe(\n        ops$,\n        filter(\n          operation =>\n            operation.kind === 'teardown' ||\n            !data[operation.key] ||\n            !!data[operation.key]!.hasNext ||\n            operation.context.requestPolicy === 'network-only'\n        ),\n        map(mapTypeNames),\n        forward\n      );\n\n      // NOTE: Since below we might delete the cached entry after accessing\n      // it once, cachedOps$ needs to be merged after forwardedOps$\n      let cachedOps$ = pipe(\n        ops$,\n        filter(\n          operation =>\n            operation.kind !== 'teardown' &&\n            !!data[operation.key] &&\n            operation.context.requestPolicy !== 'network-only'\n        ),\n        map(op => {\n          const serialized = data[op.key]!;\n          const cachedResult = deserializeResult(\n            op,\n            serialized,\n            includeExtensions\n          );\n\n          if (staleWhileRevalidate && !revalidated.has(op.key)) {\n            cachedResult.stale = true;\n            revalidated.add(op.key);\n            reexecuteOperation(client, op);\n          }\n\n          const result: OperationResult = {\n            ...cachedResult,\n            operation: addMetadata(op, {\n              cacheOutcome: 'hit',\n            }),\n          };\n          return result;\n        })\n      );\n\n      if (!isClient) {\n        // On the server we cache results in the cache as they're resolved\n        forwardedOps$ = pipe(\n          forwardedOps$,\n          tap((result: OperationResult) => {\n            const { operation } = result;\n            if (operation.kind !== 'mutation') {\n              const serialized = serializeResult(result, includeExtensions);\n              data[operation.key] = serialized;\n            }\n          })\n        );\n      } else {\n        // On the client we delete results from the cache as they're resolved\n        cachedOps$ = pipe(cachedOps$, tap(invalidate));\n      }\n\n      return merge([forwardedOps$, cachedOps$]);\n    };\n\n  ssr.restoreData = (restore: SSRData) => {\n    for (const key in restore) {\n      // We only restore data that hasn't been previously invalidated\n      if (data[key] !== null) {\n        data[key] = restore[key];\n      }\n    }\n  };\n\n  ssr.extractData = () => {\n    const result: SSRData = {};\n    for (const key in data) if (data[key] != null) result[key] = data[key]!;\n    return result;\n  };\n\n  if (params && params.initialState) {\n    ssr.restoreData(params.initialState);\n  }\n\n  return ssr;\n};\n", "import type { Subscription, Source } from 'wonka';\nimport { filter, make, merge, mergeMap, pipe, takeUntil } from 'wonka';\n\nimport {\n  makeResult,\n  mergeResultPatch,\n  makeErrorResult,\n  makeOperation,\n} from '../utils';\n\nimport type {\n  Exchange,\n  ExecutionResult,\n  Operation,\n  OperationResult,\n} from '../types';\n\nimport type { FetchBody } from '../internal';\nimport { makeFetchBody } from '../internal';\n\n/** An abstract observer-like interface.\n *\n * @remarks\n * Observer-like interfaces are passed to {@link ObservableLike.subscribe} to provide them\n * with callbacks for their events.\n *\n * @see {@link https://github.com/tc39/proposal-observable} for the full TC39 Observable proposal.\n */\nexport interface ObserverLike<T> {\n  /** Callback for values an {@link ObservableLike} emits. */\n  next: (value: T) => void;\n  /** Callback for an error an {@link ObservableLike} emits, which ends the subscription. */\n  error: (err: any) => void;\n  /** Callback for the completion of an {@link ObservableLike}, which ends the subscription. */\n  complete: () => void;\n}\n\n/** An abstract observable-like interface.\n *\n * @remarks\n * Observable, or Observable-like interfaces, are often used by GraphQL transports to abstract\n * how they send {@link ExecutionResult | ExecutionResults} to consumers. These generally contain\n * a `subscribe` method accepting an {@link ObserverLike} structure.\n *\n * @see {@link https://github.com/tc39/proposal-observable} for the full TC39 Observable proposal.\n */\nexport interface ObservableLike<T> {\n  /** Start the Observable-like subscription and returns a subscription handle.\n   *\n   * @param observer - an {@link ObserverLike} object with result, error, and completion callbacks.\n   * @returns a subscription handle providing an `unsubscribe` method to stop the subscription.\n   */\n  subscribe(observer: ObserverLike<T>): {\n    unsubscribe: () => void;\n  };\n}\n\n/** A more cross-compatible version of the {@link GraphQLRequest} structure.\n * {@link FetchBody} for more details\n */\nexport type SubscriptionOperation = FetchBody;\n\n/** A subscription forwarding function, which must accept a {@link SubscriptionOperation}.\n *\n * @param operation - A {@link SubscriptionOperation}\n * @returns An {@link ObservableLike} object issuing {@link ExecutionResult | ExecutionResults}.\n */\nexport type SubscriptionForwarder = (\n  request: FetchBody,\n  operation: Operation\n) => ObservableLike<ExecutionResult>;\n\n/** This is called to create a subscription and needs to be hooked up to a transport client. */\nexport interface SubscriptionExchangeOpts {\n  /** A subscription forwarding function, which must accept a {@link SubscriptionOperation}.\n   *\n   * @param operation - A {@link SubscriptionOperation}\n   * @returns An {@link ObservableLike} object issuing {@link ExecutionResult | ExecutionResults}.\n   *\n   * @remarks\n   * This callback is called for each {@link Operation} that this `subscriptionExchange` will\n   * handle. It receives the {@link SubscriptionOperation}, which is a more compatible version\n   * of the raw {@link Operation} objects and must return an {@link ObservableLike} of results.\n   */\n  forwardSubscription: SubscriptionForwarder;\n\n  /** Flag to enable this exchange to handle all types of GraphQL operations.\n   *\n   * @remarks\n   * When you aren’t using fetch exchanges and GraphQL over HTTP as a transport for your GraphQL requests,\n   * or you have a third-party GraphQL transport implementation, which must also be used for queries and\n   * mutations, this flag may be used to allow this exchange to handle all kinds of GraphQL operations.\n   *\n   * By default, this flag is `false` and the exchange will only handle GraphQL subscription operations.\n   */\n  enableAllOperations?: boolean;\n\n  /** A predicate function that causes an operation to be handled by this `subscriptionExchange` if `true` is returned.\n   *\n   * @param operation - an {@link Operation}\n   * @returns true when the operation is handled by this exchange.\n   *\n   * @remarks\n   * In some cases, a `subscriptionExchange` will be used to only handle some {@link Operation | Operations},\n   * e.g. all that contain `@live` directive. For these cases, this function may be passed to precisely\n   * determine which `Operation`s this exchange should handle, instead of forwarding.\n   *\n   * When specified, the {@link SubscriptionExchangeOpts.enableAllOperations} flag is disregarded.\n   */\n  isSubscriptionOperation?: (operation: Operation) => boolean;\n}\n\n/** Generic subscription exchange factory used to either create an exchange handling just subscriptions or all operation kinds.\n *\n * @remarks\n * `subscriptionExchange` can be used to create an {@link Exchange} that either\n * handles just GraphQL subscription operations, or optionally all operations,\n * when the {@link SubscriptionExchangeOpts.enableAllOperations} flag is passed.\n *\n * The {@link SubscriptionExchangeOpts.forwardSubscription} function must\n * be provided and provides a generic input that's based on {@link Operation}\n * but is compatible with many libraries implementing GraphQL request or\n * subscription interfaces.\n */\nexport const subscriptionExchange =\n  ({\n    forwardSubscription,\n    enableAllOperations,\n    isSubscriptionOperation,\n  }: SubscriptionExchangeOpts): Exchange =>\n  ({ client, forward }) => {\n    const createSubscriptionSource = (\n      operation: Operation\n    ): Source<OperationResult> => {\n      const observableish = forwardSubscription(\n        makeFetchBody(operation),\n        operation\n      );\n\n      return make<OperationResult>(observer => {\n        let isComplete = false;\n        let sub: Subscription | void;\n        let result: OperationResult | void;\n\n        function nextResult(value: ExecutionResult) {\n          observer.next(\n            (result = result\n              ? mergeResultPatch(result, value)\n              : makeResult(operation, value))\n          );\n        }\n\n        Promise.resolve().then(() => {\n          if (isComplete) return;\n\n          sub = observableish.subscribe({\n            next: nextResult,\n            error(error) {\n              if (Array.isArray(error)) {\n                // NOTE: This is an exception for transports that deliver `GraphQLError[]`, as part\n                // of the observer’s error callback (may happen as part of `graphql-ws`).\n                // We only check for arrays here, as this is an extremely “unexpected” case as the\n                // shape of `ExecutionResult` is instead strictly defined.\n                nextResult({ errors: error });\n              } else {\n                observer.next(makeErrorResult(operation, error));\n              }\n              observer.complete();\n            },\n            complete() {\n              if (!isComplete) {\n                isComplete = true;\n                if (operation.kind === 'subscription') {\n                  client.reexecuteOperation(\n                    makeOperation('teardown', operation, operation.context)\n                  );\n                }\n                if (result && result.hasNext) {\n                  nextResult({ hasNext: false });\n                }\n                observer.complete();\n              }\n            },\n          });\n        });\n\n        return () => {\n          isComplete = true;\n          if (sub) sub.unsubscribe();\n        };\n      });\n    };\n\n    const isSubscriptionOperationFn =\n      isSubscriptionOperation ||\n      (operation =>\n        operation.kind === 'subscription' ||\n        (!!enableAllOperations &&\n          (operation.kind === 'query' || operation.kind === 'mutation')));\n\n    return ops$ => {\n      const subscriptionResults$ = pipe(\n        ops$,\n        filter(\n          operation =>\n            operation.kind !== 'teardown' &&\n            isSubscriptionOperationFn(operation)\n        ),\n        mergeMap(operation => {\n          const { key } = operation;\n          const teardown$ = pipe(\n            ops$,\n            filter(op => op.kind === 'teardown' && op.key === key)\n          );\n\n          return pipe(\n            createSubscriptionSource(operation),\n            takeUntil(teardown$)\n          );\n        })\n      );\n\n      const forward$ = pipe(\n        ops$,\n        filter(\n          operation =>\n            operation.kind === 'teardown' ||\n            !isSubscriptionOperationFn(operation)\n        ),\n        forward\n      );\n\n      return merge([subscriptionResults$, forward$]);\n    };\n  };\n", "import { pipe, tap } from 'wonka';\nimport type { Exchange } from '../types';\n\n/** Simple log debugger exchange.\n *\n * @remarks\n * An exchange that logs incoming {@link Operation | Operations} and\n * {@link OperationResult | OperationResults} in development.\n *\n * This exchange is a no-op in production and often used in issue reporting\n * to understand certain usage patterns of `urql` without having access to\n * the original source code.\n *\n * Hint: When you report an issue you’re having with `urql`, adding\n * this as your first exchange and posting its output can speed up\n * issue triaging a lot!\n */\nexport const debugExchange: Exchange = ({ forward }) => {\n  if (process.env.NODE_ENV === 'production') {\n    return ops$ => forward(ops$);\n  } else {\n    return ops$ =>\n      pipe(\n        ops$,\n        // eslint-disable-next-line no-console\n        tap(op => console.log('[Exchange debug]: Incoming operation: ', op)),\n        forward,\n        tap(result =>\n          // eslint-disable-next-line no-console\n          console.log('[Exchange debug]: Completed operation: ', result)\n        )\n      );\n  }\n};\n", "/* eslint-disable @typescript-eslint/no-use-before-define */\nimport { filter, merge, mergeMap, pipe, takeUntil, onPush } from 'wonka';\n\nimport type { Exchange } from '../types';\nimport {\n  makeFetchBody,\n  makeFetchURL,\n  makeFetchOptions,\n  makeFetchSource,\n} from '../internal';\n\n/** Default GraphQL over HTTP fetch exchange.\n *\n * @remarks\n * The default fetch exchange in `urql` supports sending GraphQL over HTTP\n * requests, can optionally send GraphQL queries as GET requests, and\n * handles incremental multipart responses.\n *\n * This exchange does not handle persisted queries or multipart uploads.\n * Support for the former can be added using `@urql/exchange-persisted-fetch`\n * and the latter using `@urql/exchange-multipart-fetch`.\n *\n * Hint: The `fetchExchange` and the two other exchanges all use the built-in fetch\n * utilities in `@urql/core/internal`, which you can also use to implement\n * a customized fetch exchange.\n *\n * @see {@link makeFetchSource} for the shared utility calling the Fetch API.\n */\nexport const fetchExchange: Exchange = ({ forward, dispatchDebug }) => {\n  return ops$ => {\n    const fetchResults$ = pipe(\n      ops$,\n      filter(operation => {\n        return (\n          operation.kind !== 'teardown' &&\n          (operation.kind !== 'subscription' ||\n            !!operation.context.fetchSubscriptions)\n        );\n      }),\n      mergeMap(operation => {\n        const body = makeFetchBody(operation);\n        const url = makeFetchURL(operation, body);\n        const fetchOptions = makeFetchOptions(operation, body);\n\n        dispatchDebug({\n          type: 'fetchRequest',\n          message: 'A fetch request is being executed.',\n          operation,\n          data: {\n            url,\n            fetchOptions,\n          },\n        });\n\n        const source = pipe(\n          makeFetchSource(operation, url, fetchOptions),\n          takeUntil(\n            pipe(\n              ops$,\n              filter(op => op.kind === 'teardown' && op.key === operation.key)\n            )\n          )\n        );\n\n        if (process.env.NODE_ENV !== 'production') {\n          return pipe(\n            source,\n            onPush(result => {\n              const error = !result.data ? result.error : undefined;\n\n              dispatchDebug({\n                type: error ? 'fetchError' : 'fetchSuccess',\n                message: `A ${\n                  error ? 'failed' : 'successful'\n                } fetch response has been returned.`,\n                operation,\n                data: {\n                  url,\n                  fetchOptions,\n                  value: error || result,\n                },\n              });\n            })\n          );\n        }\n\n        return source;\n      })\n    );\n\n    const forward$ = pipe(\n      ops$,\n      filter(operation => {\n        return (\n          operation.kind === 'teardown' ||\n          (operation.kind === 'subscription' &&\n            !operation.context.fetchSubscriptions)\n        );\n      }),\n      forward\n    );\n\n    return merge([fetchResults$, forward$]);\n  };\n};\n", "import { share } from 'wonka';\nimport type { ExchangeIO, Exchange, ExchangeInput } from '../types';\n\n/** Composes an array of Exchanges into a single one.\n *\n * @param exchanges - An array of {@link Exchange | Exchanges}.\n * @returns - A composed {@link Exchange}.\n *\n * @remarks\n * `composeExchanges` returns an {@link Exchange} that when instantiated\n * composes the array of passed `Exchange`s into one, calling them from\n * right to left, with the prior `Exchange`’s {@link ExchangeIO} function\n * as the {@link ExchangeInput.forward} input.\n *\n * This simply merges all exchanges into one and is used by the {@link Client}\n * to merge the `exchanges` option it receives.\n *\n * @throws\n * In development, if {@link ExchangeInput.forward} is called repeatedly\n * by an {@link Exchange} an error is thrown, since `forward()` must only\n * be called once per `Exchange`.\n */\nexport const composeExchanges =\n  (exchanges: Exchange[]): Exchange =>\n  ({ client, forward, dispatchDebug }: ExchangeInput): ExchangeIO =>\n    exchanges.reduceRight((forward, exchange) => {\n      let forwarded = false;\n      return exchange({\n        client,\n        forward(operations$) {\n          if (process.env.NODE_ENV !== 'production') {\n            if (forwarded)\n              throw new Error(\n                'forward() must only be called once in each Exchange.'\n              );\n            forwarded = true;\n          }\n          return share(forward(share(operations$)));\n        },\n        dispatchDebug(event) {\n          dispatchDebug({\n            timestamp: Date.now(),\n            source: exchange.name,\n            ...event,\n          });\n        },\n      });\n    }, forward);\n", "import { mergeMap, fromV<PERSON><PERSON>, from<PERSON>romise, pipe } from 'wonka';\nimport type { Operation, OperationResult, Exchange } from '../types';\nimport type { CombinedError } from '../utils';\n\n/** Options for the `mapExchange` allowing it to react to incoming operations, results, or errors. */\nexport interface MapExchangeOpts {\n  /** Accepts a callback for incoming `Operation`s.\n   *\n   * @param operation - An {@link Operation} that the {@link mapExchange} received.\n   * @returns optionally a new {@link Operation} replacing the original.\n   *\n   * @remarks\n   * You may return new {@link Operation | Operations} from this function replacing\n   * the original that the {@link mapExchange} received.\n   * It’s recommended that you use the {@link makeOperation} utility to create a copy\n   * of the original when you do this. (However, this isn’t required)\n   *\n   * Hint: The callback may also be promisified and return a new {@link Operation} asynchronously,\n   * provided you place your {@link mapExchange} after all synchronous {@link Exchange | Exchanges},\n   * like after your `cacheExchange`.\n   */\n  onOperation?(operation: Operation): Promise<Operation> | Operation | void;\n  /** Accepts a callback for incoming `OperationResult`s.\n   *\n   * @param result - An {@link OperationResult} that the {@link mapExchange} received.\n   * @returns optionally a new {@link OperationResult} replacing the original.\n   *\n   * @remarks\n   * This callback may optionally return a new {@link OperationResult} that replaces the original,\n   * which you can use to modify incoming API results.\n   *\n   * Hint: The callback may also be promisified and return a new {@link Operation} asynchronously,\n   * provided you place your {@link mapExchange} after all synchronous {@link Exchange | Exchanges},\n   * like after your `cacheExchange`.\n   */\n  onResult?(\n    result: OperationResult\n  ): Promise<OperationResult> | OperationResult | void;\n  /** Accepts a callback for incoming `CombinedError`s.\n   *\n   * @param error - A {@link CombinedError} that an incoming {@link OperationResult} contained.\n   * @param operation - The {@link Operation} of the incoming {@link OperationResult}.\n   *\n   * @remarks\n   * The callback may also be promisified and return a new {@link Operation} asynchronously,\n   * provided you place your {@link mapExchange} after all synchronous {@link Exchange | Exchanges},\n   * like after your `cacheExchange`.\n   */\n  onError?(error: CombinedError, operation: Operation): void;\n}\n\n/** Creates an `Exchange` mapping over incoming operations, results, and/or errors.\n *\n * @param opts - A {@link MapExchangeOpts} configuration object, containing the callbacks the `mapExchange` will use.\n * @returns the created {@link Exchange}\n *\n * @remarks\n * The `mapExchange` may be used to react to or modify incoming {@link Operation | Operations}\n * and {@link OperationResult | OperationResults}. Optionally, it can also modify these\n * asynchronously, when a promise is returned from the callbacks.\n *\n * This is useful to, for instance, add an authentication token to a given request, when\n * the `@urql/exchange-auth` package would be overkill.\n *\n * It can also accept an `onError` callback, which can be used to react to incoming\n * {@link CombinedError | CombinedErrors} on results, and trigger side-effects.\n *\n */\nexport const mapExchange = ({\n  onOperation,\n  onResult,\n  onError,\n}: MapExchangeOpts): Exchange => {\n  return ({ forward }) =>\n    ops$ => {\n      return pipe(\n        pipe(\n          ops$,\n          mergeMap(operation => {\n            const newOperation =\n              (onOperation && onOperation(operation)) || operation;\n            return 'then' in newOperation\n              ? fromPromise(newOperation)\n              : fromValue(newOperation);\n          })\n        ),\n        forward,\n        mergeMap(result => {\n          if (onError && result.error) onError(result.error, result.operation);\n          const newResult = (onResult && onResult(result)) || result;\n          return 'then' in newResult\n            ? fromPromise(newResult)\n            : fromValue(newResult);\n        })\n      );\n    };\n};\n", "import { filter, pipe, tap } from 'wonka';\nimport type { ExchangeIO, ExchangeInput } from '../types';\n\n/** Used by the `Client` as the last exchange to warn about unhandled operations.\n *\n * @remarks\n * In a normal setup, some operations may go unhandled when a {@link Client} isn’t set up\n * with the right exchanges.\n * For instance, a `Client` may be missing a fetch exchange, or an exchange handling subscriptions.\n * This {@link Exchange} is added by the `Client` automatically to log warnings about unhandled\n * {@link Operaiton | Operations} in development.\n */\nexport const fallbackExchange: ({\n  dispatchDebug,\n}: Pick<ExchangeInput, 'dispatchDebug'>) => ExchangeIO =\n  ({ dispatchDebug }) =>\n  ops$ => {\n    if (process.env.NODE_ENV !== 'production') {\n      ops$ = pipe(\n        ops$,\n        tap(operation => {\n          if (\n            operation.kind !== 'teardown' &&\n            process.env.NODE_ENV !== 'production'\n          ) {\n            const message = `No exchange has handled operations of kind \"${operation.kind}\". Check whether you've added an exchange responsible for these operations.`;\n\n            dispatchDebug({\n              type: 'fallbackCatch',\n              message,\n              operation,\n            });\n            console.warn(message);\n          }\n        })\n      );\n    }\n\n    // All operations that skipped through the entire exchange chain should be filtered from the output\n    return filter((_x): _x is never => false)(ops$);\n  };\n", "/* eslint-disable @typescript-eslint/no-use-before-define */\n\nimport type { Source, Subscription } from 'wonka';\nimport {\n  lazy,\n  filter,\n  makeSubject,\n  onEnd,\n  onPush,\n  onStart,\n  pipe,\n  share,\n  take,\n  takeUntil,\n  takeWhile,\n  publish,\n  subscribe,\n  switchMap,\n  fromValue,\n  merge,\n  map,\n} from 'wonka';\n\nimport { composeExchanges } from './exchanges';\nimport { fallbackExchange } from './exchanges/fallback';\n\nimport type {\n  DocumentInput,\n  AnyVariables,\n  Exchange,\n  ExchangeInput,\n  GraphQLRequest,\n  Operation,\n  OperationInstance,\n  OperationContext,\n  OperationResult,\n  OperationResultSource,\n  OperationType,\n  RequestPolicy,\n  DebugEvent,\n} from './types';\n\nimport {\n  createRequest,\n  withPromise,\n  noop,\n  makeOperation,\n  getOperationType,\n} from './utils';\n\n/** Configuration options passed when creating a new {@link Client}.\n *\n * @remarks\n * The `ClientOptions` are passed when creating a new {@link Client}, and\n * are used to instantiate the pipeline of {@link Exchange | Exchanges}, configure\n * options used to initialize {@link OperationContext | OperationContexts}, or to\n * change the general behaviour of the {@link Client}.\n */\nexport interface ClientOptions {\n  /** Target URL used by fetch exchanges to make GraphQL API requests to.\n   *\n   * @remarks\n   * This is the URL that fetch exchanges will call to make GraphQL API requests.\n   * This value is copied to {@link OperationContext.url}.\n   */\n  url: string;\n  /** Additional options used by fetch exchanges that'll be passed to the `fetch` call on API requests.\n   *\n   * @remarks\n   * The options in this object or an object returned by a callback function will be merged into the\n   * {@link RequestInit} options passed to the `fetch` call.\n   *\n   * Hint: If you're trying to implement more complex changes per {@link Operation}, it's worth considering\n   * to use the {@link mapExchange} instead, which allows you to change `Operation`s and `OperationResult`s.\n   *\n   * Hint: If you're trying to use this as a function for authentication, consider checking out\n   * `@urql/exchange-auth` instead, which allows you to handle refresh auth flows, and more\n   * complex auth flows.\n   *\n   * @see {@link https://developer.mozilla.org/en-US/docs/Web/API/fetch} for a description of this object.\n   */\n  fetchOptions?: RequestInit | (() => RequestInit);\n  /** A `fetch` function polyfill used by fetch exchanges to make API calls.\n   *\n   * @remarks\n   * This is the fetch polyfill used by any fetch exchange to make an API request. By default, when this\n   * option isn't set, any fetch exchange will attempt to use the globally available `fetch` function\n   * to make a request instead.\n   *\n   * It's recommended to only pass a polyfill, if any of the environments you're running the {@link Client}\n   * in don't support the Fetch API natively.\n   *\n   * Hint: If you're using the \"Incremental Delivery\" multipart spec, for instance with `@defer` directives,\n   * you're better off using the native `fetch` function, or must ensure that your polyfill supports streamed\n   * results. However, a \"Streaming requests unsupported\" error will be thrown, to let you know that your `fetch`\n   * API doesn't support incrementally streamed responses, if this mode is used.\n   *\n   * @see {@link https://developer.mozilla.org/en-US/docs/Web/API/Fetch_API} for the Fetch API spec.\n   */\n  fetch?: typeof fetch;\n  /** Allows a subscription to be executed using a `fetch` API request.\n   *\n   * @remarks\n   * If your API supports the `text/event-stream` and/or `multipart/mixed` response protocol, and you use\n   * this protocol to handle subscriptions, then you may switch this flag to `true`.\n   *\n   * This means you won’t have to create a {@link subscriptionExchange} to handle subscriptions with an\n   * external transport, and will instead be able to use GraphQL over HTTP transports.\n   */\n  fetchSubscriptions?: boolean;\n  /** A list of `Exchange`s that will be used to create the `Client`'s execution pipeline.\n   *\n   * @remarks\n   * The {@link Client} accepts and composes a list of {@link Exchange | Exchanges} into an “exchange pipeline”\n   * which receive a stream of {@link Operation | Operations} the `Client` wishes to execute, and return a stream\n   * of {@link OperationResult | OperationResults}.\n   *\n   * This is the basis for how `urql` handles GraphQL operations, and exchanges handle the creation, execution,\n   * and control flow of exchanges for the `Client`.\n   *\n   * To easily get started you should consider using the {@link cacheExchange} and {@link fetchExchange}\n   * these are all exported from the core package.\n   *\n   * @see {@link https://urql.dev/goto/docs/architecture/#the-client-and-exchanges} for more information\n   * on what `Exchange`s are and how they work.\n   */\n  exchanges: Exchange[];\n  /** A configuration flag indicating whether support for \"Suspense\" is activated.\n   *\n   * @remarks\n   * This configuration flag is only relevant for using `urql` with the React or Preact bindings.\n   * When activated it allows `useQuery` to \"suspend\" instead of returning a loading state, which\n   * will stop updates in a querying component and instead cascade\n   * to a higher suspense boundary for a loading state.\n   *\n   * Hint: While, when this option is enabled, by default all `useQuery` hooks will suspense, you can\n   * disable Suspense selectively for each hook.\n   *\n   * @see {@link https://beta.reactjs.org/blog/2022/03/29/react-v18#new-suspense-features} for more information on React Suspense.\n   */\n  suspense?: boolean;\n  /** The request and caching strategy that all `Operation`s on this `Client` will use by default.\n   *\n   * @remarks\n   * The {@link RequestPolicy} instructs cache exchanges how to use and treat their cached results.\n   * By default `cache-first` is set and used, which will use cache results, and only make an API request\n   * on a cache miss.\n   *\n   * The `requestPolicy` can be overriden per operation, since it's added to the {@link OperationContext},\n   * which allows you to change the policy per `Operation`, rather than changing it by default here.\n   *\n   * Hint: We don’t recommend changing this from the default `cache-first` option, unless you know what\n   * you‘re doing. Setting this to `cache-and-network` is not recommend and may not lead to the behaviour\n   * you expect. If you’re looking to always update your cache frequently, use `@urql/exchange-request-policy`\n   * instead.\n   */\n  requestPolicy?: RequestPolicy;\n  /** Instructs fetch exchanges to use a GET request.\n   *\n   * @remarks\n   * This changes the {@link OperationContext.preferGetMethod} option, which tells fetch exchanges\n   * to use GET requests for queries instead of POST requests.\n   *\n   * When set to `true` or `'within-url-limit'`, built-in fetch exchanges will always attempt to send query\n   * operations as GET requests, unless the resulting URL exceeds a length of 2,048 characters.\n   * If you want to bypass this restriction, set this option to `'force'` instead, to always send GET.\n   * requests for queries.\n   */\n  preferGetMethod?: boolean | 'force' | 'within-url-limit';\n}\n\n/** The `Client` is the central hub for your GraphQL operations and holds `urql`'s state.\n *\n * @remarks\n * The `Client` manages your active GraphQL operations and their state, and contains the\n * {@link Exchange} pipeline to execute your GraphQL operations.\n *\n * It contains methods that allow you to execute GraphQL operations manually, but the `Client`\n * is also interacted with by bindings (for React, Preact, Vue, Svelte, etc) to execute GraphQL\n * operations.\n *\n * While {@link Exchange | Exchanges} are ultimately responsible for the control flow of operations,\n * sending API requests, and caching, the `Client` still has the important responsibility for\n * creating operations, managing consumers of active operations, sharing results for operations,\n * and more tasks as a “central hub”.\n *\n * @see {@link https://urql.dev/goto/docs/architecture/#requests-and-operations-on-the-client} for more information\n * on what the `Client` is and does.\n */\nexport interface Client {\n  new (options: ClientOptions): Client;\n\n  /** Exposes the stream of `Operation`s that is passed to the `Exchange` pipeline.\n   *\n   * @remarks\n   * This is a Wonka {@link Source} that issues the {@link Operation | Operations} going into\n   * the exchange pipeline.\n   * @internal\n   */\n  operations$: Source<Operation>;\n\n  /** Flag indicating whether support for “Suspense” is activated.\n   *\n   * @remarks\n   * This flag indicates whether support for “Suspense” has been activated via the\n   * {@link ClientOptions.suspense} flag.\n   *\n   * When this is enabled, the {@link Client} itself doesn’t function any differently, and the flag\n   * only serves as an instructions for the React/Preact bindings to change their behaviour.\n   *\n   * @see {@link ClientOptions.suspense} for more information.\n   * @internal\n   */\n  suspense: boolean;\n\n  /** Dispatches an `Operation` to the `Exchange` pipeline, if this `Operation` is active.\n   *\n   * @remarks\n   * This method is frequently used in {@link Exchange | Exchanges}, for instance caches, to reexecute\n   * an operation. It’s often either called because an `Operation` will need to be queried against the\n   * cache again, if a cache result has changed or been invalidated, or it’s called with an {@link Operation}'s\n   * {@link RequestPolicy} set to `network-only` to issue a network request.\n   *\n   * This method will only dispatch an {@link Operation} if it has active consumers, meaning,\n   * active subscribers to the sources of {@link OperationResult}. For instance, if no bindings\n   * (e.g. `useQuery`) is subscribed to the `Operation`, then `reexecuteOperation` will do nothing.\n   *\n   * All operations are put onto a queue and executed after a micro-tick. The queue of operations is\n   * emptied eagerly and synchronously, similar to a trampoline scheduler.\n   */\n  reexecuteOperation(operation: Operation): void;\n\n  /** Subscribe method to add an event listener to debug events.\n   *\n   * @param onEvent - A callback called with new debug events, each time an `Exchange` issues them.\n   * @returns A Wonka {@link Subscription} which is used to optionally terminate the event listener.\n   *\n   * @remarks\n   * This is a method that's only available in development, and allows the `urql-devtools` to receive\n   * to debug events that are issued by exchanges, giving the devtools more information about the flow\n   * and execution of {@link Operation | Operations}.\n   *\n   * @see {@link DebugEventTypes} for a description of all debug events.\n   * @internal\n   */\n  subscribeToDebugTarget?(onEvent: (event: DebugEvent) => void): Subscription;\n\n  /** Creates an `Operation` from a `GraphQLRequest` and optionally, overriding `OperationContext` options.\n   *\n   * @param kind - The {@link OperationType} of GraphQL operation, i.e. `query`, `mutation`, or `subscription`.\n   * @param request - A {@link GraphQLRequest} created prior to calling this method.\n   * @param opts - {@link OperationContext} options that'll override and be merged with options from the {@link ClientOptions}.\n   * @returns An {@link Operation} created from the parameters.\n   *\n   * @remarks\n   * This method is expected to be called with a `kind` set to the `OperationType` of the GraphQL operation.\n   * In development, this is enforced by checking that the GraphQL document's operation matches this `kind`.\n   *\n   * Hint: While bindings will use this method combined with {@link Client.executeRequestOperation}, if\n   * you’re executing operations manually, you can use one of the other convenience methods instead.\n   *\n   * @see {@link Client.executeRequestOperation} for the method used to execute operations.\n   * @see {@link createRequest} which creates a `GraphQLRequest` from a `DocumentNode` and variables.\n   */\n  createRequestOperation<\n    Data = any,\n    Variables extends AnyVariables = AnyVariables,\n  >(\n    kind: OperationType,\n    request: GraphQLRequest<Data, Variables>,\n    opts?: Partial<OperationContext> | undefined\n  ): Operation<Data, Variables>;\n\n  /** Creates a `Source` that executes the `Operation` and issues `OperationResult`s for this `Operation`.\n   *\n   * @param operation - {@link Operation} that will be executed.\n   * @returns A Wonka {@link Source} of {@link OperationResult | OperationResults} for the passed `Operation`.\n   *\n   * @remarks\n   * The {@link Operation} will be dispatched to the pipeline of {@link Exchange | Exchanges} when\n   * subscribing to the returned {@link Source}, which issues {@link OperationResult | OperationResults}\n   * belonging to this `Operation`.\n   *\n   * Internally, {@link OperationResult | OperationResults} are filtered and deliverd to this source by\n   * comparing the {@link Operation.key} on the operation and the {@link OperationResult.operation}.\n   * For mutations, the {@link OperationContext._instance | `OperationContext._instance`} will additionally be compared, since two mutations\n   * with, even given the same variables, will have two distinct results and will be executed separately.\n   *\n   * The {@link Client} dispatches the {@link Operation} when we subscribe to the returned {@link Source}\n   * and will from then on consider the `Operation` as “active” until we unsubscribe. When all consumers unsubscribe\n   * from an `Operation` and it becomes “inactive” a `teardown` signal will be dispatched to the\n   * {@link Exchange | Exchanges}.\n   *\n   * Hint: While bindings will use this method, if you’re executing operations manually, you can use one\n   * of the other convenience methods instead, like {@link Client.executeQuery} et al.\n   */\n  executeRequestOperation<\n    Data = any,\n    Variables extends AnyVariables = AnyVariables,\n  >(\n    operation: Operation<Data, Variables>\n  ): OperationResultSource<OperationResult<Data, Variables>>;\n\n  /** Creates a `Source` that executes the GraphQL query operation created from the passed parameters.\n   *\n   * @param query - a GraphQL document containing the query operation that will be executed.\n   * @param variables - the variables used to execute the operation.\n   * @param opts - {@link OperationContext} options that'll override and be merged with options from the {@link ClientOptions}.\n   * @returns A {@link OperationResultSource} issuing the {@link OperationResult | OperationResults} for the GraphQL operation.\n   *\n   * @remarks\n   * The `Client.query` method is useful to programmatically create and issue a GraphQL query operation.\n   * It automatically calls {@link createRequest}, {@link client.createRequestOperation}, and\n   * {@link client.executeRequestOperation} for you, and is a convenience method.\n   *\n   * Since it returns a {@link OperationResultSource} it may be chained with a `toPromise()` call to only\n   * await a single result in an async function.\n   *\n   * Hint: This is the recommended way to create queries programmatically when not using the bindings,\n   * or when you’re trying to get a single, promisified result.\n   *\n   * @example\n   * ```ts\n   * const getBookQuery = gql`\n   *   query GetBook($id: ID!) {\n   *     book(id: $id) {\n   *       id\n   *       name\n   *       author {\n   *         name\n   *       }\n   *     }\n   *   }\n   * `;\n   *\n   * async function getBook(id) {\n   *   const result = await client.query(getBookQuery, { id }).toPromise();\n   *   if (result.error) {\n   *     throw result.error;\n   *   }\n   *\n   *   return result.data.book;\n   * }\n   * ```\n   */\n  query<Data = any, Variables extends AnyVariables = AnyVariables>(\n    query: DocumentInput<Data, Variables>,\n    variables: Variables,\n    context?: Partial<OperationContext>\n  ): OperationResultSource<OperationResult<Data, Variables>>;\n\n  /** Returns the first synchronous result a `Client` provides for a given operation.\n   *\n   * @param query - a GraphQL document containing the query operation that will be executed.\n   * @param variables - the variables used to execute the operation.\n   * @param opts - {@link OperationContext} options that'll override and be merged with options from the {@link ClientOptions}.\n   * @returns An {@link OperationResult} if one became available synchronously or `null`.\n   *\n   * @remarks\n   * The `Client.readQuery` method returns a result synchronously or defaults to `null`. This is useful\n   * as it limits the result for a query operation to whatever the cache {@link Exchange} of a {@link Client}\n   * had stored and available at that moment.\n   *\n   * In `urql`, it's expected that cache exchanges return their results synchronously. The bindings\n   * and this method exploit this by using synchronous results, like these, to check what data is already\n   * in the cache.\n   *\n   * This method is similar to what all bindings do to synchronously provide the initial state for queries,\n   * regardless of whether effects afterwards that subscribe to the query operation update this state synchronously\n   * or asynchronously.\n   */\n  readQuery<Data = any, Variables extends AnyVariables = AnyVariables>(\n    query: DocumentInput<Data, Variables>,\n    variables: Variables,\n    context?: Partial<OperationContext>\n  ): OperationResult<Data, Variables> | null;\n\n  /** Creates a `Source` that executes the GraphQL query operation for the passed `GraphQLRequest`.\n   *\n   * @param query - a {@link GraphQLRequest}\n   * @param opts - {@link OperationContext} options that'll override and be merged with options from the {@link ClientOptions}.\n   * @returns A {@link PromisifiedSource} issuing the {@link OperationResult | OperationResults} for the GraphQL operation.\n   *\n   * @remarks\n   * The `Client.executeQuery` method is used to programmatically issue a GraphQL query operation.\n   * It automatically calls {@link client.createRequestOperation} and {@link client.executeRequestOperation} for you,\n   * but requires you to create a {@link GraphQLRequest} using {@link createRequest} yourself first.\n   *\n   * @see {@link Client.query} for a method that doesn't require calling {@link createRequest} yourself.\n   */\n  executeQuery<Data = any, Variables extends AnyVariables = AnyVariables>(\n    query: GraphQLRequest<Data, Variables>,\n    opts?: Partial<OperationContext> | undefined\n  ): OperationResultSource<OperationResult<Data, Variables>>;\n\n  /** Creates a `Source` that executes the GraphQL subscription operation created from the passed parameters.\n   *\n   * @param query - a GraphQL document containing the subscription operation that will be executed.\n   * @param variables - the variables used to execute the operation.\n   * @param opts - {@link OperationContext} options that'll override and be merged with options from the {@link ClientOptions}.\n   * @returns A Wonka {@link Source} issuing the {@link OperationResult | OperationResults} for the GraphQL operation.\n   *\n   * @remarks\n   * The `Client.subscription` method is useful to programmatically create and issue a GraphQL subscription operation.\n   * It automatically calls {@link createRequest}, {@link client.createRequestOperation}, and\n   * {@link client.executeRequestOperation} for you, and is a convenience method.\n   *\n   * Hint: This is the recommended way to create subscriptions programmatically when not using the bindings.\n   *\n   * @example\n   * ```ts\n   * import { pipe, subscribe } from 'wonka';\n   *\n   * const getNewsSubscription = gql`\n   *   subscription GetNews {\n   *     breakingNews {\n   *       id\n   *       text\n   *       createdAt\n   *     }\n   *   }\n   * `;\n   *\n   * function subscribeToBreakingNews() {\n   *   const subscription = pipe(\n   *     client.subscription(getNewsSubscription, {}),\n   *     subscribe(result => {\n   *       if (result.data) {\n   *         console.log(result.data.breakingNews.text);\n   *       }\n   *     })\n   *   );\n   *\n   *   return subscription.unsubscribe;\n   * }\n   * ```\n   */\n  subscription<Data = any, Variables extends AnyVariables = AnyVariables>(\n    query: DocumentInput<Data, Variables>,\n    variables: Variables,\n    context?: Partial<OperationContext>\n  ): OperationResultSource<OperationResult<Data, Variables>>;\n\n  /** Creates a `Source` that executes the GraphQL subscription operation for the passed `GraphQLRequest`.\n   *\n   * @param query - a {@link GraphQLRequest}\n   * @param opts - {@link OperationContext} options that'll override and be merged with options from the {@link ClientOptions}.\n   * @returns A {@link PromisifiedSource} issuing the {@link OperationResult | OperationResults} for the GraphQL operation.\n   *\n   * @remarks\n   * The `Client.executeSubscription` method is used to programmatically issue a GraphQL subscription operation.\n   * It automatically calls {@link client.createRequestOperation} and {@link client.executeRequestOperation} for you,\n   * but requires you to create a {@link GraphQLRequest} using {@link createRequest} yourself first.\n   *\n   * @see {@link Client.subscription} for a method that doesn't require calling {@link createRequest} yourself.\n   */\n  executeSubscription<\n    Data = any,\n    Variables extends AnyVariables = AnyVariables,\n  >(\n    query: GraphQLRequest<Data, Variables>,\n    opts?: Partial<OperationContext> | undefined\n  ): OperationResultSource<OperationResult<Data, Variables>>;\n\n  /** Creates a `Source` that executes the GraphQL mutation operation created from the passed parameters.\n   *\n   * @param query - a GraphQL document containing the mutation operation that will be executed.\n   * @param variables - the variables used to execute the operation.\n   * @param opts - {@link OperationContext} options that'll override and be merged with options from the {@link ClientOptions}.\n   * @returns A {@link PromisifiedSource} issuing the {@link OperationResult | OperationResults} for the GraphQL operation.\n   *\n   * @remarks\n   * The `Client.mutation` method is useful to programmatically create and issue a GraphQL mutation operation.\n   * It automatically calls {@link createRequest}, {@link client.createRequestOperation}, and\n   * {@link client.executeRequestOperation} for you, and is a convenience method.\n   *\n   * Since it returns a {@link PromisifiedSource} it may be chained with a `toPromise()` call to only\n   * await a single result in an async function. Since mutations will only typically issue one result,\n   * using this method is recommended.\n   *\n   * Hint: This is the recommended way to create mutations programmatically when not using the bindings,\n   * or when you’re trying to get a single, promisified result.\n   *\n   * @example\n   * ```ts\n   * const createPostMutation = gql`\n   *   mutation CreatePost($text: String!) {\n   *     createPost(text: $text) {\n   *       id\n   *       text\n   *     }\n   *   }\n   * `;\n   *\n   * async function createPost(text) {\n   *   const result = await client.mutation(createPostMutation, {\n   *     text,\n   *   }).toPromise();\n   *   if (result.error) {\n   *     throw result.error;\n   *   }\n   *\n   *   return result.data.createPost;\n   * }\n   * ```\n   */\n  mutation<Data = any, Variables extends AnyVariables = AnyVariables>(\n    query: DocumentInput<Data, Variables>,\n    variables: Variables,\n    context?: Partial<OperationContext>\n  ): OperationResultSource<OperationResult<Data, Variables>>;\n\n  /** Creates a `Source` that executes the GraphQL mutation operation for the passed `GraphQLRequest`.\n   *\n   * @param query - a {@link GraphQLRequest}\n   * @param opts - {@link OperationContext} options that'll override and be merged with options from the {@link ClientOptions}.\n   * @returns A {@link PromisifiedSource} issuing the {@link OperationResult | OperationResults} for the GraphQL operation.\n   *\n   * @remarks\n   * The `Client.executeMutation` method is used to programmatically issue a GraphQL mutation operation.\n   * It automatically calls {@link client.createRequestOperation} and {@link client.executeRequestOperation} for you,\n   * but requires you to create a {@link GraphQLRequest} using {@link createRequest} yourself first.\n   *\n   * @see {@link Client.mutation} for a method that doesn't require calling {@link createRequest} yourself.\n   */\n  executeMutation<Data = any, Variables extends AnyVariables = AnyVariables>(\n    query: GraphQLRequest<Data, Variables>,\n    opts?: Partial<OperationContext> | undefined\n  ): OperationResultSource<OperationResult<Data, Variables>>;\n}\n\nexport const Client: new (opts: ClientOptions) => Client = function Client(\n  this: Client | {},\n  opts: ClientOptions\n) {\n  if (process.env.NODE_ENV !== 'production' && !opts.url) {\n    throw new Error('You are creating an urql-client without a url.');\n  }\n\n  let ids = 0;\n\n  const replays = new Map<number, OperationResult>();\n  const active: Map<number, Source<OperationResult>> = new Map();\n  const dispatched = new Set<number>();\n  const queue: Operation[] = [];\n\n  const baseOpts = {\n    url: opts.url,\n    fetchSubscriptions: opts.fetchSubscriptions,\n    fetchOptions: opts.fetchOptions,\n    fetch: opts.fetch,\n    preferGetMethod: opts.preferGetMethod,\n    requestPolicy: opts.requestPolicy || 'cache-first',\n  };\n\n  // This subject forms the input of operations; executeOperation may be\n  // called to dispatch a new operation on the subject\n  const operations = makeSubject<Operation>();\n\n  function nextOperation(operation: Operation) {\n    if (\n      operation.kind === 'mutation' ||\n      operation.kind === 'teardown' ||\n      !dispatched.has(operation.key)\n    ) {\n      if (operation.kind === 'teardown') {\n        dispatched.delete(operation.key);\n      } else if (operation.kind !== 'mutation') {\n        dispatched.add(operation.key);\n      }\n      operations.next(operation);\n    }\n  }\n\n  // We define a queued dispatcher on the subject, which empties the queue when it's\n  // activated to allow `reexecuteOperation` to be trampoline-scheduled\n  let isOperationBatchActive = false;\n  function dispatchOperation(operation?: Operation | void) {\n    if (operation) nextOperation(operation);\n\n    if (!isOperationBatchActive) {\n      isOperationBatchActive = true;\n      while (isOperationBatchActive && (operation = queue.shift()))\n        nextOperation(operation);\n      isOperationBatchActive = false;\n    }\n  }\n\n  /** Defines how result streams are created */\n  const makeResultSource = (operation: Operation) => {\n    let result$ = pipe(\n      results$,\n      // Filter by matching key (or _instance if it’s set)\n      filter(\n        (res: OperationResult) =>\n          res.operation.kind === operation.kind &&\n          res.operation.key === operation.key &&\n          (!res.operation.context._instance ||\n            res.operation.context._instance === operation.context._instance)\n      ),\n      // End the results stream when an active teardown event is sent\n      takeUntil(\n        pipe(\n          operations.source,\n          filter(op => op.kind === 'teardown' && op.key === operation.key)\n        )\n      )\n    );\n\n    if (operation.kind !== 'query') {\n      // Interrupt subscriptions and mutations when they have no more results\n      result$ = pipe(\n        result$,\n        takeWhile(result => !!result.hasNext, true)\n      );\n    } else {\n      result$ = pipe(\n        result$,\n        // Add `stale: true` flag when a new operation is sent for queries\n        switchMap(result => {\n          const value$ = fromValue(result);\n          return result.stale || result.hasNext\n            ? value$\n            : merge([\n                value$,\n                pipe(\n                  operations.source,\n                  filter(op => op.key === operation.key),\n                  take(1),\n                  map(() => {\n                    result.stale = true;\n                    return result;\n                  })\n                ),\n              ]);\n        })\n      );\n    }\n\n    if (operation.kind !== 'mutation') {\n      result$ = pipe(\n        result$,\n        // Store replay result\n        onPush(result => {\n          if (result.stale) {\n            if (!result.hasNext) {\n              // we are dealing with an optimistic mutation or a partial result\n              dispatched.delete(operation.key);\n            } else {\n              // If the current result has queued up an operation of the same\n              // key, then `stale` refers to it\n              for (let i = 0; i < queue.length; i++) {\n                const operation = queue[i];\n                if (operation.key === result.operation.key) {\n                  dispatched.delete(operation.key);\n                  break;\n                }\n              }\n            }\n          } else if (!result.hasNext) {\n            dispatched.delete(operation.key);\n          }\n          replays.set(operation.key, result);\n        }),\n        // Cleanup active states on end of source\n        onEnd(() => {\n          // Delete the active operation handle\n          dispatched.delete(operation.key);\n          replays.delete(operation.key);\n          active.delete(operation.key);\n          // Interrupt active queue\n          isOperationBatchActive = false;\n          // Delete all queued up operations of the same key on end\n          for (let i = queue.length - 1; i >= 0; i--)\n            if (queue[i].key === operation.key) queue.splice(i, 1);\n          // Dispatch a teardown signal for the stopped operation\n          nextOperation(\n            makeOperation('teardown', operation, operation.context)\n          );\n        })\n      );\n    } else {\n      result$ = pipe(\n        result$,\n        // Send mutation operation on start\n        onStart(() => {\n          nextOperation(operation);\n        })\n      );\n    }\n\n    return share(result$);\n  };\n\n  const instance: Client =\n    this instanceof Client ? this : Object.create(Client.prototype);\n  const client: Client = Object.assign(instance, {\n    suspense: !!opts.suspense,\n    operations$: operations.source,\n\n    reexecuteOperation(operation: Operation) {\n      // Reexecute operation only if any subscribers are still subscribed to the\n      // operation's exchange results\n      if (operation.kind === 'teardown') {\n        dispatchOperation(operation);\n      } else if (operation.kind === 'mutation') {\n        queue.push(operation);\n        Promise.resolve().then(dispatchOperation);\n      } else if (active.has(operation.key)) {\n        let queued = false;\n        for (let i = 0; i < queue.length; i++) {\n          if (queue[i].key === operation.key) {\n            queue[i] = operation;\n            queued = true;\n          }\n        }\n\n        if (\n          !queued &&\n          (!dispatched.has(operation.key) ||\n            operation.context.requestPolicy === 'network-only')\n        ) {\n          queue.push(operation);\n          Promise.resolve().then(dispatchOperation);\n        } else {\n          dispatched.delete(operation.key);\n          Promise.resolve().then(dispatchOperation);\n        }\n      }\n    },\n\n    createRequestOperation(kind, request, opts) {\n      if (!opts) opts = {};\n\n      let requestOperationType: string | undefined;\n      if (\n        process.env.NODE_ENV !== 'production' &&\n        kind !== 'teardown' &&\n        (requestOperationType = getOperationType(request.query)) !== kind\n      ) {\n        throw new Error(\n          `Expected operation of type \"${kind}\" but found \"${requestOperationType}\"`\n        );\n      }\n\n      return makeOperation(kind, request, {\n        _instance:\n          kind === 'mutation'\n            ? ((ids = (ids + 1) | 0) as OperationInstance)\n            : undefined,\n        ...baseOpts,\n        ...opts,\n        requestPolicy: opts.requestPolicy || baseOpts.requestPolicy,\n        suspense: opts.suspense || (opts.suspense !== false && client.suspense),\n      });\n    },\n\n    executeRequestOperation(operation) {\n      if (operation.kind === 'mutation') {\n        return withPromise(makeResultSource(operation));\n      }\n\n      return withPromise(\n        lazy<OperationResult>(() => {\n          let source = active.get(operation.key);\n          if (!source) {\n            active.set(operation.key, (source = makeResultSource(operation)));\n          }\n\n          source = pipe(\n            source,\n            onStart(() => {\n              dispatchOperation(operation);\n            })\n          );\n\n          const replay = replays.get(operation.key);\n          if (\n            operation.kind === 'query' &&\n            replay &&\n            (replay.stale || replay.hasNext)\n          ) {\n            return pipe(\n              merge([\n                source,\n                pipe(\n                  fromValue(replay),\n                  filter(replay => replay === replays.get(operation.key))\n                ),\n              ]),\n              switchMap(fromValue)\n            );\n          } else {\n            return source;\n          }\n        })\n      );\n    },\n\n    executeQuery(query, opts) {\n      const operation = client.createRequestOperation('query', query, opts);\n      return client.executeRequestOperation(operation);\n    },\n\n    executeSubscription(query, opts) {\n      const operation = client.createRequestOperation(\n        'subscription',\n        query,\n        opts\n      );\n      return client.executeRequestOperation(operation);\n    },\n\n    executeMutation(query, opts) {\n      const operation = client.createRequestOperation('mutation', query, opts);\n      return client.executeRequestOperation(operation);\n    },\n\n    readQuery(query, variables, context) {\n      let result: OperationResult | null = null;\n\n      pipe(\n        client.query(query, variables, context),\n        subscribe(res => {\n          result = res;\n        })\n      ).unsubscribe();\n\n      return result;\n    },\n\n    query(query, variables, context) {\n      return client.executeQuery(createRequest(query, variables), context);\n    },\n\n    subscription(query, variables, context) {\n      return client.executeSubscription(\n        createRequest(query, variables),\n        context\n      );\n    },\n\n    mutation(query, variables, context) {\n      return client.executeMutation(createRequest(query, variables), context);\n    },\n  } as Client);\n\n  let dispatchDebug: ExchangeInput['dispatchDebug'] = noop;\n  if (process.env.NODE_ENV !== 'production') {\n    const { next, source } = makeSubject<DebugEvent>();\n    client.subscribeToDebugTarget = (onEvent: (e: DebugEvent) => void) =>\n      pipe(source, subscribe(onEvent));\n    dispatchDebug = next as ExchangeInput['dispatchDebug'];\n  }\n\n  // All exchange are composed into a single one and are called using the constructed client\n  // and the fallback exchange stream\n  const composedExchange = composeExchanges(opts.exchanges);\n\n  // All exchanges receive inputs using which they can forward operations to the next exchange\n  // and receive a stream of results in return, access the client, or dispatch debugging events\n  // All operations then run through the Exchange IOs in a pipeline-like fashion\n  const results$ = share(\n    composedExchange({\n      client,\n      dispatchDebug,\n      forward: fallbackExchange({ dispatchDebug }),\n    })(operations.source)\n  );\n\n  // Prevent the `results$` exchange pipeline from being closed by active\n  // cancellations cascading up from components\n  pipe(results$, publish);\n\n  return client;\n} as any;\n\n/** Accepts `ClientOptions` and creates a `Client`.\n * @param opts - A {@link ClientOptions} objects with options for the `Client`.\n * @returns A {@link Client} instantiated with `opts`.\n */\nexport const createClient = Client as any as (opts: ClientOptions) => Client;\n"], "mappings": ";;;AAAO,IAAMA,IAAO;EAClBC,MAAM;EACNC,UAAU;EACVC,sBAAsB;EACtBC,qBAAqB;EACrBC,eAAe;EACfC,OAAO;EACPC,UAAU;EACVC,iBAAiB;EACjBC,iBAAiB;EACjBC,qBAAqB;EACrBC,UAAU;EACVC,KAAK;EACLC,OAAO;EACPC,QAAQ;EACRC,SAAS;EACTC,MAAM;EACNC,MAAM;EACNC,MAAM;EACNC,QAAQ;EACRC,cAAc;EACdC,WAAW;EACXC,YAAY;EACZC,WAAW;EACXC,eAAe;;ACrBV,IAAMC,eAAN,cAA2BC,MAAAA;EAShCC,YACEC,IACAC,IACAC,IACAC,IACAC,IACAC,IACAC,IAAAA;AAEAC,UAAMP,EAAAA;AAENQ,SAAKC,OAAO;AACZD,SAAKR,UAAUA;AAEf,QAAII,IAAAA;AAAMI,WAAKJ,OAAOA;;AACtB,QAAIH,IAAAA;AAAOO,WAAKP,QAASS,MAAMC,QAAQV,EAAAA,IAASA,KAAQ,CAACA,EAAAA;;AACzD,QAAIC,IAAAA;AAAQM,WAAKN,SAASA;;AAC1B,QAAIC,IAAAA;AAAWK,WAAKL,YAAYA;;AAChC,QAAIE,IAAAA;AAAeG,WAAKH,gBAAgBA;;AAExC,QAAIO,KAAcN;AAClB,QAAA,CAAKM,MAAeP,IAAe;AACjC,UAAMQ,KAAsBR,GAAsBC;AAClD,UAAIO,MAAoD,YAAA,OAAvBA,IAAAA;AAC/BD,QAAAA,KAAcC;;IAElB;AAEAL,SAAKF,aAAaM,MAAe,CAAA;EACnC;EAEAE,SAAAA;AACE,WAAO;SAAKN;MAAMR,SAASQ,KAAKR;;EAClC;EAEAe,WAAAA;AACE,WAAOP,KAAKR;EACd;EAEA,KAAKgB,OAAOC,WAAAA,IAAAA;AACV,WAAO;EACT;;AC1CF,IAAIC;AACJ,IAAIC;AAEJ,SAASC,MAAMC,IAAAA;AACb,SAAO,IAAIxB,aAAc,qCAAoCsB,CAAAA,OAAUE,EAAAA,EAAAA;AACzE;AAEA,SAASC,QAAQC,IAAAA;AACfA,EAAAA,GAAQC,YAAYL;AACpB,MAAII,GAAQE,KAAKP,CAAAA,GAAQ;AAEvB,WADcA,EAAMQ,MAAMP,GAAMA,IAAMI,GAAQC,SAAAA;EAEhD;AACF;AAEA,IAAMG,IAAY;AAClB,SAASC,YAAYC,IAAAA;AACnB,MAAMC,KAAQD,GAAOE,MAAM,IAAA;AAC3B,MAAIC,KAAM;AACV,MAAIC,KAAe;AACnB,MAAIC,KAAoB;AACxB,MAAIC,KAAmBL,GAAMM,SAAS;AACtC,WAASC,KAAI,GAAGA,KAAIP,GAAMM,QAAQC,MAAK;AACrCV,MAAUH,YAAY;AACtB,QAAIG,EAAUF,KAAKK,GAAMO,EAAAA,CAAAA,GAAK;AAC5B,UAAIA,OAAAA,CAAOJ,MAAgBN,EAAUH,YAAYS,KAAAA;AAC/CA,QAAAA,KAAeN,EAAUH;;AAC3BU,MAAAA,KAAoBA,MAAqBG;AACzCF,MAAAA,KAAmBE;IACrB;EACF;AACA,WAASA,KAAIH,IAAmBG,MAAKF,IAAkBE,MAAK;AAC1D,QAAIA,OAAMH,IAAAA;AAAmBF,MAAAA,MAAO;;AACpCA,IAAAA,MAAOF,GAAMO,EAAAA,EAAGX,MAAMO,EAAAA,EAAcK,QAAQ,UAAU,KAAA;EACxD;AACA,SAAON;AACT;AAGA,SAASO,UAAAA;AACP,WACMC,KAAiC,IAA1BtB,EAAMuB,WAAWtB,GAAAA,GACnB,MAATqB,MACS,OAATA,MACS,OAATA,MACS,OAATA,MACS,OAATA,MACS,OAATA,MACS,UAATA,IACAA,KAAiC,IAA1BtB,EAAMuB,WAAWtB,GAAAA,GAAAA;AAExB,QAAa,OAATqB,IAAAA;AAAqB,aAA4C,QAApCA,KAAOtB,EAAMuB,WAAWtB,GAAAA,MAA2B,OAATqB,IAAAA;MAAAA;;;AAE7ErB;AACF;AAEA,IAAMuB,IAAS;AAIf,IAAMC,IAAU,IAAIC,OAClB,8BAKEF,EAAOxC,SALT,wHAeEwC,EAAOxC,SACP,MACF,GAAA;AAGF,IACW2C,IAAAA,SAAAA,IAAAA;AAAAA,EAAAA,GAAAA,GAAU,QAAA,CAAA,IAAA;AAAVA,EAAAA,GAAAA,GAAU,MAAA,CAAA,IAAA;AAAVA,EAAAA,GAAAA,GAAU,MAAA,CAAA,IAAA;AAAVA,EAAAA,GAAAA,GAAU,QAAA,CAAA,IAAA;AAAVA,EAAAA,GAAAA,GAAU,cAAA,CAAA,IAAA;AAAVA,EAAAA,GAAAA,GAAU,SAAA,CAAA,IAAA;AAAVA,EAAAA,GAAAA,GAAU,OAAA,CAAA,IAAA;AAAA,SAAVA;AAAU,EAAVA,KAAU,CAAA,CAAA;AAcrB,IAAMC,IAAkB;AAKxB,SAASC,MAAMC,IAAAA;AACb,MAAIC;AACJ,MAAIC;AACJP,IAAQnB,YAAYL;AACpB,MAA8B,OAA1BD,EAAMuB,WAAWtB,CAAAA,GAAqB;AAExCA;AACAoB,YAAAA;AACA,QAAMY,KAA0B,CAAA;AAChC,WAAiC,OAA1BjC,EAAMuB,WAAWtB,CAAAA,GAAAA;AAAqBgC,MAAAA,GAAOC,KAAKL,MAAMC,EAAAA,CAAAA;;AAC/D7B;AACAoB,YAAAA;AACA,WAAO;MACLlB,MAAM;MACN8B,QAAAA;;EAEH,WAAoC,QAA1BjC,EAAMuB,WAAWtB,CAAAA,GAAsB;AAEhDA;AACAoB,YAAAA;AACA,QAAMc,KAAgC,CAAA;AACtC,WAAiC,QAA1BnC,EAAMuB,WAAWtB,CAAAA,GAAsB;AAC5C,UAAiC,SAA5B8B,KAAQ3B,QAAQoB,CAAAA,IAAAA;AAAkB,cAAMtB,MAAM,aAAA;;AACnDmB,cAAAA;AACA,UAAgC,OAA5BrB,EAAMuB,WAAWtB,GAAAA,GAAAA;AAAuB,cAAMC,MAAM,aAAA;;AACxDmB,cAAAA;AACAc,MAAAA,GAAOD,KAAK;QACV/B,MAAM;QACNZ,MAAM;UAAEY,MAAM;UAAqB0B,OAAOE;;QAC1CF,OAAOA,MAAMC,EAAAA;;IAEjB;AACA7B;AACAoB,YAAAA;AACA,WAAO;MACLlB,MAAM;MACNgC,QAAAA;;EAEJ,WAAwD,SAA5CH,KAAOP,EAAQO,KAAKhC,CAAAA,IAA8B;AAE5DC,QAAMwB,EAAQnB;AACde,YAAAA;AACA,QAAwC,SAAnCU,KAAQC,GAAKL,EAAWS,KAAAA,IAAAA;AAC3B,aAAiB,WAAVL,KACH;QAAE5B,MAAM;UACR;QACEA,MAAM;QACN0B,OAAiB,WAAVE;;eAE8B,SAAjCA,KAAQC,GAAKL,EAAWU,GAAAA,IAAAA;AAClC,UAAIP,IAAAA;AACF,cAAM5B,MAAM,UAAA;;AAEZ,eAAO;UACLC,MAAM;UACNZ,MAAM;YACJY,MAAM;YACN0B,OAAOE;;;;eAI8B,SAAjCA,KAAQC,GAAKL,EAAWW,GAAAA,IAAe;AACjD,UAAIC;AACJ,UAA4C,SAAvCA,KAAYP,GAAKL,EAAWa,KAAAA,IAAAA;AAC/B,eAAO;UACLrC,MAAM;UACN0B,OAAOE,KAAQQ;;;AAGjB,eAAO;UACLpC,MAAM;UACN0B,OAAOE;;;IAGb,WAAqD,SAAzCA,KAAQC,GAAKL,EAAWc,WAAAA,IAAAA;AAClC,aAAO;QACLtC,MAAM;QACN0B,OAAOnB,YAAYqB,GAAMvB,MAAM,GAAA,EAAI,CAAA;QACnCkC,OAAAA;;eAE4C,SAApCX,KAAQC,GAAKL,EAAWgB,MAAAA,IAAAA;AAClC,aAAO;QACLxC,MAAM;QAGN0B,OAAOD,EAAgBrB,KAAKwB,EAAAA,IAAUa,KAAKC,MAAMd,EAAAA,IAAoBA,GAAMvB,MAAM,GAAA,EAAI;QACrFkC,OAAAA;;eAE0C,SAAlCX,KAAQC,GAAKL,EAAWmB,IAAAA,IAAAA;AAClC,aAAO;QACL3C,MAAM;QACN0B,OAAOE;;;EAGb;AAEA,QAAM7B,MAAM,OAAA;AACd;AAEA,SAAS6C,WAAWjB,IAAAA;AAClB,MAA8B,OAA1B9B,EAAMuB,WAAWtB,CAAAA,GAAqB;AACxC,QAAM+C,KAA2B,CAAA;AACjC/C;AACAoB,YAAAA;AACA,QAAI4B;AACJ,OAAG;AACD,UAAiC,SAA5BA,KAAQ7C,QAAQoB,CAAAA,IAAAA;AAAkB,cAAMtB,MAAM,UAAA;;AACnDmB,cAAAA;AACA,UAAgC,OAA5BrB,EAAMuB,WAAWtB,GAAAA,GAAAA;AAAuB,cAAMC,MAAM,UAAA;;AACxDmB,cAAAA;AACA2B,MAAAA,GAAKd,KAAK;QACR/B,MAAM;QACNZ,MAAM;UAAEY,MAAM;UAAqB0B,OAAOoB;;QAC1CpB,OAAOA,MAAMC,EAAAA;;IAEhB,SAAkC,OAA1B9B,EAAMuB,WAAWtB,CAAAA;AAC1BA;AACAoB,YAAAA;AACA,WAAO2B;EACT;AACF;AAKA,SAASE,WAAWpB,IAAAA;AAClB,MAA8B,OAA1B9B,EAAMuB,WAAWtB,CAAAA,GAAqB;AACxC,QAAMiD,KAAkC,CAAA;AACxC,QAAID;AACJ,OAAG;AACDhD;AACA,UAAiC,SAA5BgD,KAAQ7C,QAAQoB,CAAAA,IAAAA;AAAkB,cAAMtB,MAAM,WAAA;;AACnDmB,cAAAA;AACA6B,MAAAA,GAAWhB,KAAK;QACd/B,MAAM;QACNZ,MAAM;UAAEY,MAAM;UAAqB0B,OAAOoB;;QAC1CE,WAAWJ,WAAWjB,EAAAA;;IAEzB,SAAkC,OAA1B9B,EAAMuB,WAAWtB,CAAAA;AAC1B,WAAOiD;EACT;AACF;AAEA,SAASE,OAAAA;AACP,MAAIrB;AACJ,MAAIsB,KAAQ;AACZ,SAAiC,OAA1BrD,EAAMuB,WAAWtB,CAAAA,GAAqB;AAC3CoD,IAAAA;AACApD;AACAoB,YAAAA;EACF;AACA,MAAiC,SAA5BU,KAAQ3B,QAAQoB,CAAAA,IAAAA;AAAkB,UAAMtB,MAAM,WAAA;;AACnDmB,UAAAA;AACA,MAAI+B,KAAqB;IACvBjD,MAAM;IACNZ,MAAM;MAAEY,MAAM;MAAqB0B,OAAOE;;;AAE5C,KAAG;AACD,QAA8B,OAA1B/B,EAAMuB,WAAWtB,CAAAA,GAAqB;AACxCA;AACAoB,cAAAA;AACA+B,MAAAA,KAAO;QACLjD,MAAM;QACNiD,MAAMA;;IAEV;AACA,QAAIC,IAAO;AACT,UAAgC,OAA5BrD,EAAMuB,WAAWtB,GAAAA,GAAAA;AAAuB,cAAMC,MAAM,WAAA;;AACxDmB,cAAAA;AACA+B,MAAAA,KAAO;QACLjD,MAAM;QACNiD,MAAMA;;IAEV;EACD,SAAQC;AACT,SAAOD;AACT;AAIA,IAAME,IAAc,IAAI5B,OACtB,kBAKEF,EAAOxC,SACP,MACF,GAAA;AAGF,IACWuE,IAAAA,SAAAA,IAAAA;AAAAA,EAAAA,GAAAA,GAAc,SAAA,CAAA,IAAA;AAAdA,EAAAA,GAAAA,GAAc,OAAA,CAAA,IAAA;AAAA,SAAdA;AAAc,EAAdA,KAAc,CAAA,CAAA;AASzB,SAASC,eAAAA;AACP,MAAMC,KAAkC,CAAA;AACxC,MAAI1B;AACJ,MAAIC;AACJ,KAAG;AACDsB,MAAYhD,YAAYL;AACxB,QAAyD,SAApD+B,KAAOsB,EAAYtB,KAAKhC,CAAAA,IAAkC;AAC7DC,UAAMqD,EAAYhD;AAClB,UAAmC,QAA/B0B,GAAKuB,EAAeG,MAAAA,GAAiB;AACvCrC,gBAAAA;AACA,YAAIU,KAAQ3B,QAAQoB,CAAAA;AACpB,YAAa,QAATO,MAA2B,SAAVA,IAAgB;AAEnCV,kBAAAA;AACAoC,UAAAA,GAAWvB,KAAK;YACd/B,MAAM;YACNZ,MAAM;cAAEY,MAAM;cAAqB0B,OAAOE;;YAC1CmB,YAAYA,WAAAA,KAAW;;QAE3B,OAAO;AACL7B,kBAAAA;AACA,cAAc,SAAVU,IAAgB;AAElB,gBAAiC,SAA5BA,KAAQ3B,QAAQoB,CAAAA,IAAAA;AAAkB,oBAAMtB,MAAM,WAAA;;AACnDmB,oBAAAA;UACF;AACA,cAAMsC,KAAcT,WAAAA,KAAW;AAC/B,cAAgC,QAA5BlD,EAAMuB,WAAWtB,GAAAA,GAAAA;AAAwB,kBAAMC,MAAM,gBAAA;;AACzDmB,kBAAAA;AACAoC,UAAAA,GAAWvB,KAAK;YACd/B,MAAM;YACNyD,eAAe7B,KACX;cACE5B,MAAM;cACNZ,MAAM;gBAAEY,MAAM;gBAAqB0B,OAAOE;;;YAGhDmB,YAAYS;YACZH,cAAcA,aAAAA;;QAElB;MACF,WAAkD,SAAtCzB,KAAQC,GAAKuB,EAAeM,IAAAA,IAAgB;AACtD,YAAIC,KAAAA;AACJzC,gBAAAA;AAEA,YAA8B,OAA1BrB,EAAMuB,WAAWtB,CAAAA,GAAqB;AACxCA;AACAoB,kBAAAA;AACAyC,UAAAA,KAAS/B;AACT,cAAiC,SAA5BA,KAAQ3B,QAAQoB,CAAAA,IAAAA;AAAkB,kBAAMtB,MAAM,OAAA;;AACnDmB,kBAAAA;QACF;AACA,YAAM0C,KAAahB,WAAAA,KAAW;AAC9B1B,gBAAAA;AACA,YAAMsC,KAAcT,WAAAA,KAAW;AAC/B,YAAIc,KAAAA;AACJ,YAA8B,QAA1BhE,EAAMuB,WAAWtB,CAAAA,GAAsB;AACzCA;AACAoB,kBAAAA;AACA2C,UAAAA,KAAgBR,aAAAA;QAClB;AACAC,QAAAA,GAAWvB,KAAK;UACd/B,MAAM;UACN8D,OAAOH,KAAS;YAAE3D,MAAM;YAAqB0B,OAAOiC;;UACpDvE,MAAM;YAAEY,MAAM;YAAqB0B,OAAOE;;UAC1CoB,WAAWY;UACXb,YAAYS;UACZH,cAAcQ;;MAElB;IACF,OAAA;AACE,YAAM9D,MAAM,cAAA;;EAEf,SAAkC,QAA1BF,EAAMuB,WAAWtB,CAAAA;AAC1BA;AACAoB,UAAAA;AACA,SAAO;IACLlB,MAAM;IACNsD,YAAAA;;AAEJ;AAwCA,SAASS,qBAAAA;AACP,MAAIjB;AACJ,MAAIkB;AACJ,MAAiC,SAA5BlB,KAAQ7C,QAAQoB,CAAAA,IAAAA;AAAkB,UAAMtB,MAAM,oBAAA;;AACnDmB,UAAAA;AACA,MAAwB,SAApBjB,QAAQoB,CAAAA,GAAAA;AAAkB,UAAMtB,MAAM,oBAAA;;AAC1CmB,UAAAA;AACA,MAAsC,SAAjC8C,KAAa/D,QAAQoB,CAAAA,IAAAA;AAAkB,UAAMtB,MAAM,oBAAA;;AACxDmB,UAAAA;AACA,MAAMsC,KAAcT,WAAAA,KAAW;AAC/B,MAAgC,QAA5BlD,EAAMuB,WAAWtB,GAAAA,GAAAA;AAAwB,UAAMC,MAAM,oBAAA;;AACzDmB,UAAAA;AACA,SAAO;IACLlB,MAAM;IACNZ,MAAM;MAAEY,MAAM;MAAqB0B,OAAOoB;;IAC1CW,eAAe;MACbzD,MAAM;MACNZ,MAAM;QAAEY,MAAM;QAAqB0B,OAAOsC;;;IAE5CjB,YAAYS;IACZH,cAAcA,aAAAA;;AAElB;AAEA,IAAMY,IAAe;AAErB,SAASC,oBACPC,IAAAA;AAEA,MAAIrB;AACJ,MAAIsB;AACJ,MAAIZ;AACJ,MAAIW,IAAW;AACbjD,YAAAA;AACA4B,IAAAA,KAAQ7C,QAAQoB,CAAAA;AAChB+C,IAAAA,KAzEJ,SAASC,sBAAAA;AACPnD,cAAAA;AACA,UAA8B,OAA1BrB,EAAMuB,WAAWtB,CAAAA,GAAqB;AACxC,YAAMwE,KAAqC,CAAA;AAC3CxE;AACAoB,gBAAAA;AACA,YAAI4B;AACJ,WAAG;AACD,cAAgC,OAA5BjD,EAAMuB,WAAWtB,GAAAA,GAAAA;AAAuB,kBAAMC,MAAM,UAAA;;AACxD,cAAiC,SAA5B+C,KAAQ7C,QAAQoB,CAAAA,IAAAA;AAAkB,kBAAMtB,MAAM,UAAA;;AACnDmB,kBAAAA;AACA,cAAgC,OAA5BrB,EAAMuB,WAAWtB,GAAAA,GAAAA;AAAuB,kBAAMC,MAAM,oBAAA;;AACxDmB,kBAAAA;AACA,cAAMqD,KAAQtB,KAAAA;AACd,cAAIuB,KAAAA;AACJ,cAA8B,OAA1B3E,EAAMuB,WAAWtB,CAAAA,GAAqB;AACxCA;AACAoB,oBAAAA;AACAsD,YAAAA,KAAgB9C,MAAAA,IAAM;UACxB;AACAR,kBAAAA;AACAoD,UAAAA,GAAKvC,KAAK;YACR/B,MAAM;YACNyE,UAAU;cACRzE,MAAM;cACNZ,MAAM;gBAAEY,MAAM;gBAAqB0B,OAAOoB;;;YAE5CG,MAAMsB;YACNG,cAAcF;YACdzB,YAAYA,WAAAA,IAAW;;QAE1B,SAAkC,OAA1BlD,EAAMuB,WAAWtB,CAAAA;AAC1BA;AACAoB,gBAAAA;AACA,eAAOoD;MACT;IACF,EAqC2BD;AACvBb,IAAAA,KAAcT,WAAAA,KAAW;EAC3B;AACA,MAA8B,QAA1BlD,EAAMuB,WAAWtB,CAAAA,GAAsB;AACzCA;AACAoB,YAAAA;AACA,WAAO;MACLlB,MAAM;MACNmE,WAAWA,MAAc;MACzB/E,MAAM0D,KAAQ;QAAE9C,MAAM;QAAqB0B,OAAOoB;;MAClDuB,qBAAqBD;MACrBrB,YAAYS;MACZH,cAAcA,aAAAA;;EAElB;AACF;AA0DO,SAASX,MACdlC,IACAmE,IAAAA;AAGA7E,MAAM;AACN,SA9DF,SAAS8E,SAAS/E,IAAegF,IAAAA;AAC/B,QAAIjD;AACJ,QAAIkD;AACJ5D,YAAAA;AACA,QAAM6D,KAA8C,CAAA;AACpD,OAAA;AACE,UAAwC,gBAAnCnD,KAAQ3B,QAAQgE,CAAAA,IAA+B;AAClD/C,gBAAAA;AACA6D,QAAAA,GAAYhD,KAAKgC,mBAAAA,CAAAA;MAClB,WAA4E,SAAjEe,KAAaZ,oBAAoBtC,EAAAA,IAAAA;AAC3CmD,QAAAA,GAAYhD,KAAK+C,EAAAA;;AAEjB,cAAM/E,MAAM,UAAA;;aAEPD,IAAMD,GAAMkB;AAErB,QAAA,CAAK8D,IAAO;AACV,UAAIG;AACJ,aAAO;QACLhF,MAAM;QACN+E,aAAAA;QAEA,IAAA,IAAQE,IAAAA;AACND,UAAAA,KAAMC;QACP;QAGD,IAAA,MAAID;AACF,cAAA,CAAKA,IAAAA;AACHA,YAAAA,KAAM;cACJE,OAAO;cACPC,KAAKtF,GAAMkB;cACXqE,YAAAA;cACAC,UAAAA;cACAxG,QAAQ;gBACNyG,MAAMzF;gBACNT,MAAM;gBACNmG,gBAAgB;kBAAEC,MAAM;kBAAGC,QAAQ;;;;;AAIzC,iBAAOT;QACT;;IAEJ;AAEA,WAAO;MACLhF,MAAM;MACN+E,aAAAA;;EAEJ,EAUElF,IAA+B,YAAA,OAAhBW,GAAO8E,OAAoB9E,GAAO8E,OAAO9E,IAEjCmE,MAAWA,GAAQe,UAAAA;AAC5C;AExgBA,SAASC,QAAWC,IAAqBC,IAAgBC,IAAAA;AACvD,MAAIC,KAAM;AACV,WAASC,KAAQ,GAAGA,KAAQJ,GAAMK,QAAQD,MAAS;AACjD,QAAIA,IAAAA;AAAOD,MAAAA,MAAOF;;AAClBE,IAAAA,MAAOD,GAAOF,GAAMI,EAAAA,CAAAA;EACtB;AACA,SAAOD;AACT;AAEA,SAASG,YAAYC,IAAAA;AACnB,SAAOC,KAAKC,UAAUF,EAAAA;AACxB;AAEA,SAASG,iBAAiBH,IAAAA;AACxB,SAAO,UAAUA,GAAOI,QAAQ,QAAQ,OAAA,IAAW;AACrD;AAIA,IAAIC,IAAK;AAET,IAAMC,IAAQ;EACZC,oBAAoBC,IAAAA;AAClB,QAAIZ,KAAcY,GAAKC;AACvB,QAAID,GAAKE,MAAAA;AAAMd,MAAAA,MAAO,MAAMY,GAAKE,KAAKjB;;AACtC,QAAIe,GAAKG,uBAAuBH,GAAKG,oBAAoBb,QAAQ;AAC/D,UAAA,CAAKU,GAAKE,MAAAA;AAAMd,QAAAA,MAAO;;AACvBA,MAAAA,MAAO,MAAMJ,QAAQgB,GAAKG,qBAAqB,MAAML,EAAMM,kBAAAA,IAAsB;IACnF;AACA,QAAIJ,GAAKK,cAAcL,GAAKK,WAAWf,QAAAA;AACrCF,MAAAA,MAAO,MAAMJ,QAAQgB,GAAKK,YAAY,KAAKP,EAAMQ,SAAAA;;AACnD,WAAe,YAARlB,KACHA,KAAM,MAAMU,EAAMS,aAAaP,GAAKQ,YAAAA,IACpCV,EAAMS,aAAaP,GAAKQ,YAAAA;EAC7B;EACDJ,mBAAmBJ,IAAAA;AACjB,QAAIZ,KAAMU,EAAMW,SAAUT,GAAKU,QAAAA,IAAY,OAAOC,OAAOX,GAAKY,IAAAA;AAC9D,QAAIZ,GAAKa,cAAAA;AAAczB,MAAAA,MAAO,QAAQuB,OAAOX,GAAKa,YAAAA;;AAClD,QAAIb,GAAKK,cAAcL,GAAKK,WAAWf,QAAAA;AACrCF,MAAAA,MAAO,MAAMJ,QAAQgB,GAAKK,YAAY,KAAKP,EAAMQ,SAAAA;;AACnD,WAAOlB;EACR;EACD0B,MAAMd,IAAAA;AACJ,QAAIZ,KAAMY,GAAKe,QAAQf,GAAKe,MAAM9B,QAAQ,OAAOe,GAAKE,KAAKjB,QAAQe,GAAKE,KAAKjB;AAC7E,QAAIe,GAAKgB,aAAahB,GAAKgB,UAAU1B,QAAQ;AAC3C,UAAM2B,KAAOjC,QAAQgB,GAAKgB,WAAW,MAAMlB,EAAMoB,QAAAA;AACjD,UAAI9B,GAAIE,SAAS2B,GAAK3B,SAAS,IA7Bb,IAAA;AA8BhBF,QAAAA,MACE,OACCS,KAAM,QACPb,QAAQgB,GAAKgB,WAAWnB,GAAIC,EAAMoB,QAAAA,KACjCrB,IAAKA,EAAGsB,MAAM,GAAA,EAAI,KACnB;;AAEF/B,QAAAA,MAAO,MAAM6B,KAAO;;IAExB;AACA,QAAIjB,GAAKK,cAAcL,GAAKK,WAAWf,QAAAA;AACrCF,MAAAA,MAAO,MAAMJ,QAAQgB,GAAKK,YAAY,KAAKP,EAAMQ,SAAAA;;AACnD,QAAIN,GAAKQ,gBAAgBR,GAAKQ,aAAaY,WAAW9B,QAAAA;AACpDF,MAAAA,MAAO,MAAMU,EAAMS,aAAaP,GAAKQ,YAAAA;;AAEvC,WAAOpB;EACR;EACDiC,YAAYrB,IAAAA;AACV,QAAIA,GAAKsB,OAAAA;AACP,aAAO3B,iBAAiBK,GAAKf,KAAAA,EAAOW,QAAQ,OAAOC,CAAAA;;AAEnD,aAAON,YAAYS,GAAKf,KAAAA;;EAE3B;EACDsC,cAAavB,CAAAA,OACJ,KAAKA,GAAKf;EAEnBuC,WAAUC,CAAAA,OACD;EAETC,UAAS1B,CAAAA,OACAA,GAAKf;EAEd0C,YAAW3B,CAAAA,OACFA,GAAKf;EAEd2C,WAAU5B,CAAAA,OACDA,GAAKf;EAEd4C,MAAK7B,CAAAA,OACIA,GAAKf;EAEdwB,UAAST,CAAAA,OACA,MAAMA,GAAKE,KAAKjB;EAEzB6C,WAAU9B,CAAAA,OACD,MAAMhB,QAAQgB,GAAK+B,QAAQ,MAAMpB,MAAAA,IAAU;EAEpDqB,aAAYhC,CAAAA,OACH,MAAMhB,QAAQgB,GAAKiC,QAAQ,MAAMnC,EAAMoC,WAAAA,IAAe;EAE/DA,aAAYlC,CAAAA,OACHA,GAAKE,KAAKjB,QAAQ,OAAO0B,OAAOX,GAAKf,KAAAA;EAE9CkD,SAASnC,IAAAA;AACP,QAAA,CAAKA,GAAKoC,eAAAA,CAAgBpC,GAAKoC,YAAY9C,QAAAA;AAAQ,aAAO;;AAC1D,WAAON,QAAQgB,GAAKoC,aAAa,QAAQzB,MAAAA;EAC1C;EACDJ,cAAaP,CAAAA,OACJ,OAAOH,KAAM,QAAQb,QAAQgB,GAAKoB,YAAYvB,GAAIc,MAAAA,KAAWd,IAAKA,EAAGsB,MAAM,GAAA,EAAI,KAAM;EAE9FD,UAASlB,CAAAA,OACAA,GAAKE,KAAKjB,QAAQ,OAAO0B,OAAOX,GAAKf,KAAAA;EAE9CoD,eAAerC,IAAAA;AACb,QAAIZ,KAAM,QAAQY,GAAKE,KAAKjB;AAC5B,QAAIe,GAAKK,cAAcL,GAAKK,WAAWf,QAAAA;AACrCF,MAAAA,MAAO,MAAMJ,QAAQgB,GAAKK,YAAY,KAAKP,EAAMQ,SAAAA;;AACnD,WAAOlB;EACR;EACDkD,eAAetC,IAAAA;AACb,QAAIZ,KAAM;AACV,QAAIY,GAAKuC,eAAAA;AAAenD,MAAAA,MAAO,SAASY,GAAKuC,cAAcrC,KAAKjB;;AAChE,QAAIe,GAAKK,cAAcL,GAAKK,WAAWf,QAAAA;AACrCF,MAAAA,MAAO,MAAMJ,QAAQgB,GAAKK,YAAY,KAAKP,EAAMQ,SAAAA;;AAEnD,WADAlB,MAAO,MAAMU,EAAMS,aAAaP,GAAKQ,YAAAA;EAEtC;EACDgC,mBAAmBxC,IAAAA;AACjB,QAAIZ,KAAM,cAAcY,GAAKE,KAAKjB;AAClCG,IAAAA,MAAO,SAASY,GAAKuC,cAAcrC,KAAKjB;AACxC,QAAIe,GAAKK,cAAcL,GAAKK,WAAWf,QAAAA;AACrCF,MAAAA,MAAO,MAAMJ,QAAQgB,GAAKK,YAAY,KAAKP,EAAMQ,SAAAA;;AACnD,WAAOlB,KAAM,MAAMU,EAAMS,aAAaP,GAAKQ,YAAAA;EAC5C;EACDF,UAAUN,IAAAA;AACR,QAAIZ,KAAM,MAAMY,GAAKE,KAAKjB;AAC1B,QAAIe,GAAKgB,aAAahB,GAAKgB,UAAU1B,QAAAA;AACnCF,MAAAA,MAAO,MAAMJ,QAAQgB,GAAKgB,WAAW,MAAMlB,EAAMoB,QAAAA,IAAY;;AAC/D,WAAO9B;EACR;EACDqD,WAAUzC,CAAAA,OACDA,GAAKE,KAAKjB;EAEnByD,UAAS1C,CAAAA,OACA,MAAMW,OAAOX,GAAKY,IAAAA,IAAQ;EAEnC+B,aAAY3C,CAAAA,OACHW,OAAOX,GAAKY,IAAAA,IAAQ;;AAI/B,IAAMD,SAAUX,CAAAA,OAA0BF,EAAME,GAAK4C,IAAAA,EAAM5C,EAAAA;AAE3D,SAAS6C,MAAM7C,IAAAA;AACbH,MAAK;AACL,SAAOC,EAAME,GAAK4C,IAAAA,IAAQ9C,EAAME,GAAK4C,IAAAA,EAAM5C,EAAAA,IAAQ;AACrD;;;AGtLA,IAAI,sBAAsB,MAAM;AAAC;AAEjC,IAAI8C,KAAI;AAER,SAAS,MAAMA,IAAG;AAChB,SAAO;AAAA,IACL,KAAK;AAAA,IACL,GAAGA;AAAA,EACL;AACF;AAEA,SAAS,KAAKA,IAAG;AACf,SAAO;AAAA,IACL,KAAK;AAAA,IACL,GAAGA;AAAA,EACL;AACF;AAEA,IAAI,sBAAsB,MAAM,cAAc,OAAO,UAAU,OAAO,iBAAiB;AAIvF,IAAI,WAAW,CAAAC,OAAKA;AAkJpB,SAAS,OAAOC,IAAG;AACjB,SAAO,CAAAC,OAAK,CAAAC,OAAK;AACf,QAAIC,KAAIC;AACR,IAAAH,GAAG,CAAAG,OAAK;AACN,UAAI,MAAMA,IAAG;AACX,QAAAF,GAAE,CAAC;AAAA,MACL,WAAW,MAAME,GAAE,KAAK;AACtB,QAAAD,KAAIC,GAAE,CAAC;AACP,QAAAF,GAAEE,EAAC;AAAA,MACL,WAAW,CAACJ,GAAEI,GAAE,CAAC,CAAC,GAAG;AACnB,QAAAD,GAAE,CAAC;AAAA,MACL,OAAO;AACL,QAAAD,GAAEE,EAAC;AAAA,MACL;AAAA,IACF,CAAE;AAAA,EACJ;AACF;AAEA,SAAS,IAAIA,IAAG;AACd,SAAO,CAAAJ,OAAK,CAAAC,OAAKD,GAAG,CAAAA,OAAK;AACvB,QAAI,MAAMA,MAAK,MAAMA,GAAE,KAAK;AAC1B,MAAAC,GAAED,EAAC;AAAA,IACL,OAAO;AACL,MAAAC,GAAE,KAAKG,GAAEJ,GAAE,CAAC,CAAC,CAAC,CAAC;AAAA,IACjB;AAAA,EACF,CAAE;AACJ;AAEA,SAAS,SAASA,IAAG;AACnB,SAAO,CAAAC,OAAK,CAAAC,OAAK;AACf,QAAIC,KAAI,CAAC;AACT,QAAIE,KAAID;AACR,QAAIE,KAAI;AACR,QAAIC,KAAI;AACR,IAAAN,GAAG,CAAAA,OAAK;AACN,UAAIM,IAAG;AAAA,MAAC,WAAW,MAAMN,IAAG;AAC1B,QAAAM,KAAI;AACJ,YAAI,CAACJ,GAAE,QAAQ;AACb,UAAAD,GAAE,CAAC;AAAA,QACL;AAAA,MACF,WAAW,MAAMD,GAAE,KAAK;AACtB,QAAAI,KAAIJ,GAAE,CAAC;AAAA,MACT,OAAO;AACL,QAAAK,KAAI;AACJ,SAAC,SAAS,iBAAiBN,IAAG;AAC5B,cAAIC,KAAIG;AACR,UAAAJ,GAAG,CAAAI,OAAK;AACN,gBAAI,MAAMA,IAAG;AACX,kBAAID,GAAE,QAAQ;AACZ,oBAAIH,KAAIG,GAAE,QAAQF,EAAC;AACnB,oBAAID,KAAI,IAAI;AACV,mBAACG,KAAIA,GAAE,MAAM,GAAG,OAAOH,IAAG,CAAC;AAAA,gBAC7B;AACA,oBAAI,CAACG,GAAE,QAAQ;AACb,sBAAII,IAAG;AACL,oBAAAL,GAAE,CAAC;AAAA,kBACL,WAAW,CAACI,IAAG;AACb,oBAAAA,KAAI;AACJ,oBAAAD,GAAE,CAAC;AAAA,kBACL;AAAA,gBACF;AAAA,cACF;AAAA,YACF,WAAW,MAAMD,GAAE,KAAK;AACtB,cAAAD,GAAE,KAAKF,KAAIG,GAAE,CAAC,CAAC;AACf,cAAAH,GAAE,CAAC;AAAA,YACL,WAAWE,GAAE,QAAQ;AACnB,cAAAD,GAAEE,EAAC;AACH,cAAAH,GAAE,CAAC;AAAA,YACL;AAAA,UACF,CAAE;AAAA,QACJ,EAAED,GAAEC,GAAE,CAAC,CAAC,CAAC;AACT,YAAI,CAACK,IAAG;AACN,UAAAA,KAAI;AACJ,UAAAD,GAAE,CAAC;AAAA,QACL;AAAA,MACF;AAAA,IACF,CAAE;AACF,IAAAH,GAAE,MAAO,CAAAE,OAAK;AACZ,UAAI,MAAMA,IAAG;AACX,YAAI,CAACG,IAAG;AACN,UAAAA,KAAI;AACJ,UAAAF,GAAE,CAAC;AAAA,QACL;AACA,iBAASL,KAAI,GAAGC,KAAIE,IAAGD,KAAIC,GAAE,QAAQH,KAAIE,IAAGF,MAAK;AAC/C,UAAAC,GAAED,EAAC,EAAE,CAAC;AAAA,QACR;AACA,QAAAG,GAAE,SAAS;AAAA,MACb,OAAO;AACL,YAAI,CAACI,MAAK,CAACD,IAAG;AACZ,UAAAA,KAAI;AACJ,UAAAD,GAAE,CAAC;AAAA,QACL,OAAO;AACL,UAAAC,KAAI;AAAA,QACN;AACA,iBAASE,KAAI,GAAGC,KAAIN,IAAGO,KAAIP,GAAE,QAAQK,KAAIE,IAAGF,MAAK;AAC/C,UAAAC,GAAED,EAAC,EAAE,CAAC;AAAA,QACR;AAAA,MACF;AAAA,IACF,CAAE,CAAC;AAAA,EACL;AACF;AAEA,SAAS,SAASJ,IAAG;AACnB,SAAO,SAAS,QAAQ,EAAEA,EAAC;AAC7B;AAEA,SAAS,MAAMA,IAAG;AAChB,SAAO,SAAS,EAAEA,EAAC,CAAC;AACtB;AAEA,SAAS,MAAMA,IAAG;AAChB,SAAO,CAAAJ,OAAK,CAAAC,OAAK;AACf,QAAIC,KAAI;AACR,IAAAF,GAAG,CAAAA,OAAK;AACN,UAAIE,IAAG;AAAA,MAAC,WAAW,MAAMF,IAAG;AAC1B,QAAAE,KAAI;AACJ,QAAAD,GAAE,CAAC;AACH,QAAAG,GAAE;AAAA,MACJ,WAAW,MAAMJ,GAAE,KAAK;AACtB,YAAIG,KAAIH,GAAE,CAAC;AACX,QAAAC,GAAE,MAAO,CAAAD,OAAK;AACZ,cAAI,MAAMA,IAAG;AACX,YAAAE,KAAI;AACJ,YAAAC,GAAE,CAAC;AACH,YAAAC,GAAE;AAAA,UACJ,OAAO;AACL,YAAAD,GAAEH,EAAC;AAAA,UACL;AAAA,QACF,CAAE,CAAC;AAAA,MACL,OAAO;AACL,QAAAC,GAAED,EAAC;AAAA,MACL;AAAA,IACF,CAAE;AAAA,EACJ;AACF;AAEA,SAAS,OAAOI,IAAG;AACjB,SAAO,CAAAJ,OAAK,CAAAC,OAAK;AACf,QAAIC,KAAI;AACR,IAAAF,GAAG,CAAAA,OAAK;AACN,UAAIE,IAAG;AAAA,MAAC,WAAW,MAAMF,IAAG;AAC1B,QAAAE,KAAI;AACJ,QAAAD,GAAE,CAAC;AAAA,MACL,WAAW,MAAMD,GAAE,KAAK;AACtB,YAAIG,KAAIH,GAAE,CAAC;AACX,QAAAC,GAAE,MAAO,CAAAG,OAAK;AACZ,cAAI,MAAMA,IAAG;AACX,YAAAF,KAAI;AAAA,UACN;AACA,UAAAC,GAAEC,EAAC;AAAA,QACL,CAAE,CAAC;AAAA,MACL,OAAO;AACL,QAAAA,GAAEJ,GAAE,CAAC,CAAC;AACN,QAAAC,GAAED,EAAC;AAAA,MACL;AAAA,IACF,CAAE;AAAA,EACJ;AACF;AAEA,SAAS,QAAQI,IAAG;AAClB,SAAO,CAAAJ,OAAK,CAAAC,OAAKD,GAAG,CAAAA,OAAK;AACvB,QAAI,MAAMA,IAAG;AACX,MAAAC,GAAE,CAAC;AAAA,IACL,WAAW,MAAMD,GAAE,KAAK;AACtB,MAAAC,GAAED,EAAC;AACH,MAAAI,GAAE;AAAA,IACJ,OAAO;AACL,MAAAH,GAAED,EAAC;AAAA,IACL;AAAA,EACF,CAAE;AACJ;AAqEA,SAAS,MAAMW,IAAG;AAChB,MAAIC,KAAI,CAAC;AACT,MAAIC,KAAIC;AACR,MAAIC,KAAI;AACR,SAAO,CAAAD,OAAK;AACV,IAAAF,GAAE,KAAKE,EAAC;AACR,QAAI,MAAMF,GAAE,QAAQ;AAClB,MAAAD,GAAG,CAAAG,OAAK;AACN,YAAI,MAAMA,IAAG;AACX,mBAASH,KAAI,GAAGK,KAAIJ,IAAGK,KAAIL,GAAE,QAAQD,KAAIM,IAAGN,MAAK;AAC/C,YAAAK,GAAEL,EAAC,EAAE,CAAC;AAAA,UACR;AACA,UAAAC,GAAE,SAAS;AAAA,QACb,WAAW,MAAME,GAAE,KAAK;AACtB,UAAAD,KAAIC,GAAE,CAAC;AAAA,QACT,OAAO;AACL,UAAAC,KAAI;AACJ,mBAASG,KAAI,GAAGC,KAAIP,IAAGQ,KAAIR,GAAE,QAAQM,KAAIE,IAAGF,MAAK;AAC/C,YAAAC,GAAED,EAAC,EAAEJ,EAAC;AAAA,UACR;AAAA,QACF;AAAA,MACF,CAAE;AAAA,IACJ;AACA,IAAAA,GAAE,MAAO,CAAAH,OAAK;AACZ,UAAI,MAAMA,IAAG;AACX,YAAIK,KAAIJ,GAAE,QAAQE,EAAC;AACnB,YAAIE,KAAI,IAAI;AACV,WAACJ,KAAIA,GAAE,MAAM,GAAG,OAAOI,IAAG,CAAC;AAAA,QAC7B;AACA,YAAI,CAACJ,GAAE,QAAQ;AACb,UAAAC,GAAE,CAAC;AAAA,QACL;AAAA,MACF,WAAW,CAACE,IAAG;AACb,QAAAA,KAAI;AACJ,QAAAF,GAAE,CAAC;AAAA,MACL;AAAA,IACF,CAAE,CAAC;AAAA,EACL;AACF;AAuGA,SAAS,UAAUQ,IAAG;AACpB,SAAO,CAAAC,OAAK,CAAAC,OAAK;AACf,QAAIC,KAAIC;AACR,QAAIC,KAAID;AACR,QAAIE,KAAI;AACR,QAAIC,KAAI;AACR,QAAIC,KAAI;AACR,QAAIC,KAAI;AACR,IAAAR,GAAG,CAAAA,OAAK;AACN,UAAIQ,IAAG;AAAA,MAAC,WAAW,MAAMR,IAAG;AAC1B,QAAAQ,KAAI;AACJ,YAAI,CAACD,IAAG;AACN,UAAAN,GAAE,CAAC;AAAA,QACL;AAAA,MACF,WAAW,MAAMD,GAAE,KAAK;AACtB,QAAAE,KAAIF,GAAE,CAAC;AAAA,MACT,OAAO;AACL,YAAIO,IAAG;AACL,UAAAH,GAAE,CAAC;AACH,UAAAA,KAAID;AAAA,QACN;AACA,YAAI,CAACE,IAAG;AACN,UAAAA,KAAI;AACJ,UAAAH,GAAE,CAAC;AAAA,QACL,OAAO;AACL,UAAAG,KAAI;AAAA,QACN;AACA,SAAC,SAAS,iBAAiBF,IAAG;AAC5B,UAAAI,KAAI;AACJ,UAAAJ,GAAG,CAAAA,OAAK;AACN,gBAAI,CAACI,IAAG;AAAA,YAAC,WAAW,MAAMJ,IAAG;AAC3B,cAAAI,KAAI;AACJ,kBAAIC,IAAG;AACL,gBAAAP,GAAE,CAAC;AAAA,cACL,WAAW,CAACI,IAAG;AACb,gBAAAA,KAAI;AACJ,gBAAAH,GAAE,CAAC;AAAA,cACL;AAAA,YACF,WAAW,MAAMC,GAAE,KAAK;AACtB,cAAAG,KAAI;AACJ,eAACF,KAAID,GAAE,CAAC,GAAG,CAAC;AAAA,YACd,OAAO;AACL,cAAAF,GAAEE,EAAC;AACH,kBAAI,CAACG,IAAG;AACN,gBAAAF,GAAE,CAAC;AAAA,cACL,OAAO;AACL,gBAAAE,KAAI;AAAA,cACN;AAAA,YACF;AAAA,UACF,CAAE;AAAA,QACJ,EAAEP,GAAEC,GAAE,CAAC,CAAC,CAAC;AAAA,MACX;AAAA,IACF,CAAE;AACF,IAAAC,GAAE,MAAO,CAAAE,OAAK;AACZ,UAAI,MAAMA,IAAG;AACX,YAAI,CAACK,IAAG;AACN,UAAAA,KAAI;AACJ,UAAAN,GAAE,CAAC;AAAA,QACL;AACA,YAAIK,IAAG;AACL,UAAAA,KAAI;AACJ,UAAAH,GAAE,CAAC;AAAA,QACL;AAAA,MACF,OAAO;AACL,YAAI,CAACI,MAAK,CAACH,IAAG;AACZ,UAAAA,KAAI;AACJ,UAAAH,GAAE,CAAC;AAAA,QACL;AACA,YAAIK,MAAK,CAACD,IAAG;AACX,UAAAA,KAAI;AACJ,UAAAF,GAAE,CAAC;AAAA,QACL;AAAA,MACF;AAAA,IACF,CAAE,CAAC;AAAA,EACL;AACF;AAMA,SAAS,KAAKK,IAAG;AACf,SAAO,CAAAC,OAAK,CAAAC,OAAK;AACf,QAAIC,KAAIC;AACR,QAAIC,KAAI;AACR,QAAIC,KAAI;AACR,IAAAL,GAAG,CAAAG,OAAK;AACN,UAAIC,IAAG;AAAA,MAAC,WAAW,MAAMD,IAAG;AAC1B,QAAAC,KAAI;AACJ,QAAAH,GAAE,CAAC;AAAA,MACL,WAAW,MAAME,GAAE,KAAK;AACtB,YAAIJ,MAAK,GAAG;AACV,UAAAK,KAAI;AACJ,UAAAH,GAAE,CAAC;AACH,UAAAE,GAAE,CAAC,EAAE,CAAC;AAAA,QACR,OAAO;AACL,UAAAD,KAAIC,GAAE,CAAC;AAAA,QACT;AAAA,MACF,WAAWE,OAAMN,IAAG;AAClB,QAAAE,GAAEE,EAAC;AACH,YAAI,CAACC,MAAKC,MAAKN,IAAG;AAChB,UAAAK,KAAI;AACJ,UAAAH,GAAE,CAAC;AACH,UAAAC,GAAE,CAAC;AAAA,QACL;AAAA,MACF,OAAO;AACL,QAAAD,GAAEE,EAAC;AAAA,MACL;AAAA,IACF,CAAE;AACF,IAAAF,GAAE,MAAO,CAAAE,OAAK;AACZ,UAAI,MAAMA,MAAK,CAACC,IAAG;AACjB,QAAAA,KAAI;AACJ,QAAAF,GAAE,CAAC;AAAA,MACL,WAAW,MAAMC,MAAK,CAACC,MAAKC,KAAIN,IAAG;AACjC,QAAAG,GAAE,CAAC;AAAA,MACL;AAAA,IACF,CAAE,CAAC;AAAA,EACL;AACF;AA2BA,SAAS,UAAUI,IAAG;AACpB,SAAO,CAAAC,OAAK,CAAAC,OAAK;AACf,QAAIC,KAAIC;AACR,QAAIC,KAAID;AACR,QAAIE,KAAI;AACR,IAAAL,GAAG,CAAAG,OAAK;AACN,UAAIE,IAAG;AAAA,MAAC,WAAW,MAAMF,IAAG;AAC1B,QAAAE,KAAI;AACJ,QAAAD,GAAE,CAAC;AACH,QAAAH,GAAE,CAAC;AAAA,MACL,WAAW,MAAME,GAAE,KAAK;AACtB,QAAAD,KAAIC,GAAE,CAAC;AACP,QAAAJ,GAAG,CAAAI,OAAK;AACN,cAAI,MAAMA,IAAG;AAAA,UAAC,WAAW,MAAMA,GAAE,KAAK;AACpC,aAACC,KAAID,GAAE,CAAC,GAAG,CAAC;AAAA,UACd,OAAO;AACL,YAAAE,KAAI;AACJ,YAAAD,GAAE,CAAC;AACH,YAAAF,GAAE,CAAC;AACH,YAAAD,GAAE,CAAC;AAAA,UACL;AAAA,QACF,CAAE;AAAA,MACJ,OAAO;AACL,QAAAA,GAAEE,EAAC;AAAA,MACL;AAAA,IACF,CAAE;AACF,IAAAF,GAAE,MAAO,CAAAE,OAAK;AACZ,UAAI,MAAMA,MAAK,CAACE,IAAG;AACjB,QAAAA,KAAI;AACJ,QAAAH,GAAE,CAAC;AACH,QAAAE,GAAE,CAAC;AAAA,MACL,WAAW,CAACC,IAAG;AACb,QAAAH,GAAE,CAAC;AAAA,MACL;AAAA,IACF,CAAE,CAAC;AAAA,EACL;AACF;AAEA,SAAS,UAAUH,IAAGC,IAAG;AACvB,SAAO,CAAAC,OAAK,CAAAC,OAAK;AACf,QAAIE,KAAID;AACR,QAAIE,KAAI;AACR,IAAAJ,GAAG,CAAAE,OAAK;AACN,UAAIE,IAAG;AAAA,MAAC,WAAW,MAAMF,IAAG;AAC1B,QAAAE,KAAI;AACJ,QAAAH,GAAE,CAAC;AAAA,MACL,WAAW,MAAMC,GAAE,KAAK;AACtB,QAAAC,KAAID,GAAE,CAAC;AACP,QAAAD,GAAEC,EAAC;AAAA,MACL,WAAW,CAACJ,GAAEI,GAAE,CAAC,CAAC,GAAG;AACnB,QAAAE,KAAI;AACJ,YAAIL,IAAG;AACL,UAAAE,GAAEC,EAAC;AAAA,QACL;AACA,QAAAD,GAAE,CAAC;AACH,QAAAE,GAAE,CAAC;AAAA,MACL,OAAO;AACL,QAAAF,GAAEC,EAAC;AAAA,MACL;AAAA,IACF,CAAE;AAAA,EACJ;AACF;AAqGA,SAAS,KAAKG,IAAG;AACf,SAAO,CAAAC,OAAKD,GAAE,EAAEC,EAAC;AACnB;AAEA,SAAS,kBAAkBD,IAAG;AAC5B,SAAO,CAAAC,OAAK;AACV,QAAIC,KAAIF,GAAE,oBAAoB,CAAC,KAAKA,GAAE,oBAAoB,CAAC,EAAE,KAAKA;AAClE,QAAIG,KAAI;AACR,QAAIC,KAAI;AACR,QAAIC,KAAI;AACR,QAAIC;AACJ,IAAAL,GAAE,MAAO,OAAMD,OAAK;AAClB,UAAI,MAAMA,IAAG;AACX,QAAAG,KAAI;AACJ,YAAID,GAAE,QAAQ;AACZ,UAAAA,GAAE,OAAO;AAAA,QACX;AAAA,MACF,WAAWE,IAAG;AACZ,QAAAC,KAAI;AAAA,MACN,OAAO;AACL,aAAKA,KAAID,KAAI,MAAIC,MAAK,CAACF,MAAK;AAC1B,eAAKG,KAAI,MAAMJ,GAAE,KAAK,GAAG,MAAM;AAC7B,YAAAC,KAAI;AACJ,gBAAID,GAAE,QAAQ;AACZ,oBAAMA,GAAE,OAAO;AAAA,YACjB;AACA,YAAAD,GAAE,CAAC;AAAA,UACL,OAAO;AACL,gBAAI;AACF,cAAAI,KAAI;AACJ,cAAAJ,GAAE,KAAKK,GAAE,KAAK,CAAC;AAAA,YACjB,SAASN,IAAG;AACV,kBAAIE,GAAE,OAAO;AACX,oBAAIC,KAAI,CAAC,EAAE,MAAMD,GAAE,MAAMF,EAAC,GAAG,MAAM;AACjC,kBAAAC,GAAE,CAAC;AAAA,gBACL;AAAA,cACF,OAAO;AACL,sBAAMD;AAAA,cACR;AAAA,YACF;AAAA,UACF;AAAA,QACF;AACA,QAAAI,KAAI;AAAA,MACN;AAAA,IACF,CAAE,CAAC;AAAA,EACL;AACF;AAEA,SAAS,aAAaJ,IAAG;AACvB,MAAIA,GAAE,OAAO,aAAa,GAAG;AAC3B,WAAO,kBAAkBA,EAAC;AAAA,EAC5B;AACA,SAAO,CAAAC,OAAK;AACV,QAAIC,KAAIF,GAAE,OAAO,QAAQ,EAAE;AAC3B,QAAIG,KAAI;AACR,QAAIC,KAAI;AACR,QAAIC,KAAI;AACR,QAAIC;AACJ,IAAAL,GAAE,MAAO,CAAAD,OAAK;AACZ,UAAI,MAAMA,IAAG;AACX,QAAAG,KAAI;AACJ,YAAID,GAAE,QAAQ;AACZ,UAAAA,GAAE,OAAO;AAAA,QACX;AAAA,MACF,WAAWE,IAAG;AACZ,QAAAC,KAAI;AAAA,MACN,OAAO;AACL,aAAKA,KAAID,KAAI,MAAIC,MAAK,CAACF,MAAK;AAC1B,eAAKG,KAAIJ,GAAE,KAAK,GAAG,MAAM;AACvB,YAAAC,KAAI;AACJ,gBAAID,GAAE,QAAQ;AACZ,cAAAA,GAAE,OAAO;AAAA,YACX;AACA,YAAAD,GAAE,CAAC;AAAA,UACL,OAAO;AACL,gBAAI;AACF,cAAAI,KAAI;AACJ,cAAAJ,GAAE,KAAKK,GAAE,KAAK,CAAC;AAAA,YACjB,SAASN,IAAG;AACV,kBAAIE,GAAE,OAAO;AACX,oBAAIC,KAAI,CAAC,CAACD,GAAE,MAAMF,EAAC,EAAE,MAAM;AACzB,kBAAAC,GAAE,CAAC;AAAA,gBACL;AAAA,cACF,OAAO;AACL,sBAAMD;AAAA,cACR;AAAA,YACF;AAAA,UACF;AAAA,QACF;AACA,QAAAI,KAAI;AAAA,MACN;AAAA,IACF,CAAE,CAAC;AAAA,EACL;AACF;AAEA,IAAI,IAAI;AAER,SAAS,UAAUJ,IAAG;AACpB,SAAO,CAAAC,OAAK;AACV,QAAIC,KAAI;AACR,IAAAD,GAAE,MAAO,CAAAE,OAAK;AACZ,UAAI,MAAMA,IAAG;AACX,QAAAD,KAAI;AAAA,MACN,WAAW,CAACA,IAAG;AACb,QAAAA,KAAI;AACJ,QAAAD,GAAE,KAAKD,EAAC,CAAC;AACT,QAAAC,GAAE,CAAC;AAAA,MACL;AAAA,IACF,CAAE,CAAC;AAAA,EACL;AACF;AAEA,SAAS,KAAKD,IAAG;AACf,SAAO,CAAAC,OAAK;AACV,QAAIC,KAAI;AACR,QAAIC,KAAIH,GAAE;AAAA,MACR,KAAKA,IAAG;AACN,YAAI,CAACE,IAAG;AACN,UAAAD,GAAE,KAAKD,EAAC,CAAC;AAAA,QACX;AAAA,MACF;AAAA,MACA,WAAW;AACT,YAAI,CAACE,IAAG;AACN,UAAAA,KAAI;AACJ,UAAAD,GAAE,CAAC;AAAA,QACL;AAAA,MACF;AAAA,IACF,CAAC;AACD,IAAAA,GAAE,MAAO,CAAAD,OAAK;AACZ,UAAI,MAAMA,MAAK,CAACE,IAAG;AACjB,QAAAA,KAAI;AACJ,QAAAC,GAAE;AAAA,MACJ;AAAA,IACF,CAAE,CAAC;AAAA,EACL;AACF;AAEA,SAAS,cAAc;AACrB,MAAIH;AACJ,MAAIC;AACJ,SAAO;AAAA,IACL,QAAQ,MAAM,KAAM,CAAAC,OAAK;AACvB,MAAAF,KAAIE,GAAE;AACN,MAAAD,KAAIC,GAAE;AACN,aAAO;AAAA,IACT,CAAE,CAAC;AAAA,IACH,KAAKD,IAAG;AACN,UAAID,IAAG;AACL,QAAAA,GAAEC,EAAC;AAAA,MACL;AAAA,IACF;AAAA,IACA,WAAW;AACT,UAAIA,IAAG;AACL,QAAAA,GAAE;AAAA,MACJ;AAAA,IACF;AAAA,EACF;AACF;AAiCA,SAAS,YAAYM,IAAG;AACtB,SAAO,KAAM,CAAAC,OAAK;AAChB,IAAAD,GAAE,KAAM,CAAAA,OAAK;AACX,cAAQ,QAAQA,EAAC,EAAE,KAAM,MAAM;AAC7B,QAAAC,GAAE,KAAKD,EAAC;AACR,QAAAC,GAAE,SAAS;AAAA,MACb,CAAE;AAAA,IACJ,CAAE;AACF,WAAO;AAAA,EACT,CAAE;AACJ;AAEA,SAAS,UAAUA,IAAG;AACpB,SAAO,CAAAC,OAAK;AACV,QAAIC,KAAIH;AACR,QAAII,KAAI;AACR,IAAAF,GAAG,CAAAF,OAAK;AACN,UAAI,MAAMA,IAAG;AACX,QAAAI,KAAI;AAAA,MACN,WAAW,MAAMJ,GAAE,KAAK;AACtB,SAACG,KAAIH,GAAE,CAAC,GAAG,CAAC;AAAA,MACd,WAAW,CAACI,IAAG;AACb,QAAAH,GAAED,GAAE,CAAC,CAAC;AACN,QAAAG,GAAE,CAAC;AAAA,MACL;AAAA,IACF,CAAE;AACF,WAAO;AAAA,MACL,cAAc;AACZ,YAAI,CAACC,IAAG;AACN,UAAAA,KAAI;AACJ,UAAAD,GAAE,CAAC;AAAA,QACL;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;AAQA,SAAS,QAAQE,IAAG;AAClB,YAAW,CAAAA,OAAK;AAAA,EAAC,CAAE,EAAEA,EAAC;AACxB;AAmFA,SAAS,UAAUC,IAAG;AACpB,SAAO,IAAI,QAAS,CAAAC,OAAK;AACvB,QAAIC,KAAIC;AACR,QAAIC;AACJ,IAAAJ,GAAG,CAAAG,OAAK;AACN,UAAI,MAAMA,IAAG;AACX,gBAAQ,QAAQC,EAAC,EAAE,KAAKH,EAAC;AAAA,MAC3B,WAAW,MAAME,GAAE,KAAK;AACtB,SAACD,KAAIC,GAAE,CAAC,GAAG,CAAC;AAAA,MACd,OAAO;AACL,QAAAC,KAAID,GAAE,CAAC;AACP,QAAAD,GAAE,CAAC;AAAA,MACL;AAAA,IACF,CAAE;AAAA,EACJ,CAAE;AACJ;;;AClpCA,IAAMG,wBAAyBC,CAAAA,OAAAA;AAC7B,MACEA,MACyB,YAAA,OAAlBA,GAAMC,YACZD,GAAME,cAA6B,mBAAfF,GAAMG,OAAAA;AAE3B,WAAOH;aACmB,YAAA,OAAVA,MAA+C,YAAA,OAAlBA,GAAMC,SAAAA;AACnD,WAAO,IAAIG,aACTJ,GAAMC,SACND,GAAMK,OACNL,GAAMM,QACNN,GAAMO,WACNP,GAAMQ,MACNR,IACAA,GAAME,cAAc,CAAA,CAAA;;AAGtB,WAAO,IAAIE,aAAaJ,EAAAA;;AAC1B;AAiBK,IAAMS,gBAAN,cAA4BC,MAAAA;EAwCjCC,YAAYC,IAAAA;AAKV,QAAMC,MAA2BD,GAAME,iBAAiB,CAAA,GAAIC,IAC1DhB,qBAAAA;AAEF,QAAME,MAnGmBe,CAC3BC,IACAC,OAAAA;AAEA,UAAIlB,KAAQ;AACZ,UAAIiB,IAAAA;AAAY,eAAO,aAAaA,GAAWhB,OAAAA;;AAC/C,UAAIiB,IAAAA;AACF,iBAASC,KAAI,GAAGC,KAAIF,GAAYG,QAAQF,KAAIC,IAAGD,MAAK;AAClD,cAAInB,IAAAA;AAAOA,YAAAA,MAAS;;AACpBA,UAAAA,MAAS,aAAakB,GAAYC,EAAAA,EAAGlB,OAAAA;QACvC;;AAEF,aAAOD;IAAK,GAwFRY,GAAMU,cACNT,EAAAA;AAGFU,UAAMtB,EAAAA;AAENuB,SAAKrB,OAAO;AACZqB,SAAKvB,UAAUA;AACfuB,SAAKV,gBAAgBD;AACrBW,SAAKF,eAAeV,GAAMU;AAC1BE,SAAKC,WAAWb,GAAMa;EACxB;EAEAC,WAAAA;AACE,WAAOF,KAAKvB;EACd;;ACvFK,IAAM0B,QAAQA,CAACC,IAAWC,OAAAA;AAC/B,MAAIC,KAAqB,KAAhBD,MAAQ;AACjB,WAASV,KAAI,GAAGC,KAAe,IAAXQ,GAAEP,QAAYF,KAAIC,IAAGD,MAAAA;AACvCW,IAAAA,MAAKA,MAAK,KAAKA,KAAIF,GAAEG,WAAWZ,EAAAA;;AAClC,SAAOW;AAAC;ACjCV,IAAME,KAAiB,oBAAIC;AAC3B,IAAMC,KAA2B,oBAAIC;AAErC,IAAMC,YAAYA,CAACR,IAAQS,OAAAA;AACzB,MAAU,SAANT,MAAcI,GAAKM,IAAIV,EAAAA,GAAAA;AACzB,WAAO;aACe,YAAA,OAANA,IAAAA;AAChB,WAAOW,KAAKH,UAAUR,EAAAA,KAAM;aACnBA,GAAEY,QAAAA;AACX,WAAOJ,UAAUR,GAAEY,OAAAA,GAAUH,EAAAA;aACpBI,MAAMC,QAAQd,EAAAA,GAAI;AAC3B,QAAIe,KAAM;AACV,aAASxB,KAAI,GAAGC,KAAIQ,GAAEP,QAAQF,KAAIC,IAAGD,MAAK;AACxC,UAAIwB,GAAItB,SAAS,GAAA;AAAGsB,QAAAA,MAAO;;AAC3BA,MAAAA,MAAOP,UAAUR,GAAET,EAAAA,GAAIkB,EAAAA,KAAiB;IAC1C;AAEA,WADAM,MAAO;EAER,WAAM,CACJN,OACCO,OAAoBC,mBAAmBjB,cAAagB,MACnDE,OAAoBD,mBAAmBjB,cAAakB,KAAAA;AAEvD,WAAO;;AAGT,MAAMC,KAAOC,OAAOD,KAAKnB,EAAAA,EAAGqB,KAAAA;AAC5B,MAAA,CACGF,GAAK1B,UACNO,GAAEjB,eACFqC,OAAOE,eAAetB,EAAAA,EAAGjB,gBAAgBqC,OAAOG,UAAUxC,aAC1D;AACA,QAAMyC,KAAMlB,GAAMmB,IAAIzB,EAAAA,KAAM0B,KAAKC,OAAAA,EAAS7B,SAAS,EAAA,EAAI8B,MAAM,CAAA;AAC7DtB,IAAAA,GAAMuB,IAAI7B,IAAGwB,EAAAA;AACb,WAAOhB,UAAU;MAAEsB,OAAON;OAAOf,EAAAA;EACnC;AAEAL,EAAAA,GAAK2B,IAAI/B,EAAAA;AACT,MAAIe,KAAM;AACV,WAASxB,KAAI,GAAGC,KAAI2B,GAAK1B,QAAQF,KAAIC,IAAGD,MAAK;AAC3C,QAAMyC,KAAQxB,UAAUR,GAAEmB,GAAK5B,EAAAA,CAAAA,GAAKkB,EAAAA;AACpC,QAAIuB,IAAO;AACT,UAAIjB,GAAItB,SAAS,GAAA;AAAGsB,QAAAA,MAAO;;AAC3BA,MAAAA,MAAOP,UAAUW,GAAK5B,EAAAA,GAAIkB,EAAAA,IAAgB,MAAMuB;IAClD;EACF;AAEA5B,EAAAA,GAAK6B,OAAOjC,EAAAA;AAEZ,SADAe,MAAO;AACG;AAGZ,IAAMmB,UAAUA,CAAC/C,IAAcP,IAAcoB,OAAAA;AAC3C,MAAS,QAALA,MAA0B,YAAA,OAANA,MAAkBA,GAAEY,UAAUR,GAAKM,IAAIV,EAAAA,GAAAA;EAAAA,WAEpDa,MAAMC,QAAQd,EAAAA,GAAAA;AACvB,aAAST,KAAI,GAAGC,KAAIQ,GAAEP,QAAQF,KAAIC,IAAGD,MAAAA;AACnC2C,cAAQ/C,IAAK,GAAGP,EAAAA,IAAQW,EAAAA,IAAKS,GAAET,EAAAA,CAAAA;;aACxBS,cAAagB,MAAmBhB,cAAakB,IAAAA;AACtD/B,IAAAA,GAAI0C,IAAIjD,IAAMoB,EAAAA;SACT;AACLI,IAAAA,GAAK2B,IAAI/B,EAAAA;AACT,aAAWwB,MAAOxB,IAAAA;AAAGkC,cAAQ/C,IAAK,GAAGP,EAAAA,IAAQ4C,EAAAA,IAAOxB,GAAEwB,EAAAA,CAAAA;;EACxD;AAAA;IAiBWW,qBAAqBA,CAACnC,IAAQS,OAAAA;AACzCL,EAAAA,GAAKgC,MAAAA;AACL,SAAO5B,UAAUR,IAAGS,MAAAA,KAAgB;AAAM;AAG5C,IAAMQ,kBAAN,MAAMA;AAAAA;AACN,IAAMD,KAAkC,eAAA,OAATqB,OAAuBA,OAAOpB;AAC7D,IAAMC,KAAkC,eAAA,OAAToB,OAAuBA,OAAOrB;AC/D7D,IAAMsB,IAAoB;AAC1B,IAAMC,KAAkB;AAExB,IAAMC,wBAAwBA,CAACC,IAAaC,OAC1CA,KAAM,KAAM,IAAID,GAAIE,QAAQJ,IAAiB,IAAA,IAAQE;AAGvD,IAAMG,mBAAoBC,CAAAA,OACxBA,GAAKC,MAAMR,CAAAA,EAAmBpD,IAAIsD,qBAAAA,EAAuBO,KAAK,EAAA,EAAIC,KAAAA;AAEpE,IAAMC,IAAqD,oBAAIC;AAI/D,IAAMC,KAA0C,oBAAID;AAmBvCE,IAAAA,oBACXP,CAAAA,OAAAA;AAEA,MAAIQ;AACJ,MAAoB,YAAA,OAATR,IAAAA;AACTQ,IAAAA,KAAUT,iBAAiBC,EAAAA;aAClBA,GAAKS,OAAOH,GAAK3B,IAAKqB,GAA2BhB,KAAAA,MAAWgB,IAAAA;AACrEQ,IAAAA,KAAUR,GAAKS,IAAI7E,OAAO8E;SACrB;AACLF,IAAAA,KAAUJ,EAAOzB,IAAIqB,EAAAA,KAASD,iBAAiBY,MAAMX,EAAAA,CAAAA;AACrDI,MAAOrB,IAAIiB,IAAMQ,EAAAA;EACnB;AAEA,MAAoB,YAAA,OAATR,MAAAA,CAAsBA,GAAKS,KAAAA;AACnCT,IAAAA,GAAaS,MAAM;MAClBG,OAAO;MACPC,KAAKL,GAAQ7D;MACbf,QAAQ;QACN8E,MAAMF;QACN/E,MArDY;QAsDZqF,gBAAgB;UAAEC,MAAM;UAAGC,QAAQ;;;;;AAKzC,SAAOR;AAAO;AAehB,IAAMS,eACJjB,CAAAA,OAAAA;AAEA,MAAItB;AACJ,MAAKsB,GAA+BkB,YAAAA;AAClCxC,IAAAA,KAAMzB,MAAO+C,GAA+BkB,UAAAA;SACvC;AACLxC,IAAAA,KAAMzB,MAAMsD,kBAAkBP,EAAAA,CAAAA;AAE9B,QAAKA,GAAsBmB,aAAa;AACtC,UAAMC,KAAgBC,iBAAiBrB,EAAAA;AACvC,UAAIoB,IAAAA;AAAe1C,QAAAA,KAAMzB,MAAM;IAAOmE,EAAAA,IAAiB1C,EAAAA;;IACzD;EACF;AACA,SAAOA;AAAG;AAeC4C,IAAAA,cAAetB,CAAAA,OAAAA;AAC1B,MAAItB;AACJ,MAAI6C;AACJ,MAAoB,YAAA,OAATvB,IAAmB;AAC5BtB,IAAAA,KAAMuC,aAAajB,EAAAA;AACnBuB,IAAAA,KAAQjB,GAAK3B,IAAID,EAAAA,KAAQ8C,MAAMxB,IAAM;MAAEyB,YAAAA;;EACzC,OAAO;AACL/C,IAAAA,KAAOsB,GAA2BhB,SAASiC,aAAajB,EAAAA;AACxDuB,IAAAA,KAAQjB,GAAK3B,IAAID,EAAAA,KAAQsB;EAC3B;AAGA,MAAA,CAAKuB,GAAMd,KAAAA;AAAKF,sBAAkBgB,EAAAA;;AAEjCA,EAAAA,GAA4BvC,QAAQN;AACrC4B,EAAAA,GAAKvB,IAAIL,IAAK6C,EAAAA;AACd,SAAOA;AAAK;AAiBP,IAAMG,gBAAgBA,CAI3BC,IACAC,IACApG,OAAAA;AAEA,MAAMqG,KAAYD,MAAe,CAAA;AACjC,MAAML,KAAQD,YAAYK,EAAAA;AAC1B,MAAMG,KAAczC,mBAAmBwC,IAAAA,IAAW;AAClD,MAAInD,KAAM6C,GAAMvC;AAChB,MAAoB,SAAhB8C,IAAAA;AAAsBpD,IAAAA,KAAMzB,MAAM6E,IAAapD,EAAAA;;AACnD,SAAO;IAAEA,KAAAA;IAAK6C,OAAAA;IAAOM,WAAAA;IAAWrG,YAAAA;;AAAY;AAOvC,IAAM6F,mBAAoBE,CAAAA,OAAAA;AAC/B,WAAS9E,KAAI,GAAGC,KAAI6E,GAAMJ,YAAYxE,QAAQF,KAAIC,IAAGD,MAAK;AACxD,QAAMuD,KAAOuB,GAAMJ,YAAY1E,EAAAA;AAC/B,QAAIuD,GAAK+B,SAASC,EAAKC,sBAAAA;AACrB,aAAOjC,GAAKvE,OAAOuE,GAAKvE,KAAKyD,QAAAA;;EAEjC;AAAA;AAOWgD,IAAAA,mBAAoBX,CAAAA,OAAAA;AAC/B,WAAS9E,KAAI,GAAGC,KAAI6E,GAAMJ,YAAYxE,QAAQF,KAAIC,IAAGD,MAAK;AACxD,QAAMuD,KAAOuB,GAAMJ,YAAY1E,EAAAA;AAC/B,QAAIuD,GAAK+B,SAASC,EAAKC,sBAAAA;AACrB,aAAOjC,GAAKmC;;EAEhB;AAAA;AC/KK,IAAMC,aAAaA,CACxBD,IACAE,IACAtF,OAAAA;AAEA,MAAA,EACI,UAAUsF,MACT,YAAYA,MAAYtE,MAAMC,QAAQqE,GAAOC,MAAAA,IAAAA;AAEhD,UAAM,IAAItG,MAAM,YAAA;;AAGlB,MAAMuG,KAAoC,mBAAnBJ,GAAUJ;AACjC,SAAO;IACLI,WAAAA;IACAK,MAAMH,GAAOG;IACblH,OAAOyC,MAAMC,QAAQqE,GAAOC,MAAAA,IACxB,IAAIvG,cAAc;MAChBK,eAAeiG,GAAOC;MACtBvF,UAAAA;;IAGNvB,YAAY6G,GAAO7G,aAAa;SAAK6G,GAAO7G;;IAC5CiH,SAA2B,QAAlBJ,GAAOI,UAAkBF,KAAiBF,GAAOI;IAC1DC,OAAAA;;AACD;AAGH,IAAMC,YAAYA,CAACC,IAAahH,OAAAA;AAC9B,MAAsB,YAAA,OAAXgH,MAAiC,QAAVA,IAAgB;AAChD,QAAI7E,MAAMC,QAAQ4E,EAAAA,GAAS;AACzBA,MAAAA,KAAS,CAAA,GAAIA,EAAAA;AACb,eAASnG,KAAI,GAAGC,KAAId,GAAOe,QAAQF,KAAIC,IAAGD,MAAAA;AACxCmG,QAAAA,GAAOnG,EAAAA,IAAKkG,UAAUC,GAAOnG,EAAAA,GAAIb,GAAOa,EAAAA,CAAAA;;AAE1C,aAAOmG;IACT;AACA,QAAA,CAAKA,GAAO3G,eAAe2G,GAAO3G,gBAAgBqC,QAAQ;AACxDsE,MAAAA,KAAS;WAAKA;;AACd,eAAWlE,MAAO9C,IAAAA;AAChBgH,QAAAA,GAAOlE,EAAAA,IAAOiE,UAAUC,GAAOlE,EAAAA,GAAM9C,GAAO8C,EAAAA,CAAAA;;AAC9C,aAAOkE;IACT;EACF;AACA,SAAOhH;AAAM;AAqBR,IAAMiH,mBAAmBA,CAC9BC,IACAC,IACAhG,IACAiG,OAAAA;AAEA,MAAIV,KAASQ,GAAWxH,QAAQwH,GAAWxH,MAAMc,gBAAgB,CAAA;AACjE,MAAI6G,KAAAA,CAAAA,CACAH,GAAWtH,cAAAA,CAAAA,EAAiBuH,GAAWG,WAAWH,IAAYvH;AAClE,MAAMA,KAAa;OACdsH,GAAWtH;QACVuH,GAAWG,WAAWH,IAAYvH;;AAGxC,MAAI2H,KAAcJ,GAAWI;AAG7B,MAAI,UAAUJ,IAAAA;AACZI,IAAAA,KAAc,CAACJ,EAAAA;;AAGjB,MAAMK,KAAW;IAAEZ,MAAMM,GAAWN;;AACpC,MAAIW,IAAa;AAAA,QAAAE,QAAAA,WAAAA;AAEb,UAAMC,KAAQH,GAAY1G,EAAAA;AAC1B,UAAIsB,MAAMC,QAAQsF,GAAMhB,MAAAA,GAAAA;AACtBA,QAAAA,GAAOiB,KAAAA,GAASD,GAAMhB,MAAAA;;AAGxB,UAAIgB,GAAM9H,YAAY;AACpB8C,eAAOkF,OAAOhI,IAAY8H,GAAM9H,UAAAA;AAChCyH,QAAAA,KAAAA;MACF;AAEA,UAAIQ,KAAwB;AAC5B,UAAIC,KAAyCN;AAC7C,UAAItH,KAAqC,CAAA;AACzC,UAAIwH,GAAMxH,MAAAA;AACRA,QAAAA,KAAOwH,GAAMxH;iBACJkH,IAAS;AAClB,YAAMW,KAAMX,GAAQY,KAAKC,CAAAA,OAAcA,GAAWC,OAAOR,GAAMQ,EAAAA;AAC/D,YAAIR,GAAMS,SAAAA;AACRjI,UAAAA,KAAO,CAAA,GAAI6H,GAAK7H,MAAAA,GAASwH,GAAMS,OAAAA;;AAE/BjI,UAAAA,KAAO6H,GAAK7H;;MAEhB;AAEA,eAASW,KAAI,GAAGC,KAAIZ,GAAKa,QAAQF,KAAIC,IAAG+G,KAAO3H,GAAKW,IAAAA,GAAAA;AAClDiH,QAAAA,KAAOA,GAAKD,EAAAA,IAAQ1F,MAAMC,QAAQ0F,GAAKD,EAAAA,CAAAA,IACnC,CAAA,GAAIC,GAAKD,EAAAA,CAAAA,IACT;aAAKC,GAAKD,EAAAA;;;AAGhB,UAAIH,GAAMU,OAAO;AACf,YAAMC,KAAAA,CAAcR,MAAQ,IAAKA,KAAkB;AACnD,iBAAShH,KAAI,GAAGC,KAAI4G,GAAMU,MAAMrH,QAAQF,KAAIC,IAAGD,MAAAA;AAC7CiH,UAAAA,GAAKO,KAAaxH,EAAAA,IAAKkG,UACrBe,GAAKO,KAAaxH,EAAAA,GAClB6G,GAAMU,MAAMvH,EAAAA,CAAAA;;MAElB,WAAO,WAAI6G,GAAMd,MAAAA;AACfkB,QAAAA,GAAKD,EAAAA,IAAQd,UAAUe,GAAKD,EAAAA,GAAOH,GAAMd,IAAAA;;;AAvC7C,aAAS/F,KAAI,GAAGC,KAAIyG,GAAYxG,QAAQF,KAAIC,IAAGD,MAAAA;AAAG4G,YAAAA;;EA0CpD,OAAO;AACLD,IAAAA,GAASZ,QAAQO,GAAWG,WAAWH,IAAYP,QAAQM,GAAWN;AACtEF,IAAAA,KACGS,GAAWT,UACXS,GAAWG,WAAWH,GAAWG,QAAQZ,UAC1CA;EACJ;AAEA,SAAO;IACLH,WAAWW,GAAWX;IACtBK,MAAMY,GAASZ;IACflH,OAAOgH,GAAO3F,SACV,IAAIZ,cAAc;MAAEK,eAAekG;MAAQvF,UAAAA;;IAE/CvB,YAAYyH,KAAgBzH,KAAAA;IAC5BiH,SACwB,QAAtBM,GAAWN,UAAkBM,GAAWN,UAAUK,GAAWL;IAC/DC,OAAAA;;AACD;AAgBI,IAAMwB,kBAAkBA,CAC7B/B,IACA7G,IACAyB,QACqB;EACrBoF,WAAAA;EACAK,MAAAA;EACAlH,OAAO,IAAIS,cAAc;IACvBa,cAActB;IACdyB,UAAAA;;EAEFvB,YAAAA;EACAiH,SAAAA;EACAC,OAAAA;;ACnLK,SAASyB,cAGdC,IAAAA;AACA,MAAM1D,KAAkB;IACtBa,OAAAA;IACAL,YAAAA;IACAE,eAAeC,iBAAiB+C,GAAQ7C,KAAAA;IACxCM,WAAWuC,GAAQvC,aAAAA;IACnBrG,YAAY4I,GAAQ5I;;AAGtB,MACE,gBAAgB4I,GAAQ7C,SACxB6C,GAAQ7C,MAAML,eAAAA,CAGZkD,GAAQ7C,MAAMJ,eAAAA,CAAgBiD,GAAQ7C,MAAMJ,YAAYxE,SAAAA;AAE1D+D,IAAAA,GAAKQ,aAAakD,GAAQ7C,MAAML;aAC3B,CACJkD,GAAQ5I,cAAAA,CACR4I,GAAQ5I,WAAW6I,kBAClBD,GAAQ5I,WAAW6I,eAAeC,MAAAA;AAEpC5D,IAAAA,GAAKa,QAAQhB,kBAAkB6D,GAAQ7C,KAAAA;;AAGzC,SAAOb;AACT;IAaa6D,eAAeA,CAC1BpC,IACAzB,OAAAA;AAEA,MAAM8D,KACe,YAAnBrC,GAAUJ,QAAoBI,GAAUsC,QAAQC;AAClD,MAAA,CAAKF,MAAAA,CAAiB9D,IAAAA;AAAM,WAAOyB,GAAUsC,QAAQE;;AAErD,MAAMC,KAAWC,qBAAqB1C,GAAUsC,QAAQE,GAAAA;AACxD,WAAWjG,MAAOgC,IAAM;AACtB,QAAMxB,KAAQwB,GAAKhC,EAAAA;AACnB,QAAIQ,IAAAA;AACF0F,MAAAA,GAAS,CAAA,EAAG7F,IACVL,IACiB,YAAA,OAAVQ,KAAqBG,mBAAmBH,EAAAA,IAASA,EAAAA;;EAG9D;AACA,MAAM4F,KAAWF,GAAS1E,KAAK,GAAA;AAC/B,MAAI4E,GAASnI,SAAS,QAAyB,YAAjB6H,IAA0B;AACtDrC,IAAAA,GAAUsC,QAAQC,kBAAAA;AAClB,WAAOvC,GAAUsC,QAAQE;EAC3B;AAEA,SAAOG;AAAQ;AAGjB,IAAMD,uBACJF,CAAAA,OAAAA;AAEA,MAAM/D,KAAQ+D,GAAII,QAAQ,GAAA;AAC1B,SAAOnE,KAAAA,KACH,CAAC+D,GAAI7F,MAAM,GAAG8B,EAAAA,GAAQ,IAAIoE,gBAAgBL,GAAI7F,MAAM8B,KAAQ,CAAA,CAAA,CAAA,IAC5D,CAAC+D,IAAK,IAAIK,iBAAAA;AAAkB;AAIlC,IAAMC,gBAAgBA,CACpB9C,IACAzB,OAAAA;AAIA,MAAIA,MAAAA,EADiB,YAAnByB,GAAUJ,QAAAA,CAAAA,CAAsBI,GAAUsC,QAAQC,kBAC7B;AACrB,QAAMQ,KAAO7F,mBAAmBqB,EAAAA;AAChC,QAAMyE,MHnBmBjI,CAAAA,OAAAA;AAC3B,UAAMb,KAAe,oBAAIgE;AACzB,UACEnC,OAAoBC,mBACpBC,OAAoBD,iBACpB;AACAb,QAAAA,GAAKgC,MAAAA;AACLF,gBAAQ/C,IAAK,aAAaa,EAAAA;MAC5B;AACA,aAAOb;IAAG,GGUmBqE,GAAKmB,SAAAA;AAChC,QAAIsD,GAAMC,MAAM;AACd,UAAMC,KAAO,IAAIC;AACjBD,MAAAA,GAAKE,OAAO,cAAcL,EAAAA;AAC1BG,MAAAA,GAAKE,OACH,OACAlG,mBAAmB;WACd,CAAA,GAAI8F,GAAM9G,KAAAA,CAAAA,EAAQhC,IAAI6C,CAAAA,OAAS,CAACA,EAAAA,CAAAA;;AAGvC,UAAIsG,KAAQ;AACZ,eAAWC,MAAQN,GAAMO,OAAAA,GAAAA;AAAUL,QAAAA,GAAKE,OAAO,KAAGC,MAAWC,EAAAA;;AAC7D,aAAOJ;IACT;AACA,WAAOH;EACT;AAAA;IAmBWS,mBAAmBA,CAC9BxD,IACAzB,OAAAA;AAEA,MAAMkF,KAAuB;IAC3BC,QACqB,mBAAnB1D,GAAUJ,OACN,uCACA;;AAER,MAAM+D,MACuC,cAAA,OAAnC3D,GAAUsC,QAAQsB,eACtB5D,GAAUsC,QAAQsB,aAAAA,IAClB5D,GAAUsC,QAAQsB,iBAAiB,CAAA;AACzC,MAAID,GAAaF,SAAAA;AACf,SA/BeA,CAAAA,OACjB,SAASA,MAAAA,CAAYtH,OAAOD,KAAKuH,EAAAA,EAASjJ,QA8B1BmJ,GAAaF,OAAAA,GAAAA;AACzBE,MAAAA,GAAaF,QAAQI,QAAQ,CAAC9G,IAAOR,OAAAA;AACnCkH,QAAAA,GAAQlH,EAAAA,IAAOQ;MAAK,CAAA;eAEbnB,MAAMC,QAAQ8H,GAAaF,OAAAA,GAAAA;AACnCE,MAAAA,GAAaF,QAAoCI,QAChD,CAAC9G,IAAOR,OAAAA;AACN,YAAIX,MAAMC,QAAQkB,EAAAA,GAAAA;AAChB,cAAI0G,GAAQ1G,GAAM,CAAA,CAAA,GAAA;AAChB0G,YAAAA,GAAQ1G,GAAM,CAAA,CAAA,IAAM,GAAG0G,GAAQ1G,GAAM,CAAA,CAAA,CAAA,IAAOA,GAAM,CAAA,CAAA;;AAElD0G,YAAAA,GAAQ1G,GAAM,CAAA,CAAA,IAAMA,GAAM,CAAA;;;AAG5B0G,UAAAA,GAAQlH,EAAAA,IAAOQ;;MACjB,CAAA;;AAIJ,eAAWR,MAAOoH,GAAaF,SAAAA;AAC7BA,QAAAA,GAAQlH,GAAIuH,YAAAA,CAAAA,IAAiBH,GAAaF,QAAQlH,EAAAA;;;;AAKxD,MAAMwH,KAAiBjB,cAAc9C,IAAWzB,EAAAA;AAChD,MAA8B,YAAA,OAAnBwF,MAAAA,CAAgCN,GAAQ,cAAA,GAAA;AACjDA,IAAAA,GAAQ,cAAA,IAAkB;;AAC5B,SAAO;OACFE;IACHK,QAAQD,KAAiB,SAAS;IAClCxF,MAAMwF;IACNN,SAAAA;;AACD;AC/IH,IAAMQ,IAAiC,eAAA,OAAhBC,cAA8B,IAAIA,gBAAgB;AACzE,IAAMC,IAAmB;AACzB,IAAMC,IAAgB;AAMtB,IAAMvJ,WAAYd,CAAAA,OACW,aAA3BA,GAAMD,YAAYR,OACbS,GAAiBc,SAAAA,IAClBoJ,EAASI,OAAOtK,EAAAA;AAEtBuK,gBAAgBC,WAAW3J,IAAAA;AACzB,MAAIA,GAAS2D,KAAMiG,OAAOC,aAAAA,GAAAA;AACxB,mBAAiBC,MAAS9J,GAAS2D,MAAAA;YAC3B1D,SAAS6J,EAAAA;;SACZ;AACL,QAAMC,KAAS/J,GAAS2D,KAAMqG,UAAAA;AAC9B,QAAI1E;AACJ,QAAA;AACE,aAAA,EAASA,KAAAA,MAAeyE,GAAOE,KAAAA,GAAQC,MAAAA;cAAYjK,SAASqF,GAAOnD,KAAAA;;IACrE,UAAU;AACR4H,MAAAA,GAAOI,OAAAA;IACT;EACF;AACF;AAEAT,gBAAgBxG,MACdkH,IACAC,IAAAA;AAEA,MAAIC,KAAS;AACb,MAAIC;AACJ,iBAAiBT,MAASM,IAAQ;AAChCE,IAAAA,MAAUR;AACV,YAAQS,KAAgBD,GAAOtC,QAAQqC,EAAAA,KAAAA,IAAiB;YAChDC,GAAOvI,MAAM,GAAGwI,EAAAA;AACtBD,MAAAA,KAASA,GAAOvI,MAAMwI,KAAgBF,GAASzK,MAAAA;IACjD;EACF;AACF;AA4EA8J,gBAAgBc,eACdpF,IACAwC,IACAoB,IAAAA;AAEA,MAAIyB,KAAAA;AACJ,MAAInF,KAAiC;AACrC,MAAItF;AAEJ,MAAA;gBAGc0K,QAAQC,QAAAA;AAGpB,QAAMC,MADN5K,KAAAA,OAAkBoF,GAAUsC,QAAQmD,SAASA,OAAOjD,IAAKoB,EAAAA,GAC5BH,QAAQjH,IAAI,cAAA,KAAmB;AAE5D,QAAIkJ;AACJ,QAAI,oBAAoBC,KAAKH,EAAAA,GAAAA;AAC3BE,MAAAA,KAlENpB,gBAAgBsB,oBACdJ,IACA5K,IAAAA;AAEA,YAAMiL,KAAiBL,GAAYM,MAAM3B,CAAAA;AACzC,YAAMc,KAAW,QAAQY,KAAiBA,GAAe,CAAA,IAAK;AAC9D,YAAIE,KAAAA;AACJ,YAAIhF;AACJ,uBAAe2D,MAAS5G,MAAMyG,WAAW3J,EAAAA,GAAW,SAASqK,EAAAA,GAAW;AACtE,cAAIc,IAAY;AACdA,YAAAA,KAAAA;AACA,gBAAMC,KAAgBtB,GAAM9B,QAAQqC,EAAAA;AACpC,gBAAIe,KAAAA,IAAiB;AACnBtB,cAAAA,KAAQA,GAAM/H,MAAMqJ,KAAgBf,GAASzK,MAAAA;;AAE7C;;UAEJ;AACA,cAAA;kBACSuG,KAAUrF,KAAK2D,MAAMqF,GAAM/H,MAAM+H,GAAM9B,QAAQ,UAAA,IAAc,CAAA,CAAA;UACrE,SAAQzJ,IAAAA;AACP,gBAAA,CAAK4H,IAAAA;AAAS,oBAAM5H;;UACtB;AACA,cAAI4H,MAAAA,UAAWA,GAAQT,SAAAA;AAAmB;;QAC5C;AACA,YAAIS,MAAAA,UAAWA,GAAQT,SAAAA;gBACf;YAAEA,SAAAA;;;MAEZ,EAsCoCkF,IAAa5K,EAAAA;eAClC,sBAAsB+K,KAAKH,EAAAA,GAAAA;AACpCE,MAAAA,KAzFNpB,gBAAgB2B,iBACdrL,IAAAA;AAEA,YAAImG;AACJ,uBAAiB2D,MAAS5G,MAAMyG,WAAW3J,EAAAA,GAAW,MAAA,GAAS;AAC7D,cAAMkL,KAAQpB,GAAMoB,MAAM1B,CAAAA;AAC1B,cAAI0B,IAAO;AACT,gBAAMpB,KAAQoB,GAAM,CAAA;AACpB,gBAAA;oBACS/E,KAAUrF,KAAK2D,MAAMqF,EAAAA;YAC7B,SAAQvL,IAAAA;AACP,kBAAA,CAAK4H,IAAAA;AAAS,sBAAM5H;;YACtB;AACA,gBAAI4H,MAAAA,UAAWA,GAAQT,SAAAA;AAAmB;;UAC5C;QACF;AACA,YAAIS,MAAAA,UAAWA,GAAQT,SAAAA;gBACf;YAAEA,SAAAA;;;MAEZ,EAsEiC1F,EAAAA;eACtB,CAAK,UAAU+K,KAAKH,EAAAA,GAAAA;AACzBE,MAAAA,KAjGNpB,gBAAgB4B,UACdtL,IAAAA;cAEMc,KAAK2D,MAAAA,MAAYzE,GAASuL,KAAAA,CAAAA;MAClC,EA6F0BvL,EAAAA;;AAEpB8K,MAAAA,KA1CNpB,gBAAgB8B,eACdxL,IAAAA;AAEA,YAAMuL,KAAAA,MAAavL,GAASuL,KAAAA;AAC5B,YAAA;AACE,cAAMjG,KAASxE,KAAK2D,MAAM8G,EAAAA;AAC1B,cAA6B,MAAbE;AACdC,oBAAQC,KACN,+FAAA;;gBAGErG;QACP,SAAQsG,IAAAA;AACP,gBAAM,IAAI3M,MAAMsM,EAAAA;QAClB;MACF,EA2B+BvL,EAAAA;;AAG3B,QAAIiG;AACJ,mBAAiBE,MAAW2E,IAAS;AACnC,UAAI3E,GAAQF,WAAAA,CAAYX,IAAAA;AACtBW,QAAAA,KAAUE,GAAQF;iBACTE,GAAQF,SAAAA;AACjBA,QAAAA,KAAU,CAAA,GAAIA,IAAAA,GAAaE,GAAQF,OAAAA;;AAErCX,MAAAA,KAASA,KACLQ,iBAAiBR,IAAQa,IAASnG,IAAUiG,EAAAA,IAC5CZ,WAAWD,IAAWe,IAASnG,EAAAA;AACnCyK,MAAAA,KAAAA;YACMnF;AACNmF,MAAAA,KAAAA;IACF;AAEA,QAAA,CAAKnF,IAAAA;YACIA,KAASD,WAAWD,IAAW,CAAE,GAAEpF,EAAAA;;EAE7C,SAAQzB,IAAAA;AACP,QAAA,CAAKkM,IAAAA;AACH,YAAMlM;;UAGF4I,gBACJ/B,IACApF,OACGA,GAAS6L,SAAS,OAAO7L,GAAS6L,UAAU,QAC7C7L,GAAS8L,aACP,IAAI7M,MAAMe,GAAS8L,UAAAA,IACnBvN,IACJyB,EAAAA;EAEJ;AACF;AA6BO,SAAS+L,gBACd3G,IACAwC,IACAoB,IAAAA;AAEA,MAAIgD;AACJ,MAA+B,eAAA,OAApBC,iBAAAA;AACTjD,IAAAA,GAAakD,UAAUF,KAAkB,IAAIC,mBAAmBC;;AAElE,SAGEC,MAAM,MAAA;AACJ,QAAIH,IAAAA;AAAiBA,MAAAA,GAAgBI,MAAAA;;EAAO,CAAA,EAF9CC,OAAQ/G,CAAAA,OAAAA,CAAAA,CAAwCA,EAAAA,EADhDgH,kBAAkB9B,eAAepF,IAAWwC,IAAKoB,EAAAA,CAAAA,CAAAA,CAAAA;AAMrD;;;AC3QA,IAAMuD,eAAeA,CAACC,IAAgCC,OAAAA;AACpD,MAAIC,MAAMC,QAAQH,EAAAA,GAAAA;AAChB,aAASI,KAAI,GAAGC,KAAIL,GAAIM,QAAQF,KAAIC,IAAGD,MAAAA;AACrCL,mBAAaC,GAAII,EAAAA,GAAIH,EAAAA;;aAEC,YAAA,OAARD,MAA4B,SAARA,IAAAA;AACpC,aAAWO,MAAOP,IAAAA;AAChB,UAAY,iBAARO,MAA4C,YAAA,OAAbP,GAAIO,EAAAA,GAAAA;AACrCN,QAAAA,GAAMO,IAAIR,GAAIO,EAAAA,CAAAA;;AAEdR,qBAAaC,GAAIO,EAAAA,GAAMN,EAAAA;;;;AAK7B,SAAOA;AAAK;ACTd,IAAMQ,aAGJC,CAAAA,OAAAA;AAEA,MAAI,iBAAiBA,IAAM;AACzB,QAAMC,KAA+C,CAAA;AACrD,aAASP,KAAI,GAAGC,KAAIK,GAAKC,YAAYL,QAAQF,KAAIC,IAAGD,MAAK;AACvD,UAAMQ,KAAgBH,WAAWC,GAAKC,YAAYP,EAAAA,CAAAA;AAClDO,MAAAA,GAAYE,KAAKD,EAAAA;IACnB;AAEA,WAAO;SAAKF;MAAMC,aAAAA;;EACpB;AAEA,MAAI,gBAAgBD,MAAQA,GAAKI,cAAcJ,GAAKI,WAAWR,QAAQ;AACrE,QAAMQ,KAA8B,CAAA;AACpC,QAAMC,KAAc,CAAA;AACpB,aAASX,KAAI,GAAGC,KAAIK,GAAKI,WAAWR,QAAQF,KAAIC,IAAGD,MAAK;AACtD,UAAMY,KAAYN,GAAKI,WAAWV,EAAAA;AAClC,UAAIa,KAAOD,GAAUC,KAAKC;AAC1B,UAAgB,QAAZD,GAAK,CAAA,GAAA;AACPH,QAAAA,GAAWD,KAAKG,EAAAA;;AAEhBC,QAAAA,KAAOA,GAAKE,MAAM,CAAA;;AAEpBJ,MAAAA,GAAYE,EAAAA,IAAQD;IACtB;AACAN,IAAAA,KAAO;SAAKA;MAAMI,YAAAA;MAAYC,aAAAA;;EAChC;AAEA,MAAI,kBAAkBL,IAAM;AAC1B,QAAMU,KAA6C,CAAA;AACnD,QAAIC,KAAcX,GAAKY,SAASC,EAAKC;AACrC,QAAId,GAAKe,cAAc;AACrB,eAASrB,KAAI,GAAGC,KAAIK,GAAKe,aAAaL,WAAWd,QAAQF,KAAIC,IAAGD,MAAK;AACnE,YAAMsB,IAAYhB,GAAKe,aAAaL,WAAWhB,EAAAA;AAC/CiB,QAAAA,KACEA,MACCK,EAAUJ,SAASC,EAAKI,SACE,iBAAzBD,EAAUT,KAAKC,SAAAA,CACdQ,EAAUE;AACf,YAAMC,KAAepB,WAAWiB,CAAAA;AAChCN,QAAAA,GAAWP,KAAKgB,EAAAA;MAClB;AAEA,UAAA,CAAKR,IAAAA;AACHD,QAAAA,GAAWP,KAAK;UACdS,MAAMC,EAAKI;UACXV,MAAM;YACJK,MAAMC,EAAKO;YACXZ,OAAO;;UAETa,YAAAA;;;AAIJ,aAAO;WACFrB;QACHe,cAAc;aAAKf,GAAKe;UAAcL,YAAAA;;;IAE1C;EACF;AAEA,SAAOV;AAAI;AAGb,IAAMsB,IAAgD,oBAAIC;AA2B7CC,IAAAA,iBACXxB,CAAAA,OAAAA;AAEA,MAAMyB,KAAQC,YAAY1B,EAAAA;AAE1B,MAAI2B,KAASL,EAAcM,IAAIH,GAAMI,KAAAA;AACrC,MAAA,CAAKF,IAAQ;AACXL,MAAcQ,IACZL,GAAMI,OACLF,KAAS5B,WAAW0B,EAAAA,CAAAA;AAMvBM,WAAOC,eAAeL,IAAQ,SAAS;MACrCnB,OAAOiB,GAAMI;MACbI,YAAAA;;EAEJ;AAEA,SAAON;AAAM;ACrHR,SAASO,YACdC,IAAAA;AAEA,MAAMC,UAAYC,CAAAA,OAChBF,GAASE,EAAAA;AACXD,UAAQE,YAAY,MAKhBA,UADAC,KAAK,CAAA,EADLC,OAAOb,CAAAA,OAAAA,CAAWA,GAAOc,SAAAA,CAAUd,GAAOe,OAAAA,EAD1CN,OAAAA,CAAAA,CAAAA;AAKJA,UAAQO,OAAO,CAACC,IAAWC,OACzBT,QAAQE,UAAAA,EAAYK,KAAKC,IAAWC,EAAAA;AACtCT,UAAQU,YAAYC,CAAAA,OAAYD,UAAUC,EAAAA,EAAUX,OAAAA;AACpD,SAAOA;AACT;AC2BA,SAASY,cAAcpC,IAAMqC,IAASC,IAAAA;AACpC,SAAO;OACFD;IACHrC,MAAAA;IACAsC,SAASD,GAAQC,UACb;SACKD,GAAQC;SACRA;QAELA,MAAWD,GAAQC;;AAE3B;AAOO,IAAMC,cAAcA,CACzBC,IACAC,OAEOL,cAAcI,GAAUxC,MAAMwC,IAAW;EAC9CC,MAAM;OACDD,GAAUF,QAAQG;OAClBA;;;ACpEF,IAAMC,OAAOA,MAAAA;AAAAA;ACoDpB,SAASC,IAAIC,IAAAA;AACX,MAAMC,KAAgB,oBAAIlC;AAC1B,MAAMtB,KAAgC,CAAA;AACtC,MAAMyD,KAAyB,CAAA;AAG/B,MAAIC,KAAenE,MAAMC,QAAQ+D,EAAAA,IAASA,GAAM,CAAA,IAAKA,MAAS;AAC9D,WAAS9D,KAAI,GAAGA,KAAIkE,UAAUhE,QAAQF,MAAK;AACzC,QAAMc,KAAQoD,UAAUlE,EAAAA;AACxB,QAAIc,MAASA,GAAMP,aAAAA;AACjByD,MAAAA,GAAOvD,KAAKK,EAAAA;;AAEZmD,MAAAA,MAAQnD;;AAGVmD,IAAAA,MAAQC,UAAU,CAAA,EAAGlE,EAAAA;EACvB;AAEAgE,EAAAA,GAAOG,QAAQnC,YAAYiC,EAAAA,CAAAA;AAC3B,WAASjE,KAAI,GAAGA,KAAIgE,GAAO9D,QAAQF,MAAAA;AACjC,aAASoE,KAAI,GAAGA,KAAIJ,GAAOhE,EAAAA,EAAGO,YAAYL,QAAQkE,MAAK;AACrD,UAAMC,KAAaL,GAAOhE,EAAAA,EAAGO,YAAY6D,EAAAA;AACzC,UAAIC,GAAWnD,SAASC,EAAKmD,qBAAqB;AAChD,YAAMzD,KAAOwD,GAAWxD,KAAKC;AAC7B,YAAMA,KAAQyD,kBAAkBF,EAAAA;AAEhC,YAAA,CAAKN,GAAcS,IAAI3D,EAAAA,GAAO;AAC5BkD,UAAAA,GAAc3B,IAAIvB,IAAMC,EAAAA;AACxBP,UAAAA,GAAYE,KAAK4D,EAAAA;QACnB,WAEEN,GAAc7B,IAAIrB,EAAAA,MAAUC,IAAAA;AAG5B2D,kBAAQC,KACN,yDACE7D,KADF,uIAAA;;MAMN,OAAA;AACEN,QAAAA,GAAYE,KAAK4D,EAAAA;;IAErB;;AAGF,SAAOrC,YAAY;IACjBd,MAAMC,EAAKwD;IACXpE,aAAAA;;AAEJ;AC/FA,IAAMqE,aAAaA,CAAAA,EAAG1D,MAAAA,GAAAA,MACX,eAATA,MAAgC,YAATA;AAGlB,IAAM2D,eAAgBnB,CAAAA,OAAAA;AAC3B,MAAM3B,KAAQD,eAAe4B,GAAU3B,KAAAA;AACvC,MAAIA,OAAU2B,GAAU3B,OAAO;AAC7B,QAAM+C,KAAqBxB,cAAcI,GAAUxC,MAAMwC,EAAAA;AACzDoB,IAAAA,GAAmB/C,QAAQA;AAC3B,WAAO+C;EACT,OAAA;AACE,WAAOpB;;AACT;AAuBK,IAAMqB,gBAA0BA,CAAAA,EAAGC,SAAAA,IAASC,QAAAA,IAAQC,eAAAA,GAAAA,MAAAA;AACzD,MAAMC,KAA2B,oBAAItD;AACrC,MAAMuD,KAAiC,oBAAIvD;AAE3C,MAAMwD,oBAAqB3B,CAAAA,OACN,YAAnBA,GAAUxC,QAC0B,mBAApCwC,GAAUF,QAAQ8B,kBACmB,iBAApC5B,GAAUF,QAAQ8B,iBACjBH,GAAYX,IAAId,GAAUvD,GAAAA;AAE9B,SAAOoF,CAAAA,OAAAA;AACL,QAAMC,KAGJC,IAAI/B,CAAAA,OAAAA;AACF,UAAMgC,KAAeP,GAAYjD,IAAIwB,GAAUvD,GAAAA;AAE/C,MAAA+E,GAAc;QACZxB,WAAAA;WACIgC,KACA;UACEC,MAAM;UACNC,SAAS;YAEX;UACED,MAAM;UACNC,SAAS;;QACT5B,QAAA;;AAGR,UAAI/B,KACFyD,MACAG,WAAWnC,IAAW;QACpBoC,MAAM;;AAGV7D,MAAAA,KAAS;WACJA;QACHyB,WAAWD,YAAYC,IAAW;UAChCqC,cAAcL,KAAe,QAAQ;;;AAIzC,UAAwC,wBAApChC,GAAUF,QAAQ8B,eAAuC;AAC3DrD,QAAAA,GAAOc,QAAAA;AACPiD,2BAAmBf,IAAQvB,EAAAA;MAC7B;AAEA,aAAOzB;IAAM,CAAA,EAnCfa,OAAOmD,CAAAA,OAAAA,CAAOrB,WAAWqB,EAAAA,KAAOZ,kBAAkBY,EAAAA,CAAAA,EADlDV,EAAAA,CAAAA;AAwCF,QAAMW,KAiBJC,OAAIC,CAAAA,OAAAA;AACF,UAAA,EAAI1C,WAAEA,GAAAA,IAAc0C;AACpB,UAAA,CAAK1C,IAAAA;AAAW;;AAEhB,UAAI2C,KAAY3C,GAAUF,QAAQ8C,uBAAuB,CAAA;AAMzD,UAAgC,mBAA5BF,GAAS1C,UAAUxC,MAAAA;AACrBmF,QAAAA,MNvGuBD,CAAAA,OAA+B,CAAA,GAC3DzG,aAAayG,IAAwB,oBAAIG,KAAAA,CAAAA,GMsGPH,GAASN,IAAAA,EAAMU,OAAOH,EAAAA;;AAIrD,UAC8B,eAA5BD,GAAS1C,UAAUxC,QACS,mBAA5BkF,GAAS1C,UAAUxC,MACnB;AACA,YAAMuF,KAAoB,oBAAIF;AAE9B,QAAArB,GAAc;UACZS,MAAM;UACNC,SAAS,kDAAkDS,EAAAA;UAC3D3C,WAAAA;UACAoC,MAAM;YAAEO,WAAAA;YAAWD,UAAAA;;UAAUpC,QAAA;;AAG/B,iBAAShE,KAAI,GAAGA,KAAIqG,GAAUnG,QAAQF,MAAK;AACzC,cAAM0G,KAAWL,GAAUrG,EAAAA;AAC3B,cAAI2G,KAAavB,GAAelD,IAAIwE,EAAAA;AACpC,cAAA,CAAKC,IAAAA;AACHvB,YAAAA,GAAehD,IAAIsE,IAAWC,KAAa,oBAAIJ,KAAAA;;AACjD,mBAAWpG,MAAOwG,GAAWC,OAAAA,GAAAA;AAAUH,YAAAA,GAAkBrG,IAAID,EAAAA;;AAC7DwG,UAAAA,GAAWE,MAAAA;QACb;AAEA,iBAAW1G,MAAOsG,GAAkBG,OAAAA,GAAAA;AAClC,cAAIzB,GAAYX,IAAIrE,EAAAA,GAAM;AACxBuD,YAAAA,KAAayB,GAAYjD,IAAI/B,EAAAA,EAAyBuD;AACtDyB,YAAAA,GAAY2B,OAAO3G,EAAAA;AACnB6F,+BAAmBf,IAAQvB,EAAAA;UAC7B;;MAEH,WAA6B,YAAnBA,GAAUxC,QAAoBkF,GAASN,MAAM;AACtDX,QAAAA,GAAY/C,IAAIsB,GAAUvD,KAAKiG,EAAAA;AAC/B,iBAASpG,KAAI,GAAGA,KAAIqG,GAAUnG,QAAQF,MAAK;AACzC,cAAM0G,KAAWL,GAAUrG,EAAAA;AAC3B,cAAI2G,KAAavB,GAAelD,IAAIwE,EAAAA;AACpC,cAAA,CAAKC,IAAAA;AACHvB,YAAAA,GAAehD,IAAIsE,IAAWC,KAAa,oBAAIJ,KAAAA;;AACjDI,UAAAA,GAAWvG,IAAIsD,GAAUvD,GAAAA;QAC3B;MACF;IAAA,CAAA,EAtDF6E,GAHAlC,OACEmD,CAAAA,OAAkB,YAAZA,GAAG/E,QAAiD,iBAA7B+E,GAAGzC,QAAQ8B,aAAAA,EAF1CG,IAAIQ,CAAAA,OAAMxC,YAAYwC,IAAI;MAAEF,cAAc;QAX1CgB,MAAM,CAIFtB,IAAIZ,YAAAA,EADJ/B,OAAOmD,CAAAA,OAAAA,CAAOrB,WAAWqB,EAAAA,KAAAA,CAAQZ,kBAAkBY,EAAAA,CAAAA,EADnDV,EAAAA,CAAAA,GAMAzC,OAAOmD,CAAAA,OAAMrB,WAAWqB,EAAAA,CAAAA,EADxBV,EAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA;AAkEN,WAAOwB,MAAM,CAACvB,IAAYU,EAAAA,CAAAA;EAAe;AAC1C;AAMI,IAAMF,qBAAqBA,CAACf,IAAgBvB,OAC1CuB,GAAOe,mBACZ1C,cAAcI,GAAUxC,MAAMwC,IAAW;EACvC4B,eAAe;;ACnBrB,IAAM0B,IAAc,oBAAIT;AAkBjB,IAAMU,cAAcA,CAACC,KAA4B,CAAA,MAAA;AACtD,MAAMC,KAAAA,CAAAA,CAAyBD,GAAOC;AACtC,MAAMC,KAAAA,CAAAA,CAAsBF,GAAOE;AACnC,MAAMtB,KAAgD,CAAA;AAItD,MAAMuB,KAA4B,CAAA;AAClC,MAAMC,aAAcrF,CAAAA,OAAAA;AAClBoF,IAAAA,GAAgB5G,KAAKwB,GAAOyB,UAAUvD,GAAAA;AACtC,QAA+B,MAA3BkH,GAAgBnH,QAAAA;AAClBqH,cAAQC,QAAAA,EAAUvE,KAAK,MAAA;AACrB,YAAI9C;AACJ,eAAQA,KAAMkH,GAAgBI,MAAAA,GAAAA;AAC5B3B,UAAAA,GAAK3F,EAAAA,IAAO;;MACd,CAAA;;EAEJ;AAKF,MAAMuH,MACJA,CAAAA,EAAGzC,QAAAA,IAAQD,SAAAA,GAAAA,MACXO,CAAAA,OAAAA;AAGE,QAAMoC,KACJT,MAAqC,aAAA,OAApBA,GAAOS,WAAAA,CAAAA,CAClBT,GAAOS,WAAAA,CACR1C,GAAO2C;AAEd,QAAI1B,KAUFlB,GADAS,IAAIZ,YAAAA,EAPJ/B,OACEY,CAAAA,OACqB,eAAnBA,GAAUxC,QAAAA,CACT4E,GAAKpC,GAAUvD,GAAAA,KAAAA,CAAAA,CACd2F,GAAKpC,GAAUvD,GAAAA,EAAM6C,WACa,mBAApCU,GAAUF,QAAQ8B,aAAAA,EANtBC,EAAAA,CAAAA,CAAAA;AAcF,QAAIC,KAQFC,IAAIQ,CAAAA,OAAAA;AAEF,UAAMP,MAlGUmC,CACxBnE,IACAzB,IACAmF,QACqB;QACrB1D,WAAAA;QACAoC,MAAM7D,GAAO6D,OAAOgC,KAAKC,MAAM9F,GAAO6D,IAAAA,IAAAA;QACtCkC,YACEZ,MAAqBnF,GAAO+F,aACxBF,KAAKC,MAAM9F,GAAO+F,UAAAA,IAAAA;QAExBC,OAAOhG,GAAOgG,QACV,IAAIC,cAAc;UAChBC,cAAclG,GAAOgG,MAAME,eACvB,IAAIC,MAAMnG,GAAOgG,MAAME,YAAAA,IAAAA;UAE3BE,eAAepG,GAAOgG,MAAMI;;QAGlCtF,OAAAA;QACAC,SAAAA,CAAAA,CAAWf,GAAOe;UA+ERiD,IAFiBH,GAAKG,GAAG9F,GAAAA,GAIzBiH,EAAAA;AAGF,UAAID,MAAAA,CAAyBH,EAAYxC,IAAIyB,GAAG9F,GAAAA,GAAM;AACpDuF,QAAAA,GAAa3C,QAAAA;AACbiE,UAAY5G,IAAI6F,GAAG9F,GAAAA;AACnB6F,2BAAmBf,IAAQgB,EAAAA;MAC7B;AAQA,aANgC;WAC3BP;QACHhC,WAAWD,YAAYwC,IAAI;UACzBF,cAAc;;;IAGL,CAAA,EA1BfjD,OACEY,CAAAA,OACqB,eAAnBA,GAAUxC,QAAAA,CAAAA,CACR4E,GAAKpC,GAAUvD,GAAAA,KACmB,mBAApCuD,GAAUF,QAAQ8B,aAAAA,EALtBC,EAAAA,CAAAA;AA+BF,QAAA,CAAKoC,IAAAA;AAEHzB,MAAAA,KAEEC,OAAKlE,CAAAA,OAAAA;AACH,YAAA,EAAMyB,WAAEA,GAAAA,IAAczB;AACtB,YAAuB,eAAnByB,GAAUxC,MAAqB;AACjC,cAAMoH,MAvKIC,CACtBtG,IACAmF,OAAAA;AAEA,gBAAMkB,KAA+B;cACnCtF,SAASf,GAAOe;;AAGlB,gBAAA,WAAIf,GAAO6D,MAAAA;AACTwC,cAAAA,GAAWxC,OAAOgC,KAAKU,UAAUvG,GAAO6D,IAAAA;;AAG1C,gBAAIsB,MAAAA,WAAqBnF,GAAO+F,YAAAA;AAC9BM,cAAAA,GAAWN,aAAaF,KAAKU,UAAUvG,GAAO+F,UAAAA;;AAGhD,gBAAI/F,GAAOgG,OAAO;AAChBK,cAAAA,GAAWL,QAAQ;gBACjBI,eAAepG,GAAOgG,MAAMI,cAAc5C,IAAIwC,CAAAA,OAAAA;AAC5C,sBAAA,CAAKA,GAAMQ,QAAAA,CAASR,GAAMD,YAAAA;AAAY,2BAAOC,GAAMrC;;AAEnD,yBAAO;oBACLA,SAASqC,GAAMrC;oBACf6C,MAAMR,GAAMQ;oBACZT,YAAYC,GAAMD;;gBACnB,CAAA;;AAIL,kBAAI/F,GAAOgG,MAAME,cAAAA;AACfG,gBAAAA,GAAWL,MAAME,eAAe,KAAKlG,GAAOgG,MAAME;;YAEtD;AAEA,mBAAOG;UAAU,GAqI8BrG,IAAQmF,EAAAA;AAC3CtB,UAAAA,GAAKpC,GAAUvD,GAAAA,IAAOmI;QACxB;MAAA,CAAA,EANFpC,EAAAA;;AAWFV,MAAAA,KAA8BW,OAAImB,UAAAA,EAAhB9B,EAAAA;;AAGpB,WAAOuB,MAAM,CAACb,IAAeV,EAAAA,CAAAA;EAAY;AAG7CkC,MAAIgB,cAAeC,CAAAA,OAAAA;AACjB,aAAWxI,MAAOwI,IAAAA;AAEhB,UAAkB,SAAd7C,GAAK3F,EAAAA,GAAAA;AACP2F,QAAAA,GAAK3F,EAAAA,IAAOwI,GAAQxI,EAAAA;;;EAExB;AAGFuH,MAAIkB,cAAc,MAAA;AAChB,QAAM3G,KAAkB,CAAA;AACxB,aAAW9B,MAAO2F,IAAAA;AAAM,UAAiB,QAAbA,GAAK3F,EAAAA,GAAAA;AAAc8B,QAAAA,GAAO9B,EAAAA,IAAO2F,GAAK3F,EAAAA;;;AAClE,WAAO8B;EAAM;AAGf,MAAIiF,MAAUA,GAAO2B,cAAAA;AACnBnB,QAAIgB,YAAYxB,GAAO2B,YAAAA;;AAGzB,SAAOnB;AAAG;ACrLL,IAAMoB,uBACXA,CAAAA,EACEC,qBAAAA,IACAC,qBAAAA,IACAC,yBAAAA,GAAAA,MAEF,CAAA,EAAGhE,QAAAA,IAAQD,SAAAA,GAAAA,MAAAA;AA+DT,MAAMkE,KACJD,OACCvF,CAAAA,OACoB,mBAAnBA,GAAUxC,QAAAA,CAAAA,CACP8H,OACmB,YAAnBtF,GAAUxC,QAAuC,eAAnBwC,GAAUxC;AAE/C,SAAOqE,CAAAA,OAAAA;AACL,QAAM4D,KAOJC,SAAS1F,CAAAA,OAAAA;AACP,UAAA,EAAMvD,KAAEA,GAAAA,IAAQuD;AAChB,UAAM2F,KAEJvG,OAAOmD,CAAAA,OAAkB,eAAZA,GAAG/E,QAAuB+E,GAAG9F,QAAQA,EAAAA,EADlDoF,EAAAA;AAIF,aAEE+D,UAAUD,EAAAA,GArFhB3F,CAAAA,OAAAA;AAEA,YAAM6F,KAAgBR,GACpBS,cAAc9F,EAAAA,GACdA,EAAAA;AAGF,eAAO+F,KAAsBC,CAAAA,OAAAA;AAC3B,cAAIC,KAAAA;AACJ,cAAIC;AACJ,cAAI3H;AAEJ,mBAAS4H,WAAW/I,IAAAA;AAClB4I,YAAAA,GAASI,KACN7H,KAASA,KACN8H,iBAAiB9H,IAAQnB,EAAAA,IACzB+E,WAAWnC,IAAW5C,EAAAA,CAAAA;UAE9B;AAEAyG,kBAAQC,QAAAA,EAAUvE,KAAK,MAAA;AACrB,gBAAI0G,IAAAA;AAAY;;AAEhBC,YAAAA,KAAML,GAAcnG,UAAU;cAC5B0G,MAAMD;cACN5B,MAAMA,IAAAA;AACJ,oBAAInI,MAAMC,QAAQkI,EAAAA,GAAAA;AAKhB4B,6BAAW;oBAAEG,QAAQ/B;;;AAErByB,kBAAAA,GAASI,KAAKG,gBAAgBvG,IAAWuE,EAAAA,CAAAA;;AAE3CyB,gBAAAA,GAASQ,SAAAA;cACV;cACDA,WAAAA;AACE,oBAAA,CAAKP,IAAY;AACfA,kBAAAA,KAAAA;AACA,sBAAuB,mBAAnBjG,GAAUxC,MAAAA;AACZ+D,oBAAAA,GAAOe,mBACL1C,cAAc,YAAYI,IAAWA,GAAUF,OAAAA,CAAAA;;AAGnD,sBAAIvB,MAAUA,GAAOe,SAAAA;AACnB6G,+BAAW;sBAAE7G,SAAAA;;;AAEf0G,kBAAAA,GAASQ,SAAAA;gBACX;cACF;;UACA,CAAA;AAGJ,iBAAO,MAAA;AACLP,YAAAA,KAAAA;AACA,gBAAIC,IAAAA;AAAKA,cAAAA,GAAIO,YAAAA;;UAAa;QAC3B,CAAA;MACD,GA0B6BzG,EAAAA,CAAAA;IAAU,CAAA,EAbvCZ,OACEY,CAAAA,OACqB,eAAnBA,GAAUxC,QACVgI,GAA0BxF,EAAAA,CAAAA,EAJ9B6B,EAAAA,CAAAA;AAoBF,QAAM6E,KAOJpF,GALAlC,OACEY,CAAAA,OACqB,eAAnBA,GAAUxC,QAAAA,CACTgI,GAA0BxF,EAAAA,CAAAA,EAJ/B6B,EAAAA,CAAAA;AASF,WAAOwB,MAAM,CAACoC,IAAsBiB,EAAAA,CAAAA;EAAU;AAC/C;ACxNE,IAAMC,gBAA0BA,CAAAA,EAAGrF,SAAAA,GAAAA,MAAAA;AACxC,MAA6B,OAAbsF;AACd,WAAO/E,CAAAA,OAAQP,GAAQO,EAAAA;;AAEvB,WAAOA,CAAAA,OAMHY,OAAIlE,CAAAA,OAEFwC,QAAQ8F,IAAI,2CAA2CtI,EAAAA,CAAAA,EAHzD+C,GADAmB,OAAIF,CAAAA,OAAMxB,QAAQ8F,IAAI,0CAA0CtE,EAAAA,CAAAA,EAFhEV,EAAAA,CAAAA,CAAAA;;AASN;ACJK,IAAMiF,gBAA0BA,CAAAA,EAAGxF,SAAAA,IAASE,eAAAA,GAAAA,MAC1CK,CAAAA,OAAAA;AACL,MAAMkF,KASJrB,SAAS1F,CAAAA,OAAAA;AACP,QAAMO,KAAOuF,cAAc9F,EAAAA;AAC3B,QAAMgH,KAAMC,aAAajH,IAAWO,EAAAA;AACpC,QAAM2G,KAAeC,iBAAiBnH,IAAWO,EAAAA;AAEjD,IAAAiB,GAAc;MACZS,MAAM;MACNC,SAAS;MACTlC,WAAAA;MACAoC,MAAM;QACJ4E,KAAAA;QACAE,cAAAA;;MACD5G,QAAA;;AAGH,QAAMA,KAEJsF,UAGIxG,OAAOmD,CAAAA,OAAkB,eAAZA,GAAG/E,QAAuB+E,GAAG9F,QAAQuD,GAAUvD,GAAAA,EAD5DoF,EAAAA,CAAAA,EAHJuF,gBAAgBpH,IAAWgH,IAAKE,EAAAA,CAAAA;AASlC,QAA6B,MAAbN;AACd,aAEES,OAAO9I,CAAAA,OAAAA;AACL,YAAMgG,KAAAA,CAAShG,GAAO6D,OAAO7D,GAAOgG,QAAAA;AAEpC,QAAA/C,GAAc;UACZS,MAAMsC,KAAQ,eAAe;UAC7BrC,SAAS,KACPqC,KAAQ,WAAW,YAAA;UAErBvE,WAAAA;UACAoC,MAAM;YACJ4E,KAAAA;YACAE,cAAAA;YACA9J,OAAOmH,MAAShG;;UACjB+B,QAAA;;MACD,CAAA,EAfJA,EAAAA;;AAoBJ,WAAOA;EAAM,CAAA,EAtDflB,OAAOY,CAAAA,OAEgB,eAAnBA,GAAUxC,SACU,mBAAnBwC,GAAUxC,QAAAA,CAAAA,CACPwC,GAAUF,QAAQwH,mBAAAA,EAL1BzF,EAAAA,CAAAA;AA2DF,MAAM6E,KASJpF,GAPAlC,OAAOY,CAAAA,OAEgB,eAAnBA,GAAUxC,QACU,mBAAnBwC,GAAUxC,QAAAA,CACRwC,GAAUF,QAAQwH,kBAAAA,EALzBzF,EAAAA,CAAAA;AAWF,SAAOwB,MAAM,CAAC0D,IAAeL,EAAAA,CAAAA;AAAU;AChF9Ba,IAAAA,mBACVC,CAAAA,OACD,CAAA,EAAGjG,QAAAA,IAAQD,SAAAA,IAASE,eAAAA,GAAAA,MAClBgG,GAAUC,YAAY,CAACnG,IAASoG,OAAAA;AAC9B,MAAIC,KAAAA;AACJ,SAAOD,GAAS;IACdnG,QAAAA;IACAD,QAAQsG,IAAAA;AACN,UAA6B,MAAc;AACzC,YAAID,IAAAA;AACF,gBAAM,IAAIjD,MACR,sDAAA;;AAEJiD,QAAAA,KAAAA;MACF;AACA,aAAOE,MAAMvG,GAAQuG,MAAMD,EAAAA,CAAAA,CAAAA;IAC5B;IACDpG,cAAcsG,IAAAA;AACZ,MAAAtG,GAAc;QACZuG,WAAWC,KAAKC,IAAAA;QAChB3H,QAAQoH,GAASvK;WACd2K;;IAEP;;AACA,GACDxG,EAAAA;ACqBA,IAAM4G,cAAcA,CAAAA,EACzBC,aAAAA,IACAxI,UAAAA,IACAyI,SAAAA,GAAAA,MAEO,CAAA,EAAG9G,SAAAA,GAAAA,MACRO,CAAAA,OAaI6D,SAASnH,CAAAA,OAAAA;AACP,MAAI6J,MAAW7J,GAAOgG,OAAAA;AAAO6D,IAAAA,GAAQ7J,GAAOgG,OAAOhG,GAAOyB,SAAAA;;AAC1D,MAAMqI,KAAa1I,MAAYA,GAASpB,EAAAA,KAAYA;AACpD,SAAO,UAAU8J,KACbC,YAAYD,EAAAA,IACZE,UAAUF,EAAAA;AAAU,CAAA,EAN1B/G,GAREoE,SAAS1F,CAAAA,OAAAA;AACP,MAAMwI,KACHL,MAAeA,GAAYnI,EAAAA,KAAeA;AAC7C,SAAO,UAAUwI,KACbF,YAAYE,EAAAA,IACZD,UAAUC,EAAAA;AAAa,CAAA,EAN7B3G,EAAAA,CAAAA,CAAAA;ACjEH,IAAM4G,mBAGXA,CAAAA,EAAGjH,eAAAA,GAAAA,MACHK,CAAAA,OAAAA;AACE,MAA6B,MAAb+E;AACd/E,IAAAA,KAEEY,OAAIzC,CAAAA,OAAAA;AACF,UACqB,eAAnBA,GAAUxC,QACe,MACzB;AACA,YAAM0E,KAAU,+CAA+ClC,GAAUxC,IAAAA;AAEzE,QAAAgE,GAAc;UACZS,MAAM;UACNC,SAAAA;UACAlC,WAAAA;UAASM,QAAA;;AAEXS,gBAAQC,KAAKkB,EAAAA;MACf;IAAA,CAAA,EAdFL,EAAAA;;AAoBJ,SAAOzC,OAAQsJ,CAAAA,OAAAA,KAAoB,EAAO7G,EAAAA;AAAK;IC4etC8G,IAA8C,SAASA,OAElEC,IAAAA;AAEA,MAAgBhC,CAA8BgC,GAAK5B,KAAAA;AACjD,UAAM,IAAItC,MAAM,gDAAA;;AAGlB,MAAImE,KAAM;AAEV,MAAMC,KAAU,oBAAI3K;AACpB,MAAM4K,KAA+C,oBAAI5K;AACzD,MAAM6K,KAAa,oBAAInG;AACvB,MAAMoG,KAAqB,CAAA;AAE3B,MAAMC,KAAW;IACflC,KAAK4B,GAAK5B;IACVM,oBAAoBsB,GAAKtB;IACzBJ,cAAc0B,GAAK1B;IACnBiC,OAAOP,GAAKO;IACZC,iBAAiBR,GAAKQ;IACtBxH,eAAegH,GAAKhH,iBAAiB;;AAKvC,MAAMqB,KAAaoG,YAAAA;AAEnB,WAASC,cAActJ,IAAAA;AACrB,QACqB,eAAnBA,GAAUxC,QACS,eAAnBwC,GAAUxC,QAAAA,CACTwL,GAAWlI,IAAId,GAAUvD,GAAAA,GAC1B;AACA,UAAuB,eAAnBuD,GAAUxC,MAAAA;AACZwL,QAAAA,GAAW5F,OAAOpD,GAAUvD,GAAAA;iBACA,eAAnBuD,GAAUxC,MAAAA;AACnBwL,QAAAA,GAAWtM,IAAIsD,GAAUvD,GAAAA;;AAE3BwG,MAAAA,GAAWmD,KAAKpG,EAAAA;IAClB;EACF;AAIA,MAAIuJ,KAAAA;AACJ,WAASC,kBAAkBxJ,IAAAA;AACzB,QAAIA,IAAAA;AAAWsJ,oBAActJ,EAAAA;;AAE7B,QAAA,CAAKuJ,IAAwB;AAC3BA,MAAAA,KAAAA;AACA,aAAOA,OAA2BvJ,KAAYiJ,GAAMlF,MAAAA,IAAAA;AAClDuF,sBAActJ,EAAAA;;AAChBuJ,MAAAA,KAAAA;IACF;EACF;AAGA,MAAME,mBAAoBzJ,CAAAA,OAAAA;AACxB,QAAI0J,KAWF9D,UAGIxG,OAAOmD,CAAAA,OAAkB,eAAZA,GAAG/E,QAAuB+E,GAAG9F,QAAQuD,GAAUvD,GAAAA,EAD5DwG,GAAW3C,MAAAA,CAAAA,EAVflB,OACGuK,CAAAA,OACCA,GAAI3J,UAAUxC,SAASwC,GAAUxC,QACjCmM,GAAI3J,UAAUvD,QAAQuD,GAAUvD,QAAAA,CAC9BkN,GAAI3J,UAAUF,QAAQ8J,aACtBD,GAAI3J,UAAUF,QAAQ8J,cAAc5J,GAAUF,QAAQ8J,UAAAA,EAP5DC,CAAAA,CAAAA;AAkBF,QAAuB,YAAnB7J,GAAUxC,MAAAA;AAEZkM,MAAAA,KAEEI,UAAUvL,CAAAA,OAAAA,CAAAA,CAAYA,GAAOe,SAAAA,IAAS,EADtCoK,EAAAA;;AAIFA,MAAAA,KAGEK,UAAUxL,CAAAA,OAAAA;AACR,YAAMyL,KAASzB,UAAUhK,EAAAA;AACzB,eAAOA,GAAOc,SAASd,GAAOe,UAC1B0K,KACA3G,MAAM,CACJ2G,IAKEjI,IAAI,MAAA;AACFxD,UAAAA,GAAOc,QAAAA;AACP,iBAAOd;QAAM,CAAA,EAHfY,KAAK,CAAA,EADLC,OAAOmD,CAAAA,OAAMA,GAAG9F,QAAQuD,GAAUvD,GAAAA,EADlCwG,GAAW3C,MAAAA,CAAAA,CAAAA,CAAAA,CAAAA;MAQb,CAAA,EAjBRoJ,EAAAA;;AAsBJ,QAAuB,eAAnB1J,GAAUxC,MAAAA;AACZkM,MAAAA,KAyBEO,MAAM,MAAA;AAEJjB,QAAAA,GAAW5F,OAAOpD,GAAUvD,GAAAA;AAC5BqM,QAAAA,GAAQ1F,OAAOpD,GAAUvD,GAAAA;AACzBsM,QAAAA,GAAO3F,OAAOpD,GAAUvD,GAAAA;AAExB8M,QAAAA,KAAAA;AAEA,iBAASjN,KAAI2M,GAAMzM,SAAS,GAAGF,MAAK,GAAGA,MAAAA;AACrC,cAAI2M,GAAM3M,EAAAA,EAAGG,QAAQuD,GAAUvD,KAAAA;AAAKwM,YAAAA,GAAMiB,OAAO5N,IAAG,CAAA;;;AAEtDgN,sBACE1J,cAAc,YAAYI,IAAWA,GAAUF,OAAAA,CAAAA;MAChD,CAAA,EAnCHuH,OAAO9I,CAAAA,OAAAA;AACL,YAAIA,GAAOc,OAAAA;AACT,cAAA,CAAKd,GAAOe,SAAAA;AAEV0J,YAAAA,GAAW5F,OAAOpD,GAAUvD,GAAAA;;AAI5B,qBAASH,KAAI,GAAGA,KAAI2M,GAAMzM,QAAQF,MAAK;AACrC,kBAAM0D,KAAYiJ,GAAM3M,EAAAA;AACxB,kBAAI0D,GAAUvD,QAAQ8B,GAAOyB,UAAUvD,KAAK;AAC1CuM,gBAAAA,GAAW5F,OAAOpD,GAAUvD,GAAAA;AAC5B;cACF;YACF;;mBAEG,CAAK8B,GAAOe,SAAAA;AACjB0J,UAAAA,GAAW5F,OAAOpD,GAAUvD,GAAAA;;AAE9BqM,QAAAA,GAAQpK,IAAIsB,GAAUvD,KAAK8B,EAAAA;MAAO,CAAA,EArBpCmL,EAAAA,CAAAA;;AAyCFA,MAAAA,KAGES,QAAQ,MAAA;AACNb,sBAActJ,EAAAA;MAAU,CAAA,EAH1B0J,EAAAA;;AAQJ,WAAO7B,MAAM6B,EAAAA;EAAQ;AAGvB,MAAMU,KACJC,gBAAgB1B,SAAS0B,OAAO1L,OAAO2L,OAAO3B,OAAO4B,SAAAA;AACvD,MAAMhJ,KAAiB5C,OAAO6L,OAAOJ,IAAU;IAC7ClG,UAAAA,CAAAA,CAAY0E,GAAK1E;IACjB0D,aAAa3E,GAAW3C;IAExBgC,mBAAmBtC,IAAAA;AAGjB,UAAuB,eAAnBA,GAAUxC,MAAAA;AACZgM,0BAAkBxJ,EAAAA;iBACU,eAAnBA,GAAUxC,MAAqB;AACxCyL,QAAAA,GAAMlM,KAAKiD,EAAAA;AACX6D,gBAAQC,QAAAA,EAAUvE,KAAKiK,iBAAAA;MACxB,WAAUT,GAAOjI,IAAId,GAAUvD,GAAAA,GAAM;AACpC,YAAIgO,KAAAA;AACJ,iBAASnO,KAAI,GAAGA,KAAI2M,GAAMzM,QAAQF,MAAAA;AAChC,cAAI2M,GAAM3M,EAAAA,EAAGG,QAAQuD,GAAUvD,KAAK;AAClCwM,YAAAA,GAAM3M,EAAAA,IAAK0D;AACXyK,YAAAA,KAAAA;UACF;;AAGF,YAAA,EACGA,MACCzB,GAAWlI,IAAId,GAAUvD,GAAAA,KACW,mBAApCuD,GAAUF,QAAQ8B,gBACpB;AACAqH,UAAAA,GAAMlM,KAAKiD,EAAAA;AACX6D,kBAAQC,QAAAA,EAAUvE,KAAKiK,iBAAAA;QACzB,OAAO;AACLR,UAAAA,GAAW5F,OAAOpD,GAAUvD,GAAAA;AAC5BoH,kBAAQC,QAAAA,EAAUvE,KAAKiK,iBAAAA;QACzB;MACF;IACD;IAEDkB,uBAAuBlN,IAAMqC,IAAS+I,IAAAA;AACpC,UAAA,CAAKA,IAAAA;AAAMA,QAAAA,KAAO,CAAA;;AAElB,UAAI+B;AACJ,UAEW,eAATnN,OACCmN,KAAuBC,iBAAiB/K,GAAQxB,KAAAA,OAAYb,IAAAA;AAE7D,cAAM,IAAIkH,MACR,+BAA+BlH,EAAAA,gBAAoBmN,EAAAA,GAAAA;;AAIvD,aAAO/K,cAAcpC,IAAMqC,IAAS;QAClC+J,WACW,eAATpM,KACMqL,KAAOA,KAAM,IAAK,IAAA;WAEvBK;WACAN;QACHhH,eAAegH,GAAKhH,iBAAiBsH,GAAStH;QAC9CsC,UAAU0E,GAAK1E,YAAAA,UAAa0E,GAAK1E,YAAsB3C,GAAO2C;;IAEjE;IAED2G,wBAAwB7K,IAAAA;AACtB,UAAuB,eAAnBA,GAAUxC,MAAAA;AACZ,eAAOsB,YAAY2K,iBAAiBzJ,EAAAA,CAAAA;;AAGtC,aAAOlB,YACLgM,KAAsB,MAAA;AACpB,YAAIxK,KAASyI,GAAOvK,IAAIwB,GAAUvD,GAAAA;AAClC,YAAA,CAAK6D,IAAAA;AACHyI,UAAAA,GAAOrK,IAAIsB,GAAUvD,KAAM6D,KAASmJ,iBAAiBzJ,EAAAA,CAAAA;;AAGvDM,QAAAA,KAEE6J,QAAQ,MAAA;AACNX,4BAAkBxJ,EAAAA;QAAU,CAAA,EAF9BM,EAAAA;AAMF,YAAMyK,KAASjC,GAAQtK,IAAIwB,GAAUvD,GAAAA;AACrC,YACqB,YAAnBuD,GAAUxC,QACVuN,OACCA,GAAO1L,SAAS0L,GAAOzL,UAAAA;AAExB,iBAQEyK,UAAUxB,SAAAA,EAPVlF,MAAM,CACJ/C,IAGElB,OAAO2L,CAAAA,OAAUA,OAAWjC,GAAQtK,IAAIwB,GAAUvD,GAAAA,CAAAA,EADlD8L,UAAUwC,EAAAA,CAAAA,CAAAA,CAAAA,CAAAA;;AAOhB,iBAAOzK;;MACT,CAAA,CAAA;IAGL;IAED0K,aAAa3M,IAAOuK,IAAAA;AAClB,UAAM5I,KAAYuB,GAAOmJ,uBAAuB,SAASrM,IAAOuK,EAAAA;AAChE,aAAOrH,GAAOsJ,wBAAwB7K,EAAAA;IACvC;IAEDiL,oBAAoB5M,IAAOuK,IAAAA;AACzB,UAAM5I,KAAYuB,GAAOmJ,uBACvB,gBACArM,IACAuK,EAAAA;AAEF,aAAOrH,GAAOsJ,wBAAwB7K,EAAAA;IACvC;IAEDkL,gBAAgB7M,IAAOuK,IAAAA;AACrB,UAAM5I,KAAYuB,GAAOmJ,uBAAuB,YAAYrM,IAAOuK,EAAAA;AACnE,aAAOrH,GAAOsJ,wBAAwB7K,EAAAA;IACvC;IAEDmL,UAAU9M,IAAO+M,IAAWtL,IAAAA;AAC1B,UAAIvB,KAAiC;AAInCmB,gBAAUiK,CAAAA,OAAAA;AACRpL,QAAAA,KAASoL;MAAG,CAAA,EAFdpI,GAAOlD,MAAMA,IAAO+M,IAAWtL,EAAAA,CAAAA,EAI/B2G,YAAAA;AAEF,aAAOlI;IACR;IAEDF,OAAKA,CAACA,IAAO+M,IAAWtL,OACfyB,GAAOyJ,aAAaK,cAAchN,IAAO+M,EAAAA,GAAYtL,EAAAA;IAG9DwL,cAAYA,CAACjN,IAAO+M,IAAWtL,OACtByB,GAAO0J,oBACZI,cAAchN,IAAO+M,EAAAA,GACrBtL,EAAAA;IAIJyL,UAAQA,CAAClN,IAAO+M,IAAWtL,OAClByB,GAAO2J,gBAAgBG,cAAchN,IAAO+M,EAAAA,GAAYtL,EAAAA;;AAInE,MAAI0B,KAAgDtB;AACpD,MAA6B,MAAc;AACzC,QAAA,EAAMkG,MAAEA,IAAI9F,QAAEA,EAAAA,IAAW+I,YAAAA;AACzB9H,IAAAA,GAAOiK,yBAA0BC,CAAAA,OAClB/L,UAAU+L,EAAAA,EAAlBnL,CAAAA;AACPkB,IAAAA,KAAgB4E;EAClB;AAIA,MAAMsF,IAAmBnE,iBAAiBqB,GAAKpB,SAAAA;AAK/C,MAAMqC,IAAWhC,MACf6D,EAAiB;IACfnK,QAAAA;IACAC,eAAAA;IACAF,SAASmH,iBAAiB;MAAEjH,eAAAA;;KAC3ByB,GAAW3C,MAAAA,CAAAA;AAKDqL,UAAV9B,CAAAA;AAEL,SAAOtI;AACT;AAMO,IAAMqK,IAAejD;", "names": ["Kind", "NAME", "DOCUMENT", "OPERATION_DEFINITION", "VARIABLE_DEFINITION", "SELECTION_SET", "FIELD", "ARGUMENT", "FRAGMENT_SPREAD", "INLINE_FRAGMENT", "FRAGMENT_DEFINITION", "VARIABLE", "INT", "FLOAT", "STRING", "BOOLEAN", "NULL", "ENUM", "LIST", "OBJECT", "OBJECT_FIELD", "DIRECTIVE", "NAMED_TYPE", "LIST_TYPE", "NON_NULL_TYPE", "GraphQLError", "Error", "constructor", "message", "nodes", "source", "positions", "path", "originalError", "extensions", "super", "this", "name", "Array", "isArray", "_extensions", "originalExtensions", "toJSON", "toString", "Symbol", "toStringTag", "input", "idx", "error", "kind", "advance", "pattern", "lastIndex", "test", "slice", "leadingRe", "blockString", "string", "lines", "split", "out", "commonIndent", "firstNonEmptyLine", "lastNonEmptyLine", "length", "i", "replace", "ignored", "char", "charCodeAt", "nameRe", "valueRe", "RegExp", "ValueGroup", "complexStringRe", "value", "constant", "match", "exec", "values", "push", "fields", "Const", "Var", "Int", "floatPart", "Float", "BlockString", "block", "String", "JSON", "parse", "Enum", "arguments_", "args", "_name", "directives", "arguments", "type", "lists", "selectionRe", "SelectionGroup", "selectionSet", "selections", "Spread", "_directives", "typeCondition", "Name", "_alias", "_arguments", "_selectionSet", "alias", "fragmentDefinition", "_condition", "definitionRe", "operationDefinition", "operation", "_variableDefinitions", "variableDefinitions", "vars", "_type", "_defaultValue", "variable", "defaultValue", "options", "document", "noLoc", "definition", "definitions", "loc", "_loc", "start", "end", "startToken", "endToken", "body", "locationOffset", "line", "column", "noLocation", "mapJoin", "value", "joiner", "mapper", "out", "index", "length", "printString", "string", "JSON", "stringify", "printBlockString", "replace", "LF", "nodes", "OperationDefinition", "node", "operation", "name", "variableDefinitions", "VariableDefinition", "directives", "Directive", "SelectionSet", "selectionSet", "Variable", "variable", "_print", "type", "defaultValue", "Field", "alias", "arguments", "args", "Argument", "slice", "selections", "StringValue", "block", "BooleanValue", "Null<PERSON><PERSON>ue", "_node", "IntValue", "FloatValue", "EnumValue", "Name", "ListValue", "values", "ObjectValue", "fields", "ObjectField", "Document", "definitions", "FragmentSpread", "InlineFragment", "typeCondition", "FragmentDefinition", "NamedType", "ListType", "NonNullType", "kind", "print", "e", "e", "r", "t", "i", "a", "e", "f", "n", "s", "l", "u", "o", "r", "t", "i", "e", "a", "f", "n", "s", "l", "u", "r", "t", "i", "a", "e", "f", "n", "s", "l", "u", "r", "t", "i", "a", "e", "f", "n", "r", "t", "i", "a", "e", "f", "n", "e", "r", "t", "i", "a", "f", "n", "e", "r", "t", "i", "a", "e", "r", "t", "i", "e", "a", "rehydrateGraphQlError", "error", "message", "extensions", "name", "GraphQLError", "nodes", "source", "positions", "path", "CombinedError", "Error", "constructor", "input", "normalizedGraphQLErrors", "graphQLErrors", "map", "generateErrorMessage", "networkErr", "graphQlErrs", "i", "l", "length", "networkError", "super", "this", "response", "toString", "phash", "x", "seed", "h", "charCodeAt", "seen", "Set", "cache", "WeakMap", "stringify", "includeFiles", "has", "JSON", "toJSON", "Array", "isArray", "out", "FileConstructor", "NoopConstructor", "BlobConstructor", "keys", "Object", "sort", "getPrototypeOf", "prototype", "key", "get", "Math", "random", "slice", "set", "__key", "add", "value", "delete", "extract", "stringifyVariables", "clear", "File", "Blob", "GRAPHQL_STRING_RE", "REPLACE_CHAR_RE", "replaceOutsideStrings", "str", "idx", "replace", "sanitizeDocument", "node", "split", "join", "trim", "prints", "Map", "docs", "stringifyDocument", "printed", "loc", "body", "print", "start", "end", "locationOffset", "line", "column", "hashDocument", "documentId", "definitions", "operationName", "getOperationName", "keyDocument", "query", "parse", "noLocation", "createRequest", "_query", "_variables", "variables", "printedVars", "kind", "Kind", "OPERATION_DEFINITION", "getOperationType", "operation", "makeResult", "result", "errors", "defaultHasNext", "data", "hasNext", "stale", "deepMerge", "target", "mergeResultPatch", "prevResult", "nextResult", "pending", "hasExtensions", "payload", "incremental", "withData", "_loop", "patch", "push", "assign", "prop", "part", "res", "find", "pendingRes", "id", "subPath", "items", "startIndex", "makeErrorResult", "makeFetchBody", "request", "<PERSON><PERSON><PERSON><PERSON>", "miss", "makeFetchURL", "useGETMethod", "context", "preferGetMethod", "url", "urlParts", "splitOutSearchParams", "finalUrl", "indexOf", "URLSearchParams", "serializeBody", "json", "files", "size", "form", "FormData", "append", "index", "file", "values", "makeFetchOptions", "headers", "accept", "extraOptions", "fetchOptions", "for<PERSON>ach", "toLowerCase", "serializedBody", "method", "decoder", "TextDecoder", "boundaryHeaderRe", "eventStreamRe", "decode", "async", "streamBody", "Symbol", "asyncIterator", "chunk", "reader", "<PERSON><PERSON><PERSON><PERSON>", "read", "done", "cancel", "chunks", "boundary", "buffer", "boundaryIndex", "fetchOperation", "networkMode", "Promise", "resolve", "contentType", "fetch", "results", "test", "parseMultipartMixed", "<PERSON><PERSON><PERSON><PERSON>", "match", "isPreamble", "preambleIndex", "parseEventStream", "parseJSON", "text", "parseMaybeJSON", "NODE_ENV", "console", "warn", "e", "status", "statusText", "makeFetchSource", "abortController", "AbortController", "signal", "onEnd", "abort", "filter", "fromAsyncIterable", "collectTypes", "obj", "types", "Array", "isArray", "i", "l", "length", "key", "add", "formatNode", "node", "definitions", "newDefinition", "push", "directives", "_directives", "directive", "name", "value", "slice", "selections", "hasTypename", "kind", "Kind", "OPERATION_DEFINITION", "selectionSet", "selection", "FIELD", "alias", "newSelection", "NAME", "_generated", "formattedDocs", "Map", "formatDocument", "query", "keyDocument", "result", "get", "__key", "set", "Object", "defineProperty", "enumerable", "with<PERSON><PERSON><PERSON>", "_source$", "source$", "sink", "to<PERSON>romise", "take", "filter", "stale", "hasNext", "then", "onResolve", "onReject", "subscribe", "onResult", "makeOperation", "request", "context", "addMetadata", "operation", "meta", "noop", "gql", "parts", "fragmentNames", "source", "body", "arguments", "unshift", "j", "definition", "FRAGMENT_DEFINITION", "stringifyDocument", "has", "console", "warn", "DOCUMENT", "shouldSkip", "mapTypeNames", "formattedOperation", "cacheExchange", "forward", "client", "dispatchDebug", "resultCache", "operationCache", "isOperationCached", "requestPolicy", "ops$", "cachedOps$", "map", "cachedResult", "type", "message", "makeResult", "data", "cacheOutcome", "reexecuteOperation", "op", "forwardedOps$", "tap", "response", "typenames", "additionalTypenames", "Set", "concat", "pendingOperations", "typeName", "operations", "values", "clear", "delete", "merge", "revalidated", "ssrExchange", "params", "staleWhileRevalidate", "includeExtensions", "invalidate<PERSON><PERSON><PERSON>", "invalidate", "Promise", "resolve", "shift", "ssr", "isClient", "suspense", "deserializeResult", "JSON", "parse", "extensions", "error", "CombinedError", "networkError", "Error", "graphQLErrors", "serialized", "serializeResult", "stringify", "path", "restoreData", "restore", "extractData", "initialState", "subscriptionExchange", "forwardSubscription", "enableAllOperations", "isSubscriptionOperation", "isSubscriptionOperationFn", "subscriptionResults$", "mergeMap", "teardown$", "takeUntil", "observableish", "makeFetchBody", "make", "observer", "isComplete", "sub", "nextResult", "next", "mergeResultPatch", "errors", "makeErrorResult", "complete", "unsubscribe", "forward$", "debugExchange", "NODE_ENV", "log", "fetchExchange", "fetchResults$", "url", "makeFetchURL", "fetchOptions", "makeFetchOptions", "makeFetchSource", "onPush", "fetchSubscriptions", "composeExchanges", "exchanges", "reduceRight", "exchange", "forwarded", "operations$", "share", "event", "timestamp", "Date", "now", "mapExchange", "onOperation", "onError", "newResult", "fromPromise", "fromValue", "newOperation", "fallbackExchange", "_x", "Client", "opts", "ids", "replays", "active", "dispatched", "queue", "baseOpts", "fetch", "preferGetMethod", "makeSubject", "nextOperation", "isOperationBatchActive", "dispatchOperation", "makeResultSource", "result$", "res", "_instance", "results$", "<PERSON><PERSON><PERSON><PERSON>", "switchMap", "value$", "onEnd", "splice", "onStart", "instance", "this", "create", "prototype", "assign", "queued", "createRequestOperation", "requestOperationType", "getOperationType", "executeRequestOperation", "lazy", "replay", "execute<PERSON>uery", "executeSubscription", "executeMutation", "readQuery", "variables", "createRequest", "subscription", "mutation", "subscribeToDebugTarget", "onEvent", "composedExchange", "publish", "createClient"]}