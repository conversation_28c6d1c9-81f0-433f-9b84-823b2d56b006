"use strict";var V=Object.defineProperty;var R=(r,t,e)=>t in r?V(r,t,{enumerable:!0,configurable:!0,writable:!0,value:e}):r[t]=e;var P=(r,t,e)=>R(r,typeof t!="symbol"?t+"":t,e);Object.defineProperties(exports,{__esModule:{value:!0},[Symbol.toStringTag]:{value:"Module"}});const H=require("lightweight-charts"),T=require("./abstract-indicator.cjs.js"),O=require("../custom-primitive/primitive/region.cjs.js"),W=require("../helpers/utils.cjs.js"),C={color:"#3179f5",priceLineColor:"#a1c3ff",backgroundColor:"#d2e0fa",period1:7,period2:14,period3:28,weight1:4,weight2:2,weight3:1,overlay:!1};class j extends T.ChartIndicator{constructor(e,i,o){super(e,i);P(this,"ultimateOscillatorSeries");this.ultimateOscillatorSeries=e.addSeries(H.LineSeries,{color:this.options.color,lineWidth:2,priceLineVisible:!1,crosshairMarkerVisible:!1,priceScaleId:"ultimateoscillator",autoscaleInfoProvider:W.autoScaleInfoProviderCreator({maxValue:100,minValue:0})},o),this.ultimateOscillatorSeries.attachPrimitive(new O.RegionPrimitive({upPrice:75,lowPrice:70,lineColor:this.options.priceLineColor,backgroundColor:this.options.backgroundColor})),this.ultimateOscillatorSeries.attachPrimitive(new O.RegionPrimitive({upPrice:30,lowPrice:25,lineColor:this.options.priceLineColor,backgroundColor:this.options.backgroundColor}))}getDefaultOptions(){return C}formula(e){const i=this.options.period1,o=this.options.period2,s=this.options.period3,p=this.options.weight1,m=this.options.weight2,d=this.options.weight3,x=e.symbol.high,I=e.symbol.low,g=e.symbol.close,a=Math.max(i,o,s),v=e.new_var(g,a+1);if(!v.calculable())return;const b=v.get(1),f=Math.min(I,b),y=g-f,_=Math.max(x,b)-f,n=e.new_var(y,a),c=e.new_var(_,a);if(!n.calculable()||!c.calculable()||!n.calculable()||!c.calculable())return;const u=q=>{let w=0,h=0;for(let l=0;l<q;l++)w+=n.get(l),h+=c.get(l);return h>0?w/h:0},k=u(i),L=u(o),M=u(s),D=p*k+m*L+d*M,S=p+m+d;return[S>0?100*(D/S):0]}applyIndicatorData(){const e=[];for(const i of this._executionContext.data){const o=i.value;if(!o)continue;const s=i.time;e.push({time:s,value:o[0]})}this.ultimateOscillatorSeries.setData(e)}remove(){super.remove(),this.chart.removeSeries(this.ultimateOscillatorSeries)}_applyOptions(){this.ultimateOscillatorSeries.applyOptions({color:this.options.color}),this.applyIndicatorData()}setPaneIndex(e){this.ultimateOscillatorSeries.moveToPane(e)}getPaneIndex(){return this.ultimateOscillatorSeries.getPane().paneIndex()}}exports.default=j;exports.defaultOptions=C;
//# sourceMappingURL=ultimate-oscillator-indicator.cjs.js.map
