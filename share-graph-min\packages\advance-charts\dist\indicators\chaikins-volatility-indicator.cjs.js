"use strict";var S=Object.defineProperty;var V=(a,e,i)=>e in a?S(a,e,{enumerable:!0,configurable:!0,writable:!0,value:i}):a[e]=i;var d=(a,e,i)=>V(a,typeof e!="symbol"?e+"":e,i);Object.defineProperties(exports,{__esModule:{value:!0},[Symbol.toStringTag]:{value:"Module"}});const k=require("lightweight-charts"),m=require("./abstract-indicator.cjs.js"),P=require("../custom-primitive/primitive/region.cjs.js"),b=require("../helpers/utils.cjs.js"),y={color:"rgba(255, 152, 0, 1)",priceLineColor:"rgba(150, 150, 150, 0.35)",backgroundColor:"#ff98001a",period:10,rocPeriod:12,overlay:!1};class C extends m.ChartIndicator{constructor(i,t,o){super(i,t);d(this,"chaikinsVolatilitySeries");this.chaikinsVolatilitySeries=i.addSeries(k.LineSeries,{color:this.options.color,lineWidth:2,priceLineVisible:!1,crosshairMarkerVisible:!1,priceScaleId:"chaikinsvolatility",autoscaleInfoProvider:b.autoScaleInfoProviderCreator({maxValue:80,minValue:-80})},o),this.chaikinsVolatilitySeries.attachPrimitive(new P.RegionPrimitive({upPrice:5,lowPrice:-5,lineColor:this.options.priceLineColor,backgroundColor:this.options.backgroundColor}))}getDefaultOptions(){return y}formula(i){const t=this.options.period,o=this.options.rocPeriod,n=i.symbol.high,g=i.symbol.low,c=n-g,h=2/(t+1),u=i.new_var(c,t),l=i.new_var(NaN,o+1);if(!u.calculable())return;const p=l.get(1);let r;isNaN(p)?r=u.getAll().reduce((f,v)=>f+v,0)/t:r=h*c+(1-h)*p,l.set(r);const s=l.get(o);return isNaN(s)||s===0?[0]:[(r-s)/s*100]}applyIndicatorData(){const i=[];for(const t of this._executionContext.data){const o=t.value;if(!o)continue;const n=t.time;i.push({time:n,value:o[0]})}this.chaikinsVolatilitySeries.setData(i)}remove(){super.remove(),this.chart.removeSeries(this.chaikinsVolatilitySeries)}_applyOptions(){this.chaikinsVolatilitySeries.applyOptions({color:this.options.color}),this.applyIndicatorData()}setPaneIndex(i){this.chaikinsVolatilitySeries.moveToPane(i)}getPaneIndex(){return this.chaikinsVolatilitySeries.getPane().paneIndex()}}exports.default=C;exports.defaultOptions=y;
//# sourceMappingURL=chaikins-volatility-indicator.cjs.js.map
