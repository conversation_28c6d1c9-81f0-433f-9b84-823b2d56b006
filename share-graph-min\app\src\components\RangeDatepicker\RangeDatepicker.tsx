import { lazy, Suspense, useState } from 'react';
import {
  TRangeDatePickerValue,
  TValuePieceDatePicker,
} from '../../types/datepicker';
// import Datepicker from "./Datepicker";

const Datepicker = lazy(() => import('./Datepicker'));

type RangeDatePickerProps = {
  startDate?: TValuePieceDatePicker;
  endDate?: TValuePieceDatePicker;
  onChange: (range: TRangeDatePickerValue) => void;
  minDate?: Date;
  maxDate?: Date;
};

const now = new Date();

const RangeDatePicker: React.FC<RangeDatePickerProps> = ({
  startDate,
  endDate,
  onChange,
  minDate,
  maxDate,
}) => {
  const [start, setStart] = useState<TValuePieceDatePicker>(startDate || now);
  const [end, setEnd] = useState<TValuePieceDatePicker>(endDate || now);

  const handleStartDateChange = (date) => {
    const newStartDate = date || (start as TValuePieceDatePicker);
    setStart(newStartDate);
    onChange({ startDate: newStartDate, endDate: end });
  };

  const handleEndDateChange = (date) => {
    const newEndDate = date || (end as TValuePieceDatePicker);
    setEnd(newEndDate);
    onChange({ startDate: start, endDate: newEndDate });
  };

  return (
    <div className="range-datepicker">
      <Suspense fallback={null}>
        <div>
          <label>Start Date:</label>
          <Datepicker
            value={start}
            onChange={handleStartDateChange}
            minDate={minDate}
            maxDate={end || maxDate}
            clearIcon={null}
          />
        </div>
        <div>
          <label>End Date:</label>
          <Datepicker
            value={end}
            onChange={handleEndDateChange}
            minDate={start || minDate}
            maxDate={maxDate}
            clearIcon={null}
          />
        </div>
      </Suspense>
    </div>
  );
};

export default RangeDatePicker;
