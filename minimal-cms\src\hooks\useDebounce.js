import { useEffect, useMemo, useRef } from "react";
import _debounce from "lodash.debounce";

const useDebounce = (callback, miniSecond = 300) => {
  const ref = useRef();

  useEffect(() => {
    ref.current = callback;
  }, [callback]);

  const debouncedCallback = useMemo(() => {
    const func = (...args) => {
      ref.current?.(...args);
    };
    return _debounce(func, miniSecond);
  }, []);

  return debouncedCallback;
};

export default useDebounce;
