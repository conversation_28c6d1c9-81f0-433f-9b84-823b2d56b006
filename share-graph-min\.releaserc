{"branches": ["master", {"name": "next", "prerelease": true}], "plugins": ["@semantic-release/commit-analyzer", "@semantic-release/release-notes-generator", ["@semantic-release/npm", {"npmPublish": false}], ["@semantic-release/changelog", {"changelogFile": "./CHANGELOG.md"}], ["@semantic-release/git", {"assets": ["package.json", "CHANGELOG.md"], "message": "chore(release): ${nextRelease.version} [skip ci]\n\n${nextRelease.notes}"}], ["@semantic-release/gitlab", {"gitlabUrl": "https://gitlab.euroland.com", "assets": [{"path": "package.json", "label": "Package.json"}]}]]}