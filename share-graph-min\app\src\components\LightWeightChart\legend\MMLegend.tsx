import { FC } from 'react';
import { Legend } from './Legend';
import { AdvanceChart } from '@sharegraph-mini/advance-charts';
import { MomentumIndicator } from '@sharegraph-mini/advance-charts';

const MomentumLegend: FC<{
  indicator: MomentumIndicator;
  advanceChart: AdvanceChart;
}> = ({ indicator, advanceChart }) => {
  return (
    <Legend
      name={`Momentum(${indicator.options.period})`}
      indicator={indicator}
      renderer={(d) => d && d.value ? (
        <>
          <span style={{ color: indicator.options.color }}>
            {indicator.options.usePercentage
              ? `${advanceChart.numberFormatter.decimal(d.value[0])}%`
              : advanceChart.numberFormatter.decimal(d.value[0])
            }
          </span>
        </>
      ) : null}
    />
  );
};

export default MomentumLegend;
