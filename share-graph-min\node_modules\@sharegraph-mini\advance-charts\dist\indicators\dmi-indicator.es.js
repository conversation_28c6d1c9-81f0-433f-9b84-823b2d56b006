var Y = Object.defineProperty;
var Z = (a, t, e) => t in a ? Y(a, t, { enumerable: !0, configurable: !0, writable: !0, value: e }) : a[t] = e;
var g = (a, t, e) => Z(a, typeof t != "symbol" ? t + "" : t, e);
import { LineSeries as _ } from "lightweight-charts";
import { ChartIndicator as $ } from "./abstract-indicator.es.js";
import { RegionPrimitive as ee } from "../custom-primitive/primitive/region.es.js";
import { autoScaleInfoProviderCreator as L } from "../helpers/utils.es.js";
const se = {
  adxColor: "#f23645",
  // Orange for ADX
  plusDIColor: "#2962ff",
  // Green for +DI
  minusDIColor: "#ff6d00",
  // Red for -DI
  priceLineColor: "rgba(150, 150, 150, 0.35)",
  backgroundColor: "#ff98001a",
  period: 14,
  overlay: !1
};
class le extends $ {
  constructor(e, s, o) {
    super(e, s);
    g(this, "adxSeries");
    g(this, "plusDISeries");
    g(this, "minusDISeries");
    this.adxSeries = e.addSeries(_, {
      color: this.options.adxColor,
      lineWidth: 2,
      priceLineVisible: !1,
      crosshairMarkerVisible: !1,
      priceScaleId: "dmi",
      autoscaleInfoProvider: L({ maxValue: 100, minValue: 0 })
    }, o), this.plusDISeries = e.addSeries(_, {
      color: this.options.plusDIColor,
      lineWidth: 1,
      priceLineVisible: !1,
      crosshairMarkerVisible: !1,
      priceScaleId: "dmi",
      autoscaleInfoProvider: L({ maxValue: 100, minValue: 0 })
    }, o), this.minusDISeries = e.addSeries(_, {
      color: this.options.minusDIColor,
      lineWidth: 1,
      priceLineVisible: !1,
      crosshairMarkerVisible: !1,
      priceScaleId: "dmi",
      autoscaleInfoProvider: L({ maxValue: 100, minValue: 0 })
    }, o), this.adxSeries.attachPrimitive(
      new ee({
        upPrice: 25,
        lowPrice: 20,
        lineColor: this.options.priceLineColor,
        backgroundColor: this.options.backgroundColor
      })
    );
  }
  getDefaultOptions() {
    return se;
  }
  formula(e) {
    const s = this.options.period, o = e.symbol.high, c = e.symbol.low, l = e.symbol.close, n = e.new_var(o, s + 1), N = e.new_var(c, s + 1), k = e.new_var(l, s + 1);
    if (!n.calculable() || !N.calculable() || !k.calculable())
      return;
    const x = o, b = c, E = n.get(1), F = N.get(1), A = k.get(1), m = x - E, D = F - b;
    let M = 0, w = 0;
    m > D && m > 0 ? M = m : D > m && D > 0 && (w = D);
    const G = x - b, J = Math.abs(x - A), K = Math.abs(b - A), H = Math.max(G, J, K), S = 1 / s, O = e.new_var(0, s + 1), T = e.new_var(0, s + 1), R = e.new_var(0, s + 1), W = e.new_var(M, s + 1), X = e.new_var(w, s + 1), j = e.new_var(H, s + 1);
    let v, f, i;
    if (W.calculable() && X.calculable() && j.calculable()) {
      const r = O.get(1), I = T.get(1), p = R.get(1);
      if (isNaN(r) || isNaN(I) || isNaN(p)) {
        const y = W.getAll(), Q = X.getAll(), U = j.getAll();
        v = y.reduce((d, h) => d + h, 0) / s, f = Q.reduce((d, h) => d + h, 0) / s, i = U.reduce((d, h) => d + h, 0) / s;
      } else
        v = r + (M - r) * S, f = I + (w - I) * S, i = p + (H - p) * S;
    } else
      return;
    O.set(v), T.set(f), R.set(i);
    const C = i !== 0 ? 100 * v / i : 0, V = i !== 0 ? 100 * f / i : 0, q = C + V, P = q !== 0 ? 100 * Math.abs(C - V) / q : 0, z = e.new_var(P, s + 1), B = e.new_var(0, s + 1);
    let u;
    if (z.calculable()) {
      const r = B.get(1);
      isNaN(r) ? u = z.getAll().reduce((p, y) => p + y, 0) / s : u = r + (P - r) * S;
    } else
      u = P;
    return B.set(u), [
      u,
      C,
      V
    ];
  }
  applyIndicatorData() {
    const e = [], s = [], o = [];
    for (const c of this._executionContext.data) {
      const l = c.value;
      if (!l) continue;
      const n = c.time;
      e.push({ time: n, value: l[0] }), s.push({ time: n, value: l[1] }), o.push({ time: n, value: l[2] });
    }
    this.adxSeries.setData(e), this.plusDISeries.setData(s), this.minusDISeries.setData(o);
  }
  remove() {
    super.remove(), this.chart.removeSeries(this.adxSeries), this.chart.removeSeries(this.plusDISeries), this.chart.removeSeries(this.minusDISeries);
  }
  _applyOptions() {
    this.adxSeries.applyOptions({ color: this.options.adxColor }), this.plusDISeries.applyOptions({ color: this.options.plusDIColor }), this.minusDISeries.applyOptions({ color: this.options.minusDIColor }), this.applyIndicatorData();
  }
  setPaneIndex(e) {
    this.adxSeries.moveToPane(e), this.plusDISeries.moveToPane(e), this.minusDISeries.moveToPane(e);
  }
  getPaneIndex() {
    return this.adxSeries.getPane().paneIndex();
  }
}
export {
  le as default,
  se as defaultOptions
};
//# sourceMappingURL=dmi-indicator.es.js.map
