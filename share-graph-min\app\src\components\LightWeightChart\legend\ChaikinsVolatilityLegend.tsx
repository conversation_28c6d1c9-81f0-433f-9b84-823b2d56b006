import { FC } from 'react';
import { Legend } from './Legend';
import { ChaikinsVolatilityIndicator } from "@sharegraph-mini/advance-charts";
import { AdvanceChart } from "@sharegraph-mini/advance-charts";

const ChaikinsVolatilityLegend: FC<{
  indicator: ChaikinsVolatilityIndicator;
  advanceChart: AdvanceChart;
}> = ({ indicator, advanceChart }) => {
  return (
    <Legend
      name="Chaikin's Volatility"
      indicator={indicator}
      renderer={(d) =>
        d && d.value ? (
          <span style={{ color: indicator.options.color }}>
            {advanceChart.numberFormatter.decimal(d.value[0])}
          </span>
        ) : null
      }
    />
  );
};

export default ChaikinsVolatilityLegend;
